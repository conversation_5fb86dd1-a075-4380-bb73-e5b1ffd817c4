/* eslint-env node */
require("@rushstack/eslint-patch/modern-module-resolution");

module.exports = {
    root: true,
    extends: [
        "plugin:vue/vue3-essential",
        "eslint:recommended",
        "@vue/eslint-config-typescript/recommended",
        "./.eslintrc-auto-import.json",
    ],
    parserOptions: {
        ecmaVersion: "latest",
    },
    rules: {
        "vue/multi-word-component-names": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "no-undef": 0,
    },
    globals: {
        NodeJS: "readonly",
    },
};
