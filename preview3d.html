<html lang="zh">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <title>3D CAD览图版</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    #webCAD_3D {
      width: 100%;
      height: calc(100vh - 50px);
    }
    #header-3d {
      height: 50px;
      justify-content: space-between;
      padding-left: 16px;
      padding-right: 16px;
      display: flex;
      background-color: #000;
      color: #fff;
    }

    .header-3d-left {
      font-family: PingFangSC-Regular;
      font-size: 16px;
      height: 100%;
      letter-spacing: 0;
      line-height: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
    }

    .header-3d-right {
      display: flex;
      align-items: center;
      font-size: 24;

      svg {
        width: 1em;
        height: 1em;
        cursor: pointer;
        margin-right: 1rem;
      }
    }
  </style>
</head>

<body>
  <div id="header-3d">
    <div class="header-3d-left"></div>
    <div class="header-3d-right">
      <svg id="svg-close" data-v-1533ae48="" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
        <path fill="currentColor"
          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z">
        </path>
      </svg>
    </div>
  </div>
  <div id="webCAD_3D"></div>
  <script type="text/javascript" src="/assets/crypto-js.min.js"></script>
  <script type="text/javascript" src="/assets/hoops-web-viewer-monolith.umd.js"></script>
  <script type="text/javascript" src="/assets/GCAD3D.umd.js"></script>
  <script type="module">
    import { getToken, getQueryObj, errorHandler, getConfig } from '/src/app/common/utils.ts'
    async function start() {
      const token = getToken()
      errorHandler(token)
      const query = getQueryObj()
      if (!query.fileId) {
        alert('fileId不能为空')
        return
      }
      const titleDom = document.querySelector('.header-3d-left')
      titleDom.innerText = query.fileName

      const closeDom = document.querySelector('#svg-close')
      closeDom.addEventListener('click', () => {
        gstarCAD && gstarCAD._destroy()
        window.close()
      })
      const config = await getConfig()
      var gstarCAD = await window.GCAD3D({
        vendorcode: token, //必须，开发者的编码.(由服务器指定生成）
        element: "webCAD_3D",  //容器ID，CAD图纸将这个id对应的容器里面显示
        param: {
          // 图纸服务相关参数
          cadServer: config?.api_endpoint, // 可选,浩辰CAD服务器地址。 当不指定是：https://cloudapi.gstarcad.com
          fileDownLoadUrl: '', // 可选（与fileId二选一）待转换文件的下载地址
          fileId: query.fileId, // 可选（与fileDownLoadUrl二选一）此FILEID是开发商服务器向浩辰服务器PUSH文件时返回的文件ID
          fileToken: '', // 可选,文件信息。用于从数据服务器获取文件。
          fileName: query.fileName, // 可选,显示的文件名称。（该值会参与文件校验）
          etag: query.etag, // 可选,文件的Etag标识数据。（该值会参与文件校验）
          version: query.version, // 可选,文件版本,默认V1
          size: '', // 可选,文件的大小。（该值会参与文件校验）
          userToken: '', // 可选,用户的信息。用于从数据服务器获取文件。（该值不会参与文件校验）

          // 支持GStarSDK实例化构造函数的其他属性,参见其他样例。例：语言配置
          language: 'zh' // GStarSDK属性，设置语言为简中
        }
      })
    }
    start()
  </script>
</body>

</html>