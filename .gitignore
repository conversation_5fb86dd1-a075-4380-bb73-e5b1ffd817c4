# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

yarn.lock
package-lock.json
auto-imports.d.ts
components.d.ts

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/
components.d.ts
.eslintrc-auto-import.json

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.history
*.zip

# #               表示此为注释,将被Git忽略
# *.a             表示忽略所有 .a 结尾的文件
# !lib.a          表示但lib.a除外
# /TODO           表示仅仅忽略项目根目录下的 TODO 文件，不包括 subdir/TODO
# build/          表示忽略 build/目录下的所有文件，过滤整个build文件夹；
# doc/*.txt       表示会忽略doc/notes.txt但不包括 doc/server/arch.txt
# bin/:           表示忽略当前路径下的bin文件夹，该文件夹下的所有内容都会被忽略，不忽略 bin 文件
# /bin:           表示忽略根目录下的bin文件
# /*.c:           表示忽略cat.c，不忽略 build/cat.c
# debug/*.obj:    表示忽略debug/io.obj，不忽略 debug/common/io.obj和tools/debug/io.obj
# **/foo:         表示忽略/foo,a/foo,a/b/foo等
# a/**/b:         表示忽略a/b, a/x/b,a/x/y/b等
# !/bin/run.sh    表示不忽略bin目录下的run.sh文件
# *.log:          表示忽略所有 .log 文件
# config.php:     表示忽略当前路径的 config.php 文件
# /mtk/           表示过滤整个文件夹
# *.zip           表示过滤所有.zip文件