{"name": "gstar-private-clouds-admin", "version": "4.3.0", "scripts": {"dev": "vite --mode development", "build": "run-p type-check build-only", "preview": "vite preview --port 4173 --mode development", "build-only": "vite build --mode production", "build-all": "vite build --mode production && vite --config buildCloudCADBoot.ts build --mode production && vite --config build2DCADBoot.ts build --mode production && vite --config build3DCADBoot.ts build --mode production && vite --config buildCloudCADUtils.ts build --mode production && vite --config buildShareWorker.ts build --mode production", "build-all:cdn": "vite build --mode prodcdn && vite --config buildCloudCADBoot.ts build --mode prodcdn && vite --config build2DCADBoot.ts build --mode prodcdn && vite --config build3DCADBoot.ts build --mode prodcdn && vite --config buildCloudCADUtils.ts build --mode prodcdn && vite --config buildShareWorker.ts build --mode prodcdn", "build-cloudCAD": "vite --config buildCloudCADBoot.ts build --mode production", "build-preview2d": "vite --config build2DCADBoot.ts build --mode production", "build-preview3d": "vite --config build3DCADBoot.ts build --mode production", "build-cloudUtils": "vite --config buildCloudCADUtils.ts  --mode production", "build-shareWorker": "vite --config buildShareWorker.ts build --mode production", "build-cloudCADSDK": "vite --config buildCloudCADSDK.ts build --mode production", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@babel/preset-env": "^7.26.9", "@onlyoffice/document-editor-vue": "1.3.0", "@types/crypto-js": "4.1.1", "@vaadin/button": "24.4.9", "@vaadin/confirm-dialog": "24.5.1", "@vaadin/dialog": "24.4.9", "@vaadin/notification": "24.4.9", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-vue-jsx": "^3.0.0", "ace-builds": "1.31.1", "axios": "0.27.2", "core-js": "^3.41.0", "crypto-js": "4.1.1", "cssnano": "5.1.15", "docx-preview": "0.1.20", "element-plus": "2.2.16", "fingerprintjs2": "2.1.4", "js-base64": "3.7.5", "js-sha1": "0.6.0", "md-editor-v3": "4.8.1", "mdui": "2.1.2", "mitt": "3.0.0", "nprogress": "0.2.0", "pinia": "2.0.17", "pinia-plugin-persist": "1.0.0", "pinia-plugin-persistedstate-2": "2.0.14", "sass": "1.54.8", "sha1-sum": "1.0.0", "socket.io-client": "2.3.0", "spark-md5": "3.0.2", "ts-debounce-throttle": "1.0.0", "vite-plugin-compression": "0.5.1", "vue": "3.2.37", "vue-cropper": "1.0.9", "vue-i18n": "9.2.2", "vue-router": "4.1.3", "vue3-ace-editor": "2.2.2", "vue3-toastify": "0.1.11", "xlsx": "0.18.5"}, "devDependencies": {"@element-plus/icons-vue": "2.0.9", "@iconify-json/ep": "1.1.7", "@rushstack/eslint-patch": "1.1.4", "@types/fingerprintjs2": "2.0.0", "@types/node": "16.11.47", "@types/nprogress": "0.2.0", "@types/sharedworker": "0.0.96", "@types/socket.io-client": "1.4.33", "@vitejs/plugin-vue": "4.0.0", "@vue/eslint-config-typescript": "11.0.0", "@vue/tsconfig": "0.1.3", "autoprefixer": "10.4.13", "eslint": "8.21.0", "eslint-plugin-vue": "9.3.0", "npm-run-all": "4.1.5", "postcss": "8.4.21", "rollup-plugin-visualizer": "5.12.0", "tailwindcss": "3.2.4", "terser": "5.17.7", "typescript": "~4.7.4", "unplugin-auto-import": "0.11.2", "unplugin-icons": "0.14.9", "unplugin-vue-components": "0.22.4", "vite": "4.0.4", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0", "vue-tsc": "0.39.5"}, "overrides": {"@babel/helper-module-imports": "~7.22.15"}, "pnpm": {"overrides": {"@babel/helper-module-imports": "~7.22.15"}}, "resolutions": {"@babel/helper-module-imports": "~7.22.15"}}