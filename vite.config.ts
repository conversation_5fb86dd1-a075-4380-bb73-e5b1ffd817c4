import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import AutoImport from "unplugin-auto-import/vite"; // 自动导入
import Components from "unplugin-vue-components/vite"; // 自动注册
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import { resolve } from "path";
import legacy from '@vitejs/plugin-legacy'
// html script 注入query版本号
import injectScriptVersionPlugin from './vite-plugin-inject-script-version'

export default defineConfig((mode) => {
    console.log(mode);
    const env = loadEnv(mode.mode, process.cwd());
    const isBuild = mode.command === "build";
    return {
        base: env.VITE_PUBLIC_PATH === '' ? '/' : env.VITE_PUBLIC_PATH,
        plugins: [
            vue(),
            vueJsx(),
            AutoImport({
                eslintrc: {
                    enabled: true,
                },
                resolvers: [
                    // 自动导入 Element Olus 相关函数，如：ElMessage, ElMessageBox...（带样式）
                    ElementPlusResolver(),
                ],
                // dts: path.resolve(pathSrc, 'auto-imports.d.ts'),
            }),
            Components({
                resolvers: [
                    // 自动引入 Element Plus 组件
                    ElementPlusResolver(),
                ],
                // dts: path.resolve(pathSrc, 'components.d.ts'),
            }),
            // VueSetupExtend(),
            createSvgIconsPlugin({
                iconDirs: [resolve(process.cwd(), "public/assets/icons")],
                svgoOptions: isBuild,
                // default
                symbolId: "gstar-icon-[name]",
            }),
            // viteCompression({
            //     deleteOriginFile: false,
            //     algorithm: "gzip",
            //     ext: ".gz",
            //   }),
            legacy({
              targets: ['Chrome 57'],
            }),
            injectScriptVersionPlugin()
        ],
        resolve: {
            alias: {
                "@": resolve(__dirname, "src"),
                'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
            },
        },
        server: {
            host: "0.0.0.0",
            port: env.VITE_PORT as unknown as number,
            open: true,
            // cors: true,
            // https: {
            //     key: fs.readFileSync("./keys/_wildcard.51ake.com-key.pem"),
            //     cert: fs.readFileSync("./keys/_wildcard.51ake.com.pem")
            // }
        },
        optimizeDeps: {
            include: ["element-plus/dist/locale/zh-cn.mjs", "element-plus/dist/locale/en.mjs"],
        },
        build: {
          sourcemap: false,
          // target: 'es2015',
          // chunkSizeWarningLimit: 2000,
          // cssCodeSplit: true, //css 拆分
          // sourcemap: false, //不生成sourcemap
          minify: 'terser', //是否禁用最小化混淆，esbuild打包速度最快，terser打包体积最小。
          // assetsInlineLimit: 5000, //小于该值 图片将打包成Base64
          terserOptions: {
              compress: {
                  drop_console: true,
                  drop_debugger: true
              }
          },
          rollupOptions: {
              input: {
                  index: resolve(__dirname, ".", "index.html"),
                  preview2d: resolve(__dirname, ".", "preview2d.html"),
                  preview3d: resolve(__dirname, ".", "preview3d.html"),
                  cloudCAD: resolve(__dirname, ".", "cloudCAD.html"),
              },
              // 静态资源分类打包
              output: {
                  chunkFileNames: "static/js/[name]-[hash].js",
                  entryFileNames: "static/js/[name]-[hash].js",
                  assetFileNames: "static/[ext]/[name]-[hash].[ext]",
              },
              external: {

              }
          },
        },
        define: {
          '__SOCKETIO_MODE__': JSON.stringify('sharedWorker'),
          '__CLIENT_VERSION__': JSON.stringify(process.env.npm_package_version),
        }
    };
});
