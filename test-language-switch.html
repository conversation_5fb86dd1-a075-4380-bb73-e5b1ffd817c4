<!DOCTYPE html>
<html>
<head>
    <title>Language Switch Test</title>
</head>
<body>
    <h1>Language Switch Test</h1>
    <p>Open browser console and run the following commands to test language switching:</p>
    
    <h2>Switch to English:</h2>
    <pre>
localStorage.setItem('locale-store', JSON.stringify({locale: 'en'}));
window.location.reload();
    </pre>
    
    <h2>Switch to Chinese:</h2>
    <pre>
localStorage.setItem('locale-store', JSON.stringify({locale: 'zh_CN'}));
window.location.reload();
    </pre>
    
    <h2>Current locale:</h2>
    <pre id="current-locale"></pre>
    
    <script>
        // Display current locale
        const localeStore = localStorage.getItem('locale-store');
        document.getElementById('current-locale').textContent = localeStore || 'Not set (default: zh_CN)';
        
        // Add buttons for easy switching
        const switchToEn = document.createElement('button');
        switchToEn.textContent = 'Switch to English';
        switchToEn.onclick = () => {
            localStorage.setItem('locale-store', JSON.stringify({locale: 'en'}));
            window.location.reload();
        };
        
        const switchToZh = document.createElement('button');
        switchToZh.textContent = 'Switch to Chinese';
        switchToZh.onclick = () => {
            localStorage.setItem('locale-store', JSON.stringify({locale: 'zh_CN'}));
            window.location.reload();
        };
        
        document.body.appendChild(switchToEn);
        document.body.appendChild(switchToZh);
    </script>
</body>
</html>
