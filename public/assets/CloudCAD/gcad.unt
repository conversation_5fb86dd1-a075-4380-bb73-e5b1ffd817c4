﻿; .............................................................................
; gcad.unt
; Unit conversion factors for using with GCAD, Release 1.0   
;
; Copyright (c) 2012 by Suzhou Gstarsoft Co., Ltd. All Rights Reserved.
; .............................................................................

; .............................................................................
; Exponent definitions

*square,sq
^2

*cubic,cu
^3

; .............................................................................
; SI base units

*meter(s),metre(s),m,centare(s)
-1,0,1,0,-1,1.0,0

*kilogram(s),kg
0,0,0,0,1,1.0,0

*second(s),sec
-2,0,1,0,-1,1.0,0

*kelvin(s),k
2,0,0,-1,1,1.6863043358e-10,0

*ampere(s),amp(s)
2,1,-1,0,1,1.0,0

*candela,cd
4,0,-1,0,2,1.0,0

; .............................................................................
; Temperature

*celsius,centigrade,c
2,0,0,-1,1,1.6863043358e-10,4.6061402932e-8

*rankine
2,0,0,-1,1,9.3683574212e-11,0

*fahrenheit
2,0,0,-1,1,9.3683574212e-11,4.30635285578e-8

; .............................................................................
; Length

*decimeter(s),decimetre(s),dm
=meter 0.1

*centimeter(s),centimetre(s),cm
=meter 0.01

*millimeter(s),millimetre(s),mm
=meter 0.001

*micron(s)
=meter 1.0e-6

*millimicro(s),nanometer(s),nanometre(s)
=meter 1.0e-9

*dekameter(s),decameter(s),dekametre(s),decametre(s),dam
=meter 10

*hectometer(s),hectometre(s),hm
=meter 100

*kilometer(s),kilometre(s),km
=meter 1000

*gigameter(s),gigametre(s)
=meter 1.0e9

*angstrom(s)
=meter 1.0e-10

*astronomical_unit(s),au
=meter 1.495978707e+11

*lightyear(s),ly  ; light_speed by tropical_year
=meter 9.4607304725808e15

*parsec(s),pc  ;;;
=meter 3.0856775670468e16

*inch(es),in(s),"
=meter 0.0254

*microinch(es)
=meter 2.54e-8

*foot,feet,ft,'
=meter 0.3048

*survey_foot,survey_feet
=meter 0.30480060960122

*yard(s),yd
=meter 0.9144

*mil(s)
=meter 2.54e-5

*mile(s),mile_statute,mi
=meter 1609.344

*league_statute
=meter 4828.032

*mile_nautical,inm
=meter 1852

*league_nautical
=meter 5556

*fathom(s),fath
=meter 1.8288

*furlong(s),fur
=meter 201.168

*bolt(s)  ; 40 yards
=meter 36.576

*cable(s) ; 8 bolts
=meter 219.456

*chain(s) ; 66 feet (Gunter's chain)
=meter 20.1168

*link(s)  ; 1/100 chain (Gunter's link)
=meter 0.201168

*pace(s)  ; 3 inches
=meter 0.762

*palm(s)  ; 1/10 pace
=meter 0.0762

*rod(s)   ; 66 palms
=meter 5.0292

*perch(es)  ; 1 rod
=meter 5.0292

*rope(s)  ; 20 feet
=meter 6.096

*skein(s) ; 120 yards
=meter 109.728

*hand(s)
=meter 0.1016

*span(s)
=meter 0.2286

*cubit(s)
=meter 0.4572

*point(s)
=meter 3.514598e-4

*pica(s)
=meter 4.2175176e-3

*IJCAD_C
=meter 0.3030303

*IJCAD_J
=meter 1.8182

*IJCAD_L
=meter 3927.3

; .............................................................................
; Area

*square_meter(s),sq_meter(s),square_metre(s),sq_metre(s),sq(uare)_m
-2,0,2,0,-2,1.0,0

*square_centimeter(s),square_centimetre(s),square_cm(s)
=square_meter 1e-4
*sq_centimeter(s),sq_centimetre(s),sq_cm(s)
=square_meter 1e-4

*square_millimeter(s),square_millimetre(s),square_mm(s)
=square_meter 1e-6
*sq_millimeter(s),sq_millimetre(s),sq_mm(s)
=square_meter 1e-6

*square_kilometer(s),square_kilometre(s),square_km
=square_meter 1e6
*sq_kilometer(s),sq_kilometre(s),sq_km
=square_meter 1e6

*centare(s)
=square_meter 1

*are(s)
=square_meter 100

*hectare(s)
=square_meter 10000

*sq(uare)_foot,sq(uare)_feet,sq(uare)_ft
=square_meter 0.09290304

*square_inch(es),sq_inch(es),square_in(s),sq_in(s)
=square_meter 0.00064516

*square_yard(s),sq_yard(s),square_yd(s),sq_yd(s)
=square_meter 0.83612736

*acre(s)  ; 66 by 660 feet
=square_meter 4046.8564224

*rood(s)  ; 1/4 acre
=square_meter 1011.7141056

*township(s)  ; 6 by 6  miles
=square_meter 93239571.456

*barn(s)
=square_meter 1.0e-28

; .............................................................................
; Volume

*stere(s)
-3,0,3,0,-3,1.0,0

*decistere(s)
=stere 0.1

*decastere(s),dekastere(s)
=stere 10

*cc  ; cubic centimetre (cm^3)
=stere 1.0e-6

*liter(s),litre(s)  ; Redefined 0.001 m^3 after 1964
-3,0,3,0,-3,0.001000028,0

*decaliter(s),dekaliter(s),decalitre(s),dekalitre(s)
=liter 10

*hectoliter(s),hectolitre(s)
=liter 100

*kiloliter(s),kilolitre(s)
=liter 1000

*deciliter(s)
=liter 0.1

*centiliter(s),centilitre(s)
=liter 0.01

*milliliter(s),millilitre(s),ml
=liter 0.001

*board_foot,board_feet  ; 144 in^3
=stere 0.002359737216

*register_ton(s)  ; 100 ft^3
=stere 2.8316846592

*cord(s)  ; approximately 128 survey_ft^3
=stere 3.6245734

*dry_gallon(s)
=stere 0.0044048828

*dry_pint(s)  ; 1/8 dry_gallon
=stere 0.00055061035

*dry_quart(s)  ; 1/4 dry_gallon
=stere 0.0011012207

*peck(s)  ; 2 dry_gallons
=stere 0.0088097656

*bushel(s),bu  ; 8 dry_gallons
=stere 0.03523907

*fluid_ounce(s)  ;;; US
=stere 2.95735296875e-5

*fluid_cup(s),cup(s)  ; 8 fluid_ounces
=stere 0.0002365882375

*fluid_gallon(s),gallon(s),gal
=stere 0.0037854118

*fluid_pint(s),pint(s)  ; 1/8 gallon
=stere 0.000473176475

*fluid_quart(s),quart(s),qt  ; 1/4 gallon
=stere 0.00094635295

*gill(s)  ; 1/32 gallon
=stere 0.00011829411875

*fluid_dram(s),dram(s),dr  ; 1/1024 gallon
=stere 3.6966912109375e-6

*firkin(s)  ; 9 gallons
=stere 0.0340687062

*barrel(s)  ; 42 gallons
=stere 0.1589872956

*hogshead(s)  ; 63 gallons
=stere 0.2384809434

*butt(s)  ; 126 gallons or 2 hogsheads
=stere 0.4769618868

*tun(s)  ; 252 gallons or 4 hogsheads
=stere 0.9539237736

*minim(s)  ; 1/61440 gallon
=stere 6.1611520182292e-8

*british_gallon(s)  ; 1 british_gallon = 1.20095 gallon
=stere 0.004546087

*pottle(s)  ; 1/2 british_gallons
=stere 0.0022730435

*kilderkin(s)  ; 18 british_gallons
=stere 0.08182957

*seam(s)  ; 64 british_gallons
=stere 0.290949568

*puncheon(s)  ; approximately 2 barrels
=stere 0.31797510

; .............................................................................
; Mass

*gram(s),gm,g
=kg 0.001

*milligram(s),mg
=kg 1.0e-6

*microgram(s)
=kg 1.0e-9

*ton_metric,tonne(s)
=kg 1000

*pound(s),lb(s)
=kg 0.45359237

*ounce_weight,ounce(s),oz
=kg 0.028349523125

*long_ton(s)
=kg 1016.0469088

*ton_short,ton(s)
=kg 907.18474

*hundredweight(s)
=kg 45.359237

*cental(s)
=kg 45.359237

*stone(s)
=kg 6.35029318

*dalton(s)
=kg 1.66024e-27

*ounce_troy
0,0,0,0,1,0.031103486,0

*grain(s)  ; (1/480 ounce_troy)
=ounce_troy/480

*pennyweight(s),dwt,pwt  ; (1/20 ounce_troy)
=ounce_troy/20

*scruple(s)  ; (1/24 ounce_troy)
=ounce_troy/24

*poundal(s)
=kg 0.01409808

*slug(s)
=kg 14.5939

; .............................................................................
; Time

*millisecond(s),msec
=second 0.001

*microsecond(s)
=second 1.0e-6

*nanosecond(s),nsec
=second 1.0e-9

*minute(s),min(s)
=second 60

*hour(s),hr(s)
=second 3600

*day(s)
=second 86400

*week(s),wk
=second 604800

*fortnight(s)
=second 1209600

*calendar_year(s),year(s),yr
=second 31536000

*decade(s)
=second 315360000

*century,centuries
=second 3153600000

*millennium,millennia,millenium,millenia
=second 31536000000

*sidereal_year(s)
=second 31558149.504

*tropical_year(s)
=second 31556925.216

; .............................................................................
; Frequency

*hertz,hz
=1/second

; .............................................................................
; Speed, Acceleration, Force, Pressure, Energy, Power

*light_speed
1,0,0,0,0,299792458,0

*knot(s)
1,0,0,0,0,0.51444444444444444444,0


*g_acceleration
3,0,-1,0,1,9.80665,0


*newton(s)
3,0,-1,0,2,1.0,0

*dyne(s)
=newton 1.0e-5

*force_pound(s)
=newton 4.4482216765935


*pascal
5,0,-3,0,4,1.0,0

*atmosphere(s),atm
=pascal 101325

*bar(s)
=pascal 1.0e5

*barye(s)
=pascal 0.1

*torr(s),mm_of_hg
=pascal 133.32236842105


*joule(s)
2,0,0,0,1,1.0,0

*british_thermal_unit(s),btu(s)
=joule 1054.35

*calorie(s),cal
=joule 4.184

*kilocalorie(s),kcal
=joule 4184

*erg(s)
=joule 1.0e-7

*electron_volt(s),ev
2,0,0,0,1,1.60209e-19,0


*watt(s),w
4,0,-1,0,2,1.0,0

*kilowatt(s),kw
=watt 1000

*horsepower,hp
=watt 745.7

; .............................................................................
; Electric charge, current, force, resistance, capacitance, inductance

*coulomb(s)  ; ampere mul second
0,1,0,0,0,1.0,0

*electron_charge(s)
0,1,0,0,0,1.60209e-19,0


*milliampere(s),milliamp(s),ma
=ampere 1.0e-3

*microampere(s),microamp(s)
=ampere 1.0e-6


*volt(s),v
2,-1,0,0,1,1.0,0

*millivolt(s),mv
=volt 1.0e-3

*microvolt(s)
=volt 1.0e-6


*ohm(s)  ; volt div ampere
0,-2,1,0,0,1.0,0


*farad(s)  ; coulomb div volt
-2,2,0,0,-1,1.0,0


*henry,henries  ; ohm mul second
-2,-2,2,0,-1,1.0,0

; .............................................................................
; Magnetic flux, induction

*weber(s)
0,-1,1,0,0,1.0,0

*maxwell(s)
=weber 1.0e-8


*tesla(s)
2,-1,-1,0,2,1.0,0

*gauss(es)
=tesla 1.0e-4

; .............................................................................
; Angular measure and Solid measure 

*arc_second(s)
0,0,0,0,0,1,0

*arc_minute(s)
0,0,0,0,0,60,0

*degree(s),deg
0,0,0,0,0,3600,0

*circle(s)
0,0,0,0,0,1296000,0

*grad(s)
0,0,0,0,0,3240,0

*quadrant(s)
0,0,0,0,0,324000,0

*radian(s)
0,0,0,0,0,206264.80624709636,0

*steradian(s)
0,0,0,0,0,1,0

*hemisphere(s)
0,0,0,0,0,6.28318530717958647692,0

*sphere(s)
0,0,0,0,0,12.56637061435917295384,0

; .............................................................................
; Dimensionless Multiples and Fractions

*deca
0,0,0,0,0,10,0
*hecto
0,0,0,0,0,100,0
*kilo
0,0,0,0,0,1000,0
*mega
0,0,0,0,0,1e6,0
*giga
0,0,0,0,0,1e9,0
*tera
0,0,0,0,0,1e12,0
*peta
0,0,0,0,0,1e15,0
*exa
0,0,0,0,0,1e18,0

*deci
0,0,0,0,0,0.1,0
*centi
0,0,0,0,0,0,0.01,0
*milli
0,0,0,0,0,0.001,0
*micro
0,0,0,0,0,1e-6,0
*nano
0,0,0,0,0,1e-9,0
*pico
0,0,0,0,0,1e-12,0
*femto
0,0,0,0,0,1e-15,0
*atto
0,0,0,0,0,1e-18,0
