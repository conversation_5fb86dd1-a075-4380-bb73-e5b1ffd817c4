
var GCADM = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(moduleArg = {}) {

// include: shell.js
// The Module object: Our interface to the outside world. We import
// and export values on it. There are various ways Module can be used:
// 1. Not defined. We create it here
// 2. A function parameter, function(Module) { ..generated code.. }
// 3. pre-run appended it, var Module = {}; ..generated code..
// 4. External script tag defines var Module.
// We need to check if Module already exists (e.g. case 3 above).
// Substitution will be replaced with actual code on later stage of the build,
// this way Closure Compiler will not mangle it (e.g. case 4. above).
// Note that if you want to run closure, and also to use Module
// after the generated code, you will need to define   var Module = {};
// before the code. Then that object will be used in the code, and you
// can continue to use Module afterwards as well.
var Module = moduleArg;

// Set up the promise that indicates the Module is initialized
var readyPromiseResolve, readyPromiseReject;
Module['ready'] = new Promise((resolve, reject) => {
  readyPromiseResolve = resolve;
  readyPromiseReject = reject;
});

// --pre-jses are emitted after the Module integration code, so that they can
// refer to Module (if they choose; they can also define Module)


// Sometimes an existing Module object exists with properties
// meant to overwrite the default module functionality. Here
// we collect those properties and reapply _after_ we configure
// the current environment's defaults to avoid having to be so
// defensive during initialization.
var moduleOverrides = Object.assign({}, Module);

var arguments_ = [];
var thisProgram = './this.program';
var quit_ = (status, toThrow) => {
  throw toThrow;
};

// Determine the runtime environment we are in. You can customize this by
// setting the ENVIRONMENT setting at compile time (see settings.js).

// Attempt to auto-detect the environment
var ENVIRONMENT_IS_WEB = typeof window == 'object';
var ENVIRONMENT_IS_WORKER = typeof importScripts == 'function';
// N.b. Electron.js environment is simultaneously a NODE-environment, but
// also a web environment.
var ENVIRONMENT_IS_NODE = typeof process == 'object' && typeof process.versions == 'object' && typeof process.versions.node == 'string';
var ENVIRONMENT_IS_SHELL = !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_NODE && !ENVIRONMENT_IS_WORKER;

// `/` should be present at the end if `scriptDirectory` is not empty
var scriptDirectory = '';
function locateFile(path) {
  if (Module['locateFile']) {
    return Module['locateFile'](path, scriptDirectory);
  }
  return scriptDirectory + path;
}

// Hooks that are implemented differently in different runtime environments.
var read_,
    readAsync,
    readBinary,
    setWindowTitle;

if (ENVIRONMENT_IS_NODE) {

  // `require()` is no-op in an ESM module, use `createRequire()` to construct
  // the require()` function.  This is only necessary for multi-environment
  // builds, `-sENVIRONMENT=node` emits a static import declaration instead.
  // TODO: Swap all `require()`'s with `import()`'s?
  // These modules will usually be used on Node.js. Load them eagerly to avoid
  // the complexity of lazy-loading.
  var fs = require('fs');
  var nodePath = require('path');

  if (ENVIRONMENT_IS_WORKER) {
    scriptDirectory = nodePath.dirname(scriptDirectory) + '/';
  } else {
    scriptDirectory = __dirname + '/';
  }

// include: node_shell_read.js
read_ = (filename, binary) => {
  var ret = tryParseAsDataURI(filename);
  if (ret) {
    return binary ? ret : ret.toString();
  }
  // We need to re-wrap `file://` strings to URLs. Normalizing isn't
  // necessary in that case, the path should already be absolute.
  filename = isFileURI(filename) ? new URL(filename) : nodePath.normalize(filename);
  return fs.readFileSync(filename, binary ? undefined : 'utf8');
};

readBinary = (filename) => {
  var ret = read_(filename, true);
  if (!ret.buffer) {
    ret = new Uint8Array(ret);
  }
  return ret;
};

readAsync = (filename, onload, onerror, binary = true) => {
  var ret = tryParseAsDataURI(filename);
  if (ret) {
    onload(ret);
  }
  // See the comment in the `read_` function.
  filename = isFileURI(filename) ? new URL(filename) : nodePath.normalize(filename);
  fs.readFile(filename, binary ? undefined : 'utf8', (err, data) => {
    if (err) onerror(err);
    else onload(binary ? data.buffer : data);
  });
};
// end include: node_shell_read.js
  if (!Module['thisProgram'] && process.argv.length > 1) {
    thisProgram = process.argv[1].replace(/\\/g, '/');
  }

  arguments_ = process.argv.slice(2);

  // MODULARIZE will export the module in the proper place outside, we don't need to export here

  quit_ = (status, toThrow) => {
    process.exitCode = status;
    throw toThrow;
  };

  Module['inspect'] = () => '[Emscripten Module object]';

} else

// Note that this includes Node.js workers when relevant (pthreads is enabled).
// Node.js workers are detected as a combination of ENVIRONMENT_IS_WORKER and
// ENVIRONMENT_IS_NODE.
if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
  if (ENVIRONMENT_IS_WORKER) { // Check worker, not web, since window could be polyfilled
    scriptDirectory = self.location.href;
  } else if (typeof document != 'undefined' && document.currentScript) { // web
    scriptDirectory = document.currentScript.src;
  }
  // When MODULARIZE, this JS may be executed later, after document.currentScript
  // is gone, so we saved it, and we use it here instead of any other info.
  if (_scriptDir) {
    scriptDirectory = _scriptDir;
  }
  // blob urls look like blob:http://site.com/etc/etc and we cannot infer anything from them.
  // otherwise, slice off the final part of the url to find the script directory.
  // if scriptDirectory does not contain a slash, lastIndexOf will return -1,
  // and scriptDirectory will correctly be replaced with an empty string.
  // If scriptDirectory contains a query (starting with ?) or a fragment (starting with #),
  // they are removed because they could contain a slash.
  if (scriptDirectory.indexOf('blob:') !== 0) {
    scriptDirectory = scriptDirectory.substr(0, scriptDirectory.replace(/[?#].*/, "").lastIndexOf('/')+1);
  } else {
    scriptDirectory = '';
  }

  // Differentiate the Web Worker from the Node Worker case, as reading must
  // be done differently.
  {
// include: web_or_worker_shell_read.js
read_ = (url) => {
    try {
      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, false);
      xhr.send(null);
      return xhr.responseText;
    } catch (err) {
      var data = tryParseAsDataURI(url);
      if (data) {
        return intArrayToString(data);
      }
      throw err;
    }
  }

  if (ENVIRONMENT_IS_WORKER) {
    readBinary = (url) => {
      try {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', url, false);
        xhr.responseType = 'arraybuffer';
        xhr.send(null);
        return new Uint8Array(/** @type{!ArrayBuffer} */(xhr.response));
      } catch (err) {
        var data = tryParseAsDataURI(url);
        if (data) {
          return data;
        }
        throw err;
      }
    };
  }

  readAsync = (url, onload, onerror) => {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'arraybuffer';
    xhr.onload = () => {
      if (xhr.status == 200 || (xhr.status == 0 && xhr.response)) { // file URLs can return 0
        onload(xhr.response);
        return;
      }
      var data = tryParseAsDataURI(url);
      if (data) {
        onload(data.buffer);
        return;
      }
      onerror();
    };
    xhr.onerror = onerror;
    xhr.send(null);
  }

// end include: web_or_worker_shell_read.js
  }

  setWindowTitle = (title) => document.title = title;
} else
{
}

var out = Module['print'] || console.log.bind(console);
var err = Module['printErr'] || console.error.bind(console);

// Merge back in the overrides
Object.assign(Module, moduleOverrides);
// Free the object hierarchy contained in the overrides, this lets the GC
// reclaim data used e.g. in memoryInitializerRequest, which is a large typed array.
moduleOverrides = null;

// Emit code to handle expected values on the Module object. This applies Module.x
// to the proper local x. This has two benefits: first, we only emit it if it is
// expected to arrive, and second, by using a local everywhere else that can be
// minified.

if (Module['arguments']) arguments_ = Module['arguments'];

if (Module['thisProgram']) thisProgram = Module['thisProgram'];

if (Module['quit']) quit_ = Module['quit'];

// perform assertions in shell.js after we set up out() and err(), as otherwise if an assertion fails it cannot print the message

// end include: shell.js
// include: preamble.js
// === Preamble library stuff ===

// Documentation for the public APIs defined in this file must be updated in:
//    site/source/docs/api_reference/preamble.js.rst
// A prebuilt local version of the documentation is available at:
//    site/build/text/docs/api_reference/preamble.js.txt
// You can also build docs locally as HTML or other formats in site/
// An online HTML version (which may be of a different version of Emscripten)
//    is up at http://kripken.github.io/emscripten-site/docs/api_reference/preamble.js.html

var wasmBinary;
if (Module['wasmBinary']) wasmBinary = Module['wasmBinary'];
var noExitRuntime = Module['noExitRuntime'] || true;

// include: wasm2js.js
// wasm2js.js - enough of a polyfill for the WebAssembly object so that we can load
// wasm2js code that way.

// Emit "var WebAssembly" if definitely using wasm2js. Otherwise, in MAYBE_WASM2JS
// mode, we can't use a "var" since it would prevent normal wasm from working.
/** @suppress{duplicate, const} */
var
WebAssembly = {
  // Note that we do not use closure quoting (this['buffer'], etc.) on these
  // functions, as they are just meant for internal use. In other words, this is
  // not a fully general polyfill.
  /** @constructor */
  Memory: function(opts) {
    this.buffer = new ArrayBuffer(opts['initial'] * 65536);
  },

  Module: function(binary) {
    // TODO: use the binary and info somehow - right now the wasm2js output is embedded in
    // the main JS
  },

  /** @constructor */
  Instance: function(module, info) {
    // TODO: use the module somehow - right now the wasm2js output is embedded in
    // the main JS
    // This will be replaced by the actual wasm2js code.
    this.exports = (
function instantiate(info) {
function Table(ret) {
  // grow method not included; table is not growable
  ret.set = function(i, func) {
    this[i] = func;
  };
  ret.get = function(i) {
    return this[i];
  };
  return ret;
}

  var bufferView;
  var base64ReverseLookup = new Uint8Array(123/*'z'+1*/);
  for (var i = 25; i >= 0; --i) {
    base64ReverseLookup[48+i] = 52+i; // '0-9'
    base64ReverseLookup[65+i] = i; // 'A-Z'
    base64ReverseLookup[97+i] = 26+i; // 'a-z'
  }
  base64ReverseLookup[43] = 62; // '+'
  base64ReverseLookup[47] = 63; // '/'
  /** @noinline Inlining this function would mean expanding the base64 string 4x times in the source code, which Closure seems to be happy to do. */
  function base64DecodeToExistingUint8Array(uint8Array, offset, b64) {
    var b1, b2, i = 0, j = offset, bLength = b64.length, end = offset + (bLength*3>>2) - (b64[bLength-2] == '=') - (b64[bLength-1] == '=');
    for (; i < bLength; i += 4) {
      b1 = base64ReverseLookup[b64.charCodeAt(i+1)];
      b2 = base64ReverseLookup[b64.charCodeAt(i+2)];
      uint8Array[j++] = base64ReverseLookup[b64.charCodeAt(i)] << 2 | b1 >> 4;
      if (j < end) uint8Array[j++] = b1 << 4 | b2 >> 2;
      if (j < end) uint8Array[j++] = b2 << 6 | base64ReverseLookup[b64.charCodeAt(i+3)];
    }
  }
function initActiveSegments(imports) {
  base64DecodeToExistingUint8Array(bufferView, 1024, "AAYBAA==");
  base64DecodeToExistingUint8Array(bufferView, 1028, "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=");
}
function asmFunc(imports) {
 var env = imports.env;
 var memory = env.memory;
 var buffer = memory.buffer;
 var HEAP8 = new Int8Array(buffer);
 var HEAP16 = new Int16Array(buffer);
 var HEAP32 = new Int32Array(buffer);
 var HEAPU8 = new Uint8Array(buffer);
 var HEAPU16 = new Uint16Array(buffer);
 var HEAPU32 = new Uint32Array(buffer);
 var HEAPF32 = new Float32Array(buffer);
 var HEAPF64 = new Float64Array(buffer);
 var Math_imul = Math.imul;
 var Math_fround = Math.fround;
 var Math_abs = Math.abs;
 var Math_clz32 = Math.clz32;
 var Math_min = Math.min;
 var Math_max = Math.max;
 var Math_floor = Math.floor;
 var Math_ceil = Math.ceil;
 var Math_trunc = Math.trunc;
 var Math_sqrt = Math.sqrt;
 var fimport$0 = env.emscripten_resize_heap;
 var global$0 = 67072;
 // EMSCRIPTEN_START_FUNCS
;
 function $0() {
  
 }
 
 function $1() {
  return 1028;
 }
 
 function $3($0_1) {
  var $1_1 = 0, $2 = 0;
  $1_1 = HEAP32[256];
  $2 = $0_1 + 7 & -8;
  $0_1 = $1_1 + $2 | 0;
  label$1 : {
   if (!!$2 & $0_1 >>> 0 <= $1_1 >>> 0) {
    break label$1
   }
   if ($0_1 >>> 0 > __wasm_memory_size() << 16 >>> 0) {
    if (!(fimport$0($0_1 | 0) | 0)) {
     break label$1
    }
   }
   HEAP32[256] = $0_1;
   return $1_1;
  }
  HEAP32[257] = 48;
  return -1;
 }
 
 function $4($0_1) {
  $0_1 = $0_1 | 0;
  var $1_1 = 0, $2 = 0, $3_1 = 0, $4_1 = 0, $5_1 = 0, $6_1 = 0, $7_1 = 0, $8_1 = 0, $9 = 0, $10 = 0, $11 = 0;
  $11 = global$0 - 16 | 0;
  global$0 = $11;
  label$1 : {
   label$2 : {
    label$3 : {
     label$4 : {
      label$5 : {
       label$6 : {
        label$7 : {
         label$8 : {
          label$9 : {
           label$10 : {
            label$11 : {
             label$12 : {
              label$13 : {
               label$14 : {
                if ($0_1 >>> 0 <= 244) {
                 $5_1 = HEAP32[258];
                 $6_1 = $0_1 >>> 0 < 11 ? 16 : $0_1 + 11 & -8;
                 $0_1 = $6_1 >>> 3 | 0;
                 $1_1 = $5_1 >>> $0_1 | 0;
                 if ($1_1 & 3) {
                  $2 = $0_1 + (($1_1 ^ -1) & 1) | 0;
                  $1_1 = $2 << 3;
                  $0_1 = $1_1 + 1072 | 0;
                  $1_1 = HEAP32[$1_1 + 1080 >> 2];
                  $3_1 = HEAP32[$1_1 + 8 >> 2];
                  label$17 : {
                   if (($0_1 | 0) == ($3_1 | 0)) {
                    HEAP32[258] = __wasm_rotl_i32($2) & $5_1;
                    break label$17;
                   }
                   HEAP32[$3_1 + 12 >> 2] = $0_1;
                   HEAP32[$0_1 + 8 >> 2] = $3_1;
                  }
                  $0_1 = $1_1 + 8 | 0;
                  $2 = $2 << 3;
                  HEAP32[$1_1 + 4 >> 2] = $2 | 3;
                  $1_1 = $1_1 + $2 | 0;
                  HEAP32[$1_1 + 4 >> 2] = HEAP32[$1_1 + 4 >> 2] | 1;
                  break label$1;
                 }
                 $7_1 = HEAP32[260];
                 if ($7_1 >>> 0 >= $6_1 >>> 0) {
                  break label$14
                 }
                 if ($1_1) {
                  $2 = 2 << $0_1;
                  $0_1 = (0 - $2 | $2) & $1_1 << $0_1;
                  $1_1 = __wasm_ctz_i32(0 - $0_1 & $0_1);
                  $0_1 = $1_1 << 3;
                  $2 = $0_1 + 1072 | 0;
                  $0_1 = HEAP32[$0_1 + 1080 >> 2];
                  $3_1 = HEAP32[$0_1 + 8 >> 2];
                  label$20 : {
                   if (($2 | 0) == ($3_1 | 0)) {
                    $5_1 = __wasm_rotl_i32($1_1) & $5_1;
                    HEAP32[258] = $5_1;
                    break label$20;
                   }
                   HEAP32[$3_1 + 12 >> 2] = $2;
                   HEAP32[$2 + 8 >> 2] = $3_1;
                  }
                  HEAP32[$0_1 + 4 >> 2] = $6_1 | 3;
                  $8_1 = $0_1 + $6_1 | 0;
                  $1_1 = $1_1 << 3;
                  $3_1 = $1_1 - $6_1 | 0;
                  HEAP32[$8_1 + 4 >> 2] = $3_1 | 1;
                  HEAP32[$0_1 + $1_1 >> 2] = $3_1;
                  if ($7_1) {
                   $1_1 = ($7_1 & -8) + 1072 | 0;
                   $2 = HEAP32[263];
                   $4_1 = 1 << ($7_1 >>> 3);
                   label$23 : {
                    if (!($4_1 & $5_1)) {
                     HEAP32[258] = $4_1 | $5_1;
                     $4_1 = $1_1;
                     break label$23;
                    }
                    $4_1 = HEAP32[$1_1 + 8 >> 2];
                   }
                   HEAP32[$1_1 + 8 >> 2] = $2;
                   HEAP32[$4_1 + 12 >> 2] = $2;
                   HEAP32[$2 + 12 >> 2] = $1_1;
                   HEAP32[$2 + 8 >> 2] = $4_1;
                  }
                  $0_1 = $0_1 + 8 | 0;
                  HEAP32[263] = $8_1;
                  HEAP32[260] = $3_1;
                  break label$1;
                 }
                 $10 = HEAP32[259];
                 if (!$10) {
                  break label$14
                 }
                 $2 = HEAP32[(__wasm_ctz_i32($10 & 0 - $10) << 2) + 1336 >> 2];
                 $4_1 = (HEAP32[$2 + 4 >> 2] & -8) - $6_1 | 0;
                 $1_1 = $2;
                 while (1) {
                  label$25 : {
                   $0_1 = HEAP32[$1_1 + 16 >> 2];
                   if (!$0_1) {
                    $0_1 = HEAP32[$1_1 + 20 >> 2];
                    if (!$0_1) {
                     break label$25
                    }
                   }
                   $3_1 = (HEAP32[$0_1 + 4 >> 2] & -8) - $6_1 | 0;
                   $1_1 = $3_1 >>> 0 < $4_1 >>> 0;
                   $4_1 = $1_1 ? $3_1 : $4_1;
                   $2 = $1_1 ? $0_1 : $2;
                   $1_1 = $0_1;
                   continue;
                  }
                  break;
                 };
                 $9 = HEAP32[$2 + 24 >> 2];
                 $3_1 = HEAP32[$2 + 12 >> 2];
                 if (($2 | 0) != ($3_1 | 0)) {
                  $0_1 = HEAP32[$2 + 8 >> 2];
                  HEAP32[$0_1 + 12 >> 2] = $3_1;
                  HEAP32[$3_1 + 8 >> 2] = $0_1;
                  break label$2;
                 }
                 $1_1 = $2 + 20 | 0;
                 $0_1 = HEAP32[$1_1 >> 2];
                 if (!$0_1) {
                  $0_1 = HEAP32[$2 + 16 >> 2];
                  if (!$0_1) {
                   break label$13
                  }
                  $1_1 = $2 + 16 | 0;
                 }
                 while (1) {
                  $8_1 = $1_1;
                  $3_1 = $0_1;
                  $1_1 = $0_1 + 20 | 0;
                  $0_1 = HEAP32[$1_1 >> 2];
                  if ($0_1) {
                   continue
                  }
                  $1_1 = $3_1 + 16 | 0;
                  $0_1 = HEAP32[$3_1 + 16 >> 2];
                  if ($0_1) {
                   continue
                  }
                  break;
                 };
                 HEAP32[$8_1 >> 2] = 0;
                 break label$2;
                }
                $6_1 = -1;
                if ($0_1 >>> 0 > 4294967231) {
                 break label$14
                }
                $0_1 = $0_1 + 11 | 0;
                $6_1 = $0_1 & -8;
                $8_1 = HEAP32[259];
                if (!$8_1) {
                 break label$14
                }
                $4_1 = 0 - $6_1 | 0;
                $7_1 = 0;
                label$31 : {
                 if ($6_1 >>> 0 < 256) {
                  break label$31
                 }
                 $7_1 = 31;
                 if ($6_1 >>> 0 > 16777215) {
                  break label$31
                 }
                 $0_1 = Math_clz32($0_1 >>> 8 | 0);
                 $7_1 = (($6_1 >>> 38 - $0_1 & 1) - ($0_1 << 1) | 0) + 62 | 0;
                }
                $1_1 = HEAP32[($7_1 << 2) + 1336 >> 2];
                label$32 : {
                 label$33 : {
                  label$34 : {
                   if (!$1_1) {
                    $0_1 = 0;
                    break label$34;
                   }
                   $0_1 = 0;
                   $2 = $6_1 << (($7_1 | 0) != 31 ? 25 - ($7_1 >>> 1 | 0) | 0 : 0);
                   while (1) {
                    label$37 : {
                     $5_1 = (HEAP32[$1_1 + 4 >> 2] & -8) - $6_1 | 0;
                     if ($5_1 >>> 0 >= $4_1 >>> 0) {
                      break label$37
                     }
                     $3_1 = $1_1;
                     $4_1 = $5_1;
                     if ($4_1) {
                      break label$37
                     }
                     $4_1 = 0;
                     $0_1 = $1_1;
                     break label$33;
                    }
                    $5_1 = HEAP32[$1_1 + 20 >> 2];
                    $1_1 = HEAP32[(($2 >>> 29 & 4) + $1_1 | 0) + 16 >> 2];
                    $0_1 = $5_1 ? (($5_1 | 0) == ($1_1 | 0) ? $0_1 : $5_1) : $0_1;
                    $2 = $2 << 1;
                    if ($1_1) {
                     continue
                    }
                    break;
                   };
                  }
                  if (!($0_1 | $3_1)) {
                   $3_1 = 0;
                   $0_1 = 2 << $7_1;
                   $0_1 = (0 - $0_1 | $0_1) & $8_1;
                   if (!$0_1) {
                    break label$14
                   }
                   $0_1 = HEAP32[(__wasm_ctz_i32($0_1 & 0 - $0_1) << 2) + 1336 >> 2];
                  }
                  if (!$0_1) {
                   break label$32
                  }
                 }
                 while (1) {
                  $2 = (HEAP32[$0_1 + 4 >> 2] & -8) - $6_1 | 0;
                  $1_1 = $2 >>> 0 < $4_1 >>> 0;
                  $4_1 = $1_1 ? $2 : $4_1;
                  $3_1 = $1_1 ? $0_1 : $3_1;
                  $1_1 = HEAP32[$0_1 + 16 >> 2];
                  if ($1_1) {
                   $0_1 = $1_1
                  } else {
                   $0_1 = HEAP32[$0_1 + 20 >> 2]
                  }
                  if ($0_1) {
                   continue
                  }
                  break;
                 };
                }
                if (!$3_1 | HEAP32[260] - $6_1 >>> 0 <= $4_1 >>> 0) {
                 break label$14
                }
                $7_1 = HEAP32[$3_1 + 24 >> 2];
                $2 = HEAP32[$3_1 + 12 >> 2];
                if (($2 | 0) != ($3_1 | 0)) {
                 $0_1 = HEAP32[$3_1 + 8 >> 2];
                 HEAP32[$0_1 + 12 >> 2] = $2;
                 HEAP32[$2 + 8 >> 2] = $0_1;
                 break label$3;
                }
                $1_1 = $3_1 + 20 | 0;
                $0_1 = HEAP32[$1_1 >> 2];
                if (!$0_1) {
                 $0_1 = HEAP32[$3_1 + 16 >> 2];
                 if (!$0_1) {
                  break label$12
                 }
                 $1_1 = $3_1 + 16 | 0;
                }
                while (1) {
                 $5_1 = $1_1;
                 $2 = $0_1;
                 $1_1 = $0_1 + 20 | 0;
                 $0_1 = HEAP32[$1_1 >> 2];
                 if ($0_1) {
                  continue
                 }
                 $1_1 = $2 + 16 | 0;
                 $0_1 = HEAP32[$2 + 16 >> 2];
                 if ($0_1) {
                  continue
                 }
                 break;
                };
                HEAP32[$5_1 >> 2] = 0;
                break label$3;
               }
               $3_1 = HEAP32[260];
               if ($6_1 >>> 0 <= $3_1 >>> 0) {
                $0_1 = HEAP32[263];
                $1_1 = $3_1 - $6_1 | 0;
                label$45 : {
                 if ($1_1 >>> 0 >= 16) {
                  $2 = $0_1 + $6_1 | 0;
                  HEAP32[$2 + 4 >> 2] = $1_1 | 1;
                  HEAP32[$0_1 + $3_1 >> 2] = $1_1;
                  HEAP32[$0_1 + 4 >> 2] = $6_1 | 3;
                  break label$45;
                 }
                 HEAP32[$0_1 + 4 >> 2] = $3_1 | 3;
                 $1_1 = $0_1 + $3_1 | 0;
                 HEAP32[$1_1 + 4 >> 2] = HEAP32[$1_1 + 4 >> 2] | 1;
                 $2 = 0;
                 $1_1 = 0;
                }
                HEAP32[260] = $1_1;
                HEAP32[263] = $2;
                $0_1 = $0_1 + 8 | 0;
                break label$1;
               }
               $2 = HEAP32[261];
               if ($6_1 >>> 0 < $2 >>> 0) {
                $1_1 = $2 - $6_1 | 0;
                HEAP32[261] = $1_1;
                $0_1 = HEAP32[264];
                $2 = $0_1 + $6_1 | 0;
                HEAP32[264] = $2;
                HEAP32[$2 + 4 >> 2] = $1_1 | 1;
                HEAP32[$0_1 + 4 >> 2] = $6_1 | 3;
                $0_1 = $0_1 + 8 | 0;
                break label$1;
               }
               $0_1 = 0;
               if (HEAP32[376]) {
                $1_1 = HEAP32[378]
               } else {
                HEAP32[379] = -1;
                HEAP32[380] = -1;
                HEAP32[377] = 4096;
                HEAP32[378] = 4096;
                HEAP32[376] = $11 + 12 & -16 ^ 1431655768;
                HEAP32[381] = 0;
                HEAP32[369] = 0;
                $1_1 = 4096;
               }
               $4_1 = $6_1 + 47 | 0;
               $5_1 = $1_1 + $4_1 | 0;
               $8_1 = 0 - $1_1 | 0;
               $1_1 = $5_1 & $8_1;
               if ($1_1 >>> 0 <= $6_1 >>> 0) {
                break label$1
               }
               $3_1 = HEAP32[368];
               if ($3_1) {
                $9 = $3_1;
                $3_1 = HEAP32[366];
                $7_1 = $3_1 + $1_1 | 0;
                if ($9 >>> 0 < $7_1 >>> 0 | $3_1 >>> 0 >= $7_1 >>> 0) {
                 break label$1
                }
               }
               label$51 : {
                if (!(HEAPU8[1476] & 4)) {
                 label$53 : {
                  label$54 : {
                   label$55 : {
                    label$56 : {
                     $3_1 = HEAP32[264];
                     if ($3_1) {
                      $0_1 = 1480;
                      while (1) {
                       $7_1 = HEAP32[$0_1 >> 2];
                       if ($3_1 >>> 0 >= $7_1 >>> 0 & $3_1 >>> 0 < $7_1 + HEAP32[$0_1 + 4 >> 2] >>> 0) {
                        break label$56
                       }
                       $0_1 = HEAP32[$0_1 + 8 >> 2];
                       if ($0_1) {
                        continue
                       }
                       break;
                      };
                     }
                     $2 = $3(0);
                     if (($2 | 0) == -1) {
                      break label$53
                     }
                     $5_1 = $1_1;
                     $0_1 = HEAP32[377];
                     $3_1 = $0_1 - 1 | 0;
                     if ($3_1 & $2) {
                      $5_1 = ($1_1 - $2 | 0) + ($2 + $3_1 & 0 - $0_1) | 0
                     }
                     if ($6_1 >>> 0 >= $5_1 >>> 0) {
                      break label$53
                     }
                     $0_1 = HEAP32[368];
                     if ($0_1) {
                      $7_1 = $0_1;
                      $0_1 = HEAP32[366];
                      $3_1 = $0_1 + $5_1 | 0;
                      if ($7_1 >>> 0 < $3_1 >>> 0 | $0_1 >>> 0 >= $3_1 >>> 0) {
                       break label$53
                      }
                     }
                     $0_1 = $3($5_1);
                     if (($2 | 0) != ($0_1 | 0)) {
                      break label$55
                     }
                     break label$51;
                    }
                    $5_1 = $8_1 & $5_1 - $2;
                    $2 = $3($5_1);
                    if (($2 | 0) == (HEAP32[$0_1 >> 2] + HEAP32[$0_1 + 4 >> 2] | 0)) {
                     break label$54
                    }
                    $0_1 = $2;
                   }
                   if (($0_1 | 0) == -1) {
                    break label$53
                   }
                   if ($5_1 >>> 0 >= $6_1 + 48 >>> 0) {
                    $2 = $0_1;
                    break label$51;
                   }
                   $2 = HEAP32[378];
                   $2 = $2 + ($4_1 - $5_1 | 0) & 0 - $2;
                   if (($3($2) | 0) == -1) {
                    break label$53
                   }
                   $5_1 = $2 + $5_1 | 0;
                   $2 = $0_1;
                   break label$51;
                  }
                  if (($2 | 0) != -1) {
                   break label$51
                  }
                 }
                 HEAP32[369] = HEAP32[369] | 4;
                }
                $2 = $3($1_1);
                $0_1 = $3(0);
                if (($2 | 0) == -1 | ($0_1 | 0) == -1 | $0_1 >>> 0 <= $2 >>> 0) {
                 break label$9
                }
                $5_1 = $0_1 - $2 | 0;
                if ($5_1 >>> 0 <= $6_1 + 40 >>> 0) {
                 break label$9
                }
               }
               $0_1 = HEAP32[366] + $5_1 | 0;
               HEAP32[366] = $0_1;
               if (HEAPU32[367] < $0_1 >>> 0) {
                HEAP32[367] = $0_1
               }
               label$64 : {
                $4_1 = HEAP32[264];
                if ($4_1) {
                 $0_1 = 1480;
                 while (1) {
                  $1_1 = HEAP32[$0_1 >> 2];
                  $3_1 = HEAP32[$0_1 + 4 >> 2];
                  if (($1_1 + $3_1 | 0) == ($2 | 0)) {
                   break label$64
                  }
                  $0_1 = HEAP32[$0_1 + 8 >> 2];
                  if ($0_1) {
                   continue
                  }
                  break;
                 };
                 break label$11;
                }
                $0_1 = HEAP32[262];
                if (!(!!$0_1 & $0_1 >>> 0 <= $2 >>> 0)) {
                 HEAP32[262] = $2
                }
                $0_1 = 0;
                HEAP32[371] = $5_1;
                HEAP32[370] = $2;
                HEAP32[266] = -1;
                HEAP32[267] = HEAP32[376];
                HEAP32[373] = 0;
                while (1) {
                 $1_1 = $0_1 << 3;
                 $3_1 = $1_1 + 1072 | 0;
                 HEAP32[$1_1 + 1080 >> 2] = $3_1;
                 HEAP32[$1_1 + 1084 >> 2] = $3_1;
                 $0_1 = $0_1 + 1 | 0;
                 if (($0_1 | 0) != 32) {
                  continue
                 }
                 break;
                };
                $0_1 = $5_1 - 40 | 0;
                $1_1 = $2 + 8 & 7 ? -8 - $2 & 7 : 0;
                $3_1 = $0_1 - $1_1 | 0;
                HEAP32[261] = $3_1;
                $1_1 = $1_1 + $2 | 0;
                HEAP32[264] = $1_1;
                HEAP32[$1_1 + 4 >> 2] = $3_1 | 1;
                HEAP32[($0_1 + $2 | 0) + 4 >> 2] = 40;
                HEAP32[265] = HEAP32[380];
                break label$10;
               }
               if (HEAP32[$0_1 + 12 >> 2] & 8 | ($2 >>> 0 <= $4_1 >>> 0 | $1_1 >>> 0 > $4_1 >>> 0)) {
                break label$11
               }
               HEAP32[$0_1 + 4 >> 2] = $3_1 + $5_1;
               $0_1 = $4_1 + 8 & 7 ? -8 - $4_1 & 7 : 0;
               $1_1 = $0_1 + $4_1 | 0;
               HEAP32[264] = $1_1;
               $2 = HEAP32[261] + $5_1 | 0;
               $0_1 = $2 - $0_1 | 0;
               HEAP32[261] = $0_1;
               HEAP32[$1_1 + 4 >> 2] = $0_1 | 1;
               HEAP32[($2 + $4_1 | 0) + 4 >> 2] = 40;
               HEAP32[265] = HEAP32[380];
               break label$10;
              }
              $3_1 = 0;
              break label$2;
             }
             $2 = 0;
             break label$3;
            }
            if ($2 >>> 0 < HEAPU32[262]) {
             HEAP32[262] = $2
            }
            $1_1 = $2 + $5_1 | 0;
            $0_1 = 1480;
            label$71 : {
             label$72 : {
              label$73 : {
               while (1) {
                if (HEAP32[$0_1 >> 2] != ($1_1 | 0)) {
                 $0_1 = HEAP32[$0_1 + 8 >> 2];
                 if ($0_1) {
                  continue
                 }
                 break label$73;
                }
                break;
               };
               if (!(HEAPU8[$0_1 + 12 | 0] & 8)) {
                break label$72
               }
              }
              $0_1 = 1480;
              while (1) {
               $1_1 = HEAP32[$0_1 >> 2];
               if ($4_1 >>> 0 >= $1_1 >>> 0) {
                $3_1 = $1_1 + HEAP32[$0_1 + 4 >> 2] | 0;
                if ($3_1 >>> 0 > $4_1 >>> 0) {
                 break label$71
                }
               }
               $0_1 = HEAP32[$0_1 + 8 >> 2];
               continue;
              };
             }
             HEAP32[$0_1 >> 2] = $2;
             HEAP32[$0_1 + 4 >> 2] = HEAP32[$0_1 + 4 >> 2] + $5_1;
             $7_1 = ($2 + 8 & 7 ? -8 - $2 & 7 : 0) + $2 | 0;
             HEAP32[$7_1 + 4 >> 2] = $6_1 | 3;
             $5_1 = $1_1 + ($1_1 + 8 & 7 ? -8 - $1_1 & 7 : 0) | 0;
             $6_1 = $6_1 + $7_1 | 0;
             $0_1 = $5_1 - $6_1 | 0;
             if (($4_1 | 0) == ($5_1 | 0)) {
              HEAP32[264] = $6_1;
              $0_1 = HEAP32[261] + $0_1 | 0;
              HEAP32[261] = $0_1;
              HEAP32[$6_1 + 4 >> 2] = $0_1 | 1;
              break label$4;
             }
             if (($5_1 | 0) == HEAP32[263]) {
              HEAP32[263] = $6_1;
              $0_1 = HEAP32[260] + $0_1 | 0;
              HEAP32[260] = $0_1;
              HEAP32[$6_1 + 4 >> 2] = $0_1 | 1;
              HEAP32[$0_1 + $6_1 >> 2] = $0_1;
              break label$4;
             }
             $4_1 = HEAP32[$5_1 + 4 >> 2];
             if (($4_1 & 3) != 1) {
              break label$5
             }
             $9 = $4_1 & -8;
             if ($4_1 >>> 0 <= 255) {
              $1_1 = HEAP32[$5_1 + 8 >> 2];
              $2 = HEAP32[$5_1 + 12 >> 2];
              if (($1_1 | 0) == ($2 | 0)) {
               HEAP32[258] = HEAP32[258] & __wasm_rotl_i32($4_1 >>> 3 | 0);
               break label$6;
              }
              HEAP32[$1_1 + 12 >> 2] = $2;
              HEAP32[$2 + 8 >> 2] = $1_1;
              break label$6;
             }
             $8_1 = HEAP32[$5_1 + 24 >> 2];
             $2 = HEAP32[$5_1 + 12 >> 2];
             if (($5_1 | 0) != ($2 | 0)) {
              $1_1 = HEAP32[$5_1 + 8 >> 2];
              HEAP32[$1_1 + 12 >> 2] = $2;
              HEAP32[$2 + 8 >> 2] = $1_1;
              break label$7;
             }
             $1_1 = $5_1 + 20 | 0;
             $4_1 = HEAP32[$1_1 >> 2];
             if (!$4_1) {
              $4_1 = HEAP32[$5_1 + 16 >> 2];
              if (!$4_1) {
               break label$8
              }
              $1_1 = $5_1 + 16 | 0;
             }
             while (1) {
              $3_1 = $1_1;
              $2 = $4_1;
              $1_1 = $2 + 20 | 0;
              $4_1 = HEAP32[$1_1 >> 2];
              if ($4_1) {
               continue
              }
              $1_1 = $2 + 16 | 0;
              $4_1 = HEAP32[$2 + 16 >> 2];
              if ($4_1) {
               continue
              }
              break;
             };
             HEAP32[$3_1 >> 2] = 0;
             break label$7;
            }
            $0_1 = $5_1 - 40 | 0;
            $1_1 = $2 + 8 & 7 ? -8 - $2 & 7 : 0;
            $8_1 = $0_1 - $1_1 | 0;
            HEAP32[261] = $8_1;
            $1_1 = $1_1 + $2 | 0;
            HEAP32[264] = $1_1;
            HEAP32[$1_1 + 4 >> 2] = $8_1 | 1;
            HEAP32[($0_1 + $2 | 0) + 4 >> 2] = 40;
            HEAP32[265] = HEAP32[380];
            $0_1 = ($3_1 + ($3_1 - 39 & 7 ? 39 - $3_1 & 7 : 0) | 0) - 47 | 0;
            $1_1 = $0_1 >>> 0 < $4_1 + 16 >>> 0 ? $4_1 : $0_1;
            HEAP32[$1_1 + 4 >> 2] = 27;
            $0_1 = HEAP32[373];
            $8_1 = $1_1 + 16 | 0;
            HEAP32[$8_1 >> 2] = HEAP32[372];
            HEAP32[$8_1 + 4 >> 2] = $0_1;
            $0_1 = HEAP32[371];
            HEAP32[$1_1 + 8 >> 2] = HEAP32[370];
            HEAP32[$1_1 + 12 >> 2] = $0_1;
            HEAP32[372] = $1_1 + 8;
            HEAP32[371] = $5_1;
            HEAP32[370] = $2;
            HEAP32[373] = 0;
            $0_1 = $1_1 + 24 | 0;
            while (1) {
             HEAP32[$0_1 + 4 >> 2] = 7;
             $2 = $0_1 + 8 | 0;
             $0_1 = $0_1 + 4 | 0;
             if ($2 >>> 0 < $3_1 >>> 0) {
              continue
             }
             break;
            };
            if (($1_1 | 0) == ($4_1 | 0)) {
             break label$10
            }
            HEAP32[$1_1 + 4 >> 2] = HEAP32[$1_1 + 4 >> 2] & -2;
            $2 = $1_1 - $4_1 | 0;
            HEAP32[$4_1 + 4 >> 2] = $2 | 1;
            HEAP32[$1_1 >> 2] = $2;
            if ($2 >>> 0 <= 255) {
             $0_1 = ($2 & -8) + 1072 | 0;
             $1_1 = HEAP32[258];
             $2 = 1 << ($2 >>> 3);
             label$87 : {
              if (!($1_1 & $2)) {
               HEAP32[258] = $1_1 | $2;
               $1_1 = $0_1;
               break label$87;
              }
              $1_1 = HEAP32[$0_1 + 8 >> 2];
             }
             HEAP32[$0_1 + 8 >> 2] = $4_1;
             HEAP32[$1_1 + 12 >> 2] = $4_1;
             HEAP32[$4_1 + 12 >> 2] = $0_1;
             HEAP32[$4_1 + 8 >> 2] = $1_1;
             break label$10;
            }
            $0_1 = 31;
            if ($2 >>> 0 <= 16777215) {
             $0_1 = Math_clz32($2 >>> 8 | 0);
             $0_1 = (($2 >>> 38 - $0_1 & 1) - ($0_1 << 1) | 0) + 62 | 0;
            }
            HEAP32[$4_1 + 28 >> 2] = $0_1;
            HEAP32[$4_1 + 16 >> 2] = 0;
            HEAP32[$4_1 + 20 >> 2] = 0;
            $1_1 = ($0_1 << 2) + 1336 | 0;
            label$90 : {
             $3_1 = HEAP32[259];
             $5_1 = 1 << $0_1;
             label$91 : {
              if (!($3_1 & $5_1)) {
               HEAP32[259] = $3_1 | $5_1;
               HEAP32[$1_1 >> 2] = $4_1;
               break label$91;
              }
              $0_1 = $2 << (($0_1 | 0) != 31 ? 25 - ($0_1 >>> 1 | 0) | 0 : 0);
              $3_1 = HEAP32[$1_1 >> 2];
              while (1) {
               $1_1 = $3_1;
               if (($2 | 0) == (HEAP32[$1_1 + 4 >> 2] & -8)) {
                break label$90
               }
               $3_1 = $0_1 >>> 29 | 0;
               $0_1 = $0_1 << 1;
               $5_1 = ($1_1 + ($3_1 & 4) | 0) + 16 | 0;
               $3_1 = HEAP32[$5_1 >> 2];
               if ($3_1) {
                continue
               }
               break;
              };
              HEAP32[$5_1 >> 2] = $4_1;
             }
             HEAP32[$4_1 + 24 >> 2] = $1_1;
             HEAP32[$4_1 + 12 >> 2] = $4_1;
             HEAP32[$4_1 + 8 >> 2] = $4_1;
             break label$10;
            }
            $0_1 = HEAP32[$1_1 + 8 >> 2];
            HEAP32[$0_1 + 12 >> 2] = $4_1;
            HEAP32[$1_1 + 8 >> 2] = $4_1;
            HEAP32[$4_1 + 24 >> 2] = 0;
            HEAP32[$4_1 + 12 >> 2] = $1_1;
            HEAP32[$4_1 + 8 >> 2] = $0_1;
           }
           $0_1 = HEAP32[261];
           if ($0_1 >>> 0 <= $6_1 >>> 0) {
            break label$9
           }
           $1_1 = $0_1 - $6_1 | 0;
           HEAP32[261] = $1_1;
           $0_1 = HEAP32[264];
           $2 = $0_1 + $6_1 | 0;
           HEAP32[264] = $2;
           HEAP32[$2 + 4 >> 2] = $1_1 | 1;
           HEAP32[$0_1 + 4 >> 2] = $6_1 | 3;
           $0_1 = $0_1 + 8 | 0;
           break label$1;
          }
          HEAP32[257] = 48;
          $0_1 = 0;
          break label$1;
         }
         $2 = 0;
        }
        if (!$8_1) {
         break label$6
        }
        $1_1 = HEAP32[$5_1 + 28 >> 2];
        $3_1 = ($1_1 << 2) + 1336 | 0;
        label$94 : {
         if (($5_1 | 0) == HEAP32[$3_1 >> 2]) {
          HEAP32[$3_1 >> 2] = $2;
          if ($2) {
           break label$94
          }
          HEAP32[259] = HEAP32[259] & __wasm_rotl_i32($1_1);
          break label$6;
         }
         HEAP32[$8_1 + (HEAP32[$8_1 + 16 >> 2] == ($5_1 | 0) ? 16 : 20) >> 2] = $2;
         if (!$2) {
          break label$6
         }
        }
        HEAP32[$2 + 24 >> 2] = $8_1;
        $1_1 = HEAP32[$5_1 + 16 >> 2];
        if ($1_1) {
         HEAP32[$2 + 16 >> 2] = $1_1;
         HEAP32[$1_1 + 24 >> 2] = $2;
        }
        $1_1 = HEAP32[$5_1 + 20 >> 2];
        if (!$1_1) {
         break label$6
        }
        HEAP32[$2 + 20 >> 2] = $1_1;
        HEAP32[$1_1 + 24 >> 2] = $2;
       }
       $0_1 = $0_1 + $9 | 0;
       $5_1 = $5_1 + $9 | 0;
       $4_1 = HEAP32[$5_1 + 4 >> 2];
      }
      HEAP32[$5_1 + 4 >> 2] = $4_1 & -2;
      HEAP32[$6_1 + 4 >> 2] = $0_1 | 1;
      HEAP32[$0_1 + $6_1 >> 2] = $0_1;
      if ($0_1 >>> 0 <= 255) {
       $1_1 = ($0_1 & -8) + 1072 | 0;
       $2 = HEAP32[258];
       $0_1 = 1 << ($0_1 >>> 3);
       label$98 : {
        if (!($2 & $0_1)) {
         HEAP32[258] = $0_1 | $2;
         $0_1 = $1_1;
         break label$98;
        }
        $0_1 = HEAP32[$1_1 + 8 >> 2];
       }
       HEAP32[$1_1 + 8 >> 2] = $6_1;
       HEAP32[$0_1 + 12 >> 2] = $6_1;
       HEAP32[$6_1 + 12 >> 2] = $1_1;
       HEAP32[$6_1 + 8 >> 2] = $0_1;
       break label$4;
      }
      $4_1 = 31;
      if ($0_1 >>> 0 <= 16777215) {
       $1_1 = Math_clz32($0_1 >>> 8 | 0);
       $4_1 = (($0_1 >>> 38 - $1_1 & 1) - ($1_1 << 1) | 0) + 62 | 0;
      }
      HEAP32[$6_1 + 28 >> 2] = $4_1;
      HEAP32[$6_1 + 16 >> 2] = 0;
      HEAP32[$6_1 + 20 >> 2] = 0;
      $1_1 = ($4_1 << 2) + 1336 | 0;
      label$101 : {
       $2 = HEAP32[259];
       $3_1 = 1 << $4_1;
       label$102 : {
        if (!($2 & $3_1)) {
         HEAP32[259] = $2 | $3_1;
         HEAP32[$1_1 >> 2] = $6_1;
         break label$102;
        }
        $4_1 = $0_1 << (($4_1 | 0) != 31 ? 25 - ($4_1 >>> 1 | 0) | 0 : 0);
        $2 = HEAP32[$1_1 >> 2];
        while (1) {
         $1_1 = $2;
         if ((HEAP32[$2 + 4 >> 2] & -8) == ($0_1 | 0)) {
          break label$101
         }
         $2 = $4_1 >>> 29 | 0;
         $4_1 = $4_1 << 1;
         $3_1 = ($1_1 + ($2 & 4) | 0) + 16 | 0;
         $2 = HEAP32[$3_1 >> 2];
         if ($2) {
          continue
         }
         break;
        };
        HEAP32[$3_1 >> 2] = $6_1;
       }
       HEAP32[$6_1 + 24 >> 2] = $1_1;
       HEAP32[$6_1 + 12 >> 2] = $6_1;
       HEAP32[$6_1 + 8 >> 2] = $6_1;
       break label$4;
      }
      $0_1 = HEAP32[$1_1 + 8 >> 2];
      HEAP32[$0_1 + 12 >> 2] = $6_1;
      HEAP32[$1_1 + 8 >> 2] = $6_1;
      HEAP32[$6_1 + 24 >> 2] = 0;
      HEAP32[$6_1 + 12 >> 2] = $1_1;
      HEAP32[$6_1 + 8 >> 2] = $0_1;
     }
     $0_1 = $7_1 + 8 | 0;
     break label$1;
    }
    label$105 : {
     if (!$7_1) {
      break label$105
     }
     $0_1 = HEAP32[$3_1 + 28 >> 2];
     $1_1 = ($0_1 << 2) + 1336 | 0;
     label$106 : {
      if (($3_1 | 0) == HEAP32[$1_1 >> 2]) {
       HEAP32[$1_1 >> 2] = $2;
       if ($2) {
        break label$106
       }
       $8_1 = __wasm_rotl_i32($0_1) & $8_1;
       HEAP32[259] = $8_1;
       break label$105;
      }
      HEAP32[$7_1 + (HEAP32[$7_1 + 16 >> 2] == ($3_1 | 0) ? 16 : 20) >> 2] = $2;
      if (!$2) {
       break label$105
      }
     }
     HEAP32[$2 + 24 >> 2] = $7_1;
     $0_1 = HEAP32[$3_1 + 16 >> 2];
     if ($0_1) {
      HEAP32[$2 + 16 >> 2] = $0_1;
      HEAP32[$0_1 + 24 >> 2] = $2;
     }
     $0_1 = HEAP32[$3_1 + 20 >> 2];
     if (!$0_1) {
      break label$105
     }
     HEAP32[$2 + 20 >> 2] = $0_1;
     HEAP32[$0_1 + 24 >> 2] = $2;
    }
    label$109 : {
     if ($4_1 >>> 0 <= 15) {
      $0_1 = $4_1 + $6_1 | 0;
      HEAP32[$3_1 + 4 >> 2] = $0_1 | 3;
      $0_1 = $0_1 + $3_1 | 0;
      HEAP32[$0_1 + 4 >> 2] = HEAP32[$0_1 + 4 >> 2] | 1;
      break label$109;
     }
     HEAP32[$3_1 + 4 >> 2] = $6_1 | 3;
     $2 = $3_1 + $6_1 | 0;
     HEAP32[$2 + 4 >> 2] = $4_1 | 1;
     HEAP32[$2 + $4_1 >> 2] = $4_1;
     if ($4_1 >>> 0 <= 255) {
      $0_1 = ($4_1 & -8) + 1072 | 0;
      $1_1 = HEAP32[258];
      $4_1 = 1 << ($4_1 >>> 3);
      label$112 : {
       if (!($1_1 & $4_1)) {
        HEAP32[258] = $1_1 | $4_1;
        $1_1 = $0_1;
        break label$112;
       }
       $1_1 = HEAP32[$0_1 + 8 >> 2];
      }
      HEAP32[$0_1 + 8 >> 2] = $2;
      HEAP32[$1_1 + 12 >> 2] = $2;
      HEAP32[$2 + 12 >> 2] = $0_1;
      HEAP32[$2 + 8 >> 2] = $1_1;
      break label$109;
     }
     $0_1 = 31;
     if ($4_1 >>> 0 <= 16777215) {
      $0_1 = Math_clz32($4_1 >>> 8 | 0);
      $0_1 = (($4_1 >>> 38 - $0_1 & 1) - ($0_1 << 1) | 0) + 62 | 0;
     }
     HEAP32[$2 + 28 >> 2] = $0_1;
     HEAP32[$2 + 16 >> 2] = 0;
     HEAP32[$2 + 20 >> 2] = 0;
     $1_1 = ($0_1 << 2) + 1336 | 0;
     label$115 : {
      $5_1 = 1 << $0_1;
      label$116 : {
       if (!($5_1 & $8_1)) {
        HEAP32[259] = $5_1 | $8_1;
        HEAP32[$1_1 >> 2] = $2;
        break label$116;
       }
       $0_1 = $4_1 << (($0_1 | 0) != 31 ? 25 - ($0_1 >>> 1 | 0) | 0 : 0);
       $6_1 = HEAP32[$1_1 >> 2];
       while (1) {
        $1_1 = $6_1;
        if ((HEAP32[$1_1 + 4 >> 2] & -8) == ($4_1 | 0)) {
         break label$115
        }
        $5_1 = $0_1 >>> 29 | 0;
        $0_1 = $0_1 << 1;
        $5_1 = ($1_1 + ($5_1 & 4) | 0) + 16 | 0;
        $6_1 = HEAP32[$5_1 >> 2];
        if ($6_1) {
         continue
        }
        break;
       };
       HEAP32[$5_1 >> 2] = $2;
      }
      HEAP32[$2 + 24 >> 2] = $1_1;
      HEAP32[$2 + 12 >> 2] = $2;
      HEAP32[$2 + 8 >> 2] = $2;
      break label$109;
     }
     $0_1 = HEAP32[$1_1 + 8 >> 2];
     HEAP32[$0_1 + 12 >> 2] = $2;
     HEAP32[$1_1 + 8 >> 2] = $2;
     HEAP32[$2 + 24 >> 2] = 0;
     HEAP32[$2 + 12 >> 2] = $1_1;
     HEAP32[$2 + 8 >> 2] = $0_1;
    }
    $0_1 = $3_1 + 8 | 0;
    break label$1;
   }
   label$119 : {
    if (!$9) {
     break label$119
    }
    $0_1 = HEAP32[$2 + 28 >> 2];
    $1_1 = ($0_1 << 2) + 1336 | 0;
    label$120 : {
     if (($2 | 0) == HEAP32[$1_1 >> 2]) {
      HEAP32[$1_1 >> 2] = $3_1;
      if ($3_1) {
       break label$120
      }
      HEAP32[259] = __wasm_rotl_i32($0_1) & $10;
      break label$119;
     }
     HEAP32[(HEAP32[$9 + 16 >> 2] == ($2 | 0) ? 16 : 20) + $9 >> 2] = $3_1;
     if (!$3_1) {
      break label$119
     }
    }
    HEAP32[$3_1 + 24 >> 2] = $9;
    $0_1 = HEAP32[$2 + 16 >> 2];
    if ($0_1) {
     HEAP32[$3_1 + 16 >> 2] = $0_1;
     HEAP32[$0_1 + 24 >> 2] = $3_1;
    }
    $0_1 = HEAP32[$2 + 20 >> 2];
    if (!$0_1) {
     break label$119
    }
    HEAP32[$3_1 + 20 >> 2] = $0_1;
    HEAP32[$0_1 + 24 >> 2] = $3_1;
   }
   label$123 : {
    if ($4_1 >>> 0 <= 15) {
     $0_1 = $4_1 + $6_1 | 0;
     HEAP32[$2 + 4 >> 2] = $0_1 | 3;
     $0_1 = $0_1 + $2 | 0;
     HEAP32[$0_1 + 4 >> 2] = HEAP32[$0_1 + 4 >> 2] | 1;
     break label$123;
    }
    HEAP32[$2 + 4 >> 2] = $6_1 | 3;
    $3_1 = $2 + $6_1 | 0;
    HEAP32[$3_1 + 4 >> 2] = $4_1 | 1;
    HEAP32[$3_1 + $4_1 >> 2] = $4_1;
    if ($7_1) {
     $0_1 = ($7_1 & -8) + 1072 | 0;
     $1_1 = HEAP32[263];
     $6_1 = 1 << ($7_1 >>> 3);
     label$126 : {
      if (!($6_1 & $5_1)) {
       HEAP32[258] = $6_1 | $5_1;
       $5_1 = $0_1;
       break label$126;
      }
      $5_1 = HEAP32[$0_1 + 8 >> 2];
     }
     HEAP32[$0_1 + 8 >> 2] = $1_1;
     HEAP32[$5_1 + 12 >> 2] = $1_1;
     HEAP32[$1_1 + 12 >> 2] = $0_1;
     HEAP32[$1_1 + 8 >> 2] = $5_1;
    }
    HEAP32[263] = $3_1;
    HEAP32[260] = $4_1;
   }
   $0_1 = $2 + 8 | 0;
  }
  global$0 = $11 + 16 | 0;
  return $0_1 | 0;
 }
 
 function $5($0_1) {
  $0_1 = $0_1 | 0;
  var $1_1 = 0, $2 = 0, $3_1 = 0, $4_1 = 0, $5_1 = 0, $6_1 = 0, $7_1 = 0;
  label$1 : {
   if (!$0_1) {
    break label$1
   }
   $3_1 = $0_1 - 8 | 0;
   $1_1 = HEAP32[$0_1 - 4 >> 2];
   $0_1 = $1_1 & -8;
   $5_1 = $3_1 + $0_1 | 0;
   label$2 : {
    if ($1_1 & 1) {
     break label$2
    }
    if (!($1_1 & 3)) {
     break label$1
    }
    $1_1 = HEAP32[$3_1 >> 2];
    $3_1 = $3_1 - $1_1 | 0;
    if ($3_1 >>> 0 < HEAPU32[262]) {
     break label$1
    }
    $0_1 = $0_1 + $1_1 | 0;
    label$3 : {
     label$4 : {
      if (($3_1 | 0) != HEAP32[263]) {
       if ($1_1 >>> 0 <= 255) {
        $4_1 = $1_1 >>> 3 | 0;
        $2 = HEAP32[$3_1 + 8 >> 2];
        $1_1 = HEAP32[$3_1 + 12 >> 2];
        if (($2 | 0) == ($1_1 | 0)) {
         HEAP32[258] = HEAP32[258] & __wasm_rotl_i32($4_1);
         break label$2;
        }
        HEAP32[$2 + 12 >> 2] = $1_1;
        HEAP32[$1_1 + 8 >> 2] = $2;
        break label$2;
       }
       $6_1 = HEAP32[$3_1 + 24 >> 2];
       $1_1 = HEAP32[$3_1 + 12 >> 2];
       if (($3_1 | 0) != ($1_1 | 0)) {
        $2 = HEAP32[$3_1 + 8 >> 2];
        HEAP32[$2 + 12 >> 2] = $1_1;
        HEAP32[$1_1 + 8 >> 2] = $2;
        break label$3;
       }
       $4_1 = $3_1 + 20 | 0;
       $2 = HEAP32[$4_1 >> 2];
       if (!$2) {
        $2 = HEAP32[$3_1 + 16 >> 2];
        if (!$2) {
         break label$4
        }
        $4_1 = $3_1 + 16 | 0;
       }
       while (1) {
        $7_1 = $4_1;
        $1_1 = $2;
        $4_1 = $1_1 + 20 | 0;
        $2 = HEAP32[$4_1 >> 2];
        if ($2) {
         continue
        }
        $4_1 = $1_1 + 16 | 0;
        $2 = HEAP32[$1_1 + 16 >> 2];
        if ($2) {
         continue
        }
        break;
       };
       HEAP32[$7_1 >> 2] = 0;
       break label$3;
      }
      $1_1 = HEAP32[$5_1 + 4 >> 2];
      if (($1_1 & 3) != 3) {
       break label$2
      }
      HEAP32[260] = $0_1;
      HEAP32[$5_1 + 4 >> 2] = $1_1 & -2;
      HEAP32[$3_1 + 4 >> 2] = $0_1 | 1;
      HEAP32[$5_1 >> 2] = $0_1;
      return;
     }
     $1_1 = 0;
    }
    if (!$6_1) {
     break label$2
    }
    $2 = HEAP32[$3_1 + 28 >> 2];
    $4_1 = ($2 << 2) + 1336 | 0;
    label$11 : {
     if (($3_1 | 0) == HEAP32[$4_1 >> 2]) {
      HEAP32[$4_1 >> 2] = $1_1;
      if ($1_1) {
       break label$11
      }
      HEAP32[259] = HEAP32[259] & __wasm_rotl_i32($2);
      break label$2;
     }
     HEAP32[$6_1 + (HEAP32[$6_1 + 16 >> 2] == ($3_1 | 0) ? 16 : 20) >> 2] = $1_1;
     if (!$1_1) {
      break label$2
     }
    }
    HEAP32[$1_1 + 24 >> 2] = $6_1;
    $2 = HEAP32[$3_1 + 16 >> 2];
    if ($2) {
     HEAP32[$1_1 + 16 >> 2] = $2;
     HEAP32[$2 + 24 >> 2] = $1_1;
    }
    $2 = HEAP32[$3_1 + 20 >> 2];
    if (!$2) {
     break label$2
    }
    HEAP32[$1_1 + 20 >> 2] = $2;
    HEAP32[$2 + 24 >> 2] = $1_1;
   }
   if ($3_1 >>> 0 >= $5_1 >>> 0) {
    break label$1
   }
   $1_1 = HEAP32[$5_1 + 4 >> 2];
   if (!($1_1 & 1)) {
    break label$1
   }
   label$14 : {
    label$15 : {
     label$16 : {
      label$17 : {
       if (!($1_1 & 2)) {
        if (HEAP32[264] == ($5_1 | 0)) {
         HEAP32[264] = $3_1;
         $0_1 = HEAP32[261] + $0_1 | 0;
         HEAP32[261] = $0_1;
         HEAP32[$3_1 + 4 >> 2] = $0_1 | 1;
         if (HEAP32[263] != ($3_1 | 0)) {
          break label$1
         }
         HEAP32[260] = 0;
         HEAP32[263] = 0;
         return;
        }
        if (HEAP32[263] == ($5_1 | 0)) {
         HEAP32[263] = $3_1;
         $0_1 = HEAP32[260] + $0_1 | 0;
         HEAP32[260] = $0_1;
         HEAP32[$3_1 + 4 >> 2] = $0_1 | 1;
         HEAP32[$0_1 + $3_1 >> 2] = $0_1;
         return;
        }
        $0_1 = ($1_1 & -8) + $0_1 | 0;
        if ($1_1 >>> 0 <= 255) {
         $4_1 = $1_1 >>> 3 | 0;
         $2 = HEAP32[$5_1 + 8 >> 2];
         $1_1 = HEAP32[$5_1 + 12 >> 2];
         if (($2 | 0) == ($1_1 | 0)) {
          HEAP32[258] = HEAP32[258] & __wasm_rotl_i32($4_1);
          break label$15;
         }
         HEAP32[$2 + 12 >> 2] = $1_1;
         HEAP32[$1_1 + 8 >> 2] = $2;
         break label$15;
        }
        $6_1 = HEAP32[$5_1 + 24 >> 2];
        $1_1 = HEAP32[$5_1 + 12 >> 2];
        if (($1_1 | 0) != ($5_1 | 0)) {
         $2 = HEAP32[$5_1 + 8 >> 2];
         HEAP32[$2 + 12 >> 2] = $1_1;
         HEAP32[$1_1 + 8 >> 2] = $2;
         break label$16;
        }
        $4_1 = $5_1 + 20 | 0;
        $2 = HEAP32[$4_1 >> 2];
        if (!$2) {
         $2 = HEAP32[$5_1 + 16 >> 2];
         if (!$2) {
          break label$17
         }
         $4_1 = $5_1 + 16 | 0;
        }
        while (1) {
         $7_1 = $4_1;
         $1_1 = $2;
         $4_1 = $1_1 + 20 | 0;
         $2 = HEAP32[$4_1 >> 2];
         if ($2) {
          continue
         }
         $4_1 = $1_1 + 16 | 0;
         $2 = HEAP32[$1_1 + 16 >> 2];
         if ($2) {
          continue
         }
         break;
        };
        HEAP32[$7_1 >> 2] = 0;
        break label$16;
       }
       HEAP32[$5_1 + 4 >> 2] = $1_1 & -2;
       HEAP32[$3_1 + 4 >> 2] = $0_1 | 1;
       HEAP32[$0_1 + $3_1 >> 2] = $0_1;
       break label$14;
      }
      $1_1 = 0;
     }
     if (!$6_1) {
      break label$15
     }
     $2 = HEAP32[$5_1 + 28 >> 2];
     $4_1 = ($2 << 2) + 1336 | 0;
     label$26 : {
      if (HEAP32[$4_1 >> 2] == ($5_1 | 0)) {
       HEAP32[$4_1 >> 2] = $1_1;
       if ($1_1) {
        break label$26
       }
       HEAP32[259] = HEAP32[259] & __wasm_rotl_i32($2);
       break label$15;
      }
      HEAP32[$6_1 + (($5_1 | 0) == HEAP32[$6_1 + 16 >> 2] ? 16 : 20) >> 2] = $1_1;
      if (!$1_1) {
       break label$15
      }
     }
     HEAP32[$1_1 + 24 >> 2] = $6_1;
     $2 = HEAP32[$5_1 + 16 >> 2];
     if ($2) {
      HEAP32[$1_1 + 16 >> 2] = $2;
      HEAP32[$2 + 24 >> 2] = $1_1;
     }
     $2 = HEAP32[$5_1 + 20 >> 2];
     if (!$2) {
      break label$15
     }
     HEAP32[$1_1 + 20 >> 2] = $2;
     HEAP32[$2 + 24 >> 2] = $1_1;
    }
    HEAP32[$3_1 + 4 >> 2] = $0_1 | 1;
    HEAP32[$0_1 + $3_1 >> 2] = $0_1;
    if (HEAP32[263] != ($3_1 | 0)) {
     break label$14
    }
    HEAP32[260] = $0_1;
    return;
   }
   if ($0_1 >>> 0 <= 255) {
    $1_1 = ($0_1 & -8) + 1072 | 0;
    $2 = HEAP32[258];
    $0_1 = 1 << ($0_1 >>> 3);
    label$30 : {
     if (!($2 & $0_1)) {
      HEAP32[258] = $0_1 | $2;
      $0_1 = $1_1;
      break label$30;
     }
     $0_1 = HEAP32[$1_1 + 8 >> 2];
    }
    HEAP32[$1_1 + 8 >> 2] = $3_1;
    HEAP32[$0_1 + 12 >> 2] = $3_1;
    HEAP32[$3_1 + 12 >> 2] = $1_1;
    HEAP32[$3_1 + 8 >> 2] = $0_1;
    return;
   }
   $2 = 31;
   if ($0_1 >>> 0 <= 16777215) {
    $1_1 = Math_clz32($0_1 >>> 8 | 0);
    $2 = (($0_1 >>> 38 - $1_1 & 1) - ($1_1 << 1) | 0) + 62 | 0;
   }
   HEAP32[$3_1 + 28 >> 2] = $2;
   HEAP32[$3_1 + 16 >> 2] = 0;
   HEAP32[$3_1 + 20 >> 2] = 0;
   $1_1 = ($2 << 2) + 1336 | 0;
   label$33 : {
    label$34 : {
     $4_1 = HEAP32[259];
     $7_1 = 1 << $2;
     label$35 : {
      if (!($4_1 & $7_1)) {
       HEAP32[259] = $4_1 | $7_1;
       HEAP32[$1_1 >> 2] = $3_1;
       HEAP32[$3_1 + 24 >> 2] = $1_1;
       break label$35;
      }
      $2 = $0_1 << (($2 | 0) != 31 ? 25 - ($2 >>> 1 | 0) | 0 : 0);
      $1_1 = HEAP32[$1_1 >> 2];
      while (1) {
       $4_1 = $1_1;
       if ((HEAP32[$1_1 + 4 >> 2] & -8) == ($0_1 | 0)) {
        break label$34
       }
       $1_1 = $2 >>> 29 | 0;
       $2 = $2 << 1;
       $7_1 = ($4_1 + ($1_1 & 4) | 0) + 16 | 0;
       $1_1 = HEAP32[$7_1 >> 2];
       if ($1_1) {
        continue
       }
       break;
      };
      HEAP32[$7_1 >> 2] = $3_1;
      HEAP32[$3_1 + 24 >> 2] = $4_1;
     }
     HEAP32[$3_1 + 12 >> 2] = $3_1;
     HEAP32[$3_1 + 8 >> 2] = $3_1;
     break label$33;
    }
    $0_1 = HEAP32[$4_1 + 8 >> 2];
    HEAP32[$0_1 + 12 >> 2] = $3_1;
    HEAP32[$4_1 + 8 >> 2] = $3_1;
    HEAP32[$3_1 + 24 >> 2] = 0;
    HEAP32[$3_1 + 12 >> 2] = $4_1;
    HEAP32[$3_1 + 8 >> 2] = $0_1;
   }
   $0_1 = HEAP32[266] - 1 | 0;
   HEAP32[266] = $0_1 ? $0_1 : -1;
  }
 }
 
 function $6() {
  return global$0 | 0;
 }
 
 function $7($0_1) {
  $0_1 = $0_1 | 0;
  global$0 = $0_1;
 }
 
 function $8($0_1) {
  $0_1 = $0_1 | 0;
  $0_1 = global$0 - $0_1 & -16;
  global$0 = $0_1;
  return $0_1 | 0;
 }
 
 function __wasm_ctz_i32($0_1) {
  if ($0_1) {
   return 31 - Math_clz32($0_1 - 1 ^ $0_1) | 0
  }
  return 32;
 }
 
 function __wasm_rotl_i32($0_1) {
  var $1_1 = 0;
  $1_1 = $0_1 & 31;
  $0_1 = 0 - $0_1 & 31;
  return (-1 >>> $1_1 & -2) << $1_1 | (-1 << $0_1 & -2) >>> $0_1;
 }
 
 // EMSCRIPTEN_END_FUNCS
;
 bufferView = HEAPU8;
 initActiveSegments(imports);
 var FUNCTION_TABLE = Table([]);
 function __wasm_memory_size() {
  return buffer.byteLength / 65536 | 0;
 }
 
 return {
  "__wasm_call_ctors": $0, 
  "__errno_location": $1, 
  "malloc": $4, 
  "free": $5, 
  "stackSave": $6, 
  "stackRestore": $7, 
  "stackAlloc": $8, 
  "__indirect_function_table": FUNCTION_TABLE
 };
}

  return asmFunc(info);
}

)(info);
  },

  instantiate: /** @suppress{checkTypes} */ function(binary, info) {
    return {
      then: function(ok) {
        var module = new WebAssembly.Module(binary);
        ok({
          'instance': new WebAssembly.Instance(module, info)
        });
      }
    };
  },

  RuntimeError: Error
};

// We don't need to actually download a wasm binary, mark it as present but empty.
wasmBinary = [];
// end include: wasm2js.js
if (typeof WebAssembly != 'object') {
  abort('no native wasm support detected');
}

// Wasm globals

var wasmMemory;

//========================================
// Runtime essentials
//========================================

// whether we are quitting the application. no code should run after this.
// set in exit() and abort()
var ABORT = false;

// set by exit() and abort().  Passed to 'onExit' handler.
// NOTE: This is also used as the process return code code in shell environments
// but only when noExitRuntime is false.
var EXITSTATUS;

/** @type {function(*, string=)} */
function assert(condition, text) {
  if (!condition) {
    // This build was created without ASSERTIONS defined.  `assert()` should not
    // ever be called in this configuration but in case there are callers in
    // the wild leave this simple abort() implemenation here for now.
    abort(text);
  }
}

// Memory management

var HEAP,
/** @type {!Int8Array} */
  HEAP8,
/** @type {!Uint8Array} */
  HEAPU8,
/** @type {!Int16Array} */
  HEAP16,
/** @type {!Uint16Array} */
  HEAPU16,
/** @type {!Int32Array} */
  HEAP32,
/** @type {!Uint32Array} */
  HEAPU32,
/** @type {!Float32Array} */
  HEAPF32,
/** @type {!Float64Array} */
  HEAPF64;

function updateMemoryViews() {
  var b = wasmMemory.buffer;
  Module['HEAP8'] = HEAP8 = new Int8Array(b);
  Module['HEAP16'] = HEAP16 = new Int16Array(b);
  Module['HEAP32'] = HEAP32 = new Int32Array(b);
  Module['HEAPU8'] = HEAPU8 = new Uint8Array(b);
  Module['HEAPU16'] = HEAPU16 = new Uint16Array(b);
  Module['HEAPU32'] = HEAPU32 = new Uint32Array(b);
  Module['HEAPF32'] = HEAPF32 = new Float32Array(b);
  Module['HEAPF64'] = HEAPF64 = new Float64Array(b);
}

// In non-standalone/normal mode, we create the memory here.
// include: runtime_init_memory.js
// Create the wasm memory. (Note: this only applies if IMPORTED_MEMORY is defined)

var INITIAL_MEMORY = Module['INITIAL_MEMORY'] || 16777216;

assert(INITIAL_MEMORY >= 65536, 'INITIAL_MEMORY should be larger than STACK_SIZE, was ' + INITIAL_MEMORY + '! (STACK_SIZE=' + 65536 + ')');

// check for full engine support (use string 'subarray' to avoid closure compiler confusion)

  if (Module['wasmMemory']) {
    wasmMemory = Module['wasmMemory'];
  } else
  {
    wasmMemory = new WebAssembly.Memory({
      'initial': INITIAL_MEMORY / 65536,
      'maximum': INITIAL_MEMORY / 65536
    });
  }

updateMemoryViews();

// If the user provides an incorrect length, just use that length instead rather than providing the user to
// specifically provide the memory length with Module['INITIAL_MEMORY'].
INITIAL_MEMORY = wasmMemory.buffer.byteLength;
// end include: runtime_init_memory.js

// include: runtime_init_table.js
// In regular non-RELOCATABLE mode the table is exported
// from the wasm module and this will be assigned once
// the exports are available.
var wasmTable;
// end include: runtime_init_table.js
// include: runtime_stack_check.js
// end include: runtime_stack_check.js
// include: runtime_assertions.js
// end include: runtime_assertions.js
var __ATPRERUN__  = []; // functions called before the runtime is initialized
var __ATINIT__    = []; // functions called during startup
var __ATEXIT__    = []; // functions called during shutdown
var __ATPOSTRUN__ = []; // functions called after the main() is called

var runtimeInitialized = false;

var runtimeKeepaliveCounter = 0;

function keepRuntimeAlive() {
  return noExitRuntime || runtimeKeepaliveCounter > 0;
}

function preRun() {
  if (Module['preRun']) {
    if (typeof Module['preRun'] == 'function') Module['preRun'] = [Module['preRun']];
    while (Module['preRun'].length) {
      addOnPreRun(Module['preRun'].shift());
    }
  }
  callRuntimeCallbacks(__ATPRERUN__);
}

function initRuntime() {
  runtimeInitialized = true;

  
if (!Module["noFSInit"] && !FS.init.initialized)
  FS.init();
FS.ignorePermissions = false;

TTY.init();
  callRuntimeCallbacks(__ATINIT__);
}

function postRun() {

  if (Module['postRun']) {
    if (typeof Module['postRun'] == 'function') Module['postRun'] = [Module['postRun']];
    while (Module['postRun'].length) {
      addOnPostRun(Module['postRun'].shift());
    }
  }

  callRuntimeCallbacks(__ATPOSTRUN__);
}

function addOnPreRun(cb) {
  __ATPRERUN__.unshift(cb);
}

function addOnInit(cb) {
  __ATINIT__.unshift(cb);
}

function addOnExit(cb) {
}

function addOnPostRun(cb) {
  __ATPOSTRUN__.unshift(cb);
}

// include: runtime_math.js
// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/imul

// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/fround

// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/clz32

// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc

// end include: runtime_math.js
// A counter of dependencies for calling run(). If we need to
// do asynchronous work before running, increment this and
// decrement it. Incrementing must happen in a place like
// Module.preRun (used by emcc to add file preloading).
// Note that you can add dependencies in preRun, even though
// it happens right before run - run will be postponed until
// the dependencies are met.
var runDependencies = 0;
var runDependencyWatcher = null;
var dependenciesFulfilled = null; // overridden to take different actions when all run dependencies are fulfilled

function getUniqueRunDependency(id) {
  return id;
}

function addRunDependency(id) {
  runDependencies++;

  if (Module['monitorRunDependencies']) {
    Module['monitorRunDependencies'](runDependencies);
  }

}

function removeRunDependency(id) {
  runDependencies--;

  if (Module['monitorRunDependencies']) {
    Module['monitorRunDependencies'](runDependencies);
  }

  if (runDependencies == 0) {
    if (runDependencyWatcher !== null) {
      clearInterval(runDependencyWatcher);
      runDependencyWatcher = null;
    }
    if (dependenciesFulfilled) {
      var callback = dependenciesFulfilled;
      dependenciesFulfilled = null;
      callback(); // can add another dependenciesFulfilled
    }
  }
}

/** @param {string|number=} what */
function abort(what) {
  if (Module['onAbort']) {
    Module['onAbort'](what);
  }

  what = 'Aborted(' + what + ')';
  // TODO(sbc): Should we remove printing and leave it up to whoever
  // catches the exception?
  err(what);

  ABORT = true;
  EXITSTATUS = 1;

  what += '. Build with -sASSERTIONS for more info.';

  // Use a wasm runtime error, because a JS error might be seen as a foreign
  // exception, which means we'd run destructors on it. We need the error to
  // simply make the program stop.
  // FIXME This approach does not work in Wasm EH because it currently does not assume
  // all RuntimeErrors are from traps; it decides whether a RuntimeError is from
  // a trap or not based on a hidden field within the object. So at the moment
  // we don't have a way of throwing a wasm trap from JS. TODO Make a JS API that
  // allows this in the wasm spec.

  // Suppress closure compiler warning here. Closure compiler's builtin extern
  // defintion for WebAssembly.RuntimeError claims it takes no arguments even
  // though it can.
  // TODO(https://github.com/google/closure-compiler/pull/3913): Remove if/when upstream closure gets fixed.
  /** @suppress {checkTypes} */
  var e = new WebAssembly.RuntimeError(what);

  readyPromiseReject(e);
  // Throw the error whether or not MODULARIZE is set because abort is used
  // in code paths apart from instantiation where an exception is expected
  // to be thrown when abort is called.
  throw e;
}

// include: memoryprofiler.js
// end include: memoryprofiler.js
// include: URIUtils.js
// Prefix of data URIs emitted by SINGLE_FILE and related options.
var dataURIPrefix = 'data:application/octet-stream;base64,';

// Indicates whether filename is a base64 data URI.
function isDataURI(filename) {
  // Prefix of data URIs emitted by SINGLE_FILE and related options.
  return filename.startsWith(dataURIPrefix);
}

// Indicates whether filename is delivered via file protocol (as opposed to http/https)
function isFileURI(filename) {
  return filename.startsWith('file://');
}
// end include: URIUtils.js
// include: runtime_exceptions.js
// end include: runtime_exceptions.js
var wasmBinaryFile;
  wasmBinaryFile = 'boot.wasm';
  if (!isDataURI(wasmBinaryFile)) {
    wasmBinaryFile = locateFile(wasmBinaryFile);
  }

function getBinary(file) {
  try {
    if (file == wasmBinaryFile && wasmBinary) {
      return new Uint8Array(wasmBinary);
    }
    var binary = tryParseAsDataURI(file);
    if (binary) {
      return binary;
    }
    if (readBinary) {
      return readBinary(file);
    }
    throw "both async and sync fetching of the wasm failed";
  }
  catch (err) {
    abort(err);
  }
}

function getBinaryPromise(binaryFile) {
  // If we don't have the binary yet, try to load it asynchronously.
  // Fetch has some additional restrictions over XHR, like it can't be used on a file:// url.
  // See https://github.com/github/fetch/pull/92#issuecomment-140665932
  // Cordova or Electron apps are typically loaded from a file:// url.
  // So use fetch if it is available and the url is not a file, otherwise fall back to XHR.
  if (!wasmBinary && (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER)) {
    if (typeof fetch == 'function'
      && !isFileURI(binaryFile)
    ) {
      return fetch(binaryFile, { credentials: 'same-origin' }).then((response) => {
        if (!response['ok']) {
          throw "failed to load wasm binary file at '" + binaryFile + "'";
        }
        return response['arrayBuffer']();
      }).catch(() => getBinary(binaryFile));
    }
    else {
      if (readAsync) {
        // fetch is not available or url is file => try XHR (readAsync uses XHR internally)
        return new Promise((resolve, reject) => {
          readAsync(binaryFile, (response) => resolve(new Uint8Array(/** @type{!ArrayBuffer} */(response))), reject)
        });
      }
    }
  }

  // Otherwise, getBinary should be able to get it synchronously
  return Promise.resolve().then(() => getBinary(binaryFile));
}

function instantiateArrayBuffer(binaryFile, imports, receiver) {
  return getBinaryPromise(binaryFile).then((binary) => {
    return WebAssembly.instantiate(binary, imports);
  }).then((instance) => {
    return instance;
  }).then(receiver, (reason) => {
    err('failed to asynchronously prepare wasm: ' + reason);

    abort(reason);
  });
}

function instantiateAsync(binary, binaryFile, imports, callback) {
  if (!binary &&
      typeof WebAssembly.instantiateStreaming == 'function' &&
      !isDataURI(binaryFile) &&
      // Don't use streaming for file:// delivered objects in a webview, fetch them synchronously.
      !isFileURI(binaryFile) &&
      // Avoid instantiateStreaming() on Node.js environment for now, as while
      // Node.js v18.1.0 implements it, it does not have a full fetch()
      // implementation yet.
      //
      // Reference:
      //   https://github.com/emscripten-core/emscripten/pull/16917
      !ENVIRONMENT_IS_NODE &&
      typeof fetch == 'function') {
    return fetch(binaryFile, { credentials: 'same-origin' }).then((response) => {
      // Suppress closure warning here since the upstream definition for
      // instantiateStreaming only allows Promise<Repsponse> rather than
      // an actual Response.
      // TODO(https://github.com/google/closure-compiler/pull/3913): Remove if/when upstream closure is fixed.
      /** @suppress {checkTypes} */
      var result = WebAssembly.instantiateStreaming(response, imports);

      return result.then(
        callback,
        function(reason) {
          // We expect the most common failure cause to be a bad MIME type for the binary,
          // in which case falling back to ArrayBuffer instantiation should work.
          err('wasm streaming compile failed: ' + reason);
          err('falling back to ArrayBuffer instantiation');
          return instantiateArrayBuffer(binaryFile, imports, callback);
        });
    });
  } else {
    return instantiateArrayBuffer(binaryFile, imports, callback);
  }
}

// Create the wasm instance.
// Receives the wasm imports, returns the exports.
function createWasm() {
  // prepare imports
  var info = {
    'env': wasmImports,
    'wasi_snapshot_preview1': wasmImports,
  };
  // Load the wasm module and create an instance of using native support in the JS engine.
  // handle a generated wasm instance, receiving its exports and
  // performing other necessary setup
  /** @param {WebAssembly.Module=} module*/
  function receiveInstance(instance, module) {
    var exports = instance.exports;

    Module['asm'] = exports;

    wasmTable = Module['asm']['__indirect_function_table'];

    addOnInit(Module['asm']['__wasm_call_ctors']);

    removeRunDependency('wasm-instantiate');
    return exports;
  }
  // wait for the pthread pool (if any)
  addRunDependency('wasm-instantiate');

  // Prefer streaming instantiation if available.
  function receiveInstantiationResult(result) {
    // 'result' is a ResultObject object which has both the module and instance.
    // receiveInstance() will swap in the exports (to Module.asm) so they can be called
    // TODO: Due to Closure regression https://github.com/google/closure-compiler/issues/3193, the above line no longer optimizes out down to the following line.
    // When the regression is fixed, can restore the above PTHREADS-enabled path.
    receiveInstance(result['instance']);
  }

  // User shell pages can write their own Module.instantiateWasm = function(imports, successCallback) callback
  // to manually instantiate the Wasm module themselves. This allows pages to
  // run the instantiation parallel to any other async startup actions they are
  // performing.
  // Also pthreads and wasm workers initialize the wasm instance through this
  // path.
  if (Module['instantiateWasm']) {

    try {
      return Module['instantiateWasm'](info, receiveInstance);
    } catch(e) {
      err('Module.instantiateWasm callback failed with error: ' + e);
        // If instantiation fails, reject the module ready promise.
        readyPromiseReject(e);
    }
  }

  // If instantiation fails, reject the module ready promise.
  instantiateAsync(wasmBinary, wasmBinaryFile, info, receiveInstantiationResult).catch(readyPromiseReject);
  return {}; // no exports yet; we'll fill them in later
}

// Globals used by JS i64 conversions (see makeSetValue)
var tempDouble;
var tempI64;

// include: runtime_debug.js
// end include: runtime_debug.js
// === Body ===

// end include: preamble.js

  /** @constructor */
  function ExitStatus(status) {
      this.name = 'ExitStatus';
      this.message = `Program terminated with exit(${status})`;
      this.status = status;
    }

  var callRuntimeCallbacks = (callbacks) => {
      while (callbacks.length > 0) {
        // Pass the module as the first argument.
        callbacks.shift()(Module);
      }
    };

  
    /**
     * @param {number} ptr
     * @param {string} type
     */
  function getValue(ptr, type = 'i8') {
    if (type.endsWith('*')) type = '*';
    switch (type) {
      case 'i1': return HEAP8[((ptr)>>0)];
      case 'i8': return HEAP8[((ptr)>>0)];
      case 'i16': return HEAP16[((ptr)>>1)];
      case 'i32': return HEAP32[((ptr)>>2)];
      case 'i64': abort('to do getValue(i64) use WASM_BIGINT');
      case 'float': return HEAPF32[((ptr)>>2)];
      case 'double': return HEAPF64[((ptr)>>3)];
      case '*': return HEAPU32[((ptr)>>2)];
      default: abort(`invalid type for getValue: ${type}`);
    }
  }

  function intArrayToString(array) {
    var ret = [];
    for (var i = 0; i < array.length; i++) {
      var chr = array[i];
      if (chr > 0xFF) {
        chr &= 0xFF;
      }
      ret.push(String.fromCharCode(chr));
    }
    return ret.join('');
  }

  
    /**
     * @param {number} ptr
     * @param {number} value
     * @param {string} type
     */
  function setValue(ptr, value, type = 'i8') {
    if (type.endsWith('*')) type = '*';
    switch (type) {
      case 'i1': HEAP8[((ptr)>>0)] = value; break;
      case 'i8': HEAP8[((ptr)>>0)] = value; break;
      case 'i16': HEAP16[((ptr)>>1)] = value; break;
      case 'i32': HEAP32[((ptr)>>2)] = value; break;
      case 'i64': abort('to do setValue(i64) use WASM_BIGINT');
      case 'float': HEAPF32[((ptr)>>2)] = value; break;
      case 'double': HEAPF64[((ptr)>>3)] = value; break;
      case '*': HEAPU32[((ptr)>>2)] = value; break;
      default: abort(`invalid type for setValue: ${type}`);
    }
  }

  var getHeapMax = () =>
      HEAPU8.length;
  
  var abortOnCannotGrowMemory = (requestedSize) => {
      abort('OOM');
    };
  var _emscripten_resize_heap = (requestedSize) => {
      var oldSize = HEAPU8.length;
      requestedSize = requestedSize >>> 0;
      abortOnCannotGrowMemory(requestedSize);
    };

  var initRandomFill = () => {
      if (typeof crypto == 'object' && typeof crypto['getRandomValues'] == 'function') {
        // for modern web browsers
        return (view) => crypto.getRandomValues(view);
      } else
      if (ENVIRONMENT_IS_NODE) {
        // for nodejs with or without crypto support included
        try {
          var crypto_module = require('crypto');
          var randomFillSync = crypto_module['randomFillSync'];
          if (randomFillSync) {
            // nodejs with LTS crypto support
            return (view) => crypto_module['randomFillSync'](view);
          }
          // very old nodejs with the original crypto API
          var randomBytes = crypto_module['randomBytes'];
          return (view) => (
            view.set(randomBytes(view.byteLength)),
            // Return the original view to match modern native implementations.
            view
          );
        } catch (e) {
          // nodejs doesn't have crypto support
        }
      }
      // we couldn't find a proper implementation, as Math.random() is not suitable for /dev/random, see emscripten-core/emscripten/pull/7096
      abort("initRandomDevice");
    };
  var randomFill = (view) => {
      // Lazily init on the first invocation.
      return (randomFill = initRandomFill())(view);
    };
  
  var PATH = {isAbs:(path) => path.charAt(0) === '/',splitPath:(filename) => {
        var splitPathRe = /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;
        return splitPathRe.exec(filename).slice(1);
      },normalizeArray:(parts, allowAboveRoot) => {
        // if the path tries to go above the root, `up` ends up > 0
        var up = 0;
        for (var i = parts.length - 1; i >= 0; i--) {
          var last = parts[i];
          if (last === '.') {
            parts.splice(i, 1);
          } else if (last === '..') {
            parts.splice(i, 1);
            up++;
          } else if (up) {
            parts.splice(i, 1);
            up--;
          }
        }
        // if the path is allowed to go above the root, restore leading ..s
        if (allowAboveRoot) {
          for (; up; up--) {
            parts.unshift('..');
          }
        }
        return parts;
      },normalize:(path) => {
        var isAbsolute = PATH.isAbs(path),
            trailingSlash = path.substr(-1) === '/';
        // Normalize the path
        path = PATH.normalizeArray(path.split('/').filter((p) => !!p), !isAbsolute).join('/');
        if (!path && !isAbsolute) {
          path = '.';
        }
        if (path && trailingSlash) {
          path += '/';
        }
        return (isAbsolute ? '/' : '') + path;
      },dirname:(path) => {
        var result = PATH.splitPath(path),
            root = result[0],
            dir = result[1];
        if (!root && !dir) {
          // No dirname whatsoever
          return '.';
        }
        if (dir) {
          // It has a dirname, strip trailing slash
          dir = dir.substr(0, dir.length - 1);
        }
        return root + dir;
      },basename:(path) => {
        // EMSCRIPTEN return '/'' for '/', not an empty string
        if (path === '/') return '/';
        path = PATH.normalize(path);
        path = path.replace(/\/$/, "");
        var lastSlash = path.lastIndexOf('/');
        if (lastSlash === -1) return path;
        return path.substr(lastSlash+1);
      },join:function() {
        var paths = Array.prototype.slice.call(arguments);
        return PATH.normalize(paths.join('/'));
      },join2:(l, r) => {
        return PATH.normalize(l + '/' + r);
      }};
  
  
  var PATH_FS = {resolve:function() {
        var resolvedPath = '',
          resolvedAbsolute = false;
        for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {
          var path = (i >= 0) ? arguments[i] : FS.cwd();
          // Skip empty and invalid entries
          if (typeof path != 'string') {
            throw new TypeError('Arguments to path.resolve must be strings');
          } else if (!path) {
            return ''; // an invalid portion invalidates the whole thing
          }
          resolvedPath = path + '/' + resolvedPath;
          resolvedAbsolute = PATH.isAbs(path);
        }
        // At this point the path should be resolved to a full absolute path, but
        // handle relative paths to be safe (might happen when process.cwd() fails)
        resolvedPath = PATH.normalizeArray(resolvedPath.split('/').filter((p) => !!p), !resolvedAbsolute).join('/');
        return ((resolvedAbsolute ? '/' : '') + resolvedPath) || '.';
      },relative:(from, to) => {
        from = PATH_FS.resolve(from).substr(1);
        to = PATH_FS.resolve(to).substr(1);
        function trim(arr) {
          var start = 0;
          for (; start < arr.length; start++) {
            if (arr[start] !== '') break;
          }
          var end = arr.length - 1;
          for (; end >= 0; end--) {
            if (arr[end] !== '') break;
          }
          if (start > end) return [];
          return arr.slice(start, end - start + 1);
        }
        var fromParts = trim(from.split('/'));
        var toParts = trim(to.split('/'));
        var length = Math.min(fromParts.length, toParts.length);
        var samePartsLength = length;
        for (var i = 0; i < length; i++) {
          if (fromParts[i] !== toParts[i]) {
            samePartsLength = i;
            break;
          }
        }
        var outputParts = [];
        for (var i = samePartsLength; i < fromParts.length; i++) {
          outputParts.push('..');
        }
        outputParts = outputParts.concat(toParts.slice(samePartsLength));
        return outputParts.join('/');
      }};
  
  
  var lengthBytesUTF8 = (str) => {
      var len = 0;
      for (var i = 0; i < str.length; ++i) {
        // Gotcha: charCodeAt returns a 16-bit word that is a UTF-16 encoded code
        // unit, not a Unicode code point of the character! So decode
        // UTF16->UTF32->UTF8.
        // See http://unicode.org/faq/utf_bom.html#utf16-3
        var c = str.charCodeAt(i); // possibly a lead surrogate
        if (c <= 0x7F) {
          len++;
        } else if (c <= 0x7FF) {
          len += 2;
        } else if (c >= 0xD800 && c <= 0xDFFF) {
          len += 4; ++i;
        } else {
          len += 3;
        }
      }
      return len;
    };
  
  var stringToUTF8Array = (str, heap, outIdx, maxBytesToWrite) => {
      // Parameter maxBytesToWrite is not optional. Negative values, 0, null,
      // undefined and false each don't write out any bytes.
      if (!(maxBytesToWrite > 0))
        return 0;
  
      var startIdx = outIdx;
      var endIdx = outIdx + maxBytesToWrite - 1; // -1 for string null terminator.
      for (var i = 0; i < str.length; ++i) {
        // Gotcha: charCodeAt returns a 16-bit word that is a UTF-16 encoded code
        // unit, not a Unicode code point of the character! So decode
        // UTF16->UTF32->UTF8.
        // See http://unicode.org/faq/utf_bom.html#utf16-3
        // For UTF8 byte structure, see http://en.wikipedia.org/wiki/UTF-8#Description
        // and https://www.ietf.org/rfc/rfc2279.txt
        // and https://tools.ietf.org/html/rfc3629
        var u = str.charCodeAt(i); // possibly a lead surrogate
        if (u >= 0xD800 && u <= 0xDFFF) {
          var u1 = str.charCodeAt(++i);
          u = 0x10000 + ((u & 0x3FF) << 10) | (u1 & 0x3FF);
        }
        if (u <= 0x7F) {
          if (outIdx >= endIdx) break;
          heap[outIdx++] = u;
        } else if (u <= 0x7FF) {
          if (outIdx + 1 >= endIdx) break;
          heap[outIdx++] = 0xC0 | (u >> 6);
          heap[outIdx++] = 0x80 | (u & 63);
        } else if (u <= 0xFFFF) {
          if (outIdx + 2 >= endIdx) break;
          heap[outIdx++] = 0xE0 | (u >> 12);
          heap[outIdx++] = 0x80 | ((u >> 6) & 63);
          heap[outIdx++] = 0x80 | (u & 63);
        } else {
          if (outIdx + 3 >= endIdx) break;
          heap[outIdx++] = 0xF0 | (u >> 18);
          heap[outIdx++] = 0x80 | ((u >> 12) & 63);
          heap[outIdx++] = 0x80 | ((u >> 6) & 63);
          heap[outIdx++] = 0x80 | (u & 63);
        }
      }
      // Null-terminate the pointer to the buffer.
      heap[outIdx] = 0;
      return outIdx - startIdx;
    };
  /** @type {function(string, boolean=, number=)} */
  function intArrayFromString(stringy, dontAddNull, length) {
    var len = length > 0 ? length : lengthBytesUTF8(stringy)+1;
    var u8array = new Array(len);
    var numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length);
    if (dontAddNull) u8array.length = numBytesWritten;
    return u8array;
  }
  
  var UTF8Decoder = typeof TextDecoder != 'undefined' ? new TextDecoder('utf8') : undefined;
  
    /**
     * Given a pointer 'idx' to a null-terminated UTF8-encoded string in the given
     * array that contains uint8 values, returns a copy of that string as a
     * Javascript String object.
     * heapOrArray is either a regular array, or a JavaScript typed array view.
     * @param {number} idx
     * @param {number=} maxBytesToRead
     * @return {string}
     */
  var UTF8ArrayToString = (heapOrArray, idx, maxBytesToRead) => {
      var endIdx = idx + maxBytesToRead;
      var endPtr = idx;
      // TextDecoder needs to know the byte length in advance, it doesn't stop on
      // null terminator by itself.  Also, use the length info to avoid running tiny
      // strings through TextDecoder, since .subarray() allocates garbage.
      // (As a tiny code save trick, compare endPtr against endIdx using a negation,
      // so that undefined means Infinity)
      while (heapOrArray[endPtr] && !(endPtr >= endIdx)) ++endPtr;
  
      if (endPtr - idx > 16 && heapOrArray.buffer && UTF8Decoder) {
        return UTF8Decoder.decode(heapOrArray.subarray(idx, endPtr));
      }
      var str = '';
      // If building with TextDecoder, we have already computed the string length
      // above, so test loop end condition against that
      while (idx < endPtr) {
        // For UTF8 byte structure, see:
        // http://en.wikipedia.org/wiki/UTF-8#Description
        // https://www.ietf.org/rfc/rfc2279.txt
        // https://tools.ietf.org/html/rfc3629
        var u0 = heapOrArray[idx++];
        if (!(u0 & 0x80)) { str += String.fromCharCode(u0); continue; }
        var u1 = heapOrArray[idx++] & 63;
        if ((u0 & 0xE0) == 0xC0) { str += String.fromCharCode(((u0 & 31) << 6) | u1); continue; }
        var u2 = heapOrArray[idx++] & 63;
        if ((u0 & 0xF0) == 0xE0) {
          u0 = ((u0 & 15) << 12) | (u1 << 6) | u2;
        } else {
          u0 = ((u0 & 7) << 18) | (u1 << 12) | (u2 << 6) | (heapOrArray[idx++] & 63);
        }
  
        if (u0 < 0x10000) {
          str += String.fromCharCode(u0);
        } else {
          var ch = u0 - 0x10000;
          str += String.fromCharCode(0xD800 | (ch >> 10), 0xDC00 | (ch & 0x3FF));
        }
      }
      return str;
    };
  var TTY = {ttys:[],init:function () {
        // https://github.com/emscripten-core/emscripten/pull/1555
        // if (ENVIRONMENT_IS_NODE) {
        //   // currently, FS.init does not distinguish if process.stdin is a file or TTY
        //   // device, it always assumes it's a TTY device. because of this, we're forcing
        //   // process.stdin to UTF8 encoding to at least make stdin reading compatible
        //   // with text files until FS.init can be refactored.
        //   process.stdin.setEncoding('utf8');
        // }
      },shutdown:function() {
        // https://github.com/emscripten-core/emscripten/pull/1555
        // if (ENVIRONMENT_IS_NODE) {
        //   // inolen: any idea as to why node -e 'process.stdin.read()' wouldn't exit immediately (with process.stdin being a tty)?
        //   // isaacs: because now it's reading from the stream, you've expressed interest in it, so that read() kicks off a _read() which creates a ReadReq operation
        //   // inolen: I thought read() in that case was a synchronous operation that just grabbed some amount of buffered data if it exists?
        //   // isaacs: it is. but it also triggers a _read() call, which calls readStart() on the handle
        //   // isaacs: do process.stdin.pause() and i'd think it'd probably close the pending call
        //   process.stdin.pause();
        // }
      },register:function(dev, ops) {
        TTY.ttys[dev] = { input: [], output: [], ops: ops };
        FS.registerDevice(dev, TTY.stream_ops);
      },stream_ops:{open:function(stream) {
          var tty = TTY.ttys[stream.node.rdev];
          if (!tty) {
            throw new FS.ErrnoError(43);
          }
          stream.tty = tty;
          stream.seekable = false;
        },close:function(stream) {
          // flush any pending line data
          stream.tty.ops.fsync(stream.tty);
        },fsync:function(stream) {
          stream.tty.ops.fsync(stream.tty);
        },read:function(stream, buffer, offset, length, pos /* ignored */) {
          if (!stream.tty || !stream.tty.ops.get_char) {
            throw new FS.ErrnoError(60);
          }
          var bytesRead = 0;
          for (var i = 0; i < length; i++) {
            var result;
            try {
              result = stream.tty.ops.get_char(stream.tty);
            } catch (e) {
              throw new FS.ErrnoError(29);
            }
            if (result === undefined && bytesRead === 0) {
              throw new FS.ErrnoError(6);
            }
            if (result === null || result === undefined) break;
            bytesRead++;
            buffer[offset+i] = result;
          }
          if (bytesRead) {
            stream.node.timestamp = Date.now();
          }
          return bytesRead;
        },write:function(stream, buffer, offset, length, pos) {
          if (!stream.tty || !stream.tty.ops.put_char) {
            throw new FS.ErrnoError(60);
          }
          try {
            for (var i = 0; i < length; i++) {
              stream.tty.ops.put_char(stream.tty, buffer[offset+i]);
            }
          } catch (e) {
            throw new FS.ErrnoError(29);
          }
          if (length) {
            stream.node.timestamp = Date.now();
          }
          return i;
        }},default_tty_ops:{get_char:function(tty) {
          if (!tty.input.length) {
            var result = null;
            if (ENVIRONMENT_IS_NODE) {
              // we will read data by chunks of BUFSIZE
              var BUFSIZE = 256;
              var buf = Buffer.alloc(BUFSIZE);
              var bytesRead = 0;
  
              try {
                bytesRead = fs.readSync(process.stdin.fd, buf, 0, BUFSIZE, -1);
              } catch(e) {
                // Cross-platform differences: on Windows, reading EOF throws an exception, but on other OSes,
                // reading EOF returns 0. Uniformize behavior by treating the EOF exception to return 0.
                if (e.toString().includes('EOF')) bytesRead = 0;
                else throw e;
              }
  
              if (bytesRead > 0) {
                result = buf.slice(0, bytesRead).toString('utf-8');
              } else {
                result = null;
              }
            } else
            if (typeof window != 'undefined' &&
              typeof window.prompt == 'function') {
              // Browser.
              result = window.prompt('Input: ');  // returns null on cancel
              if (result !== null) {
                result += '\n';
              }
            } else if (typeof readline == 'function') {
              // Command line.
              result = readline();
              if (result !== null) {
                result += '\n';
              }
            }
            if (!result) {
              return null;
            }
            tty.input = intArrayFromString(result, true);
          }
          return tty.input.shift();
        },put_char:function(tty, val) {
          if (val === null || val === 10) {
            out(UTF8ArrayToString(tty.output, 0));
            tty.output = [];
          } else {
            if (val != 0) tty.output.push(val); // val == 0 would cut text output off in the middle.
          }
        },fsync:function(tty) {
          if (tty.output && tty.output.length > 0) {
            out(UTF8ArrayToString(tty.output, 0));
            tty.output = [];
          }
        },ioctl_tcgets:function(tty) {
          // typical setting
          return {
            c_iflag: 25856,
            c_oflag: 5,
            c_cflag: 191,
            c_lflag: 35387,
            c_cc: [
              0x03, 0x1c, 0x7f, 0x15, 0x04, 0x00, 0x01, 0x00, 0x11, 0x13, 0x1a, 0x00,
              0x12, 0x0f, 0x17, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
              0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            ]
          };
        },ioctl_tcsets:function(tty, optional_actions, data) {
          // currently just ignore
          return 0;
        },ioctl_tiocgwinsz:function(tty) {
          return [24, 80];
        }},default_tty1_ops:{put_char:function(tty, val) {
          if (val === null || val === 10) {
            err(UTF8ArrayToString(tty.output, 0));
            tty.output = [];
          } else {
            if (val != 0) tty.output.push(val);
          }
        },fsync:function(tty) {
          if (tty.output && tty.output.length > 0) {
            err(UTF8ArrayToString(tty.output, 0));
            tty.output = [];
          }
        }}};
  
  
  var zeroMemory = (address, size) => {
      HEAPU8.fill(0, address, address + size);
      return address;
    };
  
  var alignMemory = (size, alignment) => {
      return Math.ceil(size / alignment) * alignment;
    };
  var mmapAlloc = (size) => {
      abort();
    };
  var MEMFS = {ops_table:null,mount:function(mount) {
        return MEMFS.createNode(null, '/', 16384 | 511 /* 0777 */, 0);
      },createNode:function(parent, name, mode, dev) {
        if (FS.isBlkdev(mode) || FS.isFIFO(mode)) {
          // no supported
          throw new FS.ErrnoError(63);
        }
        if (!MEMFS.ops_table) {
          MEMFS.ops_table = {
            dir: {
              node: {
                getattr: MEMFS.node_ops.getattr,
                setattr: MEMFS.node_ops.setattr,
                lookup: MEMFS.node_ops.lookup,
                mknod: MEMFS.node_ops.mknod,
                rename: MEMFS.node_ops.rename,
                unlink: MEMFS.node_ops.unlink,
                rmdir: MEMFS.node_ops.rmdir,
                readdir: MEMFS.node_ops.readdir,
                symlink: MEMFS.node_ops.symlink
              },
              stream: {
                llseek: MEMFS.stream_ops.llseek
              }
            },
            file: {
              node: {
                getattr: MEMFS.node_ops.getattr,
                setattr: MEMFS.node_ops.setattr
              },
              stream: {
                llseek: MEMFS.stream_ops.llseek,
                read: MEMFS.stream_ops.read,
                write: MEMFS.stream_ops.write,
                allocate: MEMFS.stream_ops.allocate,
                mmap: MEMFS.stream_ops.mmap,
                msync: MEMFS.stream_ops.msync
              }
            },
            link: {
              node: {
                getattr: MEMFS.node_ops.getattr,
                setattr: MEMFS.node_ops.setattr,
                readlink: MEMFS.node_ops.readlink
              },
              stream: {}
            },
            chrdev: {
              node: {
                getattr: MEMFS.node_ops.getattr,
                setattr: MEMFS.node_ops.setattr
              },
              stream: FS.chrdev_stream_ops
            }
          };
        }
        var node = FS.createNode(parent, name, mode, dev);
        if (FS.isDir(node.mode)) {
          node.node_ops = MEMFS.ops_table.dir.node;
          node.stream_ops = MEMFS.ops_table.dir.stream;
          node.contents = {};
        } else if (FS.isFile(node.mode)) {
          node.node_ops = MEMFS.ops_table.file.node;
          node.stream_ops = MEMFS.ops_table.file.stream;
          node.usedBytes = 0; // The actual number of bytes used in the typed array, as opposed to contents.length which gives the whole capacity.
          // When the byte data of the file is populated, this will point to either a typed array, or a normal JS array. Typed arrays are preferred
          // for performance, and used by default. However, typed arrays are not resizable like normal JS arrays are, so there is a small disk size
          // penalty involved for appending file writes that continuously grow a file similar to std::vector capacity vs used -scheme.
          node.contents = null; 
        } else if (FS.isLink(node.mode)) {
          node.node_ops = MEMFS.ops_table.link.node;
          node.stream_ops = MEMFS.ops_table.link.stream;
        } else if (FS.isChrdev(node.mode)) {
          node.node_ops = MEMFS.ops_table.chrdev.node;
          node.stream_ops = MEMFS.ops_table.chrdev.stream;
        }
        node.timestamp = Date.now();
        // add the new node to the parent
        if (parent) {
          parent.contents[name] = node;
          parent.timestamp = node.timestamp;
        }
        return node;
      },getFileDataAsTypedArray:function(node) {
        if (!node.contents) return new Uint8Array(0);
        if (node.contents.subarray) return node.contents.subarray(0, node.usedBytes); // Make sure to not return excess unused bytes.
        return new Uint8Array(node.contents);
      },expandFileStorage:function(node, newCapacity) {
        var prevCapacity = node.contents ? node.contents.length : 0;
        if (prevCapacity >= newCapacity) return; // No need to expand, the storage was already large enough.
        // Don't expand strictly to the given requested limit if it's only a very small increase, but instead geometrically grow capacity.
        // For small filesizes (<1MB), perform size*2 geometric increase, but for large sizes, do a much more conservative size*1.125 increase to
        // avoid overshooting the allocation cap by a very large margin.
        var CAPACITY_DOUBLING_MAX = 1024 * 1024;
        newCapacity = Math.max(newCapacity, (prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2.0 : 1.125)) >>> 0);
        if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256); // At minimum allocate 256b for each file when expanding.
        var oldContents = node.contents;
        node.contents = new Uint8Array(newCapacity); // Allocate new storage.
        if (node.usedBytes > 0) node.contents.set(oldContents.subarray(0, node.usedBytes), 0); // Copy old data over to the new storage.
      },resizeFileStorage:function(node, newSize) {
        if (node.usedBytes == newSize) return;
        if (newSize == 0) {
          node.contents = null; // Fully decommit when requesting a resize to zero.
          node.usedBytes = 0;
        } else {
          var oldContents = node.contents;
          node.contents = new Uint8Array(newSize); // Allocate new storage.
          if (oldContents) {
            node.contents.set(oldContents.subarray(0, Math.min(newSize, node.usedBytes))); // Copy old data over to the new storage.
          }
          node.usedBytes = newSize;
        }
      },node_ops:{getattr:function(node) {
          var attr = {};
          // device numbers reuse inode numbers.
          attr.dev = FS.isChrdev(node.mode) ? node.id : 1;
          attr.ino = node.id;
          attr.mode = node.mode;
          attr.nlink = 1;
          attr.uid = 0;
          attr.gid = 0;
          attr.rdev = node.rdev;
          if (FS.isDir(node.mode)) {
            attr.size = 4096;
          } else if (FS.isFile(node.mode)) {
            attr.size = node.usedBytes;
          } else if (FS.isLink(node.mode)) {
            attr.size = node.link.length;
          } else {
            attr.size = 0;
          }
          attr.atime = new Date(node.timestamp);
          attr.mtime = new Date(node.timestamp);
          attr.ctime = new Date(node.timestamp);
          // NOTE: In our implementation, st_blocks = Math.ceil(st_size/st_blksize),
          //       but this is not required by the standard.
          attr.blksize = 4096;
          attr.blocks = Math.ceil(attr.size / attr.blksize);
          return attr;
        },setattr:function(node, attr) {
          if (attr.mode !== undefined) {
            node.mode = attr.mode;
          }
          if (attr.timestamp !== undefined) {
            node.timestamp = attr.timestamp;
          }
          if (attr.size !== undefined) {
            MEMFS.resizeFileStorage(node, attr.size);
          }
        },lookup:function(parent, name) {
          throw FS.genericErrors[44];
        },mknod:function(parent, name, mode, dev) {
          return MEMFS.createNode(parent, name, mode, dev);
        },rename:function(old_node, new_dir, new_name) {
          // if we're overwriting a directory at new_name, make sure it's empty.
          if (FS.isDir(old_node.mode)) {
            var new_node;
            try {
              new_node = FS.lookupNode(new_dir, new_name);
            } catch (e) {
            }
            if (new_node) {
              for (var i in new_node.contents) {
                throw new FS.ErrnoError(55);
              }
            }
          }
          // do the internal rewiring
          delete old_node.parent.contents[old_node.name];
          old_node.parent.timestamp = Date.now()
          old_node.name = new_name;
          new_dir.contents[new_name] = old_node;
          new_dir.timestamp = old_node.parent.timestamp;
          old_node.parent = new_dir;
        },unlink:function(parent, name) {
          delete parent.contents[name];
          parent.timestamp = Date.now();
        },rmdir:function(parent, name) {
          var node = FS.lookupNode(parent, name);
          for (var i in node.contents) {
            throw new FS.ErrnoError(55);
          }
          delete parent.contents[name];
          parent.timestamp = Date.now();
        },readdir:function(node) {
          var entries = ['.', '..'];
          for (var key in node.contents) {
            if (!node.contents.hasOwnProperty(key)) {
              continue;
            }
            entries.push(key);
          }
          return entries;
        },symlink:function(parent, newname, oldpath) {
          var node = MEMFS.createNode(parent, newname, 511 /* 0777 */ | 40960, 0);
          node.link = oldpath;
          return node;
        },readlink:function(node) {
          if (!FS.isLink(node.mode)) {
            throw new FS.ErrnoError(28);
          }
          return node.link;
        }},stream_ops:{read:function(stream, buffer, offset, length, position) {
          var contents = stream.node.contents;
          if (position >= stream.node.usedBytes) return 0;
          var size = Math.min(stream.node.usedBytes - position, length);
          if (size > 8 && contents.subarray) { // non-trivial, and typed array
            buffer.set(contents.subarray(position, position + size), offset);
          } else {
            for (var i = 0; i < size; i++) buffer[offset + i] = contents[position + i];
          }
          return size;
        },write:function(stream, buffer, offset, length, position, canOwn) {
  
          if (!length) return 0;
          var node = stream.node;
          node.timestamp = Date.now();
  
          if (buffer.subarray && (!node.contents || node.contents.subarray)) { // This write is from a typed array to a typed array?
            if (canOwn) {
              node.contents = buffer.subarray(offset, offset + length);
              node.usedBytes = length;
              return length;
            } else if (node.usedBytes === 0 && position === 0) { // If this is a simple first write to an empty file, do a fast set since we don't need to care about old data.
              node.contents = buffer.slice(offset, offset + length);
              node.usedBytes = length;
              return length;
            } else if (position + length <= node.usedBytes) { // Writing to an already allocated and used subrange of the file?
              node.contents.set(buffer.subarray(offset, offset + length), position);
              return length;
            }
          }
  
          // Appending to an existing file and we need to reallocate, or source data did not come as a typed array.
          MEMFS.expandFileStorage(node, position+length);
          if (node.contents.subarray && buffer.subarray) {
            // Use typed array write which is available.
            node.contents.set(buffer.subarray(offset, offset + length), position);
          } else {
            for (var i = 0; i < length; i++) {
             node.contents[position + i] = buffer[offset + i]; // Or fall back to manual write if not.
            }
          }
          node.usedBytes = Math.max(node.usedBytes, position + length);
          return length;
        },llseek:function(stream, offset, whence) {
          var position = offset;
          if (whence === 1) {
            position += stream.position;
          } else if (whence === 2) {
            if (FS.isFile(stream.node.mode)) {
              position += stream.node.usedBytes;
            }
          }
          if (position < 0) {
            throw new FS.ErrnoError(28);
          }
          return position;
        },allocate:function(stream, offset, length) {
          MEMFS.expandFileStorage(stream.node, offset + length);
          stream.node.usedBytes = Math.max(stream.node.usedBytes, offset + length);
        },mmap:function(stream, length, position, prot, flags) {
          if (!FS.isFile(stream.node.mode)) {
            throw new FS.ErrnoError(43);
          }
          var ptr;
          var allocated;
          var contents = stream.node.contents;
          // Only make a new copy when MAP_PRIVATE is specified.
          if (!(flags & 2) && contents.buffer === HEAP8.buffer) {
            // We can't emulate MAP_SHARED when the file is not backed by the
            // buffer we're mapping to (e.g. the HEAP buffer).
            allocated = false;
            ptr = contents.byteOffset;
          } else {
            // Try to avoid unnecessary slices.
            if (position > 0 || position + length < contents.length) {
              if (contents.subarray) {
                contents = contents.subarray(position, position + length);
              } else {
                contents = Array.prototype.slice.call(contents, position, position + length);
              }
            }
            allocated = true;
            ptr = mmapAlloc(length);
            if (!ptr) {
              throw new FS.ErrnoError(48);
            }
            HEAP8.set(contents, ptr);
          }
          return { ptr, allocated };
        },msync:function(stream, buffer, offset, length, mmapFlags) {
          MEMFS.stream_ops.write(stream, buffer, 0, length, offset, false);
          // should we check if bytesWritten and length are the same?
          return 0;
        }}};
  
  /** @param {boolean=} noRunDep */
  var asyncLoad = (url, onload, onerror, noRunDep) => {
      var dep = !noRunDep ? getUniqueRunDependency(`al ${url}`) : '';
      readAsync(url, (arrayBuffer) => {
        assert(arrayBuffer, `Loading data file "${url}" failed (no arrayBuffer).`);
        onload(new Uint8Array(arrayBuffer));
        if (dep) removeRunDependency(dep);
      }, (event) => {
        if (onerror) {
          onerror();
        } else {
          throw `Loading data file "${url}" failed.`;
        }
      });
      if (dep) addRunDependency(dep);
    };
  
  
  var preloadPlugins = Module['preloadPlugins'] || [];
  function FS_handledByPreloadPlugin(byteArray, fullname, finish, onerror) {
      // Ensure plugins are ready.
      if (typeof Browser != 'undefined') Browser.init();
  
      var handled = false;
      preloadPlugins.forEach(function(plugin) {
        if (handled) return;
        if (plugin['canHandle'](fullname)) {
          plugin['handle'](byteArray, fullname, finish, onerror);
          handled = true;
        }
      });
      return handled;
    }
  function FS_createPreloadedFile(parent, name, url, canRead, canWrite, onload, onerror, dontCreateFile, canOwn, preFinish) {
      // TODO we should allow people to just pass in a complete filename instead
      // of parent and name being that we just join them anyways
      var fullname = name ? PATH_FS.resolve(PATH.join2(parent, name)) : parent;
      var dep = getUniqueRunDependency(`cp ${fullname}`); // might have several active requests for the same fullname
      function processData(byteArray) {
        function finish(byteArray) {
          if (preFinish) preFinish();
          if (!dontCreateFile) {
            FS.createDataFile(parent, name, byteArray, canRead, canWrite, canOwn);
          }
          if (onload) onload();
          removeRunDependency(dep);
        }
        if (FS_handledByPreloadPlugin(byteArray, fullname, finish, () => {
          if (onerror) onerror();
          removeRunDependency(dep);
        })) {
          return;
        }
        finish(byteArray);
      }
      addRunDependency(dep);
      if (typeof url == 'string') {
        asyncLoad(url, (byteArray) => processData(byteArray), onerror);
      } else {
        processData(url);
      }
    }
  
  function FS_modeStringToFlags(str) {
      var flagModes = {
        'r': 0,
        'r+': 2,
        'w': 512 | 64 | 1,
        'w+': 512 | 64 | 2,
        'a': 1024 | 64 | 1,
        'a+': 1024 | 64 | 2,
      };
      var flags = flagModes[str];
      if (typeof flags == 'undefined') {
        throw new Error(`Unknown file open mode: ${str}`);
      }
      return flags;
    }
  
  function FS_getMode(canRead, canWrite) {
      var mode = 0;
      if (canRead) mode |= 292 | 73;
      if (canWrite) mode |= 146;
      return mode;
    }
  
  
  
  
  
  
  var IDBFS = {dbs:{},indexedDB:() => {
        if (typeof indexedDB != 'undefined') return indexedDB;
        var ret = null;
        if (typeof window == 'object') ret = window.indexedDB || window.mozIndexedDB || window.webkitIndexedDB || window.msIndexedDB;
        assert(ret, 'IDBFS used, but indexedDB not supported');
        return ret;
      },DB_VERSION:21,DB_STORE_NAME:"FILE_DATA",mount:function(mount) {
        // reuse all of the core MEMFS functionality
        return MEMFS.mount.apply(null, arguments);
      },syncfs:(mount, populate, callback) => {
        IDBFS.getLocalSet(mount, (err, local) => {
          if (err) return callback(err);
  
          IDBFS.getRemoteSet(mount, (err, remote) => {
            if (err) return callback(err);
  
            var src = populate ? remote : local;
            var dst = populate ? local : remote;
  
            IDBFS.reconcile(src, dst, callback);
          });
        });
      },quit:() => {
        Object.values(IDBFS.dbs).forEach((value) => value.close());
        IDBFS.dbs = {};
      },getDB:(name, callback) => {
        // check the cache first
        var db = IDBFS.dbs[name];
        if (db) {
          return callback(null, db);
        }
  
        var req;
        try {
          req = IDBFS.indexedDB().open(name, IDBFS.DB_VERSION);
        } catch (e) {
          return callback(e);
        }
        if (!req) {
          return callback("Unable to connect to IndexedDB");
        }
        req.onupgradeneeded = (e) => {
          var db = /** @type {IDBDatabase} */ (e.target.result);
          var transaction = e.target.transaction;
  
          var fileStore;
  
          if (db.objectStoreNames.contains(IDBFS.DB_STORE_NAME)) {
            fileStore = transaction.objectStore(IDBFS.DB_STORE_NAME);
          } else {
            fileStore = db.createObjectStore(IDBFS.DB_STORE_NAME);
          }
  
          if (!fileStore.indexNames.contains('timestamp')) {
            fileStore.createIndex('timestamp', 'timestamp', { unique: false });
          }
        };
        req.onsuccess = () => {
          db = /** @type {IDBDatabase} */ (req.result);
  
          // add to the cache
          IDBFS.dbs[name] = db;
          callback(null, db);
        };
        req.onerror = (e) => {
          callback(this.error);
          e.preventDefault();
        };
      },getLocalSet:(mount, callback) => {
        var entries = {};
  
        function isRealDir(p) {
          return p !== '.' && p !== '..';
        };
        function toAbsolute(root) {
          return (p) => {
            return PATH.join2(root, p);
          }
        };
  
        var check = FS.readdir(mount.mountpoint).filter(isRealDir).map(toAbsolute(mount.mountpoint));
  
        while (check.length) {
          var path = check.pop();
          var stat;
  
          try {
            stat = FS.stat(path);
          } catch (e) {
            return callback(e);
          }
  
          if (FS.isDir(stat.mode)) {
            check.push.apply(check, FS.readdir(path).filter(isRealDir).map(toAbsolute(path)));
          }
  
          entries[path] = { 'timestamp': stat.mtime };
        }
  
        return callback(null, { type: 'local', entries: entries });
      },getRemoteSet:(mount, callback) => {
        var entries = {};
  
        IDBFS.getDB(mount.mountpoint, (err, db) => {
          if (err) return callback(err);
  
          try {
            var transaction = db.transaction([IDBFS.DB_STORE_NAME], 'readonly');
            transaction.onerror = (e) => {
              callback(this.error);
              e.preventDefault();
            };
  
            var store = transaction.objectStore(IDBFS.DB_STORE_NAME);
            var index = store.index('timestamp');
  
            index.openKeyCursor().onsuccess = (event) => {
              var cursor = event.target.result;
  
              if (!cursor) {
                return callback(null, { type: 'remote', db, entries });
              }
  
              entries[cursor.primaryKey] = { 'timestamp': cursor.key };
  
              cursor.continue();
            };
          } catch (e) {
            return callback(e);
          }
        });
      },loadLocalEntry:(path, callback) => {
        var stat, node;
  
        try {
          var lookup = FS.lookupPath(path);
          node = lookup.node;
          stat = FS.stat(path);
        } catch (e) {
          return callback(e);
        }
  
        if (FS.isDir(stat.mode)) {
          return callback(null, { 'timestamp': stat.mtime, 'mode': stat.mode });
        } else if (FS.isFile(stat.mode)) {
          // Performance consideration: storing a normal JavaScript array to a IndexedDB is much slower than storing a typed array.
          // Therefore always convert the file contents to a typed array first before writing the data to IndexedDB.
          node.contents = MEMFS.getFileDataAsTypedArray(node);
          return callback(null, { 'timestamp': stat.mtime, 'mode': stat.mode, 'contents': node.contents });
        } else {
          return callback(new Error('node type not supported'));
        }
      },storeLocalEntry:(path, entry, callback) => {
        try {
          if (FS.isDir(entry['mode'])) {
            FS.mkdirTree(path, entry['mode']);
          } else if (FS.isFile(entry['mode'])) {
            FS.writeFile(path, entry['contents'], { canOwn: true });
          } else {
            return callback(new Error('node type not supported'));
          }
  
          FS.chmod(path, entry['mode']);
          FS.utime(path, entry['timestamp'], entry['timestamp']);
        } catch (e) {
          return callback(e);
        }
  
        callback(null);
      },removeLocalEntry:(path, callback) => {
        try {
          var stat = FS.stat(path);
  
          if (FS.isDir(stat.mode)) {
            FS.rmdir(path);
          } else if (FS.isFile(stat.mode)) {
            FS.unlink(path);
          }
        } catch (e) {
          return callback(e);
        }
  
        callback(null);
      },loadRemoteEntry:(store, path, callback) => {
        var req = store.get(path);
        req.onsuccess = (event) => { callback(null, event.target.result); };
        req.onerror = (e) => {
          callback(this.error);
          e.preventDefault();
        };
      },storeRemoteEntry:(store, path, entry, callback) => {
        try {
          var req = store.put(entry, path);
        } catch (e) {
          callback(e);
          return;
        }
        req.onsuccess = () => { callback(null); };
        req.onerror = (e) => {
          callback(this.error);
          e.preventDefault();
        };
      },removeRemoteEntry:(store, path, callback) => {
        var req = store.delete(path);
        req.onsuccess = () => { callback(null); };
        req.onerror = (e) => {
          callback(this.error);
          e.preventDefault();
        };
      },reconcile:(src, dst, callback) => {
        var total = 0;
  
        var create = [];
        Object.keys(src.entries).forEach(function (key) {
          var e = src.entries[key];
          var e2 = dst.entries[key];
          if (!e2 || e['timestamp'].getTime() != e2['timestamp'].getTime()) {
            create.push(key);
            total++;
          }
        });
  
        var remove = [];
        Object.keys(dst.entries).forEach(function (key) {
          if (!src.entries[key]) {
            remove.push(key);
            total++;
          }
        });
  
        if (!total) {
          return callback(null);
        }
  
        var errored = false;
        var db = src.type === 'remote' ? src.db : dst.db;
        var transaction = db.transaction([IDBFS.DB_STORE_NAME], 'readwrite');
        var store = transaction.objectStore(IDBFS.DB_STORE_NAME);
  
        function done(err) {
          if (err && !errored) {
            errored = true;
            return callback(err);
          }
        };
  
        transaction.onerror = (e) => {
          done(this.error);
          e.preventDefault();
        };
  
        transaction.oncomplete = (e) => {
          if (!errored) {
            callback(null);
          }
        };
  
        // sort paths in ascending order so directory entries are created
        // before the files inside them
        create.sort().forEach((path) => {
          if (dst.type === 'local') {
            IDBFS.loadRemoteEntry(store, path, (err, entry) => {
              if (err) return done(err);
              IDBFS.storeLocalEntry(path, entry, done);
            });
          } else {
            IDBFS.loadLocalEntry(path, (err, entry) => {
              if (err) return done(err);
              IDBFS.storeRemoteEntry(store, path, entry, done);
            });
          }
        });
  
        // sort paths in descending order so files are deleted before their
        // parent directories
        remove.sort().reverse().forEach((path) => {
          if (dst.type === 'local') {
            IDBFS.removeLocalEntry(path, done);
          } else {
            IDBFS.removeRemoteEntry(store, path, done);
          }
        });
      }};
  var FS = {root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:(path, opts = {}) => {
        path = PATH_FS.resolve(path);
  
        if (!path) return { path: '', node: null };
  
        var defaults = {
          follow_mount: true,
          recurse_count: 0
        };
        opts = Object.assign(defaults, opts)
  
        if (opts.recurse_count > 8) {  // max recursive lookup of 8
          throw new FS.ErrnoError(32);
        }
  
        // split the absolute path
        var parts = path.split('/').filter((p) => !!p);
  
        // start at the root
        var current = FS.root;
        var current_path = '/';
  
        for (var i = 0; i < parts.length; i++) {
          var islast = (i === parts.length-1);
          if (islast && opts.parent) {
            // stop resolving
            break;
          }
  
          current = FS.lookupNode(current, parts[i]);
          current_path = PATH.join2(current_path, parts[i]);
  
          // jump to the mount's root node if this is a mountpoint
          if (FS.isMountpoint(current)) {
            if (!islast || (islast && opts.follow_mount)) {
              current = current.mounted.root;
            }
          }
  
          // by default, lookupPath will not follow a symlink if it is the final path component.
          // setting opts.follow = true will override this behavior.
          if (!islast || opts.follow) {
            var count = 0;
            while (FS.isLink(current.mode)) {
              var link = FS.readlink(current_path);
              current_path = PATH_FS.resolve(PATH.dirname(current_path), link);
  
              var lookup = FS.lookupPath(current_path, { recurse_count: opts.recurse_count + 1 });
              current = lookup.node;
  
              if (count++ > 40) {  // limit max consecutive symlinks to 40 (SYMLOOP_MAX).
                throw new FS.ErrnoError(32);
              }
            }
          }
        }
  
        return { path: current_path, node: current };
      },getPath:(node) => {
        var path;
        while (true) {
          if (FS.isRoot(node)) {
            var mount = node.mount.mountpoint;
            if (!path) return mount;
            return mount[mount.length-1] !== '/' ? `${mount}/${path}` : mount + path;
          }
          path = path ? `${node.name}/${path}` : node.name;
          node = node.parent;
        }
      },hashName:(parentid, name) => {
        var hash = 0;
  
        for (var i = 0; i < name.length; i++) {
          hash = ((hash << 5) - hash + name.charCodeAt(i)) | 0;
        }
        return ((parentid + hash) >>> 0) % FS.nameTable.length;
      },hashAddNode:(node) => {
        var hash = FS.hashName(node.parent.id, node.name);
        node.name_next = FS.nameTable[hash];
        FS.nameTable[hash] = node;
      },hashRemoveNode:(node) => {
        var hash = FS.hashName(node.parent.id, node.name);
        if (FS.nameTable[hash] === node) {
          FS.nameTable[hash] = node.name_next;
        } else {
          var current = FS.nameTable[hash];
          while (current) {
            if (current.name_next === node) {
              current.name_next = node.name_next;
              break;
            }
            current = current.name_next;
          }
        }
      },lookupNode:(parent, name) => {
        var errCode = FS.mayLookup(parent);
        if (errCode) {
          throw new FS.ErrnoError(errCode, parent);
        }
        var hash = FS.hashName(parent.id, name);
        for (var node = FS.nameTable[hash]; node; node = node.name_next) {
          var nodeName = node.name;
          if (node.parent.id === parent.id && nodeName === name) {
            return node;
          }
        }
        // if we failed to find it in the cache, call into the VFS
        return FS.lookup(parent, name);
      },createNode:(parent, name, mode, rdev) => {
        var node = new FS.FSNode(parent, name, mode, rdev);
  
        FS.hashAddNode(node);
  
        return node;
      },destroyNode:(node) => {
        FS.hashRemoveNode(node);
      },isRoot:(node) => {
        return node === node.parent;
      },isMountpoint:(node) => {
        return !!node.mounted;
      },isFile:(mode) => {
        return (mode & 61440) === 32768;
      },isDir:(mode) => {
        return (mode & 61440) === 16384;
      },isLink:(mode) => {
        return (mode & 61440) === 40960;
      },isChrdev:(mode) => {
        return (mode & 61440) === 8192;
      },isBlkdev:(mode) => {
        return (mode & 61440) === 24576;
      },isFIFO:(mode) => {
        return (mode & 61440) === 4096;
      },isSocket:(mode) => {
        return (mode & 49152) === 49152;
      },flagsToPermissionString:(flag) => {
        var perms = ['r', 'w', 'rw'][flag & 3];
        if ((flag & 512)) {
          perms += 'w';
        }
        return perms;
      },nodePermissions:(node, perms) => {
        if (FS.ignorePermissions) {
          return 0;
        }
        // return 0 if any user, group or owner bits are set.
        if (perms.includes('r') && !(node.mode & 292)) {
          return 2;
        } else if (perms.includes('w') && !(node.mode & 146)) {
          return 2;
        } else if (perms.includes('x') && !(node.mode & 73)) {
          return 2;
        }
        return 0;
      },mayLookup:(dir) => {
        var errCode = FS.nodePermissions(dir, 'x');
        if (errCode) return errCode;
        if (!dir.node_ops.lookup) return 2;
        return 0;
      },mayCreate:(dir, name) => {
        try {
          var node = FS.lookupNode(dir, name);
          return 20;
        } catch (e) {
        }
        return FS.nodePermissions(dir, 'wx');
      },mayDelete:(dir, name, isdir) => {
        var node;
        try {
          node = FS.lookupNode(dir, name);
        } catch (e) {
          return e.errno;
        }
        var errCode = FS.nodePermissions(dir, 'wx');
        if (errCode) {
          return errCode;
        }
        if (isdir) {
          if (!FS.isDir(node.mode)) {
            return 54;
          }
          if (FS.isRoot(node) || FS.getPath(node) === FS.cwd()) {
            return 10;
          }
        } else {
          if (FS.isDir(node.mode)) {
            return 31;
          }
        }
        return 0;
      },mayOpen:(node, flags) => {
        if (!node) {
          return 44;
        }
        if (FS.isLink(node.mode)) {
          return 32;
        } else if (FS.isDir(node.mode)) {
          if (FS.flagsToPermissionString(flags) !== 'r' || // opening for write
              (flags & 512)) { // TODO: check for O_SEARCH? (== search for dir only)
            return 31;
          }
        }
        return FS.nodePermissions(node, FS.flagsToPermissionString(flags));
      },MAX_OPEN_FDS:4096,nextfd:() => {
        for (var fd = 0; fd <= FS.MAX_OPEN_FDS; fd++) {
          if (!FS.streams[fd]) {
            return fd;
          }
        }
        throw new FS.ErrnoError(33);
      },getStreamChecked:(fd) => {
        var stream = FS.getStream(fd);
        if (!stream) {
          throw new FS.ErrnoError(8);
        }
        return stream;
      },getStream:(fd) => FS.streams[fd],createStream:(stream, fd = -1) => {
        if (!FS.FSStream) {
          FS.FSStream = /** @constructor */ function() {
            this.shared = { };
          };
          FS.FSStream.prototype = {};
          Object.defineProperties(FS.FSStream.prototype, {
            object: {
              /** @this {FS.FSStream} */
              get: function() { return this.node; },
              /** @this {FS.FSStream} */
              set: function(val) { this.node = val; }
            },
            isRead: {
              /** @this {FS.FSStream} */
              get: function() { return (this.flags & 2097155) !== 1; }
            },
            isWrite: {
              /** @this {FS.FSStream} */
              get: function() { return (this.flags & 2097155) !== 0; }
            },
            isAppend: {
              /** @this {FS.FSStream} */
              get: function() { return (this.flags & 1024); }
            },
            flags: {
              /** @this {FS.FSStream} */
              get: function() { return this.shared.flags; },
              /** @this {FS.FSStream} */
              set: function(val) { this.shared.flags = val; },
            },
            position : {
              /** @this {FS.FSStream} */
              get: function() { return this.shared.position; },
              /** @this {FS.FSStream} */
              set: function(val) { this.shared.position = val; },
            },
          });
        }
        // clone it, so we can return an instance of FSStream
        stream = Object.assign(new FS.FSStream(), stream);
        if (fd == -1) {
          fd = FS.nextfd();
        }
        stream.fd = fd;
        FS.streams[fd] = stream;
        return stream;
      },closeStream:(fd) => {
        FS.streams[fd] = null;
      },chrdev_stream_ops:{open:(stream) => {
          var device = FS.getDevice(stream.node.rdev);
          // override node's stream ops with the device's
          stream.stream_ops = device.stream_ops;
          // forward the open call
          if (stream.stream_ops.open) {
            stream.stream_ops.open(stream);
          }
        },llseek:() => {
          throw new FS.ErrnoError(70);
        }},major:(dev) => ((dev) >> 8),minor:(dev) => ((dev) & 0xff),makedev:(ma, mi) => ((ma) << 8 | (mi)),registerDevice:(dev, ops) => {
        FS.devices[dev] = { stream_ops: ops };
      },getDevice:(dev) => FS.devices[dev],getMounts:(mount) => {
        var mounts = [];
        var check = [mount];
  
        while (check.length) {
          var m = check.pop();
  
          mounts.push(m);
  
          check.push.apply(check, m.mounts);
        }
  
        return mounts;
      },syncfs:(populate, callback) => {
        if (typeof populate == 'function') {
          callback = populate;
          populate = false;
        }
  
        FS.syncFSRequests++;
  
        if (FS.syncFSRequests > 1) {
          err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`);
        }
  
        var mounts = FS.getMounts(FS.root.mount);
        var completed = 0;
  
        function doCallback(errCode) {
          FS.syncFSRequests--;
          return callback(errCode);
        }
  
        function done(errCode) {
          if (errCode) {
            if (!done.errored) {
              done.errored = true;
              return doCallback(errCode);
            }
            return;
          }
          if (++completed >= mounts.length) {
            doCallback(null);
          }
        };
  
        // sync all mounts
        mounts.forEach((mount) => {
          if (!mount.type.syncfs) {
            return done(null);
          }
          mount.type.syncfs(mount, populate, done);
        });
      },mount:(type, opts, mountpoint) => {
        var root = mountpoint === '/';
        var pseudo = !mountpoint;
        var node;
  
        if (root && FS.root) {
          throw new FS.ErrnoError(10);
        } else if (!root && !pseudo) {
          var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
  
          mountpoint = lookup.path;  // use the absolute path
          node = lookup.node;
  
          if (FS.isMountpoint(node)) {
            throw new FS.ErrnoError(10);
          }
  
          if (!FS.isDir(node.mode)) {
            throw new FS.ErrnoError(54);
          }
        }
  
        var mount = {
          type,
          opts,
          mountpoint,
          mounts: []
        };
  
        // create a root node for the fs
        var mountRoot = type.mount(mount);
        mountRoot.mount = mount;
        mount.root = mountRoot;
  
        if (root) {
          FS.root = mountRoot;
        } else if (node) {
          // set as a mountpoint
          node.mounted = mount;
  
          // add the new mount to the current mount's children
          if (node.mount) {
            node.mount.mounts.push(mount);
          }
        }
  
        return mountRoot;
      },unmount:(mountpoint) => {
        var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
  
        if (!FS.isMountpoint(lookup.node)) {
          throw new FS.ErrnoError(28);
        }
  
        // destroy the nodes for this mount, and all its child mounts
        var node = lookup.node;
        var mount = node.mounted;
        var mounts = FS.getMounts(mount);
  
        Object.keys(FS.nameTable).forEach((hash) => {
          var current = FS.nameTable[hash];
  
          while (current) {
            var next = current.name_next;
  
            if (mounts.includes(current.mount)) {
              FS.destroyNode(current);
            }
  
            current = next;
          }
        });
  
        // no longer a mountpoint
        node.mounted = null;
  
        // remove this mount from the child mounts
        var idx = node.mount.mounts.indexOf(mount);
        node.mount.mounts.splice(idx, 1);
      },lookup:(parent, name) => {
        return parent.node_ops.lookup(parent, name);
      },mknod:(path, mode, dev) => {
        var lookup = FS.lookupPath(path, { parent: true });
        var parent = lookup.node;
        var name = PATH.basename(path);
        if (!name || name === '.' || name === '..') {
          throw new FS.ErrnoError(28);
        }
        var errCode = FS.mayCreate(parent, name);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        if (!parent.node_ops.mknod) {
          throw new FS.ErrnoError(63);
        }
        return parent.node_ops.mknod(parent, name, mode, dev);
      },create:(path, mode) => {
        mode = mode !== undefined ? mode : 438 /* 0666 */;
        mode &= 4095;
        mode |= 32768;
        return FS.mknod(path, mode, 0);
      },mkdir:(path, mode) => {
        mode = mode !== undefined ? mode : 511 /* 0777 */;
        mode &= 511 | 512;
        mode |= 16384;
        return FS.mknod(path, mode, 0);
      },mkdirTree:(path, mode) => {
        var dirs = path.split('/');
        var d = '';
        for (var i = 0; i < dirs.length; ++i) {
          if (!dirs[i]) continue;
          d += '/' + dirs[i];
          try {
            FS.mkdir(d, mode);
          } catch(e) {
            if (e.errno != 20) throw e;
          }
        }
      },mkdev:(path, mode, dev) => {
        if (typeof dev == 'undefined') {
          dev = mode;
          mode = 438 /* 0666 */;
        }
        mode |= 8192;
        return FS.mknod(path, mode, dev);
      },symlink:(oldpath, newpath) => {
        if (!PATH_FS.resolve(oldpath)) {
          throw new FS.ErrnoError(44);
        }
        var lookup = FS.lookupPath(newpath, { parent: true });
        var parent = lookup.node;
        if (!parent) {
          throw new FS.ErrnoError(44);
        }
        var newname = PATH.basename(newpath);
        var errCode = FS.mayCreate(parent, newname);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        if (!parent.node_ops.symlink) {
          throw new FS.ErrnoError(63);
        }
        return parent.node_ops.symlink(parent, newname, oldpath);
      },rename:(old_path, new_path) => {
        var old_dirname = PATH.dirname(old_path);
        var new_dirname = PATH.dirname(new_path);
        var old_name = PATH.basename(old_path);
        var new_name = PATH.basename(new_path);
        // parents must exist
        var lookup, old_dir, new_dir;
  
        // let the errors from non existant directories percolate up
        lookup = FS.lookupPath(old_path, { parent: true });
        old_dir = lookup.node;
        lookup = FS.lookupPath(new_path, { parent: true });
        new_dir = lookup.node;
  
        if (!old_dir || !new_dir) throw new FS.ErrnoError(44);
        // need to be part of the same mount
        if (old_dir.mount !== new_dir.mount) {
          throw new FS.ErrnoError(75);
        }
        // source must exist
        var old_node = FS.lookupNode(old_dir, old_name);
        // old path should not be an ancestor of the new path
        var relative = PATH_FS.relative(old_path, new_dirname);
        if (relative.charAt(0) !== '.') {
          throw new FS.ErrnoError(28);
        }
        // new path should not be an ancestor of the old path
        relative = PATH_FS.relative(new_path, old_dirname);
        if (relative.charAt(0) !== '.') {
          throw new FS.ErrnoError(55);
        }
        // see if the new path already exists
        var new_node;
        try {
          new_node = FS.lookupNode(new_dir, new_name);
        } catch (e) {
          // not fatal
        }
        // early out if nothing needs to change
        if (old_node === new_node) {
          return;
        }
        // we'll need to delete the old entry
        var isdir = FS.isDir(old_node.mode);
        var errCode = FS.mayDelete(old_dir, old_name, isdir);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        // need delete permissions if we'll be overwriting.
        // need create permissions if new doesn't already exist.
        errCode = new_node ?
          FS.mayDelete(new_dir, new_name, isdir) :
          FS.mayCreate(new_dir, new_name);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        if (!old_dir.node_ops.rename) {
          throw new FS.ErrnoError(63);
        }
        if (FS.isMountpoint(old_node) || (new_node && FS.isMountpoint(new_node))) {
          throw new FS.ErrnoError(10);
        }
        // if we are going to change the parent, check write permissions
        if (new_dir !== old_dir) {
          errCode = FS.nodePermissions(old_dir, 'w');
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
        }
        // remove the node from the lookup hash
        FS.hashRemoveNode(old_node);
        // do the underlying fs rename
        try {
          old_dir.node_ops.rename(old_node, new_dir, new_name);
        } catch (e) {
          throw e;
        } finally {
          // add the node back to the hash (in case node_ops.rename
          // changed its name)
          FS.hashAddNode(old_node);
        }
      },rmdir:(path) => {
        var lookup = FS.lookupPath(path, { parent: true });
        var parent = lookup.node;
        var name = PATH.basename(path);
        var node = FS.lookupNode(parent, name);
        var errCode = FS.mayDelete(parent, name, true);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        if (!parent.node_ops.rmdir) {
          throw new FS.ErrnoError(63);
        }
        if (FS.isMountpoint(node)) {
          throw new FS.ErrnoError(10);
        }
        parent.node_ops.rmdir(parent, name);
        FS.destroyNode(node);
      },readdir:(path) => {
        var lookup = FS.lookupPath(path, { follow: true });
        var node = lookup.node;
        if (!node.node_ops.readdir) {
          throw new FS.ErrnoError(54);
        }
        return node.node_ops.readdir(node);
      },unlink:(path) => {
        var lookup = FS.lookupPath(path, { parent: true });
        var parent = lookup.node;
        if (!parent) {
          throw new FS.ErrnoError(44);
        }
        var name = PATH.basename(path);
        var node = FS.lookupNode(parent, name);
        var errCode = FS.mayDelete(parent, name, false);
        if (errCode) {
          // According to POSIX, we should map EISDIR to EPERM, but
          // we instead do what Linux does (and we must, as we use
          // the musl linux libc).
          throw new FS.ErrnoError(errCode);
        }
        if (!parent.node_ops.unlink) {
          throw new FS.ErrnoError(63);
        }
        if (FS.isMountpoint(node)) {
          throw new FS.ErrnoError(10);
        }
        parent.node_ops.unlink(parent, name);
        FS.destroyNode(node);
      },readlink:(path) => {
        var lookup = FS.lookupPath(path);
        var link = lookup.node;
        if (!link) {
          throw new FS.ErrnoError(44);
        }
        if (!link.node_ops.readlink) {
          throw new FS.ErrnoError(28);
        }
        return PATH_FS.resolve(FS.getPath(link.parent), link.node_ops.readlink(link));
      },stat:(path, dontFollow) => {
        var lookup = FS.lookupPath(path, { follow: !dontFollow });
        var node = lookup.node;
        if (!node) {
          throw new FS.ErrnoError(44);
        }
        if (!node.node_ops.getattr) {
          throw new FS.ErrnoError(63);
        }
        return node.node_ops.getattr(node);
      },lstat:(path) => {
        return FS.stat(path, true);
      },chmod:(path, mode, dontFollow) => {
        var node;
        if (typeof path == 'string') {
          var lookup = FS.lookupPath(path, { follow: !dontFollow });
          node = lookup.node;
        } else {
          node = path;
        }
        if (!node.node_ops.setattr) {
          throw new FS.ErrnoError(63);
        }
        node.node_ops.setattr(node, {
          mode: (mode & 4095) | (node.mode & ~4095),
          timestamp: Date.now()
        });
      },lchmod:(path, mode) => {
        FS.chmod(path, mode, true);
      },fchmod:(fd, mode) => {
        var stream = FS.getStreamChecked(fd);
        FS.chmod(stream.node, mode);
      },chown:(path, uid, gid, dontFollow) => {
        var node;
        if (typeof path == 'string') {
          var lookup = FS.lookupPath(path, { follow: !dontFollow });
          node = lookup.node;
        } else {
          node = path;
        }
        if (!node.node_ops.setattr) {
          throw new FS.ErrnoError(63);
        }
        node.node_ops.setattr(node, {
          timestamp: Date.now()
          // we ignore the uid / gid for now
        });
      },lchown:(path, uid, gid) => {
        FS.chown(path, uid, gid, true);
      },fchown:(fd, uid, gid) => {
        var stream = FS.getStreamChecked(fd);
        FS.chown(stream.node, uid, gid);
      },truncate:(path, len) => {
        if (len < 0) {
          throw new FS.ErrnoError(28);
        }
        var node;
        if (typeof path == 'string') {
          var lookup = FS.lookupPath(path, { follow: true });
          node = lookup.node;
        } else {
          node = path;
        }
        if (!node.node_ops.setattr) {
          throw new FS.ErrnoError(63);
        }
        if (FS.isDir(node.mode)) {
          throw new FS.ErrnoError(31);
        }
        if (!FS.isFile(node.mode)) {
          throw new FS.ErrnoError(28);
        }
        var errCode = FS.nodePermissions(node, 'w');
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        node.node_ops.setattr(node, {
          size: len,
          timestamp: Date.now()
        });
      },ftruncate:(fd, len) => {
        var stream = FS.getStreamChecked(fd);
        if ((stream.flags & 2097155) === 0) {
          throw new FS.ErrnoError(28);
        }
        FS.truncate(stream.node, len);
      },utime:(path, atime, mtime) => {
        var lookup = FS.lookupPath(path, { follow: true });
        var node = lookup.node;
        node.node_ops.setattr(node, {
          timestamp: Math.max(atime, mtime)
        });
      },open:(path, flags, mode) => {
        if (path === "") {
          throw new FS.ErrnoError(44);
        }
        flags = typeof flags == 'string' ? FS_modeStringToFlags(flags) : flags;
        mode = typeof mode == 'undefined' ? 438 /* 0666 */ : mode;
        if ((flags & 64)) {
          mode = (mode & 4095) | 32768;
        } else {
          mode = 0;
        }
        var node;
        if (typeof path == 'object') {
          node = path;
        } else {
          path = PATH.normalize(path);
          try {
            var lookup = FS.lookupPath(path, {
              follow: !(flags & 131072)
            });
            node = lookup.node;
          } catch (e) {
            // ignore
          }
        }
        // perhaps we need to create the node
        var created = false;
        if ((flags & 64)) {
          if (node) {
            // if O_CREAT and O_EXCL are set, error out if the node already exists
            if ((flags & 128)) {
              throw new FS.ErrnoError(20);
            }
          } else {
            // node doesn't exist, try to create it
            node = FS.mknod(path, mode, 0);
            created = true;
          }
        }
        if (!node) {
          throw new FS.ErrnoError(44);
        }
        // can't truncate a device
        if (FS.isChrdev(node.mode)) {
          flags &= ~512;
        }
        // if asked only for a directory, then this must be one
        if ((flags & 65536) && !FS.isDir(node.mode)) {
          throw new FS.ErrnoError(54);
        }
        // check permissions, if this is not a file we just created now (it is ok to
        // create and write to a file with read-only permissions; it is read-only
        // for later use)
        if (!created) {
          var errCode = FS.mayOpen(node, flags);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
        }
        // do truncation if necessary
        if ((flags & 512) && !created) {
          FS.truncate(node, 0);
        }
        // we've already handled these, don't pass down to the underlying vfs
        flags &= ~(128 | 512 | 131072);
  
        // register the stream with the filesystem
        var stream = FS.createStream({
          node,
          path: FS.getPath(node),  // we want the absolute path to the node
          flags,
          seekable: true,
          position: 0,
          stream_ops: node.stream_ops,
          // used by the file family libc calls (fopen, fwrite, ferror, etc.)
          ungotten: [],
          error: false
        });
        // call the new stream's open function
        if (stream.stream_ops.open) {
          stream.stream_ops.open(stream);
        }
        if (Module['logReadFiles'] && !(flags & 1)) {
          if (!FS.readFiles) FS.readFiles = {};
          if (!(path in FS.readFiles)) {
            FS.readFiles[path] = 1;
          }
        }
        return stream;
      },close:(stream) => {
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if (stream.getdents) stream.getdents = null; // free readdir state
        try {
          if (stream.stream_ops.close) {
            stream.stream_ops.close(stream);
          }
        } catch (e) {
          throw e;
        } finally {
          FS.closeStream(stream.fd);
        }
        stream.fd = null;
      },isClosed:(stream) => {
        return stream.fd === null;
      },llseek:(stream, offset, whence) => {
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if (!stream.seekable || !stream.stream_ops.llseek) {
          throw new FS.ErrnoError(70);
        }
        if (whence != 0 && whence != 1 && whence != 2) {
          throw new FS.ErrnoError(28);
        }
        stream.position = stream.stream_ops.llseek(stream, offset, whence);
        stream.ungotten = [];
        return stream.position;
      },read:(stream, buffer, offset, length, position) => {
        if (length < 0 || position < 0) {
          throw new FS.ErrnoError(28);
        }
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if ((stream.flags & 2097155) === 1) {
          throw new FS.ErrnoError(8);
        }
        if (FS.isDir(stream.node.mode)) {
          throw new FS.ErrnoError(31);
        }
        if (!stream.stream_ops.read) {
          throw new FS.ErrnoError(28);
        }
        var seeking = typeof position != 'undefined';
        if (!seeking) {
          position = stream.position;
        } else if (!stream.seekable) {
          throw new FS.ErrnoError(70);
        }
        var bytesRead = stream.stream_ops.read(stream, buffer, offset, length, position);
        if (!seeking) stream.position += bytesRead;
        return bytesRead;
      },write:(stream, buffer, offset, length, position, canOwn) => {
        if (length < 0 || position < 0) {
          throw new FS.ErrnoError(28);
        }
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if ((stream.flags & 2097155) === 0) {
          throw new FS.ErrnoError(8);
        }
        if (FS.isDir(stream.node.mode)) {
          throw new FS.ErrnoError(31);
        }
        if (!stream.stream_ops.write) {
          throw new FS.ErrnoError(28);
        }
        if (stream.seekable && stream.flags & 1024) {
          // seek to the end before writing in append mode
          FS.llseek(stream, 0, 2);
        }
        var seeking = typeof position != 'undefined';
        if (!seeking) {
          position = stream.position;
        } else if (!stream.seekable) {
          throw new FS.ErrnoError(70);
        }
        var bytesWritten = stream.stream_ops.write(stream, buffer, offset, length, position, canOwn);
        if (!seeking) stream.position += bytesWritten;
        return bytesWritten;
      },allocate:(stream, offset, length) => {
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if (offset < 0 || length <= 0) {
          throw new FS.ErrnoError(28);
        }
        if ((stream.flags & 2097155) === 0) {
          throw new FS.ErrnoError(8);
        }
        if (!FS.isFile(stream.node.mode) && !FS.isDir(stream.node.mode)) {
          throw new FS.ErrnoError(43);
        }
        if (!stream.stream_ops.allocate) {
          throw new FS.ErrnoError(138);
        }
        stream.stream_ops.allocate(stream, offset, length);
      },mmap:(stream, length, position, prot, flags) => {
        // User requests writing to file (prot & PROT_WRITE != 0).
        // Checking if we have permissions to write to the file unless
        // MAP_PRIVATE flag is set. According to POSIX spec it is possible
        // to write to file opened in read-only mode with MAP_PRIVATE flag,
        // as all modifications will be visible only in the memory of
        // the current process.
        if ((prot & 2) !== 0
            && (flags & 2) === 0
            && (stream.flags & 2097155) !== 2) {
          throw new FS.ErrnoError(2);
        }
        if ((stream.flags & 2097155) === 1) {
          throw new FS.ErrnoError(2);
        }
        if (!stream.stream_ops.mmap) {
          throw new FS.ErrnoError(43);
        }
        return stream.stream_ops.mmap(stream, length, position, prot, flags);
      },msync:(stream, buffer, offset, length, mmapFlags) => {
        if (!stream.stream_ops.msync) {
          return 0;
        }
        return stream.stream_ops.msync(stream, buffer, offset, length, mmapFlags);
      },munmap:(stream) => 0,ioctl:(stream, cmd, arg) => {
        if (!stream.stream_ops.ioctl) {
          throw new FS.ErrnoError(59);
        }
        return stream.stream_ops.ioctl(stream, cmd, arg);
      },readFile:(path, opts = {}) => {
        opts.flags = opts.flags || 0;
        opts.encoding = opts.encoding || 'binary';
        if (opts.encoding !== 'utf8' && opts.encoding !== 'binary') {
          throw new Error(`Invalid encoding type "${opts.encoding}"`);
        }
        var ret;
        var stream = FS.open(path, opts.flags);
        var stat = FS.stat(path);
        var length = stat.size;
        var buf = new Uint8Array(length);
        FS.read(stream, buf, 0, length, 0);
        if (opts.encoding === 'utf8') {
          ret = UTF8ArrayToString(buf, 0);
        } else if (opts.encoding === 'binary') {
          ret = buf;
        }
        FS.close(stream);
        return ret;
      },writeFile:(path, data, opts = {}) => {
        opts.flags = opts.flags || 577;
        var stream = FS.open(path, opts.flags, opts.mode);
        if (typeof data == 'string') {
          var buf = new Uint8Array(lengthBytesUTF8(data)+1);
          var actualNumBytes = stringToUTF8Array(data, buf, 0, buf.length);
          FS.write(stream, buf, 0, actualNumBytes, undefined, opts.canOwn);
        } else if (ArrayBuffer.isView(data)) {
          FS.write(stream, data, 0, data.byteLength, undefined, opts.canOwn);
        } else {
          throw new Error('Unsupported data type');
        }
        FS.close(stream);
      },cwd:() => FS.currentPath,chdir:(path) => {
        var lookup = FS.lookupPath(path, { follow: true });
        if (lookup.node === null) {
          throw new FS.ErrnoError(44);
        }
        if (!FS.isDir(lookup.node.mode)) {
          throw new FS.ErrnoError(54);
        }
        var errCode = FS.nodePermissions(lookup.node, 'x');
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        FS.currentPath = lookup.path;
      },createDefaultDirectories:() => {
        FS.mkdir('/tmp');
        FS.mkdir('/home');
        FS.mkdir('/home/<USER>');
      },createDefaultDevices:() => {
        // create /dev
        FS.mkdir('/dev');
        // setup /dev/null
        FS.registerDevice(FS.makedev(1, 3), {
          read: () => 0,
          write: (stream, buffer, offset, length, pos) => length,
        });
        FS.mkdev('/dev/null', FS.makedev(1, 3));
        // setup /dev/tty and /dev/tty1
        // stderr needs to print output using err() rather than out()
        // so we register a second tty just for it.
        TTY.register(FS.makedev(5, 0), TTY.default_tty_ops);
        TTY.register(FS.makedev(6, 0), TTY.default_tty1_ops);
        FS.mkdev('/dev/tty', FS.makedev(5, 0));
        FS.mkdev('/dev/tty1', FS.makedev(6, 0));
        // setup /dev/[u]random
        // use a buffer to avoid overhead of individual crypto calls per byte
        var randomBuffer = new Uint8Array(1024), randomLeft = 0;
        var randomByte = () => {
          if (randomLeft === 0) {
            randomLeft = randomFill(randomBuffer).byteLength;
          }
          return randomBuffer[--randomLeft];
        };
        FS.createDevice('/dev', 'random', randomByte);
        FS.createDevice('/dev', 'urandom', randomByte);
        // we're not going to emulate the actual shm device,
        // just create the tmp dirs that reside in it commonly
        FS.mkdir('/dev/shm');
        FS.mkdir('/dev/shm/tmp');
      },createSpecialDirectories:() => {
        // create /proc/self/fd which allows /proc/self/fd/6 => readlink gives the
        // name of the stream for fd 6 (see test_unistd_ttyname)
        FS.mkdir('/proc');
        var proc_self = FS.mkdir('/proc/self');
        FS.mkdir('/proc/self/fd');
        FS.mount({
          mount: () => {
            var node = FS.createNode(proc_self, 'fd', 16384 | 511 /* 0777 */, 73);
            node.node_ops = {
              lookup: (parent, name) => {
                var fd = +name;
                var stream = FS.getStreamChecked(fd);
                var ret = {
                  parent: null,
                  mount: { mountpoint: 'fake' },
                  node_ops: { readlink: () => stream.path },
                };
                ret.parent = ret; // make it look like a simple root node
                return ret;
              }
            };
            return node;
          }
        }, {}, '/proc/self/fd');
      },createStandardStreams:() => {
        // TODO deprecate the old functionality of a single
        // input / output callback and that utilizes FS.createDevice
        // and instead require a unique set of stream ops
  
        // by default, we symlink the standard streams to the
        // default tty devices. however, if the standard streams
        // have been overwritten we create a unique device for
        // them instead.
        if (Module['stdin']) {
          FS.createDevice('/dev', 'stdin', Module['stdin']);
        } else {
          FS.symlink('/dev/tty', '/dev/stdin');
        }
        if (Module['stdout']) {
          FS.createDevice('/dev', 'stdout', null, Module['stdout']);
        } else {
          FS.symlink('/dev/tty', '/dev/stdout');
        }
        if (Module['stderr']) {
          FS.createDevice('/dev', 'stderr', null, Module['stderr']);
        } else {
          FS.symlink('/dev/tty1', '/dev/stderr');
        }
  
        // open default streams for the stdin, stdout and stderr devices
        var stdin = FS.open('/dev/stdin', 0);
        var stdout = FS.open('/dev/stdout', 1);
        var stderr = FS.open('/dev/stderr', 1);
      },ensureErrnoError:() => {
        if (FS.ErrnoError) return;
        FS.ErrnoError = /** @this{Object} */ function ErrnoError(errno, node) {
          // We set the `name` property to be able to identify `FS.ErrnoError`
          // - the `name` is a standard ECMA-262 property of error objects. Kind of good to have it anyway.
          // - when using PROXYFS, an error can come from an underlying FS
          // as different FS objects have their own FS.ErrnoError each,
          // the test `err instanceof FS.ErrnoError` won't detect an error coming from another filesystem, causing bugs.
          // we'll use the reliable test `err.name == "ErrnoError"` instead
          this.name = 'ErrnoError';
          this.node = node;
          this.setErrno = /** @this{Object} */ function(errno) {
            this.errno = errno;
          };
          this.setErrno(errno);
          this.message = 'FS error';
  
        };
        FS.ErrnoError.prototype = new Error();
        FS.ErrnoError.prototype.constructor = FS.ErrnoError;
        // Some errors may happen quite a bit, to avoid overhead we reuse them (and suffer a lack of stack info)
        [44].forEach((code) => {
          FS.genericErrors[code] = new FS.ErrnoError(code);
          FS.genericErrors[code].stack = '<generic error, no stack>';
        });
      },staticInit:() => {
        FS.ensureErrnoError();
  
        FS.nameTable = new Array(4096);
  
        FS.mount(MEMFS, {}, '/');
  
        FS.createDefaultDirectories();
        FS.createDefaultDevices();
        FS.createSpecialDirectories();
  
        FS.filesystems = {
          'MEMFS': MEMFS,
          'IDBFS': IDBFS,
        };
      },init:(input, output, error) => {
        FS.init.initialized = true;
  
        FS.ensureErrnoError();
  
        // Allow Module.stdin etc. to provide defaults, if none explicitly passed to us here
        Module['stdin'] = input || Module['stdin'];
        Module['stdout'] = output || Module['stdout'];
        Module['stderr'] = error || Module['stderr'];
  
        FS.createStandardStreams();
      },quit:() => {
        FS.init.initialized = false;
        // force-flush all streams, so we get musl std streams printed out
        // close all of our streams
        for (var i = 0; i < FS.streams.length; i++) {
          var stream = FS.streams[i];
          if (!stream) {
            continue;
          }
          FS.close(stream);
        }
      },findObject:(path, dontResolveLastLink) => {
        var ret = FS.analyzePath(path, dontResolveLastLink);
        if (!ret.exists) {
          return null;
        }
        return ret.object;
      },analyzePath:(path, dontResolveLastLink) => {
        // operate from within the context of the symlink's target
        try {
          var lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
          path = lookup.path;
        } catch (e) {
        }
        var ret = {
          isRoot: false, exists: false, error: 0, name: null, path: null, object: null,
          parentExists: false, parentPath: null, parentObject: null
        };
        try {
          var lookup = FS.lookupPath(path, { parent: true });
          ret.parentExists = true;
          ret.parentPath = lookup.path;
          ret.parentObject = lookup.node;
          ret.name = PATH.basename(path);
          lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
          ret.exists = true;
          ret.path = lookup.path;
          ret.object = lookup.node;
          ret.name = lookup.node.name;
          ret.isRoot = lookup.path === '/';
        } catch (e) {
          ret.error = e.errno;
        };
        return ret;
      },createPath:(parent, path, canRead, canWrite) => {
        parent = typeof parent == 'string' ? parent : FS.getPath(parent);
        var parts = path.split('/').reverse();
        while (parts.length) {
          var part = parts.pop();
          if (!part) continue;
          var current = PATH.join2(parent, part);
          try {
            FS.mkdir(current);
          } catch (e) {
            // ignore EEXIST
          }
          parent = current;
        }
        return current;
      },createFile:(parent, name, properties, canRead, canWrite) => {
        var path = PATH.join2(typeof parent == 'string' ? parent : FS.getPath(parent), name);
        var mode = FS_getMode(canRead, canWrite);
        return FS.create(path, mode);
      },createDataFile:(parent, name, data, canRead, canWrite, canOwn) => {
        var path = name;
        if (parent) {
          parent = typeof parent == 'string' ? parent : FS.getPath(parent);
          path = name ? PATH.join2(parent, name) : parent;
        }
        var mode = FS_getMode(canRead, canWrite);
        var node = FS.create(path, mode);
        if (data) {
          if (typeof data == 'string') {
            var arr = new Array(data.length);
            for (var i = 0, len = data.length; i < len; ++i) arr[i] = data.charCodeAt(i);
            data = arr;
          }
          // make sure we can write to the file
          FS.chmod(node, mode | 146);
          var stream = FS.open(node, 577);
          FS.write(stream, data, 0, data.length, 0, canOwn);
          FS.close(stream);
          FS.chmod(node, mode);
        }
        return node;
      },createDevice:(parent, name, input, output) => {
        var path = PATH.join2(typeof parent == 'string' ? parent : FS.getPath(parent), name);
        var mode = FS_getMode(!!input, !!output);
        if (!FS.createDevice.major) FS.createDevice.major = 64;
        var dev = FS.makedev(FS.createDevice.major++, 0);
        // Create a fake device that a set of stream ops to emulate
        // the old behavior.
        FS.registerDevice(dev, {
          open: (stream) => {
            stream.seekable = false;
          },
          close: (stream) => {
            // flush any pending line data
            if (output && output.buffer && output.buffer.length) {
              output(10);
            }
          },
          read: (stream, buffer, offset, length, pos /* ignored */) => {
            var bytesRead = 0;
            for (var i = 0; i < length; i++) {
              var result;
              try {
                result = input();
              } catch (e) {
                throw new FS.ErrnoError(29);
              }
              if (result === undefined && bytesRead === 0) {
                throw new FS.ErrnoError(6);
              }
              if (result === null || result === undefined) break;
              bytesRead++;
              buffer[offset+i] = result;
            }
            if (bytesRead) {
              stream.node.timestamp = Date.now();
            }
            return bytesRead;
          },
          write: (stream, buffer, offset, length, pos) => {
            for (var i = 0; i < length; i++) {
              try {
                output(buffer[offset+i]);
              } catch (e) {
                throw new FS.ErrnoError(29);
              }
            }
            if (length) {
              stream.node.timestamp = Date.now();
            }
            return i;
          }
        });
        return FS.mkdev(path, mode, dev);
      },forceLoadFile:(obj) => {
        if (obj.isDevice || obj.isFolder || obj.link || obj.contents) return true;
        if (typeof XMLHttpRequest != 'undefined') {
          throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");
        } else if (read_) {
          // Command-line.
          try {
            // WARNING: Can't read binary files in V8's d8 or tracemonkey's js, as
            //          read() will try to parse UTF8.
            obj.contents = intArrayFromString(read_(obj.url), true);
            obj.usedBytes = obj.contents.length;
          } catch (e) {
            throw new FS.ErrnoError(29);
          }
        } else {
          throw new Error('Cannot load without read() or XMLHttpRequest.');
        }
      },createLazyFile:(parent, name, url, canRead, canWrite) => {
        // Lazy chunked Uint8Array (implements get and length from Uint8Array). Actual getting is abstracted away for eventual reuse.
        /** @constructor */
        function LazyUint8Array() {
          this.lengthKnown = false;
          this.chunks = []; // Loaded chunks. Index is the chunk number
        }
        LazyUint8Array.prototype.get = /** @this{Object} */ function LazyUint8Array_get(idx) {
          if (idx > this.length-1 || idx < 0) {
            return undefined;
          }
          var chunkOffset = idx % this.chunkSize;
          var chunkNum = (idx / this.chunkSize)|0;
          return this.getter(chunkNum)[chunkOffset];
        };
        LazyUint8Array.prototype.setDataGetter = function LazyUint8Array_setDataGetter(getter) {
          this.getter = getter;
        };
        LazyUint8Array.prototype.cacheLength = function LazyUint8Array_cacheLength() {
          // Find length
          var xhr = new XMLHttpRequest();
          xhr.open('HEAD', url, false);
          xhr.send(null);
          if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status);
          var datalength = Number(xhr.getResponseHeader("Content-length"));
          var header;
          var hasByteServing = (header = xhr.getResponseHeader("Accept-Ranges")) && header === "bytes";
          var usesGzip = (header = xhr.getResponseHeader("Content-Encoding")) && header === "gzip";
  
          var chunkSize = 1024*1024; // Chunk size in bytes
  
          if (!hasByteServing) chunkSize = datalength;
  
          // Function to get a range from the remote URL.
          var doXHR = (from, to) => {
            if (from > to) throw new Error("invalid range (" + from + ", " + to + ") or no bytes requested!");
            if (to > datalength-1) throw new Error("only " + datalength + " bytes available! programmer error!");
  
            // TODO: Use mozResponseArrayBuffer, responseStream, etc. if available.
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, false);
            if (datalength !== chunkSize) xhr.setRequestHeader("Range", "bytes=" + from + "-" + to);
  
            // Some hints to the browser that we want binary data.
            xhr.responseType = 'arraybuffer';
            if (xhr.overrideMimeType) {
              xhr.overrideMimeType('text/plain; charset=x-user-defined');
            }
  
            xhr.send(null);
            if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status);
            if (xhr.response !== undefined) {
              return new Uint8Array(/** @type{Array<number>} */(xhr.response || []));
            }
            return intArrayFromString(xhr.responseText || '', true);
          };
          var lazyArray = this;
          lazyArray.setDataGetter((chunkNum) => {
            var start = chunkNum * chunkSize;
            var end = (chunkNum+1) * chunkSize - 1; // including this byte
            end = Math.min(end, datalength-1); // if datalength-1 is selected, this is the last block
            if (typeof lazyArray.chunks[chunkNum] == 'undefined') {
              lazyArray.chunks[chunkNum] = doXHR(start, end);
            }
            if (typeof lazyArray.chunks[chunkNum] == 'undefined') throw new Error('doXHR failed!');
            return lazyArray.chunks[chunkNum];
          });
  
          if (usesGzip || !datalength) {
            // if the server uses gzip or doesn't supply the length, we have to download the whole file to get the (uncompressed) length
            chunkSize = datalength = 1; // this will force getter(0)/doXHR do download the whole file
            datalength = this.getter(0).length;
            chunkSize = datalength;
            out("LazyFiles on gzip forces download of the whole file when length is accessed");
          }
  
          this._length = datalength;
          this._chunkSize = chunkSize;
          this.lengthKnown = true;
        };
        if (typeof XMLHttpRequest != 'undefined') {
          if (!ENVIRONMENT_IS_WORKER) throw 'Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc';
          var lazyArray = new LazyUint8Array();
          Object.defineProperties(lazyArray, {
            length: {
              get: /** @this{Object} */ function() {
                if (!this.lengthKnown) {
                  this.cacheLength();
                }
                return this._length;
              }
            },
            chunkSize: {
              get: /** @this{Object} */ function() {
                if (!this.lengthKnown) {
                  this.cacheLength();
                }
                return this._chunkSize;
              }
            }
          });
  
          var properties = { isDevice: false, contents: lazyArray };
        } else {
          var properties = { isDevice: false, url: url };
        }
  
        var node = FS.createFile(parent, name, properties, canRead, canWrite);
        // This is a total hack, but I want to get this lazy file code out of the
        // core of MEMFS. If we want to keep this lazy file concept I feel it should
        // be its own thin LAZYFS proxying calls to MEMFS.
        if (properties.contents) {
          node.contents = properties.contents;
        } else if (properties.url) {
          node.contents = null;
          node.url = properties.url;
        }
        // Add a function that defers querying the file size until it is asked the first time.
        Object.defineProperties(node, {
          usedBytes: {
            get: /** @this {FSNode} */ function() { return this.contents.length; }
          }
        });
        // override each stream op with one that tries to force load the lazy file first
        var stream_ops = {};
        var keys = Object.keys(node.stream_ops);
        keys.forEach((key) => {
          var fn = node.stream_ops[key];
          stream_ops[key] = function forceLoadLazyFile() {
            FS.forceLoadFile(node);
            return fn.apply(null, arguments);
          };
        });
        function writeChunks(stream, buffer, offset, length, position) {
          var contents = stream.node.contents;
          if (position >= contents.length)
            return 0;
          var size = Math.min(contents.length - position, length);
          if (contents.slice) { // normal array
            for (var i = 0; i < size; i++) {
              buffer[offset + i] = contents[position + i];
            }
          } else {
            for (var i = 0; i < size; i++) { // LazyUint8Array from sync binary XHR
              buffer[offset + i] = contents.get(position + i);
            }
          }
          return size;
        }
        // use a custom read function
        stream_ops.read = (stream, buffer, offset, length, position) => {
          FS.forceLoadFile(node);
          return writeChunks(stream, buffer, offset, length, position)
        };
        // use a custom mmap function
        stream_ops.mmap = (stream, length, position, prot, flags) => {
          FS.forceLoadFile(node);
          var ptr = mmapAlloc(length);
          if (!ptr) {
            throw new FS.ErrnoError(48);
          }
          writeChunks(stream, HEAP8, ptr, length, position);
          return { ptr, allocated: true };
        };
        node.stream_ops = stream_ops;
        return node;
      }};




  var FSNode = /** @constructor */ function(parent, name, mode, rdev) {
    if (!parent) {
      parent = this;  // root node sets parent to itself
    }
    this.parent = parent;
    this.mount = parent.mount;
    this.mounted = null;
    this.id = FS.nextInode++;
    this.name = name;
    this.mode = mode;
    this.node_ops = {};
    this.stream_ops = {};
    this.rdev = rdev;
  };
  var readMode = 292/*292*/ | 73/*73*/;
  var writeMode = 146/*146*/;
  Object.defineProperties(FSNode.prototype, {
   read: {
    get: /** @this{FSNode} */function() {
     return (this.mode & readMode) === readMode;
    },
    set: /** @this{FSNode} */function(val) {
     val ? this.mode |= readMode : this.mode &= ~readMode;
    }
   },
   write: {
    get: /** @this{FSNode} */function() {
     return (this.mode & writeMode) === writeMode;
    },
    set: /** @this{FSNode} */function(val) {
     val ? this.mode |= writeMode : this.mode &= ~writeMode;
    }
   },
   isFolder: {
    get: /** @this{FSNode} */function() {
     return FS.isDir(this.mode);
    }
   },
   isDevice: {
    get: /** @this{FSNode} */function() {
     return FS.isChrdev(this.mode);
    }
   }
  });
  FS.FSNode = FSNode;
  FS.createPreloadedFile = FS_createPreloadedFile;
  FS.staticInit();Module["FS_createPath"] = FS.createPath;Module["FS_createDataFile"] = FS.createDataFile;Module["FS_createPreloadedFile"] = FS.createPreloadedFile;Module["FS_unlink"] = FS.unlink;Module["FS_createLazyFile"] = FS.createLazyFile;Module["FS_createDevice"] = FS.createDevice;;
var wasmImports = {
  "emscripten_resize_heap": _emscripten_resize_heap,
  "memory": wasmMemory
};
var asm = createWasm();
/** @type {function(...*):?} */
var ___wasm_call_ctors = function() {
  return (___wasm_call_ctors = Module["asm"]["__wasm_call_ctors"]).apply(null, arguments);
};

/** @type {function(...*):?} */
var ___errno_location = function() {
  return (___errno_location = Module["asm"]["__errno_location"]).apply(null, arguments);
};

/** @type {function(...*):?} */
var _malloc = function() {
  return (_malloc = Module["asm"]["malloc"]).apply(null, arguments);
};

/** @type {function(...*):?} */
var _free = function() {
  return (_free = Module["asm"]["free"]).apply(null, arguments);
};

/** @type {function(...*):?} */
var stackSave = function() {
  return (stackSave = Module["asm"]["stackSave"]).apply(null, arguments);
};

/** @type {function(...*):?} */
var stackRestore = function() {
  return (stackRestore = Module["asm"]["stackRestore"]).apply(null, arguments);
};

/** @type {function(...*):?} */
var stackAlloc = function() {
  return (stackAlloc = Module["asm"]["stackAlloc"]).apply(null, arguments);
};



// include: postamble.js
// === Auto-generated postamble setup entry stuff ===

// include: base64Utils.js
// Converts a string of base64 into a byte array.
// Throws error on invalid input.
function intArrayFromBase64(s) {
  if (typeof ENVIRONMENT_IS_NODE != 'undefined' && ENVIRONMENT_IS_NODE) {
    var buf = Buffer.from(s, 'base64');
    return new Uint8Array(buf['buffer'], buf['byteOffset'], buf['byteLength']);
  }

  try {
    var decoded = atob(s);
    var bytes = new Uint8Array(decoded.length);
    for (var i = 0 ; i < decoded.length ; ++i) {
      bytes[i] = decoded.charCodeAt(i);
    }
    return bytes;
  } catch (_) {
    throw new Error('Converting base64 string to bytes failed.');
  }
}

// If filename is a base64 data URI, parses and returns data (Buffer on node,
// Uint8Array otherwise). If filename is not a base64 data URI, returns undefined.
function tryParseAsDataURI(filename) {
  if (!isDataURI(filename)) {
    return;
  }

  return intArrayFromBase64(filename.slice(dataURIPrefix.length));
}
// end include: base64Utils.js
Module["addRunDependency"] = addRunDependency;
Module["removeRunDependency"] = removeRunDependency;
Module["FS_createPath"] = FS.createPath;
Module["FS_createDataFile"] = FS.createDataFile;
Module["FS_createLazyFile"] = FS.createLazyFile;
Module["FS_createDevice"] = FS.createDevice;
Module["FS_unlink"] = FS.unlink;
Module["FS_createPreloadedFile"] = FS.createPreloadedFile;
Module["FS"] = FS;
Module["IDBFS"] = IDBFS;


var calledRun;

dependenciesFulfilled = function runCaller() {
  // If run has never been called, and we should call run (INVOKE_RUN is true, and Module.noInitialRun is not false)
  if (!calledRun) run();
  if (!calledRun) dependenciesFulfilled = runCaller; // try this again later, after new deps are fulfilled
};

function run() {

  if (runDependencies > 0) {
    return;
  }

  preRun();

  // a preRun added a dependency, run will be called later
  if (runDependencies > 0) {
    return;
  }

  function doRun() {
    // run may have just been called through dependencies being fulfilled just in this very frame,
    // or while the async setStatus time below was happening
    if (calledRun) return;
    calledRun = true;
    Module['calledRun'] = true;

    if (ABORT) return;

    initRuntime();

    readyPromiseResolve(Module);
    if (Module['onRuntimeInitialized']) Module['onRuntimeInitialized']();

    postRun();
  }

  if (Module['setStatus']) {
    Module['setStatus']('Running...');
    setTimeout(function() {
      setTimeout(function() {
        Module['setStatus']('');
      }, 1);
      doRun();
    }, 1);
  } else
  {
    doRun();
  }
}

if (Module['preInit']) {
  if (typeof Module['preInit'] == 'function') Module['preInit'] = [Module['preInit']];
  while (Module['preInit'].length > 0) {
    Module['preInit'].pop()();
  }
}

run();


// end include: postamble.js


  return moduleArg.ready
}

);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = GCADM;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return GCADM; });
else if (typeof exports === 'object')
  exports["GCADM"] = GCADM;
