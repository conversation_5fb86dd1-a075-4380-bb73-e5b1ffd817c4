<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="属性定义-插入字段 " id="_属性定义-插入字段_" viewBox="0 0 16 16">
  <defs>
    <style>
      .TPbg {
        fill: none;
      }

      .<PERSON> {
        fill: #d5d5d5;
      }

      .Blue {
        fill: #1aa0ff;
      }
    </style>
  </defs>
  <g id="Blue">
    <path class="Blue" d="M13.5,3H6.41l.4,1h6.19v2H7.62l.4,1h5.48c.28,0,.5-.22,.5-.5V3.5c0-.28-.22-.5-.5-.5Z"/>
    <path class="Blue" d="M4.06,2l-1.96,5h.99l.42-1.14h1.95l.44,1.14h1.05L4.92,2h-.87Zm-.24,3.01l.65-1.76,.67,1.76h-1.32Z"/>
  </g>
  <path class="Gray" d="M13.5,10H2.5c-.28,0-.5,.22-.5,.5v3c0,.28,.22,.5,.5,.5H13.5c.28,0,.5-.22,.5-.5v-3c0-.28-.22-.5-.5-.5Zm-.5,3H3v-2H13v2Z" id="Gray"/>
  <rect class="TPbg" height="16" id="TPbg" width="16"/>
</svg>
