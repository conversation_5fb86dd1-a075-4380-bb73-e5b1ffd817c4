<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="属性定义-选择对象 " id="_属性定义-选择对象_" viewBox="0 0 16 16">
  <defs>
    <style>
      .TPbg {
        fill: none;
      }

      .Blue {
        fill: #1aa0ff;
      }

      .<PERSON> {
        fill: #d5d5d5;
        fill-rule: evenodd;
      }
    </style>
  </defs>
  <path class="Gray" d="M9.76,12.64c-.1,.1-.27,.1-.37,0-.03-.03-.05-.06-.06-.1l-1.38-4.13c-.05-.14,.03-.29,.17-.33,.05-.02,.11-.02,.17,0l4.13,1.38c.14,.05,.21,.2,.17,.33-.01,.04-.03,.07-.06,.1l-.82,.82,2.24,2.24-1.12,1.12-2.24-2.24-.82,.82Z" id="Gray"/>
  <rect class="TPbg" height="16" id="TPbg" width="16" y="0"/>
  <path class="Blue" d="M7.06,7.76c.15-.3,.41-.53,.73-.63l.15-.05h.24c.14-.01,.28,0,.41,.05l1.38,.46s.02-.06,.02-.09V2.5c0-.28-.22-.5-.5-.5H2.5c-.28,0-.5,.22-.5,.5V7.5c0,.28,.22,.5,.5,.5H7c.02-.08,.03-.16,.07-.24ZM3,3h6V7H3V3Z" id="Blue"/>
</svg>
