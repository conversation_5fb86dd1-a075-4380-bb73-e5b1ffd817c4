<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="块定义-选择对象 " id="_块定义-选择对象_" viewBox="0 0 16 16">
  <defs>
    <style>
      .TPbg {
        fill: none;
      }

      .<PERSON> {
        fill: #d5d5d5;
      }

      .<PERSON> {
        fill: #1aa0ff;
      }
    </style>
  </defs>
  <rect class="TPbg" height="16" id="TPbg" width="16" y="0"/>
  <g id="Gray">
    <path class="Gray" d="M12.69,10.71l.82-.82s.05-.06,.06-.1c.05-.14-.03-.29-.17-.33l-4.13-1.38c-.05-.02-.11-.02-.17,0-.14,.05-.21,.2-.17,.33l1.38,4.13s.03,.07,.06,.1c.1,.1,.27,.1,.37,0l.82-.82,2.24,2.24,1.12-1.12-2.24-2.24Z"/>
    <path class="Gray" d="M5,10c-1.1,0-2-.9-2-2v-2.22c-.61,.55-1,1.34-1,2.22,0,1.66,1.34,3,3,3,1.3,0,2.4-.84,2.82-2h-1.09c-.35,.59-.98,1-1.72,1Z"/>
  </g>
  <path class="Blue" d="M8.06,7.77c.15-.3,.41-.53,.73-.64l.15-.05h.25c.14-.01,.28,0,.41,.05l2.22,.74c.1-.09,.17-.22,.17-.37V2.5c0-.28-.22-.5-.5-.5H4.5c-.28,0-.5,.22-.5,.5V7.5c0,.28,.22,.5,.5,.5h3.5c.02-.08,.03-.16,.06-.23ZM5,3h6V7H5V3Z" id="Blue"/>
</svg>
