<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" height="16" id="阵列-选择对象" viewBox="0 0 16 16" width="16">
  <defs>
    <style>.<PERSON>{fill:#d5d5d5;}.<PERSON>{fill:#1aa0ff;}.TPbg{fill:none;}</style>
  </defs>
  <title>阵列-选择对象</title>
  <path class="Gray" d="M9.82,12.57a.27.27,0,0,1-.37,0,.21.21,0,0,1-.06-.1L8,8.34A.25.25,0,0,1,8.18,8a.25.25,0,0,1,.17,0l4.13,1.38a.27.27,0,0,1,.17.33.21.21,0,0,1-.06.1l-.82.82L14,12.88,12.89,14l-2.24-2.24-.82.82Z" id="Gray"/>
  <path class="Blue" d="M7.06,7.76a1.23,1.23,0,0,1,.73-.63l.15-.05h.24a1,1,0,0,1,.41.05L10,7.59a.32.32,0,0,0,0-.09v-5a.5.5,0,0,0-.5-.5h-7a.5.5,0,0,0-.5.5v5a.5.5,0,0,0,.5.5H7a1.09,1.09,0,0,1,.07-.24ZM3,3H9V7H3Z" id="Blue"/>
  <rect class="TPbg" height="16" id="TPbg" width="16"/>
</svg>
