<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 16 16" width="16">
  <defs>
    <style>.TPbg{fill:none;}.Gray{fill:#d5d5d5;}.Yellow{fill:#f7990c;}</style>
  </defs>
  <title>阵列-提示</title>
  <g id="阵列-提示">
    <rect class="TPbg" height="16" id="TPbg" width="16"/>
    <path class="Gray" d="M8,2A6,6,0,1,1,2,8,6,6,0,0,1,8,2M8,1a7,7,0,1,0,7,7A7,7,0,0,0,8,1Z" id="Gray"/>
    <g id="Yellow">
      <circle class="Yellow" cx="8" cy="11" r="1"/>
      <rect class="Yellow" height="5" rx="0.5" ry="0.5" width="2" x="7" y="4"/>
    </g>
  </g>
</svg>
