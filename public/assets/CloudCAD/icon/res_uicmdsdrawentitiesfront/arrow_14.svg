<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 16 16">
  <defs>
    <style>.TPbg{fill:none;}.<PERSON>{fill:#d5d5d5;}</style>
  </defs>
  <g id="符号和箭头-空心小点-16">
    <rect class="TPbg" height="16" id="TPbg" width="16"/>
    <path class="Gray" d="M8,4a4,4,0,1,0,4,4A4,4,0,0,0,8,4ZM8,5A3,3,0,1,1,5,8,3,3,0,0,1,8,5Z" id="Gray"/>
  </g>
</svg>
