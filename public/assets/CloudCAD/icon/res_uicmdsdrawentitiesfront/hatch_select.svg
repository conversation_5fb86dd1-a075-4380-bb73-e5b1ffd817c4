<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.<PERSON>{fill:#d5d5d5;}.Blue{fill:#1aa0ff;}.TPbg{fill:none;}</style>
  </defs>
  <g data-name="添加（选择对象）" id="添加_选择对象_">
    <g id="Gray">
      <path class="Gray" d="M11.5,4.09a1.43,1.43,0,0,1,1,0V2.5A.5.5,0,0,0,12,2H2V3h9.5Z"/>
      <path class="Gray" d="M12,12a1.64,1.64,0,0,1-.5-.09V13H2v1H12a.5.5,0,0,0,.5-.5V11.91A1.64,1.64,0,0,1,12,12Z"/>
      <path class="Gray" d="M10.5,6.5V6.24L8.19,3.94a.49.49,0,0,0-.7.7L9.36,6.51H10.5Z"/>
      <path class="Gray" d="M8.57,6.83,5.67,3.94a.5.5,0,0,0-.71,0,.5.5,0,0,0,0,.7L8,7.71A1.53,1.53,0,0,1,8.57,6.83Z"/>
      <path class="Gray" d="M9.82,9.5l0,0a.49.49,0,0,0,.35.15.48.48,0,0,0,.29-.11V9.5Z"/>
      <path class="Gray" d="M3.14,3.93a.5.5,0,0,0-.7,0,.5.5,0,0,0,0,.71l7.42,7.43a.52.52,0,0,0,.71,0,.51.51,0,0,0,0-.71Z"/>
      <path class="Gray" d="M3.15,9a.51.51,0,0,0-.71,0,.5.5,0,0,0,0,.71l2.37,2.37a.51.51,0,0,0,.36.15.5.5,0,0,0,.35-.85Z"/>
      <path class="Gray" d="M3.14,6.46a.48.48,0,0,0-.7,0,.5.5,0,0,0,0,.71l4.9,4.9a.52.52,0,0,0,.35.14A.5.5,0,0,0,8,11.36Z"/>
    </g>
    <g id="Blue">
      <path class="Blue" d="M14.5,7.5h-2v-2a.5.5,0,0,0-1,0v2h-2a.5.5,0,0,0,0,1h2v2a.5.5,0,0,0,1,0v-2h2a.5.5,0,0,0,0-1Z"/>
      <path class="Blue" d="M12,2H2V3H12a.5.5,0,0,0,0-1Z"/>
    </g>
    <rect class="TPbg" height="16" id="TPbg" width="16"/>
  </g>
</svg>
