<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 16 16">
  <defs>
    <style>.TPbg{fill:none;}.<PERSON>{fill:#d5d5d5;}</style>
  </defs>
  <!--形状图层未在定义的集合中-->
  <g id="符号和箭头-空心闭合-16">
    <rect class="TPbg" height="16" id="TPbg" width="16"/>
    <path class="Gray" d="M6.6,1,15,8,6.6,15V9H1V7H6.6ZM7.8,3.5v9L13.2,8Z" id="Gray"/>
  </g>
</svg>
