<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 32 32">
  <defs>
    <style>.TPbg{fill:none;}.<PERSON>{fill:#d5d5d5;}</style>
  </defs>
  <g id="特征符号-13-32">
    <rect class="TPbg" height="32" id="TPbg" width="32"/>
    <path class="Gray" d="M19.61,20.88l4.52-13-13,4.52L15,16.28l-7.49,7.5.7.7L15.72,17Zm-.34-1.74L12.9,12.78l10-3.64Z" id="Gray"/>
  </g>
</svg>
