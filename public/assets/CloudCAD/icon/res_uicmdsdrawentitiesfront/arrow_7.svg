<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 16 16">
  <defs>
    <style>.TPbg{fill:none;}.Gray{fill:#d5d5d5;}</style>
  </defs>
  <g id="符号和箭头-打开-16">
    <rect class="TPbg" height="16" id="TPbg" width="16"/>
    <polygon class="Gray" id="Gray" points="15 4 15 12 1 12 1 11 14 11 14 8.5 1 8.5 1 7.5 14 7.5 14 5 1 5 1 4 15 4"/>
  </g>
</svg>
