<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" data-name="图层 1" id="图层_1" viewBox="0 0 64 64">
  <defs>
    <style>.TPbg{fill:none;}.Blue{fill:url(#未命名的渐变);}.Gray{fill:#d5d5d5;}</style>
    <linearGradient gradientTransform="matrix(64, 0, 0, -64, 40950, 26337)" gradientUnits="userSpaceOnUse" id="未命名的渐变" x1="-639.34" x2="-639.34" y1="410.52" y2="411.52">
      <stop offset="0" stop-color="#1aa0ff" stop-opacity="0.2"/>
      <stop offset="1" stop-color="#1aa0ff"/>
    </linearGradient>
  </defs>
  <g id="弧岛样式-渐变忽略-64">
    <rect class="TPbg" height="64" id="TPbg" width="64"/>
    <rect class="Blue" height="64" id="Blue" width="64"/>
    <path class="Gray" d="M18.92,9H45.08L58.15,31.65,45.08,54.3H18.92L5.85,31.65Zm25,2H20.08L8.15,31.65,20.08,52.3H43.92L55.84,31.65ZM32,16A16,16,0,1,1,16,32,16,16,0,0,1,32,16Zm0,2A14,14,0,1,0,46,32,14,14,0,0,0,32,18Z" id="Gray"/>
  </g>
</svg>
