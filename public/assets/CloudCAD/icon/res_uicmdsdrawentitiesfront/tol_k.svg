<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 32 32">
  <defs>
    <style>.TPbg{fill:none;}.Gray{fill:#d5d5d5;fill-rule:evenodd;}</style>
  </defs>
  <g id="特征符号-12-32">
    <rect class="TPbg" height="32" id="TPbg" width="32"/>
    <path class="Gray" d="M16,11a10,10,0,0,1,10,9.72V21H25A9,9,0,0,0,7,21H6A10,10,0,0,1,16,11Z" id="Gray"/>
  </g>
</svg>
