<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 16 16">
  <defs>
    <style>.TPbg{fill:none;}.Blue{fill:#1aa0ff;fill-rule:evenodd;}.Gray{fill:#d5d5d5;}.Red{fill:#e04d4d;}</style>
  </defs>
  <g id="弧形文字-方向朝上">
    <rect class="TPbg" height="16" id="TPbg" width="16"/>
    <path class="Blue" d="M8,2a11.1,11.1,0,0,1,7,2.53V5.86A10.2,10.2,0,0,0,8,3,10.19,10.19,0,0,0,1,5.86V4.53A11.1,11.1,0,0,1,8,2Z" id="Blue"/>
    <path class="Gray" d="M7.9,9.86h.18a.45.45,0,0,0,.37-.45V6.53h.9a.46.46,0,0,0,.37-.72l-.55-.64-.81-1a.43.43,0,0,0-.69,0L6.31,5.76a.46.46,0,0,0,.32.77h.91V9.41A.49.49,0,0,0,7.9,9.86Z" id="Gray"/>
    <circle class="Red" cx="8" cy="12" id="Red" r="1"/>
  </g>
</svg>
