<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
  <defs>
    <style>.<PERSON>{fill:#d5d5d5;}.<PERSON>{fill:#1aa0ff;}.<PERSON>{fill:#18ae55;}.TPbg{fill:none;}</style>
  </defs>
  <!--路径,形状图层未在定义的集合中-->
  <g id="查看选择集">
    <g id="Gray">
      <path class="Gray" d="M11.36,7a.5.5,0,1,0,.7-.71L9.69,3.94a.5.5,0,0,0-.71,0,.5.5,0,0,0,0,.7Z"/>
      <path class="Gray" d="M11.53,8.3,7.17,3.94a.5.5,0,0,0-.71,0,.48.48,0,0,0,0,.7L10.3,8.49a3.86,3.86,0,0,1,1.2-.19Z"/>
      <path class="Gray" d="M4.64,3.93a.5.5,0,0,0-.71.71L8.66,9.36a6.22,6.22,0,0,1,.86-.55Z"/>
      <path class="Gray" d="M7.05,11.41l0-.05L4.64,9a.5.5,0,0,0-.7,0,.5.5,0,0,0,0,.71l2.37,2.37a.5.5,0,0,0,.35.15A.51.51,0,0,0,7,12.06l0-.05A2,2,0,0,1,7.05,11.41Z"/>
      <path class="Gray" d="M8.06,9.88,4.64,6.46a.5.5,0,0,0-.71,0,.51.51,0,0,0,0,.71l3.45,3.44a.53.53,0,0,0,.06-.1C7.65,10.28,7.85,10.07,8.06,9.88Z"/>
    </g>
    <path class="Blue" d="M7.55,13H3V3H13V8.59a5.08,5.08,0,0,1,1,.53V2.5a.5.5,0,0,0-.5-.5H2.5a.5.5,0,0,0-.5.5v11a.5.5,0,0,0,.5.5H8.62A7.1,7.1,0,0,1,7.55,13Z" id="Blue"/>
    <g id="Green">
      <path class="Green" d="M14.8,11.17A4.52,4.52,0,0,0,11.5,9.3a4.52,4.52,0,0,0-3.3,1.87.79.79,0,0,0,0,1A4.27,4.27,0,0,0,11.5,14a4.27,4.27,0,0,0,3.31-1.79A.79.79,0,0,0,14.8,11.17Zm-.82.7a3.24,3.24,0,0,1-2.48,1.36A3.24,3.24,0,0,1,9,11.87a.29.29,0,0,1,0-.37,3.43,3.43,0,0,1,2.47-1.43A3.43,3.43,0,0,1,14,11.5.29.29,0,0,1,14,11.87Z" id="形状"/>
      <path class="Green" d="M11.5,10.4a1.25,1.25,0,1,0,1.25,1.25A1.25,1.25,0,0,0,11.5,10.4Z" id="路径"/>
    </g>
    <rect class="TPbg" height="16" id="TPbg" width="16"/>
  </g>
</svg>
