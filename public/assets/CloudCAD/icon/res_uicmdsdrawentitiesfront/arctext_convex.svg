<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 16 16">
  <defs>
    <style>.TPbg{fill:none;}.Blue{fill:#1aa0ff;fill-rule:evenodd;}.Gray{fill:#d5d5d5;}</style>
  </defs>
  <g id="弧形文字-文字居上">
    <rect class="TPbg" height="16" id="TPbg" width="16"/>
    <path class="Blue" d="M8,11a11.1,11.1,0,0,1,7,2.53v1.33A10.2,10.2,0,0,0,8,12a10.19,10.19,0,0,0-7,2.86V13.53A11.1,11.1,0,0,1,8,11Z" id="Blue"/>
    <path class="Gray" d="M8.66,2,12,10H10.26L9.54,8.17H6.33L5.63,10H4L7.23,2ZM9,6.82,7.91,4,6.84,6.82Z" id="Gray"/>
  </g>
</svg>
