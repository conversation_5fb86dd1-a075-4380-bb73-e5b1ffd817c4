<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 32 32">
  <defs>
    <style>.TPbg{fill:none;}.Gray{fill:#d5d5d5;fill-rule:evenodd;}</style>
  </defs>
  <g id="源块-方框-32">
    <rect class="TPbg" height="32" id="TPbg" width="32"/>
    <path class="Gray" d="M28,3a1,1,0,0,1,1,1V28a1,1,0,0,1-1,1H4a1,1,0,0,1-1-1V4A1,1,0,0,1,4,3Zm0,1H4V28H28Z" id="Gray"/>
  </g>
</svg>
