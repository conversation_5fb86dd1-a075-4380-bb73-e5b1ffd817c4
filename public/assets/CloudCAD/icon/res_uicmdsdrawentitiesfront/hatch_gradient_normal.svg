<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" data-name="图层 1" id="图层_1" viewBox="0 0 64 64">
  <defs>
    <style>.TPbg{fill:none;}.Blue{fill:url(#未命名的渐变);}.Gray{fill:#d5d5d5;fill-rule:evenodd;}</style>
    <linearGradient gradientTransform="matrix(64, 0, 0, -64, 40950, 26337)" gradientUnits="userSpaceOnUse" id="未命名的渐变" x1="-639.64" x2="-639.14" y1="410.42" y2="411.42">
      <stop offset="0" stop-color="#1aa0ff" stop-opacity="0.2"/>
      <stop offset="1" stop-color="#1aa0ff"/>
    </linearGradient>
  </defs>
  <g id="弧岛样式-渐变普通-64">
    <rect class="TPbg" height="64" id="TPbg" width="64"/>
    <rect class="Blue" height="64" id="Blue" width="64"/>
    <path class="Gray" d="M44.5,10,57,31.65,44.5,53.3h-25L7,31.65,19.5,10ZM32,17A15,15,0,1,0,47,32,15,15,0,0,0,32,17Z" id="Gray"/>
  </g>
</svg>
