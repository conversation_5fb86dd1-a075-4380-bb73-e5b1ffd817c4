<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 16 16">
  <defs>
    <style>.TPbg{fill:none;}.<PERSON>{fill:#d5d5d5;}</style>
  </defs>
  <g id="符号和箭头-积分-16">
    <rect class="TPbg" height="16" id="TPbg" width="16"/>
    <path class="Gray" d="M15,1V2.07c-2.31,0-4,.5-4.95,1.42A5.85,5.85,0,0,0,8.46,8a6.5,6.5,0,0,1-2,5.19A7.08,7.08,0,0,1,1.31,15H1V13.9a6.34,6.34,0,0,0,4.83-1.58A5.39,5.39,0,0,0,7.47,8,6.85,6.85,0,0,1,9.4,2.68,7.8,7.8,0,0,1,14.62,1Z" id="<PERSON>"/>
  </g>
</svg>
