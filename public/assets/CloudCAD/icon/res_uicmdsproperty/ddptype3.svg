<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 32 32">
  <defs>
    <style>.TPbg{fill:none;}.<PERSON>{fill:#d8d8d8;fill-rule:evenodd;}</style>
  </defs>
  <g id="点样式-交叉">
    <rect class="TPbg" height="32" id="TPbg" width="32"/>
    <path class="Gray" d="M5.39,4.69,16,15.29,26.61,4.69a.48.48,0,0,1,.63-.06l.07.06a.48.48,0,0,1,.06.63l-.06.07L16.71,16l10.6,10.61a.49.49,0,1,1-.7.7L16,16.71,5.39,27.31a.48.48,0,0,1-.63.06l-.07-.06a.48.48,0,0,1-.06-.63l.06-.07L15.29,16,4.69,5.39a.49.49,0,0,1,.7-.7Z" id="Gray"/>
  </g>
</svg>
