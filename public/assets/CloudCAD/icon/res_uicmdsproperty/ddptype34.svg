<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 32 32">
  <defs>
    <style>.TPbg{fill:none;}.<PERSON>{fill:#d8d8d8;fill-rule:evenodd;}</style>
  </defs>
  <g id="点样式-圈十字">
    <rect class="TPbg" height="32" id="TPbg" width="32"/>
    <path class="Gray" d="M16,3a.5.5,0,0,1,.5.5V6A10,10,0,0,1,26,15.5H28.5a.5.5,0,0,1,0,1H26A10,10,0,0,1,16.5,26V28.5a.5.5,0,0,1-1,0V26A10,10,0,0,1,6,16.5H3.5a.5.5,0,0,1,0-1H6A10,10,0,0,1,15.5,6V3.5A.5.5,0,0,1,16,3Zm-.5,22V16.5H7A9,9,0,0,0,15.5,25ZM25,16.5H16.5V25A9,9,0,0,0,25,16.76ZM16.5,7V15.5H25A9,9,0,0,0,16.76,7ZM7,15.5H15.5V7A9,9,0,0,0,7,15.5Z" id="Gray"/>
  </g>
</svg>
