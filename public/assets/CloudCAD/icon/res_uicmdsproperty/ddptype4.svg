<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="图层 1" id="图层_1" viewBox="0 0 32 32">
  <defs>
    <style>.TPbg{fill:none;}.Gray{fill:#d8d8d8;fill-rule:evenodd;}</style>
  </defs>
  <g id="点样式-短竖">
    <rect class="TPbg" height="32" id="TPbg" width="32"/>
    <path class="Gray" d="M16.5,6.5v7a.5.5,0,0,1-1,0v-7a.5.5,0,0,1,1,0Z" id="Gray"/>
  </g>
</svg>
