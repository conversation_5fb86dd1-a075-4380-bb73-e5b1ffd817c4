<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="块定义-快速选择 " id="_块定义-快速选择_" viewBox="0 0 16 16">
  <defs>
    <style>
      .TPbg {
        fill: none;
      }

      .Yellow {
        fill: #f7990c;
      }

      .Blue {
        fill: #1aa0ff;
      }

      .<PERSON> {
        fill: #d5d5d5;
        fill-rule: evenodd;
      }
    </style>
  </defs>
  <rect class="TPbg" height="16" id="TPbg" width="16" y="0"/>
  <path class="Gray" d="M12.69,10.71l.82-.82s.05-.06,.06-.1c.05-.14-.03-.29-.17-.33l-1.54-.51-.32,.37c-.19,.22-.52,.37-.81,.37-.19,0-.4-.06-.56-.16-.41-.25-.61-.83-.44-1.28h0s-.47-.17-.47-.17c-.05-.02-.11-.02-.17,0-.14,.05-.21,.2-.17,.33l1.38,4.13s.03,.07,.06,.1c.1,.1,.27,.1,.37,0l.82-.82,2.24,2.24,1.12-1.12-2.24-2.24Z" id="Gray"/>
  <path class="Yellow" d="M12.77,2.19l-2.76,3.16c-.07,.08-.06,.2,.02,.27,.03,.03,.08,.05,.13,.05h1.54s.04,.01,.05,.03c.01,.02,.01,.04,0,.06l-1.08,2.84s0,.06,.03,.08,.06,.01,.08-.01l3-3.49c.07-.08,.06-.2-.02-.27-.03-.03-.08-.05-.13-.05h-1.65s-.04-.01-.05-.03c-.01-.02-.01-.04,0-.06l.94-2.52s0-.03,0-.05-.02-.03-.04-.03c-.03,0-.05,0-.07,.02Z" id="Yellow"/>
  <g id="Blue">
    <path class="Blue" d="M8.17,7.12s.07-.08,.09-.12H3V3h6v2.32c.03-.23,.11-.46,.27-.64l.73-.84v-1.34c0-.28-.22-.5-.5-.5H2.5c-.28,0-.5,.22-.5,.5V7.5c0,.28,.22,.5,.5,.5h5.59c-.08-.29-.06-.6,.08-.88Z"/>
  </g>
</svg>
