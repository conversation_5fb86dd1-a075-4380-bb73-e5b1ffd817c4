<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="块定义-拾取点" id="_块定义-拾取点" viewBox="0 0 16 16">
  <defs>
    <style>
      .TPbg {
        fill: none;
      }

      .Blue {
        fill: #1aa0ff;
      }

      .<PERSON> {
        fill: #d5d5d5;
        fill-rule: evenodd;
      }
    </style>
  </defs>
  <g id="Blue">
    <rect class="Blue" height="2" rx=".5" ry=".5" width="2" x="5" y="5"/>
  </g>
  <g id="Gray">
    <path class="Gray" d="M3,9V3h6V7.26l1,.33V2.5c0-.28-.22-.5-.5-.5H2.5c-.28,0-.5,.22-.5,.5v7c0,.28,.22,.5,.5,.5H7.42l-.33-1H3Z"/>
    <path class="Gray" d="M11.69,10.71l.82-.82s.05-.06,.06-.1c.05-.14-.03-.29-.17-.33l-4.13-1.38c-.05-.02-.11-.02-.17,0-.14,.05-.21,.2-.17,.33l1.38,4.13s.03,.07,.06,.1c.1,.1,.27,.1,.37,0l.82-.82,2.24,2.24,1.12-1.12-2.24-2.24Z"/>
  </g>
  <rect class="TPbg" height="16" id="TPbg" width="16" y="0"/>
</svg>
