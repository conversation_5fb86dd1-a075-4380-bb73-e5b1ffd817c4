<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" data-name="块定义-未选定对象 " id="_块定义-未选定对象_" viewBox="0 0 16 16">
  <defs>
    <style>
      .TPbg {
        fill: none;
      }

      .Yellow {
        fill: #f7990c;
        fill-rule: evenodd;
      }
    </style>
  </defs>
  <rect class="TPbg" height="16" id="TPbg" width="16"/>
  <path class="Yellow" d="M13.91,11.23l-2.68-3.82-2.82-4.03c-.2-.29-.62-.29-.82,0l-2.82,4.03-2.68,3.82c-.23,.33,0,.79,.41,.79H13.5c.41,0,.64-.46,.41-.79Zm-5.41-.37c0,.09-.07,.16-.16,.16h-.69c-.09,0-.16-.07-.16-.16v-.69c0-.09,.07-.16,.16-.16h.69c.09,0,.16,.07,.16,.16v.69Zm0-2.17c0,.18-.14,.32-.32,.32h-.37c-.18,0-.32-.14-.32-.32v-3.38c0-.18,.14-.32,.32-.32h.37c.18,0,.32,.14,.32,.32v3.38Z" id="Yellow"/>
</svg>
