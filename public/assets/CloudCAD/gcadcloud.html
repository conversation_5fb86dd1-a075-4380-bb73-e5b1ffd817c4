<!doctype html>
<html lang="en-us">

<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">


  <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0" />

  <title>gcadcloud</title>
  <style>
    html,
    body {
      padding: 0;
      margin: 0;
      overflow: hidden;
      height: 100vh
    }

    canvas {
      border: 0px none;
      background-color: white;
      height: 100%;
      width: 100%;
    }

    canvas {
      outline: 0px solid transparent;
      caret-color: transparent;
      cursor: default
    }
  </style>
</head>

<body onload="load()">
  <figure style="overflow:visible;" id="spinner">
    <center style="margin-top:1.5em; line-height:150%">
      <img src="gcadsplash.jpg" width="320" height="200" style="display:block"></img>
      <strong>正在运行: gcadcloud</strong>
      <div id="status"></div>
      <noscript>JavaScript is disabled. Please enable JavaScript to use this application.</noscript>
    </center>
  </figure>
  <div id="wcadWrap"></div>
  <script type="text/javascript" src="boot.js"></script>
  <script type="text/javascript" src="GStarCloudCAD.js"></script>
  <script type="text/javascript" src="cadboot.js"></script>
  <script type="text/javascript" src="jszip.min.js"></script>
  <script type="text/javascript" src="crypto-js.min.js"></script>
  <script type="text/javascript">
    async function load() {
      var statusElement = document.getElementById('status');
      var progressElement = document.getElementById('progress');
      var spinnerElement = document.getElementById('spinner');

      /*
      1.下载boot.js
      2.由boot.js引导程序启动过程
        a.下载需要的文件列表
        b.执行阶段1任务检查
        c.执行阶段1下载任务
        d.阶段1下载任务结束。并行：开始执行阶段2任务检查，及执行阶段2下载任务
        e.执行阶段1运行任务
        f.加载正式运行WASM文件
 
        以下开始并行：
        d1.加载正式运行WASM文件
        
      */
      var cfg_param = {
        application: {
          'name': "GstarCAD Cloud",
          'sandbox': "cloud.gstarcad.com",
          'version': 2300,
          'vendor': '苏州浩辰软件股份有限公司',
          'user': '张三',
          'userid': 123456,
          'mode': 3,
          'runEnv': "standalone"
        },
        document: {
          'name': '浩辰大厦一层平面图',
          'size': 565656,
          'version': '1.1',
          'sha1': 'abcdedfabcdedfabcdedfabcdedf',
          'createtime': '2023-01-01 12:00:20',
          'lastmodifytime': '2023-01-01 12:00:20',
          'lastmodifyuserid': 123456,
          'lastmodifyuserid': '张三',
          'logicpath': '/浩辰大厦/施工图/浩辰大厦一层平面图.dwg',
          'data': '',
          'did':'test123456',
          'uploadType':'incrment',
          'localPath': '/data/sample.dwg',
		  'cloudPath': '/data/test.dwg',
          'fileId': 2342342
        },
        license: {},
        collaboration: {},
        services: {
          document: [
            'https://document.gstarcad.com'
          ],
          meetings: [ //多个用于负载均横
            'https://metting0.gstarcad.com',
            'https://metting1.gstarcad.com',
            'https://metting2.gstarcad.com'
          ],
          cdns: ''
        },
        'var': {
          "DOWNLOADSVR": /^.*\//.exec(window.location.href)[0],   //下载源目录 使用baseURL
          "APPROOT": "/data/"    //db存储目录 /opt/gstarcad/v2023/
        },
        files: {
          s1_need: async () => {
            var fileinfo = [];
            function getJsonFileInfo() {
              return new Promise((resolve, reject) => {
                fetch('s1_need.json')
                  .then(response => {
                    return response.arrayBuffer()
                  })
                  .then(res => {
                    var dataView = new DataView(res);
                    var decoder = new TextDecoder("utf-8");
                    var jsonString = decoder.decode(dataView);
                    fileinfo = JSON.parse(jsonString);
                    resolve(res);
                  })
                  .catch(err => reject(err))
              })
            }
            await getJsonFileInfo()
            return fileinfo
          },
          s2_need: async () => {
            var fileinfo = [];
            function getJsonFileInfo() {
              return new Promise((resolve, reject) => {
                fetch('s2_need.json')
                  .then(response => {
                    return response.arrayBuffer()
                  })
                  .then(res => {
                    var dataView = new DataView(res);
                    var decoder = new TextDecoder("utf-8");
                    var jsonString = decoder.decode(dataView);
                    fileinfo = JSON.parse(jsonString);
                    resolve(res);
                  })
                  .catch(err => reject(err))
              })
            }
            await getJsonFileInfo()
            return fileinfo
          }
        },
        fetches: async () => {
          console.log("fetches")
          var fileinfo = {};
          function getJsonFileInfo() {
            return new Promise((resolve, reject) => {
              fetch('fetch_list.json')
                .then(response => {
                  return response.arrayBuffer()
                })
                .then(res => {
                  var dataView = new DataView(res);
                  var decoder = new TextDecoder("utf-8");
                  fileinfo = decoder.decode(dataView);
                  resolve(res);
                })
                .catch(err => reject(err))
            })
          }
          await getJsonFileInfo()
          return fileinfo
        }
      };
      var invoke_callBack = {
        eventBeginStage1: () => {
          console.log('stage1 begin');
        },
        eventEndStage1: () => {

        },

        eventPrepareStage2Files: () => { },
        eventBeginStage2: () => { },
        eventEndStage2: () => { },

        showMsg: (code, msg) => {
          console.log(msg);
        },
        setProcess: (code, msg) => {
          var processStr = [
            "启动完成10%,下载1阶段完成",
            "启动完成20%,下载2阶段完成,开始应用启动",
            "启动完成30%,应用启动完成,进入应用循环",
            "启动完成40%,开图开始",
            "启动完成100%,开图完成",

          ]
          //console.log(processStr[code/10-1]);
          if (msg) {
            cloudCAD.wasmPostCallback({
              type: 'loadProcess',
              payload: JSON.stringify({ process: code, message:msg })
            })
          } else {
            cloudCAD.wasmPostCallback({
              type: 'loadProcess',
              payload: JSON.stringify({ process: code })
            })
          }
        }, 
        showMain: (code, msg) => {
          spinnerElement.style.display = 'none';
        }
      };

      const cloudCAD = new GStarCloudCAD()
      const cloudcanvas = await cloudCAD.init({
        wrapId: 'wcadWrap',
        iconUrl: '/icon'
      })
      var externalMount = {
          getFontInfoList: async () => {
              return new Promise( function(resolve,reject) {
                    var result = {
                          "crumbsList": [
                            {
                              "folderId": 1,
                              "folderName": "data"
                            },
                            {
                              "folderId": 5,
                              "folderName": "sys_resource"
                            },
                            {
                              "folderId": 10,
                              "folderName": "part"
                            }
                          ],
                          "currPageNums": 3,
                          "fontFileList": [
                            {
                              "lastModifyTime": "2024-02-07 11:38:27",
                              "etag": "24fd1171dbcbc1c4373f4e8fda7f30686cb6083b",
                              "userTag": "2024-02-07 11:38:26",
                              "editStatus": 0
                            },
                            {
                              "lastModifyTime": "2024-02-07 11:38:11",
                              "etag": "213da55af2bde9df445d1a7a5bbae2ef25d7cdee",
                              "userTag": "2024-02-07 11:38:10",
                              "editStatus": 0
                            },
                            {
                              "lastModifyTime": "2024-02-07 11:37:40",
                              "etag": "d27fb47a750688e5e79adf0623647a61a88dd91d",
                              "userTag": "2024-02-07 11:37:39",
                              "editStatus": 0
                            }
                          ],
                          "fontInfoList": [
                            {
                              "bShx": false,
                              "localName": "SourceHanSansCN-Regular.ttf",
                              "globalName": "Regular.ttf",
                              "charSet": "???? CN",
                              "pitchFamily": 1
                            },
                            {
                              "bShx": true,
                              "localName": "??_GB2312.shx",
                              "globalName": "Regular.shx",
                              "charSet": "??_GB2312",
                              "pitchFamily": 1
                            },
                            {
                              "bShx": true,
                              "localName": "HZTXT.SHX",
                              "globalName": "HZTXT.SHX",
                              "charSet": "bigfont 1.0",
                              "pitchFamily": 1
                            },
                            {
                              "bShx": true,
                              "localName": "HZFS1.SHX",
                              "globalName": "HZFS1.SHX",
                              "charSet": "unifont 1.0",
                              "pitchFamily": 1
                            },
                            {
                              "bShx": true,
                              "localName": "HZFS2.SHX",
                              "globalName": "HZFS2.SHX",
                              "charSet": "shapes 1.0",
                              "pitchFamily": 1
                            },
                            {
                              "bShx": true,
                              "localName": "HZFS3.SHX",
                              "globalName": "HZFS3.SHX",
                              "charSet": "shapes 1.1",
                              "pitchFamily": 1
                            },
                            {
                              "bShx": true,
                              "localName": "HZFS.SHX",
                              "globalName": "HZFS.SHX",
                              "charSet": "bigfont 1.0",
                              "pitchFamily": 1
                            },
                            {
                              "bShx": true,
                              "localName": "??_GB2312.shx",
                              "globalName": "Regular.shx",
                              "charSet": "??_GB2312",
                              "pitchFamily": 1
                            }
                          ],
                          "totalFontNums": 3
                    };
                    resolve(result);
                })      
          }
      }
      
      cloudCAD.externalMount = externalMount

      cloudCAD.externalMount.openHelp = (val) => {
        if (val) {
          if(val === 'MAINHELP') {
            let href = "https://www.gstarcad.com/help/GstarCAD_2024_zh-CN.html"
            window.open(href, '_blank')
          } else {
            let firstChar = val.charAt(0).toUpperCase();
            let href = firstChar === "_" ? `https://www.gstarcad.com/help/CloudGstarCAD_zh-CN/detail/commandReferences/${val}.html` : `https://www.gstarcad.com/help/CloudGstarCAD_zh-CN/detail/commandReferences_${firstChar}/${val}.html` 
            window.open(href, '_blank')
          }
        }
      }

      cfg_param.cloudcanvas = cloudcanvas
      cfg_param.JSZip = JSZip
      cfg_param.CryptoJS = CryptoJS
      cfg_param.writeFile = (path, data) => {
        FS.writeFile(path, data);
      }
      
      cfg_param.big_fssync = async (load) => {
        return new Promise((resolve, reject) => {
          FS.syncfs(load, function (err) {
            if (err) {
              reject(err);
            } else {
              resolve(err);
            }
          });
        });
      };
      cfg_param.big_local_existFile = (path) => {//文件是否存在
        try {
          return FS.isFile(FS.stat(path.replace('//', '/')).mode);//
        } catch (e) {
          return false;
        }
      };
      cfg_param.fs_delfile = (path) => {
        return new Promise((resolve, reject) => {
          FS.unlink(path, function (err) {
            if (err) {
              reject(err);
            } else {
              resolve(err);
            }
          });
          cfg_param.big_fssync(false);
        });
      };
      GstarCADBoot.start(cfg_param, invoke_callBack);
    }
  </script>
</body>

</html>