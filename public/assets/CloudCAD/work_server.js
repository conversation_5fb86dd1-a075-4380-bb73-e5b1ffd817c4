// domainName https://gcad-dev-cn.51ake.com:8271
//import { CryptoJS } from '/assets/CloudCAD/crypto-js.min.js';
// const { CryptoJS } = require('/assets/CloudCAD/crypto-js.min.js');

// needURL 视情况更新 或注入
var needURL = {
    "getFontInfoList": { "url": "/api/v2/_st/_font/_subFontInfoList", "method": "get", "data": { "folderId": 7, "limit": 1000000 } }, // subFontInfoList
    "getFontList": { "url": "/api/v2/_st/_font/_fontList", "method": "get", "data": { "folderId": 7, "limit": 1000000 } },
    "getFontMatrixList": { "url": "/api/v2/_st/_font/_fontMatrix", "method": "get", "data": { "folderId": 7, "limit": 1000000, "requiredFont": "", "requiredStr": "" } },
    "getFileInfoByPath": { "url": "/api/v2/_st/_file/_getFileInfoByPath", "method": "get", "data": { "filePath": "" } },
    "getDownloadUrlDirect":{"url":"/api/v3/_st/_file/_download_url/direct","method":"get","data":{"fileId":"4097"}}, // 字体等使用
    "getDownloadUrlSafe":{"url":"/api/v3/_st/_file/_download_url/safe","method":"get","data":{"fileId":"4097"}},      //dwg encrypt
    "getDownloadUrlExport":{"url":"/api/v3/_st/_file/_download_url/export","method":"get","data":{"fileId":"4097"}},  //dwg not encrypt
    "downloadByFileId": { "url": "/api/v2/_st/_file/_download", "method": "get", "data": { "fileName": "", "fileId": "" } },
    "getKVMeta": { "url": "/api/v2/_st/_file/_getKVMeta", "method": "get", "data": { "key": "11111111" } },
    "downloadByStorageId": { "url": "/api/v2/_st/_storage/_download", "method": "get", "data": { "storageId": "11111111" } },
    "getMeta": { "url": "/api/v2/_st/_file/_getMeta", "method": "get", "data": { "fileId": "4110", "metaKey": "gcad2d_gs_instruction" } },
    "postTask": { "url": "/api/v2/_st/_task", "method": "post", "data": { "callback": "", "config": "", "fileId": "", "type": "", "version": "" } },
    "uploadModifyRecord": { "url": "/api/v2/_st/_filemodify/_uploadModifyRecord", "method": "post", "data": { "fileId": "", "etag": "", "baseId": "", "file": "" } },
    "upload": { "url": "/api/v2/_st/_file/_upload", "method": "post", "data": { "folderId": "", "etag": "", "file": "" } },
    "findFile": { "url": "/api/v2/_st/_file/_findFile", "method": "get", "data": { "fileId": "", "dependentFile": "", "fileType": "" } },
};

var kEnumTaskPending = 1
var kEnumTaskExecuting = 2
var kEnumTaskFinished = 3
var kEnumTaskPartialFinished = 4
var kEnumTaskFailed = 5
var kEnumTaskCanceled = 6
var waitTime = 1000 // 1000=1秒
var reqNum = 20

const DBNAME = '/data'
const DBVERSION = 21
const OBJSTORE = 'FILE_DATA'

const GCAD_DBG_DEFAULT_GRADE = 1
const GCAD_DBG_INFO_GRADE = 2
const GCAD_DBG_DEBUG_GRADE = 3
const GCAD_DBG_WARN_GRADE = 4
const GCAD_DBG_ERROR_GRADE = 5

const GCAD_LOCALFS_STORAGE_TYPE = 1        // Localfs_StoraeType
const GCAD_OSS_ALI_STORAGE_TYPE = 2        // Oss_Aliyun_StorageType CloudStorage_StoraeType
const GCAD_OSS_AWS_STORAGE_TYPE = 3        // Oss_aws_StorageType

var encryptMode = false
var logLevel = 1
function GcadLog(level, strLog, params) {
    if (logLevel == GCAD_DBG_DEFAULT_GRADE
        || logLevel == GCAD_DBG_INFO_GRADE
        || (logLevel == GCAD_DBG_DEBUG_GRADE && (level == "ERROR" || level == "WARN" || level == "DEBUG"))
        || (logLevel == GCAD_DBG_WARN_GRADE && (level == "ERROR" || level == "WARN"))
        || (logLevel == GCAD_DBG_ERROR_GRADE && level == "ERROR")) {
        if (arguments.length === 2) {
            console.log(strLog);
        }
        else {
            console.log(strLog, params);
        }
    }
};
function GcadILog(strLog, params) {
    if (arguments.length === 2) {
        GcadLog("INFO", "INFO: " + strLog, params);
    }
    else {
        GcadLog("INFO", "INFO: " + strLog);
    }
}
function GcadDLog(strLog, params) {
    if (arguments.length === 2) {
        GcadLog("DEBUG", "DEBUG: " + strLog, params);
    }
    else {
        GcadLog("DEBUG", "DEBUG: " + strLog);
    }
}
function GcadWLog(strLog, params) {
    if (arguments.length === 2) {
        GcadLog("WARN", "WARN: " + strLog, params);
    }
    else {
        GcadLog("WARN", "WARN: " + strLog);
    }
}
function GcadELog(strLog, params) {
    if (arguments.length === 2) {
        GcadLog("ERROR", "ERROR: " + strLog, params);
    }
    else {
        GcadLog("ERROR", "ERROR: " + strLog);
    }
}

Date.prototype.format = function (format) {
    var o = {
        "M+": this.getMonth() + 1, //month
        "d+": this.getDate(), //day
        "h+": this.getHours(), //hour
        "m+": this.getMinutes(), //minute
        "s+": this.getSeconds(), //second
        "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
        "S": this.getMilliseconds() //millisecond
    }

    if (/(y+)/.test(format)) {
        format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    }

    for (var k in o) {
        if (new RegExp("(" + k + ")").test(format)) {
            format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
        }
    }
    return format;
}

fetchFromServer = async (inputUrl, inputMethod, inputToken) => {
    return new Promise(async (resolve, reject) => {
        try {
            const response = await fetch(inputUrl, {
                method: inputMethod,
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    Authorization: inputToken,
                },
            })
            if (response.ok) {
                GcadILog('fetchFromServer : oooooooooooooook.');
                resolve(response.arrayBuffer())
            } else {
                if (response.status === 404) {
                    GcadILog('fetchFromServer : err= 404, Not found.');
                } else if (response.status === 500) {
                    GcadILog('fetchFromServer : err= 500, internal server error.');
                } else {
                    GcadILog('fetchFromServer : err= other error, url invalid.');
                }
                resolve("fail")
            }
        } catch (e) {
            GcadILog('onmessage : fetch is fail, Promise rejected.');
            GcadILog('onmessage : e=', e);
            resolve("fail")
        }
    })
};

requireServer = async (headerUrl, reqType, reqParam, token) => {
    GcadILog('requireServer: reqType=', reqType);
    GcadILog('requireServer: reqParam=', reqParam);

    mUrl = headerUrl + needURL[reqType].url + reqParam
    mMethod = needURL[reqType].method

    GcadILog('requireServer : mUrl=', mUrl);
    GcadILog('requireServer : mMethod=', mMethod);
    var retData = await fetchFromServer(mUrl, mMethod, token)

    if (retData == "fail") {
        GcadILog('requireServer : fetch failed');
    } else {
        if (reqType == "downloadByFileId") {
            return retData
        }
        var retDataStr = new TextDecoder().decode(retData)
        if (retDataStr == "illegal request") {
            GcadILog('requireServer : getUrlSourceByKey fetch failed, illegal request');
        } else {
            GcadILog('requireServer : fetch Finished');
            GcadILog('requireServer : retData=', retData);
            if (reqType == "downloadByStorageId") {
                return retData
            }
            try {
                testjson = JSON.parse(new TextDecoder().decode(retData))
            } catch (e) {
                GcadILog("requireServer : JSON.parse error, retData is invalid JSON.")
                if (reqType == "getKVMeta") {
                    return ''
                }
                if (reqType == "getMeta") {
                    return kEnumTaskFailed
                }
            }
            GcadILog('requireServer : testjson=', testjson);
            if (reqType == "getMeta") {

                // 此处获取不同任务的结果 要区分任务类型 gcad2d_font_chardata  gcad2d_gs_instruction
                if (reqParam.includes("gcad2d_gs_instruction")) {
                    GcadILog('requireServer : testjson.data.gcad2d_gs_instruction=', testjson.data.gcad2d_gs_instruction);
                    if (testjson.data.gcad2d_gs_instruction == '') {
                        return kEnumTaskFailed;
                    }
                    metainfo = JSON.parse(testjson.data.gcad2d_gs_instruction)
                    GcadILog('requireServer : metainfo=', metainfo);
                    myMetaInfo = metainfo.metaInfo[0];
                    GcadILog('requireServer : myMetaInfo=', myMetaInfo);
                    return myMetaInfo.history.V1.info.transition.taskStatus;
                }
                else if (reqParam.includes("gcad2d_font_chardata")) {
                    GcadILog('requireServer : metainfo= gcad2d_font_chardata');
                    GcadILog('requireServer : testjson.data.gcad2d_font_chardata=', testjson.data.gcad2d_font_chardata);
                    GcadILog('requireServer : testjson.data.version=', testjson.data.version);
                    return testjson.data.gcad2d_font_chardata;
                }
                else if (reqParam.includes("gcad2dthumb")) {
                    GcadILog('requireServer : metainfo= gcad2dthumb');
                    GcadILog('requireServer : testjson.data.gcad2dthumb=', testjson.data.gcad2dthumb);
                    GcadILog('requireServer : testjson.data.version=', testjson.data.version);
                    return testjson.data.gcad2dthumb;
                }
                else {
                    GcadILog('requireServer : reqType= getMeta, other task');
                }
            }
            if (reqType == "getKVMeta" || reqType == "findFile" || reqType == "getDownloadUrlDirect" 
                || reqType == "getDownloadUrlSafe" || reqType == "getDownloadUrlExport") {
                // jsonobj
                return testjson.data
            }
            if (reqType == "getFontList") {
                return testjson.data.fontFileList
            }
        }
    }
};

downloadFromLocalfs = async (reqUrl, token) => {
    GcadILog('downloadFromLocalfs: reqUrl=', reqUrl);
    var retData = await fetchFromServer(reqUrl, 'get', token)
    if (retData == "fail") {
        GcadILog('downloadFromLocalfs : fetch failed');
    }
    return retData
};

downloadFromOSS = async (url) => {
    const response = await fetch(url , {
        mode: 'cors' // 禁用 CORS，响应不可读
    });
    const reader = response.body.getReader();
    let receivedLength = 0;
    const chunks = [];
    while(true) {
        const {done, value} = await reader.read();
        if (done) {
            const blob = new Blob(chunks);
            GcadILog("downloadFromOSS blob=", blob)
            break;
        }
        chunks.push(value);
        receivedLength += value.length;
        GcadILog("downloadFromOSS receivedLength=", receivedLength)
    }
    // 组成ret
    GcadILog("downloadFromOSS chunks.length=", chunks.length)
    GcadILog("downloadFromOSS chunks=", chunks)
    const ret = new Uint8Array(receivedLength);
    var tmpLen = 0 
    for (var i=0; i<chunks.length; i++) {
        ret.set(chunks[i],tmpLen)
        tmpLen = tmpLen + chunks[i].length
    }
    GcadILog("downloadFromOSS ret.length=", ret.length)
    GcadILog("downloadFromOSS tmpLen=", tmpLen)
    return ret; //chunks;
}

getDownload = async (reqHeaderUrl, fileId, token, urlType) => {
    var sampleArrayBuffer = new Uint8Array();
    var reqParams = "?fileId=" + fileId
    var tmpUrlData = await requireServer(reqHeaderUrl, urlType, reqParams, token)   
    GcadILog("getDownload: tmpUrlData=", tmpUrlData)
    if (tmpUrlData.storageType == GCAD_LOCALFS_STORAGE_TYPE) {
        var reqUrl = tmpUrlData.downloadUrl + "?fileId=" + fileId;
        //var retvalue = await requireServer(reqHeaderUrl, "downloadByFileId", reqParams, token)
        var retvalue = await downloadFromLocalfs(reqUrl, token);
        sampleArrayBuffer = new Uint8Array(retvalue);                                         
    } 
    else if (tmpUrlData.storageType == GCAD_OSS_ALI_STORAGE_TYPE || tmpUrlData.storageType == GCAD_OSS_AWS_STORAGE_TYPE) {
        if (tmpUrlData.storageType) {
            var tmptesturl = tmpUrlData.downloadUrl
            sampleArrayBuffer = await downloadFromOSS(tmptesturl);
            GcadILog("getDownload: fetchFromOSS tmpBuf=", sampleArrayBuffer)   
        }
        else {
            GcadILog("getDownload: need coding. error")   // cdn false要通过私有api下载
        }                                                         
    }
    return sampleArrayBuffer
};


reqFileInfo = async (headerUrl, fileId, token) => {
    var url = headerUrl + "/api/v2/_st/_file/" + fileId
    GcadILog('reqFileInfo : url=', url);
    var retData = await fetchFromServer(url, "get", token)
    if (retData == "fail") {
        GcadILog('reqFileInfo : fetch failed');
    } else {
        GcadILog('reqFileInfo : retData=', retData);
        try {
            testjson = JSON.parse(new TextDecoder().decode(retData))
        } catch (e) {
            GcadILog("reqFileInfo : JSON.parse error, retData is invalid JSON.")
        }
        GcadILog('reqFileInfo : testjson=', testjson);
        return testjson.data.FileInfo
    }
}

postServerFormData = async (inputUrl, inputMethod, inputToken, form) => {
    GcadILog('postServerFormData : start');
    try {
        const response = await fetch(inputUrl, {
            method: inputMethod,
            headers: {
                Authorization: inputToken,
            },
            body: form,
        })
        if (response.ok) {
            GcadILog('uploadModifyRecord : response ok, status=200.');
        } else {
            if (response.status === 404) {
                GcadILog('uploadModifyRecord : err= 404, Not found.');
            } else if (response.status === 500) {
                GcadILog('uploadModifyRecord : err= 500, internal server error.');
            } else {
                GcadILog('uploadModifyRecord : err= other error, url invalid.');
            }
        }
    } catch (e) {
        GcadILog('onmessage : fetch is fail, Promise rejected. e=', e);
    }
};

function sleep(time) {
    return new Promise((resolve) => setTimeout(resolve, time));
};

function rewriteArrayBuffer(key, data, db) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([OBJSTORE], 'readwrite');
        const objectStore = transaction.objectStore(OBJSTORE);

        transaction.oncomplete = function () {
            resolve(db);
        };

        transaction.onerror = function (event) {
            reject(event.target.error);
        };
        const putRequest = objectStore.put({ timestamp: new Date(), mode: 33279, contents: data }, key);

        putRequest.onsuccess = function () {
            GcadILog("putRequest.onsuccess=")
            resolve(db);
        };

        putRequest.onerror = function (event) {
            GcadILog("putRequest.onerror=", event.target.error)
            reject(event.target.error);
        };
    });
};

function isExistArrayBuffer(key, db) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([OBJSTORE], 'readonly');
        const objectStore = transaction.objectStore(OBJSTORE);

        transaction.oncomplete = function () {
            resolve(db);
        };

        transaction.onerror = function (event) {
            reject(event.target.error);
        };
        let getRequest = objectStore.get(key);

        getRequest.onsuccess = function (event) {
            if (getRequest.result) {
                GcadILog("isExistArrayBuffer: Key exists:", getRequest.result);
                resolve('true');
            }
            else {
                GcadILog("isExistArrayBuffer: Key does not exist.");
                resolve('false');
            }
        };

        getRequest.onerror = function (event) {
            console.error("Error checking for key:", event.target.error);
        };

    });
};

function writeArrayBuffer(key, data, db) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([OBJSTORE], 'readwrite');
        const objectStore = transaction.objectStore(OBJSTORE);

        transaction.oncomplete = function () {
            resolve(db);
        };

        transaction.onerror = function (event) {
            reject(event.target.error);
        };
        const addRequest = objectStore.add({ timestamp: new Date(), mode: 33279, contents: data }, key);

        addRequest.onsuccess = function () {
            GcadILog("addRequest.onsuccess=")
            resolve(db);
        };

        addRequest.onerror = function (event) {
            GcadILog("addRequest.onerror=", event.target.error)
            reject(event.target.error);
        };
    });
};

function readArrayBuffer(key, db) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([OBJSTORE], 'readonly');
        const objectStore = transaction.objectStore(OBJSTORE);

        transaction.oncomplete = function () {
            resolve(db);
        };

        transaction.onerror = function (event) {
            reject(event.target.error);
        };
        const addRequest = objectStore.get(key);

        addRequest.onsuccess = function () {
            GcadILog("addRequest.result=", addRequest.result.contents);
            //data = addRequest.result.contents;
            //GcadILog("addRequest.result=")
            //GcadILog("addRequest.result=")
            resolve(addRequest.result.contents);
        };

        addRequest.onerror = function (event) {
            GcadILog("addRequest.onerror=", event.target.error)
            reject(event.target.error);
        };
    });
};

function writeDir(key, db) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([OBJSTORE], 'readwrite');
        const objectStore = transaction.objectStore(OBJSTORE);

        transaction.oncomplete = function () {
            resolve(db);
        };

        transaction.onerror = function (event) {
            reject(event.target.error);
        };
        const addRequest = objectStore.add({ timestamp: new Date(), mode: 16895 }, key);

        addRequest.onsuccess = function () {
            resolve(db);
        };

        addRequest.onerror = function (event) {
            reject(event.target.error);
        };
    });
};

function openDatabase(databaseName, version) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(databaseName, version);

        request.onupgradeneeded = function (event) {
            const db = event.target.result;
            const transaction = event.target.transaction;

            let fileStore;

            if (db.objectStoreNames.contains(OBJSTORE)) {
                fileStore = transaction.objectStore(OBJSTORE);
            } else {
                fileStore = db.createObjectStore(OBJSTORE);
            }

            if (!fileStore.indexNames.contains('timestamp')) {
                fileStore.createIndex('timestamp', 'timestamp', { unique: false });
            }
        };

        request.onsuccess = function (event) {
            const db = event.target.result;
            resolve(db);
        };

        request.onerror = function (event) {
            reject(event.target.error);
        };
    });
};

// 从服务端findFIle下载依赖文件
findFile = (reqHeaderUrl, reqParams, token, db) => {
    //GcadILog("reqParams=", reqParams)
    return new Promise(async (resolve, reject) => {
        var storageid = 0;
        var fileId = 0;
        var size = 0;
        var tmpinfo = await requireServer(reqHeaderUrl, "findFile", reqParams, token)
        GcadILog('收到了findFile消息: tmpinfo=', tmpinfo);
        storageid = tmpinfo.storageId;
        fileId = tmpinfo.fileId;
        size = tmpinfo.size;
        GcadILog("findFile: storageid = ", storageid)
        GcadILog("findFile: fileId = ", fileId)
        if (storageid == 0 && fileId == 0 ) {
            GcadELog("findFile: findFile failed");
            //return new Promise((resolve, reject) => resolve(true));
            var tmp = reqParams.split("&", 2);
            var dfn = decodeURIComponent(tmp[1].split("=")[1]);
            var data = {}
            data.source = dfn
            data.fileId = ""
            data.fileSha = ""
            data.fileSize = ""
            data.filePath = ""
            data.status = "notexists"
            GcadILog('findFile: data=', data);  //主js需要写入索引
            resolve(data);
        }
        else {
            var tmp = reqParams.split("&", 2);
            var dfn = decodeURIComponent(tmp[1].split("=")[1]);

            var ext;
            var ind = dfn.lastIndexOf(".");
            if (ind != -1 && ind != dfn.length - 1) {
                ext = dfn.substring(ind);
            } else {
                ext = ".file";
            }
            GcadILog("ext=", ext);
            var path = "/data/"
            // 注:dwg依赖文件放到vsData下; 其他依赖pdf png作为临时文件,放至tmp目录下
            if (ext == ".dwg") {
                path = path + "vsData/" + tmpinfo.etag + ext
            } else {
                path = path + "tmp/" + tmpinfo.etag + ext
            }
            GcadILog('findFile: write path=', path);
            let isExist = await isExistArrayBuffer(path, db)
            if (isExist == 'false') {
                var sampleArrayBuffer = new Uint8Array();
                var urlType = "getDownloadUrlExport"
                if(encryptMode)
                {
                    urlType = "getDownloadUrlSafe"
                }
                sampleArrayBuffer = await getDownload(reqHeaderUrl, fileId, token, urlType); 
                GcadILog('findFile: start write indexdb');
                if (sampleArrayBuffer.length != size) {
                    var data = {}
                    data.source = dfn
                    data.fileId = ""
                    data.fileSha = ""
                    data.fileSize = ""
                    data.filePath = ""
                    data.status = "downloadFailed"
                    GcadILog('findFile: data=', data);  //主js需要写入索引
                    resolve(data);
                    //return;
                }
                else {
                    await rewriteArrayBuffer(path, sampleArrayBuffer, db)
                }
            }
            GcadILog('findFile: tmpinfo.fileId=', tmpinfo.fileId);
            var fileInfo = await reqFileInfo(reqHeaderUrl, tmpinfo.fileId, token)

            //db.close()
            var data = {}
            data.source = dfn
            data.fileId = tmpinfo.fileId
            data.fileSha = tmpinfo.etag
            data.fileSize = tmpinfo.size
            data.filePath = path
            data.createTime = fileInfo.createTime
            data.lastModifyTime = fileInfo.lastModifyTime
            data.status = "downloaded"
            GcadILog('findFile: data=', data);  //主js需要写入索引
            resolve(data);
        }
    })
};

exeWithRetry = async (asyncFunc, reqHeaderUrl, reqParams, token, db, times) => {
    return new Promise(async (resolve, reject) => {
        reTryFunc = (times) => {
            asyncFunc(reqHeaderUrl, reqParams, token, db).then((res) => {
                resolve(res)
            }).catch((err) => {
                if (times > 0) {
                    setTimeout(() => {
                        //GcadILog("exeWithRetry: path=", fileObject.path, " times=", times);
                        reTryFunc(times - 1)
                    })
                } else {
                    GcadILog("下载文件：", fileObject.path, '失败!');
                    reject(err)
                }
            })
        };
        //GcadILog("exeWithRetry: path=", fileObject.path, " times=", times);
        reTryFunc(times);
    })
};

startAsyncDownloadList = (fileList, headerUrl, token, taskId, db, retryTimes) => {
    var downloadList = []
    fileList.map(async (fileInfo) => {
        exeWithRetry(findFile, headerUrl, fileInfo, token, db, retryTimes).then((res) => {
            downloadList.push(res)
        }).then((list) => {
            if (fileList.length == downloadList.length) {
                GcadILog("exeWithRetry postmessage=")
                var tmpAllInfo = {}
                tmpAllInfo.command = "findFile"
                tmpAllInfo.taskId = taskId
                var data = {}
                data.findFileList = downloadList
                var ret = {}
                ret.data = data
                tmpAllInfo.ret = ret
                postMessage(tmpAllInfo);
            }
        })
    })
    return downloadList;
}

onmessage = (async (event) => {
    GcadILog('收到了主进程发出的消息: event.data=', event.data);
    GcadILog('onmessage : event.data.comand=', event.data.command);
    if (event.data.command == "getFontInfoList") {
        mUrl = event.data.reqHeaderUrl + needURL[event.data.command].url
        mData = needURL[event.data.command].data
        mLength = Object.keys(mData).length
        GcadILog("onmessage : mLength =", mLength);
        if (mLength >= 1) {
            mUrl = mUrl + '?'
        }
        for (var key in mData) {
            GcadILog("mData=", key + ':' + mData[key]);
            mUrl = mUrl + key + '=' + mData[key];
            mLength = mLength - 1
            if (mLength >= 1) {
                mUrl = mUrl + '&'
            }
        }
        mMethod = needURL[event.data.command].method
        mToken = event.data.token
        GcadILog('onmessage : mUrl=', mUrl);
        GcadILog('onmessage : mData=', mData);
        GcadILog('onmessage : mMethod=', mMethod);
        function getInfo() {
            return new Promise(async (resolve, reject) => {
                try {
                    const response = await fetch(mUrl, {
                        method: mMethod,
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8',
                            Authorization: mToken,
                        },
                    })
                    if (response.ok) {
                        resolve(response.arrayBuffer())
                    } else {
                        if (response.status === 404) {
                            GcadILog('onmessage : err= 404, Not found.');
                        } else if (response.status === 500) {
                            GcadILog('onmessage : err= 500, internal server error.');
                        } else {
                            GcadILog('onmessage : err= other error, url invalid.');
                        }
                        resolve("fail")
                    }
                } catch (e) {
                    GcadILog('onmessage : fetch is fail, Promise rejected.');
                    GcadILog('onmessage : e=', e);
                    resolve("fail")
                }
            })
        }
        var retData = await getInfo()
        if (retData == "fail") {
            event.data.status = "Failed"
            event.data.ErrMsg = retData
            event.data.ErrCode = 500
            GcadILog('onmessage : event.data=', event.data);
            postMessage(event.data);
        } else {
            //GcadILog('onmessage : retData=', retData);
            //GcadILog("onmessage : new TextDecoder().decode(retData) = ", new TextDecoder().decode(retData)) 
            var retDataStr = new TextDecoder().decode(retData)
            if (retDataStr == "illegal request") {
                event.data.status = "Failed"
                event.data.ErrMsg = "illegal request"
                event.data.ErrCode = 400
                GcadILog('onmessage : event.data=', event.data);
                postMessage(event.data);
            } else {
                event.data.status = "Finished"
                try {
                    event.data.ret = JSON.parse(new TextDecoder().decode(retData))
                } catch (e) {
                    GcadILog("onmessage : JSON.parse error, retData is invalid JSON.")
                    return
                }
                GcadILog('onmessage : event.data=', event.data);
                postMessage(event.data);
            }
        }
    }
    else if (event.data.command == "getAndStorageVsData") {
        var reqkey = event.data.reqUrlData.fileId + '/' + event.data.reqUrlData.version + '/' + event.data.reqUrlData.sha1 + "/gs/catalog"
        var reqParams = "?key=" + reqkey;
        var metaObj = await requireServer(event.data.reqHeaderUrl, "getKVMeta", reqParams, event.data.token)
        GcadILog("getAndStorageVsData: metaObj = ", metaObj)
        var storageid = 0
        if (metaObj && metaObj.value == '') {
            GcadILog("getAndStorageVsData: get gscatalog failed, need run MakeGsInstruction.");
            // todo 没有获取sid，需触发任务，并轮询任务结束，再次获取sid
            var typeTask = "MakeGsInstruction";
            var config = "{}";
            var callback = "";
            var fileId = event.data.reqUrlData.fileId;
            var version = event.data.reqUrlData.version;
            reqParams = "?callback=" + callback + "&config=" + config + "&fileId=" + fileId + "&type=" + typeTask + "&version=" + version;
            var ret = await requireServer(event.data.reqHeaderUrl, "postTask", reqParams, event.data.token);
            var metaKey = 'gcad2d_gs_instruction';
            // ?fileId=4110&metaKey=gcad2d_gs_instruction  // 构造测试
            await sleep(waitTime);
            reqParams = "?fileId=" + fileId + "&metaKey=" + metaKey;
            var retStatus = await requireServer(event.data.reqHeaderUrl, "getMeta", reqParams, event.data.token);
            for (var i = 0; i <= reqNum && retStatus != kEnumTaskFinished; i++) {
                await sleep(waitTime);
                GcadILog("requireServer getMeta i=", i);
                retStatus = await requireServer(event.data.reqHeaderUrl, "getMeta", reqParams, event.data.token);
            }
            if (retStatus != kEnumTaskFinished) {
                GcadILog("getAndStorageVsData failed");
                return new Promise((resolve, reject) => resolve(true));
            }
            reqParams = "?key=" + reqkey;
            metaObj = await requireServer(event.data.reqHeaderUrl, "getKVMeta", reqParams, event.data.token)
            if (metaObj && metaObj.value == '') {
                GcadILog("getAndStorageVsData: get gcad2d_gs_instruction and gscatalog failed.");
                return new Promise((resolve, reject) => resolve(true));
            }
        }

        if (!metaObj) {
            GcadILog("getAndStorageVsData failed.")
            return new Promise((resolve, reject) => resolve(true));
        }
        else {
            var tmpjson = JSON.parse(metaObj.value)
            GcadILog("tmpjson = ", tmpjson)
            var tmplayout = tmpjson.layouts[0]
            GcadILog("tmplayout.storageId = ", tmplayout.storageId)
            storageid = tmplayout.storageId
            var tmpSha1 = tmplayout.sha1
            reqParams = "?storageId=" + storageid;
            var retvalue = await requireServer(event.data.reqHeaderUrl, "downloadByStorageId", reqParams, event.data.token)
            var localWriteIndexdb = event.data.needReturnData
            if (localWriteIndexdb) {
                // let tmpBuf = new Uint8Array(retvalue);
                event.data.vsDataSha1 = tmplayout.sha1
                event.data.vsDataSize = tmplayout.size
                event.data.ret = retvalue
                GcadILog('onmessage : needReturnData=true event.data=', event.data);
                postMessage(event.data);
            } else {
                GcadILog('onmessage : start write indexdb');
                //let indexdb = IDBDatabase
                openDatabase(DBNAME, DBVERSION).then(async db => {
                    //let indexdb = IDBDatabase
                    /*
                    try {
                        await writeDir('/data/vsData', db)
                    } catch (err) {
                        console.warn("onmessage : writeDir warning, err=",err)
                    }
                    */
                    const sampleArrayBuffer = new Uint8Array(retvalue);
                    var path = "/data/vsData/" + tmpSha1 + ".dwg.gs"
                    GcadILog('onmessage : write path=', path);
                    await writeArrayBuffer(path, sampleArrayBuffer, db)
                    //db.close()
                    //indexdb = undefined
                    GcadILog('onmessage : start 333');

                    event.data.vsDataSha1 = tmplayout.sha1
                    event.data.vsDataSize = tmplayout.size
                    event.data.vsDataPath = path
                    GcadILog('onmessage : needReturnData=false event.data=', event.data);
                    postMessage(event.data);
                })
                    .catch(error => {
                        console.error('openDatabase 初始化失败:', error);
                    });
            }
        }

    }
    else if (event.data.command == "uploadModifyRecord" || event.data.command == "upload") {
        /*todo 提交增量 or 快照*/
        GcadILog('收到了命令event.data.command=', event.data.command);
        GcadILog('onmessage : start read indexdb');
        openDatabase(DBNAME, DBVERSION).then(async db => {
            var path = event.data.keyData
            var tmpArrayBuffer = await readArrayBuffer(path, db)
            GcadILog('onmessage : read path=', path);
            GcadILog('onmessage : start tmpArrayBuffer=', tmpArrayBuffer);
            /*
                var  JSElement=document.createElement("script");
                JSElement.setAttribute("type","text/javascript");
                JSElement.setAttribute("src","/assets/CloudCAD/crypto-js.min.js");
                GcadILog("JSElement=", JSElement);
                document.body.appendChild(JSElement);
                GcadILog("document=", document);
                
      
                var wordArray = CryptoJS.lib.WordArray.create(sampleArrayBuffer)
                var tmpSha1 = CryptoJS.SHA1(wordArray).toString()//计算
                GcadILog('onmessage : start tmpSha1=', tmpSha1); 
            */
            var form = new FormData()
            var blob = new Blob([tmpArrayBuffer]);
            if (event.data.command == "uploadModifyRecord") {
                form.append('fileId', event.data.fileId);
                form.append('etag', event.data.tmpSHA);
                form.append('baseId', event.data.baseId);
                form.append('file', blob);
            } else if (event.data.command == "upload") {
                form.append('folderId', event.data.folderId);
                form.append('etag', event.data.tmpSHA);
                form.append('file', blob); // 不传名称则会以blob为名
            }
            var inputUrl = event.data.reqHeaderUrl + needURL[event.data.command].url
            var inputMethod = needURL[event.data.command].method
            GcadILog('uploadModifyRecord : inputUrl=', inputUrl);
            GcadILog('uploadModifyRecord : inputMethod=', inputMethod);
            var retvalue = await postServerFormData(inputUrl, inputMethod, event.data.token, form);
            GcadILog('uploadModifyRecord : retvalue=', retvalue);
            //db.close() 
        })
            .catch(error => {
                console.error('upload openDatabase 初始化失败:', error);
            });
    }
    else if (event.data.command == "updateKeepAlive") {
        /*todo 更新 keepalive */
        GcadILog('收到了命令event.data.command=', event.data.command);
        GcadILog('onmessage : start read indexdb');
        openDatabase(DBNAME, DBVERSION).then(async db => {
            var timeFlag = new Date().format("yyyy-MM-dd hh:mm:ss.S");
            const encoder = new TextEncoder();
            var uint8Array = encoder.encode(timeFlag);
            var path = event.data.filePath;
            GcadILog("timeFlag=", timeFlag);
            GcadILog('onmessage : write path=', path);
            await rewriteArrayBuffer(path, uint8Array, db);
            db.close()
        })
            .catch(error => {
                console.error('updateKeepAlive openDatabase 初始化失败:', error);
            });

    }
    else if (event.data.command == "findFont") {
        /*todo findFont 怎么请求 怎么保存写入 怎么通知底层 */
        GcadILog('收到了命令event.data.command=', event.data.command);
        var fileId = 0;
        var size = 0;
        var storageid = 0;
        var fontFileSHA1 = "123456";
        var tmpReqParams = "?folderId=7&limit=1000000"; // 字体文件目录=7
        var version = 'V1'
        var tmpfontFileList = await requireServer(event.data.reqHeaderUrl, "getFontList", tmpReqParams, event.data.token)
        GcadILog('收到了getFontList消息: tmpfontFileList=', tmpfontFileList);

        var isFind = false
        for (var key in tmpfontFileList) {
            GcadILog("key=", key);
            GcadILog("tmpfontFileList[key].name=", tmpfontFileList[key].name)
            if (tmpfontFileList[key].name == event.data.fontName) {
                GcadILog("tmpfontFileList[key].id=", tmpfontFileList[key].id);
                isFind = true
                fileId = tmpfontFileList[key].id;
                fontFileSHA1 = tmpfontFileList[key].etag;
                version = tmpfontFileList[key].version;
                storageid = tmpfontFileList[key].sid;
                size = tmpfontFileList[key].size;
            }
        }
        GcadILog("findFont: fileId=", fileId)
        GcadILog("findFont: storageid = ", storageid)
        if (storageid == 0 && fileId == 0) {
            GcadILog("findFont: findFont failed");
            return new Promise((resolve, reject) => resolve(true));
        }
        else {
            GcadILog("findFont: fileId = ", fileId)
            var sampleArrayBuffer = new Uint8Array();
            sampleArrayBuffer = await getDownload(event.data.reqHeaderUrl, fileId, event.data.token, "getDownloadUrlDirect");
            GcadILog('findFont: start write indexdb');
            openDatabase(DBNAME, DBVERSION).then(async db => {
                //*
                //const sampleArrayBuffer = new Uint8Array(retvalue);
                var path = "/data/FontsServer/" + event.data.fontName
                GcadILog('findFont: write path=', path);
                await rewriteArrayBuffer(path, sampleArrayBuffer, db)
                db.close()
                GcadILog('findFont: start 333');
                event.data.fontSha1 = fontFileSHA1
                event.data.fontSize = size
                event.data.fontPath = path //*/
                GcadILog('findFont: event.data=', event.data);
                postMessage(event.data);
            })
            .catch(error => {
                console.error('findFont: openDatabase 初始化失败:', error);
            });
        }
    }
    else if (event.data.command == "findFileForOne") // 下载单个  废弃
    {
        GcadILog('收到了命令event.data.command=', event.data.command);
        var storageid = 0;
        var tmpReqParams = "?fileId=" + event.data.fileId + "&dependentFile=" + encodeURIComponent(event.data.dependentFile) + "&fop=" + event.data.fop;
        var tmpinfo = await requireServer(event.data.reqHeaderUrl, "findFile", tmpReqParams, event.data.token)
        GcadILog('收到了findFile消息: tmpinfo=', tmpinfo);
        storageid = tmpinfo.storageId;
        GcadILog("findFile: storageid = ", storageid)
        if (storageid == 0) {
            GcadILog("findFile: findFile failed");
            return new Promise((resolve, reject) => resolve(true));
        }
        else {
            var reqParams = "?storageId=" + storageid;
            var retvalue = await requireServer(event.data.reqHeaderUrl, "downloadByStorageId", reqParams, event.data.token)
            GcadILog('findFile: start write indexdb');
            openDatabase(DBNAME, DBVERSION).then(async db => {
                const sampleArrayBuffer = new Uint8Array(retvalue);
                var ext;
                var s = 0;
                var dfn = event.data.dependentFile;
                for (var ii = 0; ii < dfn.length; ii++) {
                    if (dfn[ii] == ".") {
                        s++;
                    } else {
                        break;
                    }
                }
                if (s > 0) {
                    dfn = dfn.substring(s);
                }
                var ind = dfn.lastIndexOf(".");
                if (ind != -1 && ind != dfn.length - 1) {
                    ext = dfn.substring(ind);
                } else {
                    ext = ".file";
                }
                var path = "/data/"
                // 注:dwg依赖文件放到vsData下; 其他依赖pdf png作为临时文件,放至tmp目录下
                if (ext == ".dwg") {
                    path = path + "vsData/" + tmpinfo.etag + ext
                } else {
                    path = path + "tmp/" + tmpinfo.etag + ext
                }
                GcadILog('findFile: write path=', path);
                await rewriteArrayBuffer(path, sampleArrayBuffer, db)
                db.close()
                GcadILog('findFile: start 333');
                var downloadList = []
                var tempdata = {}
                tempdata.source = event.data.dependentFile
                tempdata.fileSha = tmpinfo.etag
                tempdata.fileSize = tmpinfo.size
                tempdata.filePath = path
                downloadList.push(tempdata)
                GcadILog('findFile: event.data=', event.data);  //主js需要写入索引
                var tmpAllInfo = {}
                tmpAllInfo.command = "findFile"
                tmpAllInfo.taskId = event.data.taskId
                var data = {}
                data.findFileList = downloadList
                var ret = {}
                ret.data = data
                tmpAllInfo.ret = ret
                postMessage(tmpAllInfo);
            })
                .catch(error => {
                    console.error('findFile: openDatabase 初始化失败:', error);
                });
        }
    }
    else if (event.data.command == "findFiles") // 下载多个 串行 废弃
    {
        GcadILog('收到了命令event.data.command=', event.data.command);
        var depFileList = event.data.dependentFiles;
        GcadILog('收到了findFile消息:  depFileList=', depFileList);
        var downloadList = []
        GcadILog('收到了findFile消息:  depFileList.length=', depFileList.length);
        for (var i = 0; i < depFileList.length; i++) {
            GcadILog('收到了findFile消息:  depFileList[i]=', depFileList[i]);
            var storageid = 0;
            var tmpReqParams = "?fileId=" + event.data.fileId + "&dependentFile=" + encodeURIComponent(depFileList[i]) + "&fop=" + event.data.fop;
            var tmpinfo = await requireServer(event.data.reqHeaderUrl, "findFile", tmpReqParams, event.data.token)
            GcadILog('收到了findFile消息: tmpinfo=', tmpinfo);
            storageid = tmpinfo.storageId;
            GcadILog("收到了findFile消息: storageid = ", storageid)
            GcadILog('收到了findFile消息:  depFileList[i]=', depFileList[i]);
            var dealFile = depFileList[i];
            if (storageid == 0) {
                GcadILog("findFile: findFile failed");
                //return new Promise((resolve, reject) => resolve(true));
                GcadILog('收到了命令event.data.command storageid == 0 dealFile=', dealFile);
                var data = {}
                data.source = dealFile
                data.fileSha = ""
                data.fileSize = ""
                data.filePath = ""
                downloadList.push(data)
                GcadILog('收到了命令event.data.command dealFile=', dealFile);
                GcadILog('收到了命令event.data.command downloadList=', downloadList);
                if (downloadList.length == depFileList.length) {
                    var tmpAllInfo = {}
                    tmpAllInfo.command = "findFile"
                    tmpAllInfo.taskId = event.data.taskId
                    var data = {}
                    data.findFileList = downloadList
                    var ret = {}
                    ret.data = data
                    tmpAllInfo.ret = ret
                    postMessage(tmpAllInfo);
                    GcadILog('收到了findFile消息: do end.');
                }
            }
            else {
                GcadILog('收到了findFile消息:  storageid != 0 dealFile=', dealFile);
                var reqParams = "?storageId=" + storageid;
                var retvalue = await requireServer(event.data.reqHeaderUrl, "downloadByStorageId", reqParams, event.data.token)
                GcadILog('findFile: start write indexdb');
                openDatabase(DBNAME, DBVERSION).then(async db => {
                    const sampleArrayBuffer = new Uint8Array(retvalue);
                    var ext;
                    var dfn = dealFile;
                    var ind = dfn.lastIndexOf(".");
                    if (ind != -1 && ind != dfn.length - 1) {
                        ext = dfn.substring(ind);
                    }
                    else {
                        ext = ".file";
                    }
                    var path = "/data/"
                    // 注:dwg依赖文件放到vsData下; 其他依赖pdf png作为临时文件,放至tmp目录下
                    if (ext == ".dwg") {
                        path = path + "vsData/" + tmpinfo.etag + ext
                    } else {
                        path = path + "tmp/" + tmpinfo.etag + ext
                    }
                    GcadILog('findFile: write path=', path);
                    await rewriteArrayBuffer(path, sampleArrayBuffer, db)
                    db.close()
                    var data = {}
                    data.source = dealFile
                    data.fileSha = tmpinfo.etag
                    data.fileSize = tmpinfo.size
                    data.filePath = path
                    downloadList.push(data)
                    GcadILog('收到了findFile消息: dealFile=', dealFile);
                    GcadILog('收到了findFile消息: downloadList=', downloadList);
                    if (downloadList.length == depFileList.length) {
                        var tmpAllInfo = {}
                        tmpAllInfo.command = "findFile"
                        tmpAllInfo.taskId = event.data.taskId
                        var data = {}
                        data.findFileList = downloadList
                        var ret = {}
                        ret.data = data
                        tmpAllInfo.ret = ret
                        postMessage(tmpAllInfo);
                        GcadILog('收到了findFile消息: do end.');
                    }
                })
                    .catch(error => {
                        console.error('收到了findFile消息:  openDatabase 初始化失败:', error);
                    });
            }
        }
    }
    else if (event.data.command == "findFile") // 下载多个 并行
    {
        GcadILog('收到了命令event.data.command=', event.data.command);
        GcadILog('收到了命令event.data.dependentFiles=', event.data.dependentFiles);
        var findFileList = event.data.dependentFiles;
        var reqParamsList = []
        for (var i = 0; i < findFileList.length; i++) {
            reqParamsList[i] = "?fileId=" + event.data.fileId + "&dependentFile=" + encodeURIComponent(findFileList[i]) + "&fop=" + event.data.fop;
        }
        openDatabase(DBNAME, DBVERSION).then(async db => {
            //GcadILog('收到了命令event.data.command=findFiles, reqParamsList=', reqParamsList);
            var tmpbarrier = startAsyncDownloadList(reqParamsList, event.data.reqHeaderUrl, event.data.token, event.data.taskId, db, 3);
            await Promise.all(tmpbarrier).then((res) => {
                GcadILog('收到了命令event.data.command=findFiles, 执行end res=', res);
            }).catch((error) => {
                GcadILog('收到了命令event.data.command=findFiles, 执行catch');
            });
        })
            .catch(error => {
                console.error('findFiles: openDatabase 初始化失败:', error);
            });
    }
    else if (event.data.command == "findFileWithDepends") // 下载多个以及依赖 并行
    {
        GcadILog('收到了命令event.data.command=', event.data.command);
        GcadILog('收到了命令event.data.dependentFiles=', event.data.dependentFiles);
        if (event.data.dependentFiles.length <= 0) {
            return;
        }
        openDatabase(DBNAME, DBVERSION).then(async db => {
            var result=[];
            var findFileList = [];
            var tmpAllInfo = {}
            var data = {}
            data.findFileList = findFileList
            data.result=result;
            var ret = {}
            ret.data = data
            tmpAllInfo.ret = ret
            GcadILog("exeWithRetry postmessage=")

            for(var i=0;i<event.data.dependentFiles.length;i++) {
              var dependentFile = event.data.dependentFiles[i];
              var fileInfo = "?fileId=" + event.data.fileId + "&dependentFile=" + encodeURIComponent(dependentFile) + "&fop=" + event.data.fop;
              await exeWithRetry(findFile, event.data.reqHeaderUrl, fileInfo, event.data.token, db, 3).then((res) => {
                  res.originalDependentFile=event.data.originalDependentFiles[i];
                  findFileList.push(res);
                  result.push(res);
                  GcadILog('收到了命令event.data.command=findFiles, 执行end res=', res);
              }).catch((error) => {
                  GcadILog('收到了命令event.data.command=findFiles, 执行catch');
              });
            }
            tmpAllInfo.command = "findFile";
            tmpAllInfo.taskId=event.data.taskId;
            data.reqHeaderUrl = event.data.reqHeaderUrl;
            data.token = event.data.token;
            data.fileId = event.data.fileId;
            data.fop = event.data.fop;
            data.needCheckDepends = true;
            postMessage(tmpAllInfo);
        })
            .catch(error => {
                console.error('findFiles: openDatabase 初始化失败:', error);
            });
    }
    else if (event.data.command == "finishFindFileWithDepends") // 下载多个以及依赖 并行
    {
        GcadILog('收到了命令event.data.command=', event.data.command);
        GcadILog('收到了命令event.data.dependentFiles=', event.data.dependentFiles);

        openDatabase(DBNAME, DBVERSION).then(async db => {
            const oldData = event.data.ret.data;
            var downloadList = [];
            var tmpAllInfo = {}

            var data = {}
            data.findFileList = downloadList
            var ret = {}
            ret.data = data
            tmpAllInfo.ret = ret

            tmpAllInfo.taskId = event.data.taskId
            data.fileId=oldData.fileId;
            data.fop=oldData.fop;
            data.reqHeaderUrl=oldData.reqHeaderUrl;
            data.token=oldData.token;

            data.result=oldData.result;

            tmpAllInfo.command = "findFileWithDepends"
            GcadILog("exeWithRetry postmessage=")

            var depends = event.data.depends;
            if (depends.length == 0) {
                data.needCheckDepends = false;
                postMessage(tmpAllInfo);
            } else {
                var reqParamsList = []
                for (var i = 0; i < depends.length; i++) {
                    reqParamsList[i] = "?fileId=" + oldData.fileId + "&dependentFile=" + encodeURIComponent(depends[i]) + "&fop=" + oldData.fop;
                }
                reqParamsList.map(async (fileInfo) => {
                    exeWithRetry(findFile, oldData.reqHeaderUrl, fileInfo, oldData.token, db, 3).then((res) => {
                        downloadList.push(res);
                        data.result.push(res);
                        if (downloadList.length == reqParamsList.length) {
                            data.needCheckDepends = true;
                            postMessage(tmpAllInfo);
                        }
                    });
                });
            }
        })
            .catch(error => {
                console.error('findFiles: openDatabase 初始化失败:', error);
            });
    }
    else if (event.data.command == "getCharData") {
        /*单机测试使用 返回*/
        /*
        event.data.ret = "ok"
        event.data.cfDataSha1 = "3123123"
        event.data.cfDataSize = "34234234"
        event.data.cfDataPath = "/data/a/b/c.cf"
        GcadILog('onmessage : needReturnData=false event.data=', event.data); 
        postMessage(event.data); 
        return;
        */
        /*
           todo 
           1. 先获取字体文件的fileID与sha1                 ------getFontList
              然后组成key，获取cf/catalog;                 ------getKVMeta
              然后解析获取字模数据的StorageID，再下载；     ------downloadByStorageId
           2. 若没有catalog，则需要触发字模转换；           ------postTask
              然后等待转换完成。                            ------getMeta
    
           为什么key不用名称构成，避免不同版本字体文件，名称相同内容不同；
        */
        var fileId = 123456;
        var fontFileSHA1 = "123456";
        var tmpReqParams = "?folderId=7&limit=1000000"; // 字体文件目录=7
        var version = 'V1'
        //var version = event.data.reqUrlData.version; 
        var tmpfontFileList = await requireServer(event.data.reqHeaderUrl, "getFontList", tmpReqParams, event.data.token)
        GcadILog('收到了getFontList消息: tmpfontFileList=', tmpfontFileList);

        var isFind = false
        for (var key in tmpfontFileList) {
            GcadILog("key=", key);
            GcadILog("event.data.reqUrlData.fontName=", event.data.reqUrlData.fontName)
            if (tmpfontFileList[key].name == event.data.reqUrlData.fontName) {
                GcadILog("tmpfontFileList[key].id=", tmpfontFileList[key].id);
                isFind = true
                fileId = tmpfontFileList[key].id;
                fontFileSHA1 = tmpfontFileList[key].etag;
                version = tmpfontFileList[key].version;
            }
        }
        GcadILog("fileId=", fileId)
        if (isFind == false) {
            GcadILog("getCharData: failed, not find font file.");
            return new Promise((resolve, reject) => resolve(true));
        }

        var reqkey = fileId + '/' + version + '/' + fontFileSHA1 + "/cf/catalog/" + event.data.reqUrlData.fontName + '/' + event.data.reqUrlData.requireStr

        GcadILog("getCharData:reqkey=", reqkey);

        reqkey = "4145/V1/9a8dbf807ec5bab6cea851fb67c8931755f8cec6/cf/catalog/仿宋_GB2312.ttf/a"  // test
        var reqParams = "?key=" + reqkey;
        var metaObj = await requireServer(event.data.reqHeaderUrl, "getKVMeta", reqParams, event.data.token)
        GcadILog("getCharData: metaObj = ", metaObj)
        var storageid = 0
        if (metaObj && metaObj.value == '') {
            GcadILog("getCharData: get cfcatalog failed, need run MakeCharData.");
            // todo 没有获取sid，需触发任务，并轮询任务结束，再次获取sid
            var typeTask = "MakeCharData";
            var config = "{}";
            config.fontName = event.data.reqUrlData.fontName;
            config.requiredStr = event.data.reqUrlData.requiredStr;
            var callback = "";
            reqParams = "?callback=" + callback + "&config=" + config + "&fileId=" + fileId + "&type=" + typeTask + "&version=" + version;
            var ret = await requireServer(event.data.reqHeaderUrl, "postTask", reqParams, event.data.token);
            var metaKey = 'gcad2d_font_chardata';
            await sleep(waitTime);
            reqParams = "?fileId=" + fileId + "&metaKey=" + metaKey;
            var retStatus = await requireServer(event.data.reqHeaderUrl, "getMeta", reqParams, event.data.token);
            for (var i = 0; i <= reqNum && retStatus != kEnumTaskFinished; i++) {
                await sleep(waitTime);
                GcadILog("requireServer getMeta i=", i);
                retStatus = await requireServer(event.data.reqHeaderUrl, "getMeta", reqParams, event.data.token);
            }
            if (retStatus != kEnumTaskFinished) {
                GcadILog("getCharData failed");
                return new Promise((resolve, reject) => resolve(true));
            }
            reqParams = "?key=" + reqkey;
            metaObj = await requireServer(event.data.reqHeaderUrl, "getKVMeta", reqParams, event.data.token)
            if (metaObj && metaObj.value == '') {
                GcadILog("getCharData: get gcad2d_font_chardata and cfcatalog failed.");
                return new Promise((resolve, reject) => resolve(true));
            }
        }

        if (!metaObj) {
            GcadILog("getCharData failed.")
            return new Promise((resolve, reject) => resolve(true));
        } else {
            var tmpjson = JSON.parse(metaObj.value)
            GcadILog("tmpjson = ", tmpjson)
            //var tmpjson = tmpjson.layouts[0]
            GcadILog("tmpjson.storageId = ", tmpjson.storageId)
            storageid = tmpjson.storageId
            var tmpSha1 = tmpjson.sha1
            reqParams = "?storageId=" + storageid;
            var retvalue = await requireServer(event.data.reqHeaderUrl, "downloadByStorageId", reqParams, event.data.token)
            var localWriteIndexdb = event.data.needReturnData
            if (localWriteIndexdb) {
                // let tmpBuf = new Uint8Array(retvalue);
                event.data.vsDataSha1 = tmpjson.sha1
                event.data.vsDataSize = tmpjson.size
                event.data.ret = retvalue
                GcadILog('onmessage : needReturnData=true event.data=', event.data);
                postMessage(event.data);
            } else {
                GcadILog('onmessage : start write indexdb');
                //let indexdb = IDBDatabase
                openDatabase(DBNAME, DBVERSION).then(async db => {
                    //let indexdb = IDBDatabase
                    /*
                    try {
                        await writeDir('/data/cfData', db)
                    } catch (err) {
                        console.warn("onmessage : writeDir warning, err=",err)
                    }
                    */
                    const sampleArrayBuffer = new Uint8Array(retvalue);
                    var path = "/data/cfData/" + event.data.reqUrlData.fontName + '-' + event.data.reqUrlData.requireStr + ".cf"
                    GcadILog('onmessage : write path=', path);
                    await rewriteArrayBuffer(path, sampleArrayBuffer, db)
                    //db.close()
                    //indexdb = undefined
                    GcadILog('onmessage : start 333');

                    event.data.cfDataSha1 = tmpjson.sha1
                    event.data.cfDataSize = tmpjson.size
                    event.data.cfDataPath = path
                    GcadILog('onmessage : needReturnData=false event.data=', event.data);
                    postMessage(event.data);
                })
                    .catch(error => {
                        console.error('openDatabase 初始化失败:', error);
                    });
            }
        }
    }
    else if (event.data.command == "findThumbs") {
        var reqParams = "?fileId=" + event.data.fileId +"&metaKey=gcad2dthumb";
        var metaObj = await requireServer(event.data.reqHeaderUrl, "getMeta", reqParams, event.data.token)
        GcadILog("getAndStorageVsData: metaObj = ", metaObj)
        
        var res=[];
        if (metaObj) {
            GcadILog("metaObj = ", (typeof metaObj));
            var metaObj =JSON.parse(metaObj);
            var tmpjson=metaObj.metaInfo;
            GcadILog("tmpjson = ",tmpjson);
            if(tmpjson instanceof Array)
            {
                if(tmpjson.length>0)
                {
                    var lastHis=tmpjson[tmpjson.length-1];
                    GcadILog("lastHis = ",lastHis);

                    var his=lastHis.history;
                    var keys=Object.keys(his);
                    GcadILog("keys = ",keys);
                    var mLength = keys.length
                    if(mLength>0){
                      var obj=his[keys[keys.length-1]];
                      GcadILog(obj);
                      if(obj.image) {
                        res.push(obj.image["64x48"].storageId);
                        res.push(obj.image["128x96"].storageId);
                        res.push(obj.image["256x192"].storageId);
                      }
                    }
                }
            }
        }
        GcadILog(res);

        var tmpAllInfo = {};
        tmpAllInfo.command = event.data.command;
        tmpAllInfo.taskId = event.data.taskId;
        var data = {};
        data.fileId=event.data.fileId;
        data.reqHeaderUrl = event.data.reqHeaderUrl;
        data.token = event.data.token;
        data.findThumbsList = res;
        var ret = {};
        ret.data = data;
        tmpAllInfo.ret = ret;
        postMessage(tmpAllInfo);
    }
    else if (event.data.command == "testDownloadFile") {
        GcadILog('收到了命令event.data.command=', event.data.command);
        GcadILog('收到了命令event.data.fileName=', event.data.fileName);
        var path = event.data.fileName
        openDatabase(DBNAME, DBVERSION).then(async db => {
            var tmpArrayBuffer = await readArrayBuffer(path, db)
            //const buffer = event.target.result.contents.buffer
            // 可以提交给服务端

            let url = URL.createObjectURL(new Blob([tmpArrayBuffer], { type: "arraybuffer" }))
            const link = document.createElement('a');
            link.style.display = 'none';
            link.href = url;
            link.setAttribute('download', 'data');
            //document.body.appendChild(link);
            link.click();
            //document.body.removeChild(link);
        })
            .catch(error => {
                console.error('upload openDatabase 初始化失败:', error);
            });

    }
    else if (event.data.command == "testSetLogLevel") {
        logLevel = event.data.level
        GcadILog('收到了命令event.data.command=', event.data.command);
    }
    else if (event.data.command == "SetEncryptMode") {
        encryptMode = event.data.mode
        GcadILog('收到了命令event.data.command=', event.data.command);
    }
});