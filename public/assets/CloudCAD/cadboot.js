const GCAD_DBG_DEFAULT_GRADE = 1
const GCAD_DBG_INFO_GRADE = 2
const GCAD_DBG_DEBUG_GRADE = 3
const GCAD_DBG_WARN_GRADE = 4
const GCAD_DBG_ERROR_GRADE = 5

const GCAD_LOCALFS_STORAGE_TYPE = 1        // Localfs_StoraeType
const GCAD_OSS_ALI_STORAGE_TYPE = 2        // Oss_Aliyun_StorageType CloudStorage_StoraeType
const GCAD_OSS_AWS_STORAGE_TYPE = 3        // Oss_aws_StorageType

function GcadLog(level, strLog, params) {
    if (typeof Module.cfg_param !== "undefined" && typeof Module.cfg_param.document !== "undefined" && typeof Module.cfg_param.document.logLevel !== "undefined") {
        if (Module.cfg_param.document.logLevel == GCAD_DBG_DEFAULT_GRADE
            || Module.cfg_param.document.logLevel == GCAD_DBG_INFO_GRADE
            || (Module.cfg_param.document.logLevel == GCAD_DBG_DEBUG_GRADE && (level == "ERROR" || level == "WARN" || level == "DEBUG"))
            || (Module.cfg_param.document.logLevel == GCAD_DBG_WARN_GRADE && (level == "ERROR" || level == "WARN"))
            || (Module.cfg_param.document.logLevel == GCAD_DBG_ERROR_GRADE && level == "ERROR")) {
            if (arguments.length === 2) {
                console.log(strLog);
            }
            else {
                console.log(strLog, params);
            }
        }
    }
    else {
        //console.log("Module.document.logLevel undefined, 不输出日志.");
    }
};
function GcadILog(strLog, params) {
    if (arguments.length === 2) {
        GcadLog("INFO", "INFO: " + strLog, params);
    }
    else {
        GcadLog("INFO", "INFO: " + strLog);
    }
}
function GcadDLog(strLog, params) {
    if (arguments.length === 2) {
        GcadLog("DEBUG", "DEBUG: " + strLog, params);
    }
    else {
        GcadLog("DEBUG", "DEBUG: " + strLog);
    }
}
function GcadWLog(strLog, params) {
    if (arguments.length === 2) {
        GcadLog("WARN", "WARN: " + strLog, params);
    }
    else {
        GcadLog("WARN", "WARN: " + strLog);
    }
}
function GcadELog(strLog, params) {
    if (arguments.length === 2) {
        GcadLog("ERROR", "ERROR: " + strLog, params);
    }
    else {
        GcadLog("ERROR", "ERROR: " + strLog);
    }
}

function MemoryPool() {
    this.Mem = [];
    this.FreeMemory = function () {
        this.Mem.forEach(e => {
            Module._free(e.begin);
        });
        this.Mem = []
    };
    this.allocUTF8_to_cpp = function (s) {
        let len = lengthBytesUTF8(s)
        //if(this.Mem.length == 0)
        {
            this.Mem[this.Mem.length] = m = { begin: Module._malloc(len + 5), free: 0 };
            HEAP32[m.begin >> 2] = len;
            HEAP8[m.begin + len + 4] = 0;
            stringToUTF8(s, m.begin + 4, len + 1);
            return m.begin;
        }
    };
    this.alloc = function (v, s) {
        if (s == 'I') {
            this.Mem[this.Mem.length] = m = { begin: Module._malloc(8), free: 0 };
            if (typeof v == "number") {
                if (v > 0)
                    HEAP32[m.begin >> 2] = v
            }
            else if (typeof v == "number") {
                HEAP32[m.begin >> 2] = v
            }

        }
    };
    this.allocUInt64 = this.allocInt64 = function (v) {
        let l = 0, h = 0;
        if (Array.isArray(v)) {
            l = (v.length > 0) ? v[0] : 0;
            h = (v.length > 1) ? v[1] : 0;
        }
        else if (typeof v == "number") {
            if (v >= 0) {
                h = (v / 0x100000000) >>> 0;
                l = (v % 0x100000000) >>> 0;
            } else {
                if (v < (0x100000000 * -1)) {
                    let v1 = v * -1;
                    h = 0x100000000 - (v1 / 0x100000000);
                    l = 0x100000000 - (v1 % 0x100000000);
                }
                else {
                    h = 0xFFFFFFFF;
                    l = v >>> 0;
                }

            }
        }
        this.Mem[this.Mem.length] = m = { begin: Module._malloc(8), free: 0 };
        HEAP32[m.begin >> 2] = l;
        HEAP32[((m.begin >> 2) + 1)] = h;
        return m.begin
    };
    this.allocVariant = function (v) {
        this.Mem[this.Mem.length] = m = { begin: Module._malloc(24), free: 0 };
        if (typeof (v) == 'number') {//数字
            HEAP16[m.begin >>> 1] = 'F'.charCodeAt(0); HEAP16[(m.begin >>> 1) + 1] = 0;
            HEAPF64[(m.begin + 4) >>> 3] = v + 0.0;
        }
        else if (typeof (v) == 'string') {//字符串
            HEAP16[m.begin >>> 1] = 's'.charCodeAt(0); HEAP16[(m.begin >>> 1) + 1] = 0;
            HEAPU32[(m.begin + 4) >>> 2] = this.allocUTF8_to_cpp(v) >>> 0;
        }
        return m.begin
    }
    this.recordMemory = function (ptr) {
        this.Mem.push({ begin: (ptr >> 0), free: 0 })
    }
};
var Module = {
    GcJsBinaryToJson: function (param) {
        // Start GcJsBinaryToJson
        var pos = 0;
        var jsonRoot = {};

        tmp = param.slice(pos, pos += 1);
        if (tmp[0] != 0xE0) {
            GcadILog("Is Not Binary Packet .. Error ");
            return;
        }
        GcJsReadBinary(param, jsonRoot, null);
        GcadILog("JsonRoot->", JSON.stringify(jsonRoot));

        return jsonRoot;
        // End GcJsBinaryToJson

        function GcJsReadBinary(param, pJson, keyName) {

            type = GcJsType(param.slice(pos, pos += 2));
            if (pos >= param.length) {
                return;
            }

            if (type == 1) //array
            {
                if (keyName != null) {
                    pJson[keyName] = [];
                }
                let len = GcJsByteToInt32(param.slice(pos, pos += 4));
                for (let i = 0; i < len; i++) {
                    GcJsReadBinary(param, pJson[keyName], i);
                }
            } else if (type == 2) //Object
            {
                if (keyName != null) {
                    pJson[keyName] = {};
                }

                let len = GcJsByteToInt32(param.slice(pos, pos += 4));

                for (let i = 0; i < len; i++) {
                    let lName = GcJsByteToInt32(param.slice(pos, pos += 4));

                    let name = GcJsByteToString(param.slice(pos, pos += lName));

                    if (keyName != null) {
                        GcJsReadBinary(param, pJson[keyName], name);
                    } else {
                        GcJsReadBinary(param, pJson, name);
                    }
                }

            } else if (type == 4) //string
            {
                let lName = GcJsByteToInt32(param.slice(pos, pos += 4));
                pJson[keyName] = GcJsByteToString(param.slice(pos, pos += lName));

            } else if (type == 8) //bool
            {
                if (GcJsByteToBool(param.slice(pos, pos += 8)) == 1) pJson[keyName] = true;
                else pJson[keyName] = false;
            } else if (type == 16) //int64
            {
                pJson[keyName] = GcJsByteToInt64(param.slice(pos, pos += 8));
            } else if (type == 32) //double
            {
                pJson[keyName] = GcJsByteToDouble64(param.slice(pos, pos += 8));
            } else // others use type == 16 int64
            {
                pJson[keyName] = GcJsByteToInt64(param.slice(pos, pos += 8));
            }

        }

        function GcJsByteToInt16(param) {
            if (param.length == 2) {
                return new Int16Array(param.buffer)[0];
            }
            return 0;
        }

        function GcJsByteToInt32(param) {
            if (param.length == 4) {
                return new Int32Array(param.buffer)[0];
            }
            return 0;
        }

        function GcJsByteToInt64(param) {
            if (param.length == 8) {
                return Number((new BigInt64Array(param.buffer))[0]);
            }
            return 0;
        }

        function GcJsByteToBool(param) {
            if (param.length == 8) {
                return Number((new BigInt64Array(param.buffer))[0]);
            }
            return 0;
        }

        function GcJsByteToDouble64(param) {
            if (param.length == 8) {
                return new Float64Array(param.buffer)[0];
            }
            return 0.0;
        }

        function GcJsByteToString(param) {
            if (param.length != 0) {
                const decoder = new TextDecoder('utf-8');
                return decoder.decode(param.buffer);
            }
            return "";
        }

        function GcJsType(param) {
            return GcJsByteToInt16(param);
        }
    },

    GcJsJsonToBinary: function (param) {
        //--Start GcJsJsonToBinary
        var binary = [];
        binary.push(0xE0);
        GcJsWriteBinary(param);
        //下面代码需根据具体情况在外层调用 生成了二进制，准备调用wasm接口 记得free
        var BuffU8 = new Uint8Array(binary);
        var pHeap;
        try {
            pHeap = Module._malloc(BuffU8.length * BuffU8.BYTES_PER_ELEMENT);
            Module.HEAP8.set(BuffU8, pHeap);

            //Module._jsBinaryJson(pHeap, BuffU8.length);
            //GcadILog(BuffU8);
        }
        finally {
            Module._free(pHeap);
        }

        //--End GcJsJsonToBinary

        function GcJsWriteBinary(param) {
            if (typeof (param) === 'object') {
                if (GcJsObjectIs(param) === true) //array
                {
                    GcJsInt16ToByte(1);
                    let len = param.length;
                    GcJsInt32ToByte(len);
                    for (let i = 0; i < len; i++) {
                        GcJsWriteBinary(param[i]);
                    }
                }
                else //object
                {
                    GcJsInt16ToByte(2);
                    const keys = Object.keys(param);
                    GcJsInt32ToByte(keys.length);
                    for (let i = 0; i < keys.length; i++) {
                        GcJsStringToByte(keys[i]);
                        GcJsWriteBinary(param[keys[i]]);
                    }
                }
            }
            else if (typeof (param) === 'number') {
                let res = GcJsNumberIs(param);
                if (res === 'int') //int
                {
                    GcJsInt16ToByte(16);
                    GcJsInt64ToByte(BigInt(param))
                }
                else if (res === 'double') //double
                {
                    GcJsInt16ToByte(32);
                    GcJsDoubleToByte(param);
                }
            }
            else if (typeof (param) === 'boolean') {
                GcJsInt16ToByte(8);
                GcJsInt64ToByte(BigInt(param))
            }
            else if (typeof (param) === 'string') //string
            {
                GcJsInt16ToByte(4);
                GcJsStringToByte(param);
            }
        }

        function GcJsInt16ToByte(param) {
            let intArray = new Int16Array(1);
            intArray[0] = param;
            let bytes = new Uint8Array(intArray.buffer);
            binary.push(...bytes);
        }

        function GcJsInt32ToByte(param) {
            let intArray = new Int32Array(1);
            intArray[0] = param;
            let bytes = new Uint8Array(intArray.buffer);
            binary.push(...bytes);
        }

        function GcJsInt64ToByte(param) {
            let intArray = new BigInt64Array(1);
            intArray[0] = param;
            let bytes = new Uint8Array(intArray.buffer);
            binary.push(...bytes);
        }

        function GcJsDoubleToByte(param) {
            let doubleArray = new Float64Array(1);
            doubleArray[0] = param;
            let bytes = new Uint8Array(doubleArray.buffer);
            binary.push(...bytes);
        }

        function GcJsStringToByte(param) {
            const encoder = new TextEncoder();
            const bytes = encoder.encode(param);
            GcJsInt32ToByte(bytes.length);
            binary.push(...bytes);
        }

        function GcJsNumberIs(param) {
            var intPatt = /^\d+$/;
            var doublePatt = /^\d+\.\d+$/;
            if (intPatt.test(param)) {
                return "int";
            } else if (doublePatt.test(param)) {
                return "double";
            } else {
                return null;
            }
        }

        function GcJsObjectIs(param) {
            if (param.concat) {
                return true; // Array
            } else {
                return false; // Object
            }
        }
    },

    last_async: [],
    asyncExec: function (func) {
        let f = func;
        this.last_async.push(f);
        this._jsExecAsyncFuntion();
    },
    _do_async: function () {
        while (this.last_async.length > 0) {
            let func = this.last_async[Module.last_async.length - 1];
            this.last_async.pop();
            func();
        }
    },
    __object_properties: {},
    createCppString: function (str) {
        return { ptr: stringToNewUTF8(str), deleteObject: function () { Module._free(this.ptr); this.ptr = null; }, getPtr() { return this.ptr; } }
    },
    __toArg: function (v, s, pool) {
        if (s == 'b') //bool
        {
            return v ? 1 : 0;
        }
        else if (s == 'i') //int32
        {
            return v + 0.0;
        }
        else if (s == 'I')//int64
        {
            return pool.allocInt64(v) + 0.0;
        }
        else if (s == 'u') //uint32
        {
            return (v >>> 0) + 0.0;
        }
        else if (s == 'U')//uint64
        {
            return pool.allocUInt64(v) + 0.0;
        }
        else if (s == 'f')//float
        {
            return v + 0.0;
        }
        else if (s == 'F')//float
        {
            return v + 0.0;
        }
        else if (s == 's')//string
        {
            return pool.allocUTF8_to_cpp(v) + 0.0;
        }
        else if (s == 'C')//底层对象的指针
        {
            return (v >> 0) + 0.0;//指针
        }
        else if (s == 'V')//变体类型
        {
            return pool.allocVariant(v);
        }
        return undefined;
    },
    to_js_obj: function (cppResult, s) {
        if (s == 'i') //int32
        {
            return cppResult;
        }
        else if (s == 'I' || s == 'U')//int64
        {
            let d = [HEAP32[cppResult >> 2], HEAP32[(cppResult >> 2) + 1]];
            Module._free(cppResult);
            return d;
        }
        else if (s == 'u') //uint32
        {
            return cppResult >>> 0;
        }
        else if (s == 'f')//float
        {
            return v + 0.0;
        }
        else if (s == 'F')//float
        {
            return v + 0.0;
        }
        else if (s == 's')//string
        {
            let t = Module.UTF8ToString((cppResult >> 0) + 4, HEAP32[cppResult >> 2]);
            Module._free(cppResult);
            return t;
        }
        else if (s == 'o')//JsonObject
        {
            let t = Module.UTF8ToString((cppResult >> 0) + 4, HEAP32[cppResult >> 2]);
            let obj = JSON.parse(Module.UTF8ToString(t));
            Module._free(cppResult);
            return obj;
        }
        else if (s == 'V')//变体类型
        {
            let subtype = String.fromCharCode(HEAP8[cppResult]);
            let isArray = (HEAP8[cppResult + 1] != 0);
            let ret = 0;
            if (isArray) {//是数组
                let len = HEAPU32[(cppResult + 4) >>> 2];
                ret = [];
                let pos = HEAPU32[(cppResult + 8) >>> 2];
                for (let i = 0; i < len; i++) {
                    if (subtype == 'i') {
                        ret.push(HEAP32[pos >>> 2]); pos += 4;
                    }
                    else if (subtype == 'u') {
                        ret.push(HEAPU32[pos >>> 2]); pos += 4;
                    }
                    else if (subtype == 'f') {
                        ret.push(HEAPF32[pos >>> 2]); pos += 4;
                    }
                    else if (subtype == 'F') {
                        ret.push(HEAPF64[pos >>> 3]); pos += 8;
                    }
                    else if (subtype == 'I') {
                        ret.push([HEAP32[pos >>> 2], HEAP32[(pos >>> 2) + 1]]); pos += 8;
                    }
                    else if (subtype == 'U') {
                        ret.push([HEAPU32[pos >>> 2], HEAPU32[(pos >>> 2) + 1]]); pos += 8;
                    }
                    else if (subtype == 's') {
                        ret.push(Module.UTF8ToString(pos + 4, HEAPU32[pos >>> 2]));
                        Module._free(pos + 4);//释放字符串
                        pos += 8;
                    }
                    else if (subtype == 'C') {
                        ret.push(Module.bindCppObject(pos));
                        pos += 4;
                    }
                }
                Module._free(HEAPU32[(cppResult + 8) >>> 2]);//释放数组的内存                
            }
            else {
                let pos = cppResult + 4;
                if (subtype == 'i') {
                    ret = HEAP32[pos >>> 2];
                }
                else if (subtype == 'u') {
                    ret = HEAPU32[pos >>> 2];
                }
                else if (subtype == 'f') {
                    ret = HEAPF32[pos >>> 2];
                }
                else if (subtype == 'F') {
                    ret = HEAPF64[pos >>> 3];
                }
                else if (subtype == 'I') {
                    ret = [HEAP32[pos >> 2], HEAP32[(pos >> 2) + 1]];
                }
                else if (subtype == 'U') {
                    ret = [HEAPU32[pos >> 2], HEAPU32[(pos >> 2) + 1]];
                }
                else if (subtype == 's') {
                    ret = Module.UTF8ToString(pos + 4, HEAPU32[pos >>> 2]);
                    Module._free((cppResult >> 0) + 8);//释放字符串
                }
                else if (subtype == 'C') {
                    ret = Module.bindCppObject(pos);
                }
            }
            Module._free(cppResult);
            return ret;
        }
        else if (s == 'C')//底层对象的指针
        {
            return Module.bindCppObject(cppResult >>> 0);
        }
    },
    bindCppObject: function (c_obj) {
        if (c_obj == 0) return null;
        let properties = {};
        let _clsname = Module._getJsClassName(c_obj);
        let js_obj = {
            __native_c_obj: c_obj,
            get _c_obj() { return this.__native_c_obj; },
            get _c_cls() { return Module._getJsClassName(this.__native_c_obj); },
            deleteObject: function () { Module._releaseObject(this.__native_c_obj); this.__native_c_obj = 0; }
        };
        if (!Module.__object_properties.hasOwnProperty(_clsname)) {
            Module.__object_properties[_clsname] = this.gcad_getProperties(c_obj);
        }
        properties = Module.__object_properties[_clsname].properties;
        if (properties) {
            Object.defineProperties(js_obj, properties);
        }
        let method = Module.__object_properties[_clsname].method;
        for (let e in method) {
            js_obj[e] = method[e];
        }
        return js_obj;
    },
    gcad_getProperties: function (obj) {
        let rprop = Module._gcad_getProperties(obj);
        if (rprop) {
            prop = Module.to_js_obj(rprop, 's');
            GcadILog(prop)
            let properties_def = JSON.parse(prop);
            //properties = Module.__object_properties[clsname];       
            //let properties_def = {};"aaa":{name:"","type":1,"signature":"v_i_i_i","funcode":1},"aaa":{name:"","type":1,"signature":"v_i_i_i","funcode":1}};
            properties = {};
            method = {};
            let module = this;
            for (let e in properties_def) {
                let m = properties_def[e];
                if (m.hasOwnProperty("type") && m.type == 1) {
                    method[e] = function () {
                        //arguments;
                        var arr = m.signature.split(/_/);
                        var newArgs = [];
                        newArgs.push(this._c_obj);
                        newArgs.push(m.funcode);
                        let pool = new MemoryPool
                        for (i = 1; i < arr.length; i++) {
                            if (arr[i] != 'v') {
                                newArgs.push(Module.__toArg(arguments[i - 1], arr[i], pool));
                            }
                        }
                        let r = Module.to_js_obj(module["___invoke_" + m.signature].apply(this, newArgs), arr[0]);
                        pool.FreeMemory()
                        return r;
                    };
                }
                else {
                    properties[e] = {};
                    if (m.hasOwnProperty("set")) {//属性设置
                        properties[e]["set"] = function () {
                            //arguments;
                            let d = m.set
                            var arr = d.signature.split(/_/);
                            var newArgs = [];
                            newArgs.push(this._c_obj);
                            newArgs.push(d.funcode);
                            let pool = new MemoryPool
                            for (i = 1; i < arr.length; i++) {
                                if (arr[i] != 'v') {
                                    newArgs.push(Module.__toArg(arguments[i - 1], arr[i], pool));
                                }
                            }
                            let r = Module.to_js_obj(module["___invoke_" + d.signature].apply(this, newArgs), arr[0]);
                            pool.FreeMemory()
                        };
                    }
                    if (m.hasOwnProperty("get")) {//属性获取
                        properties[e]["get"] = function () {
                            //arguments;
                            let d = m.get
                            var arr = d.signature.split(/_/);
                            var newArgs = [];
                            newArgs.push(this._c_obj);
                            newArgs.push(d.funcode);
                            let pool = new MemoryPool
                            for (i = 1; i < arr.length; i++) {
                                if (arr[i] != 'v') {
                                    newArgs.push(Module.__toArg(arguments[i - 1], arr[i], pool));
                                }
                            }
                            let r = Module.to_js_obj(module["___invoke_" + d.signature].apply(this, newArgs), arr[0]);
                            pool.FreeMemory()
                            return r;
                        };
                    }
                }
            }
            return { "method": method, "properties": properties };
        }
        return { "method": {}, "properties": {} }
    },
    createClass: function (_clsname) {
        let pool = new MemoryPool
        let c_obj = this._createClass(Module.__toArg(_clsname, 's', pool));
        if (c_obj) {
            return Module.bindCppObject(c_obj);
        }
        return undefined;
    },
	instantiated: function() {
      return new Promise( (resolve, reject) => {
        FS.mkdir('/data');
        FS.mount(IDBFS, {}, '/data');
        FS.syncfs(true, function(err){
          if (err) {
            GcadILog("instantiated: syncfs failed.");
            Module.cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "instantiated: syncfs true failed.")
            reject(err);
          } else {
            GcadILog("instantiated: syncfs ok.\n");
            // gcadcloud.wasm引用ExeRootPath，非AppDataPath，JS层面不能修改;
            FS.symlink('/data/adinit.dat', '/adinit.dat');
            FS.symlink('/data/gcad.ldr', '/gcad.ldr');
            FS.symlink('/data/gcad.json', '/gcad.json');
            FS.symlink('/data/gcad.unt', '/gcad.unt');
            FS.symlink('/data/Support', '/Support');
            FS.symlink('/data/opt', '/opt');
            FS.symlink('/data/var', '/var');
            FS.symlink('/data/resposity', '/resposity');
            FS.symlink('/data/runtime', '/runtime');
            FS.symlink('/data/ico', '/ico');
            FS.symlink('/data/ico_dark', '/ico_dark');
            FS.symlink('/data/ico_gcad', '/ico_gcad');
            FS.symlink('/data/UserDataCache', '/UserDataCache');
            FS.symlink('/data/Fonts', '/Fonts');
            FS.symlink('/data/FontsServer', '/FontsServer');
            FS.symlink('/data/properties-xml-new', '/properties-xml-new');
            FS.syncfs(false,function(err){
              if (err) {
                GcadILog("instantiated: syncfs failed.\n");
                Module.cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "instantiated: syncfs false failed.")
                reject(err);
              } else {
                GcadILog("instantiated: syncfs ok.\n");
                resolve(err);
              }
            });
          }
        });
        GcadILog("instantiated end.\n");			
      });
    }
}

Module.GcadILog = GcadILog
Module.GcadDLog = GcadDLog
Module.GcadWLog = GcadWLog
Module.GcadELog = GcadELog

var GstarCADBoot = {
    start: (cfg_param, callback) => {
        GcadILog("application start.\n");
        checkNeed = (files, cfg_param) => {
            var notExitFiles = [];
            var fileIndexes = cfg_param.local_fileIndexes;
            var fs = cfg_param.FS;

            if (fileIndexes) {
                files.forEach(fileObj => {
                    var result = 0;
                    fileObj.path = cfg_param.variant_replace(fileObj.path).replace('//', '/');
                    if (cfg_param.local_existFile(fileObj.path)
                        && cfg_param.local_filesize(fileObj.path) == fileObj.size
                        && fileIndexes[fileObj.path]
                        && fileObj.sha1 == fileIndexes[fileObj.path].sha1) //文件路径、文件大小、索引的SHA1完全一致，才认为文件相同
                    {

                    }
                    else {
                        //GcadILog("Need renew file: fileObj.path= ", fileObj.path);
                        notExitFiles.push(fileObj);
                    }
                });
            }
            else {
                files.forEach(fileObj => {
                    var result = 0;
                    fileObj.path = cfg_param.path_tidy(cfg_param.variant_replace(fileObj.path));
                    notExitFiles.push(fileObj);
                });
            }
            return notExitFiles;
        };
        //开始一个并行任务,返回一个Promise(用于barrier)
        startAsyncTask = (task) => {

        };
        exeWithRetry = async (asyncFunc, fileObject, cfg, fetches, times) => {
            return new Promise(async (resolve, reject) => {
                reTryFunc = (times) => {
                    asyncFunc(fileObject, cfg, fetches).then((res) => {
                        resolve(res)
                    }).catch((err) => {
                        if (times > 0) {
                            setTimeout(() => {
                                //GcadILog("requesttWithRetry: path=", fileObject.path, " times=", times);
                                reTryFunc(times - 1)
                            })
                        } else {
                            GcadILog("下载文件：", fileObject.path, '失败!');
                            reject(err)
                        }
                    })
                };
                //GcadILog("requesttWithRetry: path=", fileObject.path, " times=", times);
                reTryFunc(times);
            })
        };

        unzip = async (zipData) => {
            const zip = new cfg_param.JSZip();
            const result = await zip.loadAsync(zipData);

            const files = [];
            result.forEach((relativePath, file) => {
                if (!file.dir) {
                    files.push({
                        name: file.name,
                        content: file.async('arraybuffer'),   // string text arraybuffer binarystring
                    });
                } else {
                }
            });
            return files;
        };
        downloadFile = (fileObject, cfg_param, fetches) => { //同步函数（下载文件）
            var local_sha1Index = cfg_param.local_sha1Index;
            var local_fileIndexes = cfg_param.local_fileIndexes;
            var fs = cfg_param.local_fs;
            let local_existFile = cfg_param.local_existFile;
            let local_filesize = cfg_param.local_filesize;
            let local_CopyFile = cfg_param.local_CopyFile;
            var remote_sha1Index = fetches;
            fileObject.path = cfg_param.path_tidy(fileObject.path);
            if (local_sha1Index && local_sha1Index[fileObject.sha1]
                && local_existFile(local_sha1Index[fileObject.sha1].path)
                && local_sha1Index[fileObject.sha1].size == local_filesize(local_sha1Index[fileObject.sha1].path)
                && local_sha1Index[fileObject.sha1].size == fileObject.size) {//本地是存在这个文件的,可以直接复制这个文件
                if (local_CopyFile(local_sha1Index[fileObject.sha1].path, fileObject.path)) {
                    return new Promise((resolve, reject) => resolve(true));
                }
            }

            if (remote_sha1Index.get(fileObject.sha1)) {
                if (typeof remote_sha1Index.get(fileObject.sha1) === "string") {//下载地址
                    let url = cfg_param.variant_replace(remote_sha1Index.get(fileObject.sha1));
                    return new Promise(async (resolve, reject) => {
                        try {
                            const response = await fetch(url)
                            if (response.ok) {
                                return response.arrayBuffer().then(async (data) => {
                                    GcadILog("downloadFile: data writefile=", fileObject.path);
                                    var tmpBuf = new Uint8Array(data);
                                    // 下载内容校验size
                                    var tmpSize = tmpBuf.length;
                                    if (tmpSize != fileObject.size) {
                                        GcadELog('downloadFile : size check error. fileObject=', fileObject);
                                        reject('downloadFile : size check error.')
                                    }
                                    cfg_param.mkdirTree(cfg_param.getPath(fileObject.path));
                                    cfg_param.fs.writeFile(fileObject.path, tmpBuf);//new DataView(data));
                                    local_sha1Index[fileObject.sha1] = fileObject;
                                    local_fileIndexes[fileObject.path] = fileObject;
                                    // load url file is zip/package, unzip and store to idbfs
                                    if (url.length >= 8 && url.substr(-8, 8) === ".package") {
                                        const files = await unzip(data);
                                        files.forEach((file) => {
                                            file.content.then(result => {
                                                var tmpfileObject = {}
                                                // zip 压缩打包了tmps1/tmps2名称，因此要substr(5)
                                                tmpfileObject.path = cfg_param.getPath(fileObject.path) + file.name.substr(5)
                                                var wordArray = cfg_param.CryptoJS.lib.WordArray.create(result)
                                                tmpfileObject.sha1 = cfg_param.CryptoJS.SHA1(wordArray).toString()//计算
                                                if (local_sha1Index && local_sha1Index[tmpfileObject.sha1]
                                                    && local_existFile(local_sha1Index[tmpfileObject.sha1].path)
                                                    && local_fileIndexes && local_fileIndexes[tmpfileObject.path]
                                                    && local_existFile(local_fileIndexes[tmpfileObject.path].path)
                                                    && local_sha1Index[tmpfileObject.sha1].path == tmpfileObject.path)   // 暂时规避软链接文件为空，不能读取问题;解决sha和path互换导致缓存刷新问题
                                                {
                                                    if (!local_existFile(tmpfileObject.path)) {
                                                        //GcadILog("downloadFile: data from zip file, file exit, path not exit, need copy.");
                                                        local_CopyFile(local_sha1Index[tmpfileObject.sha1].path, tmpfileObject.path)
                                                    } else {
                                                        GcadILog("downloadFile: data from zip file, file exit,not need write.");
                                                    }
                                                } else {
                                                    GcadILog("downloadFile: data from zip file, writefile=", tmpfileObject.path);
                                                    cfg_param.mkdirTree(cfg_param.getPath(tmpfileObject.path));
                                                    cfg_param.fs.writeFile(tmpfileObject.path, new Uint8Array(result));
                                                    local_sha1Index[tmpfileObject.sha1] = tmpfileObject;
                                                    local_fileIndexes[tmpfileObject.path] = tmpfileObject;
                                                }
                                            })
                                        });
                                    }
                                    await fs_sync(false, cfg_param.fs);
                                    resolve(true);
                                })
                            } else {
                                // 404, Not found
                                // 500, internal server error
                                GcadILog('downloadFile : fetch url=', url, ', response.status=', response.status);
                                reject(response)
                            }
                        } catch (error) {
                            GcadILog('downloadFile : fetch url=', url, ', error=', error);
                            reject(error)
                        }
                        GcadILog('downloadFile : fetch error.');
                        reject("downloadFile : fetch error.")
                    });
                }
                else {
                    return new Promise((resolve, reject) => {
                        setTimeout(async () => {
                            let ok = false;
                            for (let i = 0; !ok && i < remote_sha1Index.get(fileObject.sha1).length; i++) {
                                let url = cfg_param.variant_replace(remote_sha1Index.get(fileObject.sha1)[i]);
                                try {
                                    const response = await fetch(url)
                                    if (response.ok) {
                                        return response.arrayBuffer().then(async (data) => {
                                            cfg_param.mkdirTree(cfg_param.getPath(fileObject.path));
                                            cfg_param.fs.writeFile(fileObject.path, data);
                                            local_sha1Index[fileObject.sha1] = fileObject;
                                            local_fileIndexes[fileObject.path] = fileObject;
                                            ok = true;
                                            resolve(true);
                                        })
                                    } else {
                                        GcadILog('downloadFile : fetch url=', url, ', response.status=', response.status);
                                    }
                                } catch (error) {
                                    GcadILog('downloadFile : fetch url=', url, ', fetch error, error=', error);
                                }
                            }
                            if (!ok) {
                                reject(false);//所有地址均下载失败
                            }
                        }, 0);
                    });
                }
            }
        };
        startAsyncDownloadList = (fileList, cfg, fetches, retryTimes) => {
            const downloadList = fileList.map(async (fileInfo) => {
                fileInfo.path = cfg.variant_replace(fileInfo.path);
                return exeWithRetry(downloadFile, fileInfo, cfg, fetches, retryTimes)
            })
            return downloadList;
        }
        waitAsyncTask = async (barrier) => {
            if (Array.isArray(barrier)) {
                await Promise.all(barrier).then((results) => {
                    return true;
                }).catch((error) => {
                    console.error(error);
                });

            }
            else {
                await barrier;
            }
        };
        //以UTF8格式读取本地文件
        readLocalFileWithUtf8 = (fs, filePath) => {
            try {
                return fs.readFile(filePath, { encoding: 'utf8' });
            } catch (error) {
                return "";
            }
        };

        initFSFromIDB = async (m, cfg) => {
            return new Promise((resolve, reject) => {
                GcadILog("initFSFromIDB start.\n");
                var FS = m.FS;
                FS.mkdir('/data');
                FS.mount(m.IDBFS, {}, '/data');
                try {
                    FS.syncfs(true, async function (err) {
                        if (err) {
                            GcadILog("initFSFromIDB syncfs true FAILED.\n");
                            resolve(err);
                        }
                        else {
                            await initFS(m, cfg);
                            GcadILog("initFSFromIDB end.\n");
                            resolve(err);
                        }
                    })
                } catch {
                    GcadILog("initFSFromIDB syncfs true, try catch.\n");
                    resolve('initFSFromIDB syncfs true, try catch');
                }
            });
        };
        initFS = (m, cfg_param) => {
            return new Promise((resolve, reject) => {
                var FS = m.FS;
                GcadILog("initFileSystem start.\n");
                //创建文件系统
                cfg_param.mkdirTree('/data/opt/gstarcad/v2023');
                cfg_param.mkdirTree('/data/opt/gstarcad/v2023/appconf');
                cfg_param.mkdirTree('/data/var/gstarcad');
                cfg_param.mkdirTree('/data/var/gstarcad/Fonts');
                cfg_param.mkdirTree('/data/var/gstarcad/ico');
                cfg_param.mkdirTree('/data/var/gstarcad/ico_dark');
                cfg_param.mkdirTree('/data/var/gstarcad/ico_gcad');
                cfg_param.mkdirTree('/data/var/gstarcad/ico_gcad/plot');
                cfg_param.mkdirTree('/data/var/gstarcad/ico_gcad/xref');
                cfg_param.mkdirTree('/data/ico');
                cfg_param.mkdirTree('/data/ico_dark');
                cfg_param.mkdirTree('/data/ico_gcad');
                cfg_param.mkdirTree('/data/ico_gcad/plot');
                cfg_param.mkdirTree('/data/ico_gcad/xref');
                cfg_param.mkdirTree('/data/UserDataCache');
                cfg_param.mkdirTree('/data/UserDataCache/Template');
                cfg_param.mkdirTree('/data/resposity');
                cfg_param.mkdirTree('/data/resposity/gstarcad');
                cfg_param.mkdirTree('/data/runtime');
                cfg_param.mkdirTree('/data/Support');
                cfg_param.mkdirTree('/data/Fonts');
                cfg_param.mkdirTree('/data/FontsServer');
                cfg_param.mkdirTree('/data/tmp');
                cfg_param.mkdirTree('/data/home');
                cfg_param.mkdirTree('/data/cfData');
                cfg_param.mkdirTree('/data/vsData');
                cfg_param.mkdirTree('/data/vsData/' + cfg_param.document.did);
                cfg_param.mkdirTree('/data/tmp/' + cfg_param.document.did);

                FS.syncfs(false, function (err) {

                    if (err) {
                        GcadILog("initFileSystem FAILED.\n");
                        cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "initFileSystem FAILED.")
                        reject(err);
                    }
                    else {
                        GcadILog("initFileSystem end.\n");
                        resolve(err);
                    }
                });
            });
        };
        fs_sync = (load, fs) => {
            return new Promise((resolve, reject) => {
                try {
                    fs.syncfs(load, function (err) {
                        if (err) {
                            GcadILog("fs_sync, resolve err=", err);
                            resolve(err);
                        }
                        else {
                            resolve(err);
                        }
                    });
                } catch {
                    GcadILog("fs_sync, try catch.\n");
                    resolve('fs_sync, try catch');
                }
            });
        };

        const GCAD_DWG_DATA_NOT_READY = 0;              // 没有dwg数据
        const GCAD_DWG_DATA_READY = 1;                  // dwg数据就绪
        const GCAD_VS_DATA_NOT_READY = 0;               // 没有显示数据
        const GCAD_VS_DATA_READY = 2;                   // 显示数据就绪
        const GCAD_VS_DATA_DOWNLOADING = 4;             // 显示数据下载中，底层需循环等待 设置超时
        const GCAD_LICENSE_NOT_READY = 0;              // 没有LICENSE数据
        const GCAD_LICENSE_READY = 1;                  // LICENSE数据就绪
        cfg_param.statusDwgData = GCAD_DWG_DATA_NOT_READY;
        cfg_param.statusLicense = GCAD_LICENSE_NOT_READY;
        cfg_param.statusVsData = GCAD_VS_DATA_NOT_READY;
        const GCAD_VS_DATA_DIR = '/data/vsData/'

        //启动入口程序
        gcad_boot = async (cfg_param, cb) => {
            loadPrintPreviewPromise = loadPrintPreview(cfg_param.var.DOWNLOADSVR)
            if (cfg_param.application.runEnv != "standalone") {
                // cfg_param.document.logLevel = 1
                function getJsonFileInfo(path) {
                    return new Promise((resolve, reject) => {
                        fetch(path + '?v=' + (window.sdkver || Date.now()))
                            .then(response => {
                                return response.arrayBuffer()
                            })
                            .then(res => {
                                var dataView = new DataView(res);
                                var decoder = new TextDecoder("utf-8");
                                var jsonString = decoder.decode(dataView);
                                resolve(jsonString);
                            })
                            .catch(err => {
                                var msg = "input json file error. path=" + path
                                cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, msg)
                                reject(err)
                            })
                    })
                };
                cfg_param.fetches = async () => {
                    var fileinfo = {};
                    fileinfo = await getJsonFileInfo(cfg_param.var.DOWNLOADSVR+'fetch_list.json')
                    GcadILog("cfg_param.fetches fileinfo=", fileinfo)
                    return fileinfo
                };
                cfg_param.files.s1_need = async () => {
                    var fileinfo = [];
                    jsonString = await getJsonFileInfo(cfg_param.var.DOWNLOADSVR+'s1_need.json')
                    fileinfo = JSON.parse(jsonString);
                    GcadILog("cfg_param.files.s1_need fileinfo=", fileinfo)
                    return fileinfo
                };
                cfg_param.files.s2_need = async () => {
                    var fileinfo = [];
                    jsonString = await getJsonFileInfo(cfg_param.var.DOWNLOADSVR+'s2_need.json')
                    fileinfo = JSON.parse(jsonString);
                    GcadILog("cfg_param.files.s2_need fileinfo=", fileinfo)
                    return fileinfo
                };
            }
            var fetchesinfo = await cfg_param.fetches();
            var fetches = new Map(fetchesinfo.split(',').map(entry => {
                const [key, value] = entry.split(':');
                return [key.trim(), value.trim()];
            }));

            GcadILog("fetchesinfo=", fetchesinfo)
            var s1_need = await cfg_param.files.s1_need();
            var s1_download = checkNeed(s1_need, cfg_param);
            GcadILog("s1_download=", s1_download)
            if (s1_download.length > 0) {
                GcadILog("s1_download.length=", s1_download.length)
                var num = s1_download.length / 100;  //并发超过连接数，报错net::ERR_INSUFFICIENT_RESOURCES
                GcadILog("num=", num);
                var next = num
                while (next >= 0) {
                    GcadILog("start,阶段=", next)
                    var barrier = startAsyncDownloadList(s1_download.slice((num - next) * 100, (num - next) * 100 + 100), cfg_param, fetches, cfg_param.retryTimes || 3);
                    await Promise.all(barrier).catch((error) => {
                        cb.showMsg(GCAD_EXECUTE_APP_ERROR, "阶段1程序下载文件失败");
                        cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "Phase 1 download file failed.")
                    });
                    GcadILog("end,阶段=", next)
                    next = next - 1
                }
            }
            await cfg_param.update_index(false, cfg_param);
            //await fs_sync(false, cfg_param.fs);
            cb && cb.eventPrepareStage2Files && cb.eventPrepareStage2Files();//开始准备第二阶段的文件
            cb.setProcess(GCAD_DOWNLOAD_S1_NEED_END);
            cfg_param.fontInfo = await __GSTAR_CLOUD_CAD__.instance.externalMount.getFontInfoList();

            cfg_param.statusSetDocument = false
            var workerServerPath = 'work_server.js'
            if (cfg_param.application.runEnv != "standalone") {
                workerServerPath = cfg_param.var.DOWNLOADSVR+'work_server.js'
            }
            cfg_param.document.userAgent = navigator.userAgent
            cfg_param.document.useCharData = false;  // 默认关闭
            cfg_param.document.useSDF = false;
            cfg_param.workServer = new Worker(URL.createObjectURL(await fetch(workerServerPath + '?v=' + (window.sdkver || Date.now())).then(res => res.blob())));
            cfg_param.sendCommand = (message) => {
                GcadILog('sendCommand : message=', message);
                var cmd = message.command
                message.reqHeaderUrl = cfg_param.headerUrl
                message.token = cfg_param.token
                if (message.command == "findFile" || message.command == "findFileWithDepends") {
                    message.fileId = cfg_param.document.fileId
                }
                //message.reqURL.data = message.data
                GcadILog("sendCommand : message.reqHeaderUrl=", message.reqHeaderUrl)
                GcadILog("sendCommand : message.token=", message.token)
                return cfg_param.workServer.postMessage(message)
            };
            cfg_param.workServer.onmessage = async (event) => {
                GcadILog('workServer.onmessage: ', event.data);
                if (event.data.command == "getAndStorageVsData") {
                    var renderDataFilePath = GCAD_VS_DATA_DIR + event.data.vsDataSha1 + ".dwg.gs";
                    if (event.data.ret) {
                        let tmpBuf = new Uint8Array(event.data.ret);
                        let fs = cfg_param.fs;
                        fs.writeFile(renderDataFilePath, tmpBuf);
                        await fs_sync(false, cfg_param.fs);
                        if (event.data.vsDataSize != tmpBuf.length) {
                            GcadILog("workServer onmessage : ret, getAndStorageVsData vsdata file size error.")
                            return new Promise((resolve, reject) => resolve(true));
                        } else {
                            //await fs_sync(true, cfg_param.fs);
                            await cfg_param.addVsDataIndex(event.data.vsDataSize, renderDataFilePath, event.data.vsDataSha1);
                            GcadILog('workServer onmessage : ret, getAndStorageVsData write ok');
                            cfg_param.local_openFileIndex.push(cfg_param.local_vsDataIndex[event.data.vsDataSha1]);
                            cfg_param.statusVsData = GCAD_VS_DATA_READY;
                            // 通知底层显示数据就绪
                            return new Promise((resolve, reject) => resolve(true));
                        }
                    }
                    if (event.data.vsDataPath) {
                        await fs_sync(true, cfg_param.fs);
                        //await cfg_param.big_fssync(true);
                        var tmpExitDwgGsFile = false
                        tmpExitDwgGsFile = cfg_param.local_existFile(event.data.vsDataPath)
                        //tmpExitDwgGsFile = cfg_param.big_local_existFile(event.data.vsDataPath)
                        if (!tmpExitDwgGsFile) {
                            GcadILog("workServer onmessage : vsData file not exit in indexdb.", event.data.vsDataPath)
                            cfg_param.postMessageVsData(true)
                            return new Promise((resolve, reject) => resolve(true));
                        }
                        await cfg_param.addVsDataIndex(event.data.vsDataSize, event.data.vsDataPath, event.data.vsDataSha1);
                        GcadILog('workServer onmessage : getAndStorageVsData write ok');
                        cfg_param.local_openFileIndex.push(cfg_param.local_vsDataIndex[event.data.vsDataSha1]);
                        cfg_param.statusVsData = GCAD_VS_DATA_READY;
                        if (cfg_param.statusSetDocument) {
                            Module.onRuntimeInitialized();
                            GcadILog('workServer onmessage : onRuntimeInitialized ok');
                        }

                    }
                }
                else if (event.data.command == "getCharData") {
                    if (event.data.cfDataPath) {
                        //await fs_sync(true, cfg_param.fs); 
                        await cfg_param.big_fssync(true);
                        GcadILog('workServer onmessage : getCharData ok, cfDataPath=', event.data.cfDataPath);
                        Module.GcJsUiWorker.doCommandResult(JSON.stringify(event.data));
                    }
                }
                else if (event.data.command == "findFont") {
                    GcadILog('onmessage : findFont ok, event.data=', event.data);
                    if (event.data.fontPath) {
                        //await fs_sync(true, cfg_param.fs); 
                        await cfg_param.big_fssync(true);
                        GcadILog('workServer onmessage : findFont ok, fontPath=', event.data.fontPath);
                        //Module._fontFileDownloadFinish(event.data.fontPath); //底层需要export接口
                        event.data.ret = "finished"
                        Module.GcJsUiWorker.doCommandResult(JSON.stringify(event.data));
                    }
                }
                else if (event.data.command == "findThumbs") {
                    GcadILog('onmessage : findThumbs ok, event.data=', event.data);
                    //await fs_sync(true, cfg_param.fs); 
                    //await cfg_param.big_fssync(true);
                    GcadILog('workServer onmessage : findThumbs ok, ret=', event.data.ret);
                    //Module._fontFileDownloadFinish(event.data.fontPath); //底层需要export接口
                    Module.GcJsUiWorker.doCommandResult(JSON.stringify(event.data));
                }
                else if (event.data.command == "findFile" || event.data.command == "findFileWithDepends") {
                    GcadILog('onmessage : findFile ok, event.data=', event.data);
                    if (event.data.ret && event.data.ret.data && event.data.ret.data.findFileList) {
                        var list = event.data.ret.data.findFileList
                        if (list.length != 0) {
                            var isDownloaded = false
                            for (let index of list) {
                                if (index.status == "downloaded") {
                                    isDownloaded = true
                                }
                            }
                            if (isDownloaded) {
                                GcadILog('workServer onmessage : findFile ok, fssync');
                                await cfg_param.big_fssync(true);
                                GcadILog('workServer onmessage : findFile ok, fssync end');
                            }
                        }
                        var needSync = false
                        for (let value of list) {
                            GcadILog('workServer onmessage : findFile ok, for value=', value);
                            if (value.status == "downloaded" && (value.source.endsWith(".dwg") || value.source.endsWith(".DWG"))) {
                                needSync = true

                                var atime = new Date(value.createTime).getTime()
                                var mtime = new Date(value.lastModifyTime).getTime()
                                GcadELog('workServer onmessage : findFile ok, atime=', atime);
                                GcadELog('workServer onmessage : findFile ok, mtime=', mtime);
                                cfg_param.big_utime(value.filePath, atime, mtime)

                                // cfg_param.document.cloudPath     /A/B/test.dwg       /A/B/C/test.dwg         /A/B/C/test.dwg
                                // value.source                     ../xref/test2.dwg   ../../xref/test2.dwg    ../../xref/test2.dwg
                                // 格式化
                                let lastPathResult = value.source.split("/")
                                let matchs = value.source.split("..");
                                GcadILog('workServer onmessage : findFile ok, matchs=', matchs);
                                let delPath = matchs.length - 1;
                                let inPathResult = cfg_param.document.cloudPath.split("/")
                                GcadILog('workServer onmessage : findFile ok, delPath=', delPath);
                                GcadILog('workServer onmessage : findFile ok, lastPathResult=', lastPathResult);
                                GcadILog('workServer onmessage : findFile ok, inPathResult=', inPathResult);
                                let inNum = inPathResult.length - delPath - 1
                                let lastNum = lastPathResult.length - delPath
                                let allLen = inNum + lastNum
                                var tmpPath = cfg_param.document.localVsRootPath
                                for (var k = 0; k < allLen; k++) {
                                    if (k < inNum) {
                                        tmpPath = tmpPath + inPathResult[k]
                                    }
                                    else {
                                        tmpPath = tmpPath + lastPathResult[lastPathResult.length - (allLen - k)]
                                    }
                                    if (k != allLen - 1) {
                                        tmpPath = tmpPath + '/'
                                    }
                                }
                                GcadELog("workServer onmessage : findFile ok,  symlink tmpPath=", tmpPath)
                                let cloudPathResult = tmpPath.split("/")
                                var tmpPathTree = ''
                                for (var k = 1; k < cloudPathResult.length - 1; k++) {
                                    tmpPathTree = tmpPathTree + '/' + cloudPathResult[k]
                                }
                                GcadELog("workServer onmessage : findFile ok,  symlink tmpPathTree=", tmpPathTree)
                                // 需要检查软链接是否已存在
                                cfg_param.big_mkdirTree(tmpPathTree);
                                if (cfg_param.big_local_existFile(tmpPath)) {
                                    if (cfg_param.document.symlinkNeedCreat == 2) {
                                        window.FS.unlink(tmpPath, function (err) {
                                            if (err) {
                                                GcadELog("onRuntimeInitialized fs.unlink err=", err)
                                                reject(err);
                                            } else {
                                                GcadELog("onRuntimeInitialized fs.unlink tmpPath=", tmpPath)
                                                resolve(err);
                                            }
                                        });
                                        cfg_param.big_symlink(value.filePath, tmpPath);
                                        var tmpStr = cfg_param.document.localVsRootPath + "/ls-" + value.fileId
                                        if (!cfg_param.big_local_existFile(tmpStr)) {
                                            cfg_param.writeFile(tmpStr, tmpPath); // 记录key-value  value.fileId tmpPath
                                        }
                                    }
                                }
                                else {
                                    cfg_param.big_symlink(value.filePath, tmpPath);
                                    var tmpStr = cfg_param.document.localVsRootPath + "/ls-" + value.fileId
                                    cfg_param.writeFile(tmpStr, tmpPath);
                                }

                                value.filePath = tmpPath
                                GcadELog("workServer onmessage : findFile ok,  symlink value.filePath=", value.filePath)
                                await cfg_param.big_addVsDataIndex(value.fileSize, value.filePath, value.fileSha);
                            }
                        }
                        if (needSync) {
                            await cfg_param.big_update_vsDataIndex(false, cfg_param);
                        }
                        GcadILog('onmessage : findFile ok, event.data=', event.data);
                        const oldData=event.data.ret.data;
                        if (oldData.needCheckDepends) {
                            var data = {}
                            var ret = {}
                            ret.data = data
                            var tmpAllInfo = {};
                            tmpAllInfo.ret = ret
                            data.fileId=oldData.fileId;
                            data.fop=oldData.fop;
                            data.reqHeaderUrl=oldData.reqHeaderUrl;
                            data.token=oldData.token;

                            data.result = oldData.result;
                            
                            tmpAllInfo.command = "finishFindFileWithDepends";
                            tmpAllInfo.taskId = event.data.taskId
                            tmpAllInfo.depends = [];

                            for (let index = 0; index < oldData.findFileList.length; index++) {
                              const mainFile = oldData.findFileList[index];
                              var ptr = stringToNewUTF8(mainFile.filePath);
                              var c_ptr = Module._getDwgDepends(ptr);
                              Module._free(ptr);
                              if (c_ptr) {
                                  var json = UTF8ToString(c_ptr);
                                  Module._free(c_ptr);
                                  var depends = JSON.parse(json);
                                  if (Array.isArray(depends)) {
                                      depends.forEach(element => {
                                        var downloaded=false;
                                        if(oldData.result)
                                        {
                                          for(var k=0;k<oldData.result.length;k++)
                                          {
                                            var oldRes=oldData.result[k];
                                            if(oldRes&&oldRes.source.toLowerCase()==element.toLowerCase()){
                                              downloaded=true;
                                              break;
                                            }
                                          }
                                        }
                                        if(!downloaded){
                                          tmpAllInfo.depends.push(element);
                                        }
                                      });
                                  }
                              }
                            }
                            cfg_param.workServer.postMessage(tmpAllInfo);
                        } else {
                            //event.data.ret.logicpath = cfg_param.document.logicpath
                            var data = {};
                            var ret = {};
                            ret.data = data;
                            var tmpAllInfo = {};
                            tmpAllInfo.ret = ret;
                            if(event.data.command == "findFile")
                            {
                              tmpAllInfo.command = "findFile";
                              tmpAllInfo.taskId = event.data.taskId
                              data.findFileList=oldData.findFileList;
                              Module.GcJsUiWorker.doCommandResult(JSON.stringify(tmpAllInfo));
                            }else if(event.data.command == "findFileWithDepends"){
                              tmpAllInfo.command = "findFileWithDepends";
                              tmpAllInfo.taskId = event.data.taskId
                              data.findFileList=oldData.result;
                              Module.GcJsUiWorker.doCommandResult(JSON.stringify(tmpAllInfo));
                            }
                        }
                    }
                }
                else {
                    Module.GcJsUiWorker.doCommandResult(JSON.stringify(event.data));
                }
                //cfg_param.workServer.terminate(); //关闭work
            }
            cfg_param.keepAlive = () => {
                var message = {}
                message.command = "updateKeepAlive"
                message.filePath = cfg_param.document.localVsRootPath + '/keepAlive.flag'
                cfg_param.workServer.postMessage(message)
            }

            if (typeof cfg_param.document !== "undefined" && typeof cfg_param.document.logLevel !== "undefined") {
                var message = {}
                message.command = "testSetLogLevel"
                message.level = cfg_param.document.logLevel
                GcadILog("setLogLevel : message=", message)
                cfg_param.workServer.postMessage(message)
            }

            var s2_need = await cfg_param.files.s2_need();
            var s2_download = checkNeed(s2_need, cfg_param);
            GcadILog("s2_download=", s2_download)
            var s2_barrier;
            if (s2_download.length > 0) {
                //s2_barrier = startAsyncDownloadList(s2_download, cfg_param, fetches, cfg_param.retryTimes || 3);
                var s2_num = s2_download.length / 100;  //并发超过连接数，报错net::ERR_INSUFFICIENT_RESOURCES
                GcadILog("s2_num=", s2_num);
                var s2_next = s2_num
                while (s2_next >= 0) {
                    GcadILog("s2_download start,阶段=", s2_next)
                    var tmpbarrier = startAsyncDownloadList(s2_download.slice((s2_num - s2_next) * 100, (s2_num - s2_next) * 100 + 100), cfg_param, fetches, cfg_param.retryTimes || 3);
                    await Promise.all(tmpbarrier).catch((error) => {
                        cb.showMsg(GCAD_EXECUTE_APP_ERROR, "阶段2程序下载文件失败");
                        cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "Phase 2 download file failed.")
                    });
                    GcadILog("s2_download end,阶段=", s2_next)
                    s2_next = s2_next - 1
                }
                s2_barrier = s2_download.map(() => {
                    return new Promise((resolve, reject) => resolve(true))
                })
            } else {
                s2_barrier = s2_download.map(() => {
                    return new Promise((resolve, reject) => resolve(true))
                })
            }


            GcadILog("s3_vs download start.")
            var s3_vs_barrier;
            if (cfg_param.application.runEnv != "standalone") {
                GcadILog("cfg_param.document=", cfg_param.document);
                var tmpstr = cfg_param.document.cloudPath
                var lastIndex = tmpstr.lastIndexOf('/')
                GcadILog("lastIndex = ", lastIndex)
                cfg_param.document.name = tmpstr.slice(lastIndex + 1)
                cfg_param.document.logicpath = cfg_param.document.cloudPath
                cfg_param.document.localVsRootPath = GCAD_VS_DATA_DIR + cfg_param.document.did;
                GcadILog("cfg_param.document.localVsRootPath = ", cfg_param.document.localVsRootPath)
                cfg_param.keepAlive()
                await cfg_param.manageVsDataPath()
                var retGetDwg = await cfg_param.getAndStorageDwgData();
                if(!retGetDwg) {
                    GcadELog("gcad_boot: failed, open dwg failed")   
                } 
                var retGetLic = await cfg_param.getLicense();
                if(retGetLic) {
                    cfg_param.statusLicense = GCAD_LICENSE_READY;
                }
                cfg_param.statusVsData = GCAD_VS_DATA_DOWNLOADING;
                //var tmpkey = cfg_param.document.fileId + '/' + cfg_param.document.version + '/' + cfg_param.document.sha1 + "/gs/catalog" //todo
                //cfg_param.getAndStorageVsData(tmpkey, cfg_param.document.name); 
                /* //异步等待通知流程
                cfg_param.getAndStorageVsData(tmpkey, cfg_param.document.name).then(()=>{
                    cfg_param.statusVsData = GCAD_VS_DATA_READY;
                    Module.onRuntimeInitialized()
                })
                */
                s3_vs_barrier = (() => {
                    return new Promise((resolve, reject) => resolve(true))
                })
            } else {
                cfg_param.statusDwgData = GCAD_DWG_DATA_READY; // 前端已下载
                cfg_param.statusLicense = GCAD_LICENSE_READY; // 单机默认证书就绪
                cfg_param.document.useEnMode = false  // 单机不加密
                cfg_param.document.localVsRootPath = GCAD_VS_DATA_DIR + cfg_param.document.did;
                s3_vs_barrier = (() => {
                    return new Promise((resolve, reject) => resolve(true))
                })
            }

            cb.eventEndStage1();

            var intervalId = setInterval(function () {
                //GcadILog("setInterval: 每隔1分钟keepalive");
                cfg_param.keepAlive()
            }, 60000);

            if (cfg_param.document.uploadInterval) {
                var uploadInterval = cfg_param.document.uploadInterval * 1000 //ms
                var uploadType = false
                if (cfg_param.document.uploadType == 'snapshot') //  increment = false
                {
                    uploadType = true;
                }
                var intervalUploadId = setInterval(function () {
                    //console.log("intervalUpload: 每隔毫秒",uploadInterval);
                    // cfg_param.uploadRecord() // 原方案
                    var tmp = window.Module._gcloud_data_commit(uploadType);
                    console.log("intervalUpload: tmp=", tmp);
                    //console.log("intervalUpload: _gcloud_data_commit=", Module.UTF8ToString(tmp)); 
                }, uploadInterval);
            }

            // 遍历vsData下的目录 清理超时目录 运行一次
            //var clearTimeoutId = setTimeout(function() {
            //    GcadILog("clearTimeoutId: 启动5分钟后执行清理");
            //    cfg_param.manageVsDataPath()
            //}, 300000);

            //加载程序
            cfg_param.load_application(cfg_param.application.entry).then(() => {
                GcadILog("load_application end");
                cb && cb.eventEndStage1 && cb.eventEndStage1();//第一阶段结束了        
                if (s2_barrier && s3_vs_barrier) {
                    cb.setProcess(GCAD_DOWNLOAD_S2_NEED_END);
                    Promise.all([s2_barrier, s3_vs_barrier]).then(async () => {
                        await cfg_param.update_index(false, cfg_param);
                        //await fs_sync(false, cfg_param.fs);
                        //执行程序
                        await cfg_param.execute_application(cfg_param.application.entry);
                    }).catch((error) => {
                        GcadILog("阶段2程序下载文件失败");
                        cb.showMsg(GCAD_EXECUTE_APP_ERROR, "阶段2程序下载文件失败");
                        cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "Phase 2 download file failed.")
                    });
                }
            });
            //websocket_link();
        }

        const GCAD_IDBFS_CHECK_BEGIN = 1;
        const GCAD_IDBFS_CHECK_END = 2;
        const GCAD_READ_LOCAL_DATA_BEGIN = 3;
        const GCAD_READ_LOCAL_DATA_END = 4;
        const GCAD_LOAD_APPLICATION_DATA_BEGIN = 5;
        const GCAD_LOAD_APPLICATION_DATA_PROGRESS = 6;
        const GCAD_LOAD_APPLICATION_DATA_END = 7;

        const GCAD_DOWNLOAD_S1_NEED_END = 10;
        const GCAD_DOWNLOAD_S2_NEED_END = 20;
        const GCAD_DOWN_DWG_ERROR = 76;
        const GCAD_OPEN_DWG_FINISH = 100;
        const GCAD_MAKE_OCF_FINISH = 800;
        const GCAD_OPEN_DWG_FAILED = -1;
        const GCAD_EXECUTE_APP_ERROR = -1;
        const GcadOpenDWGErrorMsg = {       // the define from  gcad ErrorStatus
            3: "Invalid Input.",
            430: "Open File Cancelled.",
            76: "Download dwg and gs Data Failed." // eFileNotFound
        };

        const GCAD_IDBFS_USED_LIMIT = 90; //percentageUsed=0.9

        fetchResource = (filePath) => {
            return fetch(filePath).then(function (response) {
                if (!response.ok) {
                    self.error = response.status + " " + response.statusText + " " + response.url;
                    return Promise.reject(self.error)
                } else {
                    return response;
                }
            });
        }

        fetchText = (filePath) => {
            return fetchResource(filePath).then(function (response) {
                return response.text();
            });
        }

        fetchThenCompileWasm = (response) => {
            return response.arrayBuffer().then(function (data) {
                self.loaderSubState = "Compiling";
                return WebAssembly.compile(data);
            });
        }

        fetchCompileWasm = (filePath) => {
            return fetchResource(filePath).then(function (response) {
                if (typeof WebAssembly.compileStreaming !== "undefined") {
                    self.loaderSubState = "Downloading/Compiling";
                    return WebAssembly.compileStreaming(response).catch(function (error) {
                        return fetchThenCompileWasm(response);
                    });
                } else {
                    return fetchThenCompileWasm(response);
                }
            });
        }

        fetchTextLocal = (filePath) => {
            var tmppath = '/data/' + filePath
            var opts = {}
            opts.flags = opts.flags || 0;
            opts.encoding = opts.encoding || 'utf8';
            var tmpUtf8 = cfg_param.fs.readFile(tmppath, opts);
            return tmpUtf8;
        }

        fetchCompileWasmLocal = async (filePath) => {
            var tmppath = '/data/' + filePath
            var opts = {}
            opts.flags = opts.flags || 0;
            opts.encoding = opts.encoding || 'binary';
            var tmpUint8Array = cfg_param.fs.readFile(tmppath, opts);
            var blob = new Blob([tmpUint8Array.buffer]);
            var arrayBuffer = await blob.arrayBuffer();
            // 将Blob对象转换成ArrayBuffer对象
            self.loaderSubState = "Compiling";
            return WebAssembly.compile(arrayBuffer);
        }

        

        loadEmscriptenModule = (applicationName, cloudcanvas) => {
            return new Promise(async (resolve, reject) => {
                var wasmModulePromise = fetchCompileWasmLocal(applicationName + ".wasm")
                Promise.all([wasmModulePromise]).then(function ([wasmModule]) {
                    Module._DOM_canvas = document.createElement('canvas');
                    Module.previewCtx = Module._DOM_canvas.getContext('2d');
                    Module.canvas = cloudcanvas;

                    Module.instantiateWasm = function (imports, successCallback) {
                        WebAssembly.instantiate(wasmModule, imports)
                            .then(async function (instance) {
                                //await Module.instantiated();
                                //successCallback(instance, wasmModule) 
                                var exports = instance.exports;
                                exports = Asyncify.instrumentWasmExports(exports);
                                Module["asm"] = exports;
                                wasmMemory = Module["asm"]["memory"];
                                //assert(wasmMemory, "memory not found in wasm exports");
                                var b = wasmMemory.buffer;
                                Module["HEAP8"] = HEAP8 = new Int8Array(b);
                                Module["HEAP16"] = HEAP16 = new Int16Array(b);
                                Module["HEAP32"] = HEAP32 = new Int32Array(b);
                                Module["HEAPU8"] = HEAPU8 = new Uint8Array(b);
                                Module["HEAPU16"] = HEAPU16 = new Uint16Array(b);
                                Module["HEAPU32"] = HEAPU32 = new Uint32Array(b);
                                Module["HEAPF32"] = HEAPF32 = new Float32Array(b);
                                Module["HEAPF64"] = HEAPF64 = new Float64Array(b);

                                await Module.instantiated();

                                loadPrintPreviewPromise.then(async function () {
                                  Module.printPreview = await window.PreviewModule()
                                  Module.printPreview._changeLoopStatus(0)
                                }).catch(function(err) { console.error('loadPrintPreview error', err) })

                                wasmTable = Module["asm"]["__indirect_function_table"];
                                //assert(wasmTable, "table not found in wasm exports");
                                __ATINIT__.unshift(Module["asm"]["__wasm_call_ctors"]);
                                //Module.removeRunDependency("wasm-instantiate");
                                runDependencies--;
                                if (Module["monitorRunDependencies"]) {
                                    Module["monitorRunDependencies"](runDependencies);
                                }
                                if ("wasm-instantiate") {
                                    assert(runDependencyTracking["wasm-instantiate"]);
                                    delete runDependencyTracking["wasm-instantiate"];
                                } else {
                                    err("warning: run dependency removed without ID");
                                }
                                if (runDependencies == 0) {
                                    if (runDependencyWatcher !== null) {
                                        clearInterval(runDependencyWatcher);
                                        runDependencyWatcher = null;
                                    }
                                    if (dependenciesFulfilled) {
                                        var callback = dependenciesFulfilled;
                                        dependenciesFulfilled = null;
                                        callback();
                                    }
                                }

                            }, function (error) {
                                console.error(error)
                                self.error = error;
                            });
                        return {};
                    };
                    callback.showMain();
                    //self.eval(jsModule);
                    resolve()
                }).catch(function (error) {
                    console.error(error)
                    self.error = error;
                });
                GcadILog("loadEmscriptenModule.");
            })
        };

        GCADM().then(async m => {
            let cb = callback;
            let showMsg = callback.showMsg;
            cfg_param.fs = m.FS;
            cfg_param.path_tidy = (path) => {//获取文件大小                
                return path.replace('//', '/');
            };
            cfg_param.getPath = (path) => {
                path = cfg_param.path_tidy(path);
                let pos = path.lastIndexOf("/");
                if (pos > 0) {
                    return path.substr(0, pos);
                }
                return "/";
            };
            cfg_param.mkdirTree = (path) => {
                try {
                    path = cfg_param.path_tidy(path);
                    cfg_param.fs.mkdirTree(path);
                } catch (e) {

                }
            };
            cfg_param.rmDirPath = (path) => {
                try {
                    var tmpstat = cfg_param.fs.readdir(path);
                    GcadILog("rmDirPath: tmpstat.length=", tmpstat.length);
                    for (var i = 0; i < tmpstat.length; i++) {
                        if (tmpstat[i] != '.' && tmpstat[i] != '..') {
                            if (cfg_param.fs.isFile(cfg_param.fs.stat(path + '/' + tmpstat[i]).mode)) {
                                GcadILog("rmDirPath: isFile **=", path + '/' + tmpstat[i]);
                                cfg_param.fs.unlink(path + '/' + tmpstat[i])
                                if (tmpstat[i].endsWith('.dwg.gs')) // 删除.dwg.gs时 需删除索引对应内容
                                {
                                    //GcadILog("len=",tmpstat[i].length)
                                    var strSHA = tmpstat[i].substr(0, tmpstat[i].length - 6)
                                    //GcadILog("strSHA=",strSHA)
                                    //cfg_param.local_sha1Index
                                    for (var prop in cfg_param.local_sha1Index) {
                                        if (cfg_param.local_sha1Index[prop].sha1 == strSHA) {
                                            //GcadILog("delete strSHA=",strSHA)
                                            delete cfg_param.local_sha1Index[prop]
                                        }
                                    }
                                }
                            } else {
                                GcadILog("rmDirPath: isDir **=", path + '/' + tmpstat[i]);
                                cfg_param.rmDirPath(path + '/' + tmpstat[i])
                            }
                        }
                    }
                    cfg_param.fs.rmdir(path);
                } catch (e) {
                    GcadILog("rmDirPath: e=", e)
                }
            };
            cfg_param.update_index = (load, cfg_param) => {
                let fs = cfg_param.fs;
                if (!load) {
                    fs.writeFile('/data/local_sha1Index.json', JSON.stringify(cfg_param.local_sha1Index));
                    fs.writeFile('/data/local_indexes.json', JSON.stringify(cfg_param.local_fileIndexes));
                }
                return new Promise((resolve, reject) => {
                    fs.syncfs(load, function (err) {
                        if (err) {
                            GcadILog("update_index syncfs, reject err=", err);
                            resolve(err);
                        }
                        else {
                            resolve(err);
                        }
                    });
                });
            };
            cfg_param.update_vsDataIndex = (load, cfg_param) => {
                let fs = cfg_param.fs;
                if (!load) {
                    fs.writeFile('/data/local_vsDataIndex.json', JSON.stringify(cfg_param.local_vsDataIndex));
                }
                return new Promise((resolve, reject) => {
                    try {
                        fs.syncfs(load, function (err) {
                            if (err) {
                                GcadILog("update_vsDataIndex syncfs, reject err=", err);
                                resolve(err);
                            }
                            else {
                                resolve(err);
                            }
                        });
                    } catch {
                        GcadILog("update_vsDataIndex syncfs, try catch. err=", err);
                        resolve('update_vsDataIndex syncfs, try catch');
                    }
                });
            };
            cfg_param.big_fssync = async (load) => {
                return new Promise((resolve, reject) => {
                    try {
                        window.FS.syncfs(load, function (err) {
                            if (err) {
                                //GcadILog("big_fssync syncfs, try catch. 222 err=", err);
                                resolve(err);
                            } else {
                                resolve(err);
                            }
                        });
                    } catch {
                        GcadILog("big_fssync syncfs, try catch. err=", err);
                        resolve('big_fssync syncfs, try catch');
                    }
                });
            };
            cfg_param.big_symlink = (sourcePath, decPath) => {
                window.FS.symlink(sourcePath, decPath)
            };
            cfg_param.big_mkdirTree = (path) => {
                try {
                    path = cfg_param.path_tidy(path);
                    window.FS.mkdirTree(path);
                } catch (e) {

                }
            };

            cfg_param.big_rmDirPath = (path) => {
                try {
                    var tmpstat = window.FS.readdir(path);
                    GcadILog("rmDirPath: tmpstat.length=", tmpstat.length);
                    for (var i = 0; i < tmpstat.length; i++) {
                        if (tmpstat[i] != '.' && tmpstat[i] != '..') {
                            if (window.FS.isFile(window.FS.stat(path + '/' + tmpstat[i]).mode)) {
                                GcadILog("rmDirPath: isFile **=", path + '/' + tmpstat[i]);
                                window.FS.unlink(path + '/' + tmpstat[i])
                            } else {
                                GcadILog("rmDirPath: isDir **=", path + '/' + tmpstat[i]);
                                cfg_param.rmDirPath(path + '/' + tmpstat[i])
                            }
                        }
                    }
                    window.FS.rmdir(path);
                } catch (e) {
                    GcadILog("rmDirPath: e=", e)
                }
            };

            // 订阅服务端回调，更新idbfs数据 type = del or update
            cfg_param.updateFileId = (fileId, type) => {
                GcadILog("updateFileId start");
                var tmpStr = cfg_param.document.localVsRootPath + '/ls-' + fileId
                if (cfg_param.big_local_existFile(tmpStr)) {
                    GcadILog("updateFileId tmpStr exist tmpStr=", tmpStr);
                    if (type == "del")     // 删除软连接 和 k-v记录
                    {
                        GcadILog("updateFileId tmpStr exist type=", type);
                        var lsStr = window.FS.readFile(tmpStr, { encoding: 'utf8' })
                        GcadILog("updateFileId tmpStr exist lsStr=", lsStr);
                        if (cfg_param.big_local_existFile(lsStr)) {
                            GcadILog("updateFileId tmpStr lsStr exist lsStr=", lsStr);
                            window.FS.unlink(lsStr)
                        }
                        window.FS.unlink(tmpStr)
                        cfg_param.big_fssync(false)
                    }
                    else if (type == 'update') {
                        GcadILog("updateFileId type=", type);// todo
                    }
                }
                else {
                    GcadILog("updateFileId not exist fileId=", fileId);
                }
                GcadILog("updateFileId end");
            };

            cfg_param.big_update_vsDataIndex = async (load, cfg_param) => {
                if (!load) {
                    cfg_param.writeFile('/data/local_vsDataIndex.json', JSON.stringify(cfg_param.local_vsDataIndex));
                }
                await cfg_param.big_fssync(false);
            };
            cfg_param.updateKeepAlive = async () => {
                var timeFlag = new Date().format("yyyy-MM-dd hh:mm:ss.S");
                GcadILog("timeFlag=", timeFlag);
                cfg_param.writeFile(cfg_param.document.localVsRootPath + '/keepAlive.flag', timeFlag);
                await cfg_param.big_fssync(false);
            };

            cfg_param.uploadRecord = async () => {
                console.log("uploadRecord strat.");
                var tmp = window.Module._gcloud_data_commit();
                console.log("uploadRecord Module.UTF8ToString(tmp)=", Module.UTF8ToString(tmp));
                var path = Module.UTF8ToString(tmp);
                if (path == '') {
                    console.log("uploadRecord, not path, not need uploadrecord.");
                } else {
                    var message = {}
                    message.command = "uploadModifyRecord"  // 区分快照或者增量 来自配置 upload cfg_param.document.type
                    message.reqHeaderUrl = cfg_param.headerUrl
                    message.token = cfg_param.token
                    message.keyData = path
                    message.needReturnData = false
                    message.folderId = '10' //  cfg_param.document.folderId
                    message.fileId = cfg_param.document.fileId
                    message.baseId = cfg_param.document.fileId // cfg_param.document.baseId

                    var opts = {}
                    opts.flags = opts.flags || 0;
                    opts.encoding = opts.encoding || 'binary';
                    var tmpUint8Array = cfg_param.fs.readFile(path, opts);
                    var blob = new Blob([tmpUint8Array.buffer]);
                    var tmputf = await blob.arrayBuffer();
                    //var tmputf = fs.readFile(path + '/keepAlive.flag', { encoding: 'utf8' });
                    var wordArray = cfg_param.CryptoJS.lib.WordArray.create(tmputf)
                    var tmpSha1 = cfg_param.CryptoJS.SHA1(wordArray).toString()//计算
                    message.tmpSHA = tmpSha1
                    //console.log("sendCommand : message=", message)
                    cfg_param.workServer.postMessage(message)
                }

            };
            cfg_param.isTimeoutKeepAlive = (path) => {
                let fs = cfg_param.fs;
                var date1 = new Date();
                //GcadILog("now time: date1=", date1);
                var tmputf = fs.readFile(path + '/keepAlive.flag', { encoding: 'utf8' });
                //GcadILog("old time: tmputf=", tmputf);
                var date2 = new Date(tmputf);
                //GcadILog("old time: date2=", date2);
                //GcadILog("isTimeoutKeepAlive: date1=", date1, "date2=",date2); 
                if (date1.getTime() >= date2.getTime() + 24 * 60 * 60 * 1000) { //24*60*60*1000
                    GcadILog("KeepAlive is Timeout, date1.getTime()-date2.getTime()=", date1.getTime() - date2.getTime());
                    return true;
                } else {
                    //GcadILog("KeepAlive is not Timeout"); 
                    return false;
                }

            };

            cfg_param.downloadFromLocalfs = async (reqUrl, token) => {
                GcadILog('downloadFromLocalfs: reqUrl=', reqUrl);
                var retData = await cfg_param.fetchFromServer(reqUrl, 'get', token)
                if (retData == "fail") {
                    GcadILog('downloadFromLocalfs : fetch failed');
                }
                return retData
            };

            cfg_param.downloadFromOSS = async (url) => {
                const response = await fetch(url , {
                    mode: 'cors' // 禁用 CORS，响应不可读
                });
                const reader = response.body.getReader();
                let receivedLength = 0;
                const chunks = [];
                while(true) {
                    const {done, value} = await reader.read();
                    if (done) {
                        const blob = new Blob(chunks);
                        GcadILog("downloadFromOSS blob=", blob)
                        break;
                    }
                    chunks.push(value);
                    receivedLength += value.length;
                    GcadILog("downloadFromOSS receivedLength=", receivedLength)
                }
                // 组成ret
                GcadILog("downloadFromOSS chunks.length=", chunks.length)
                GcadILog("downloadFromOSS chunks=", chunks)
                const ret = new Uint8Array(receivedLength);
                var tmpLen = 0 
                for (var i=0; i<chunks.length; i++) {
                    ret.set(chunks[i],tmpLen)
                    tmpLen = tmpLen + chunks[i].length
                }
                GcadILog("downloadFromOSS ret.length=", ret.length)
                GcadILog("downloadFromOSS tmpLen=", tmpLen)
                return ret; //chunks;
            }

            cfg_param.getDownload = async (fileId) => {
                var tmpBuf = new Uint8Array();
                var reqParams = "?fileId=" + fileId
                var downloadUrlType = "getDownloadUrlExport"
                if(cfg_param.document.useEnMode)
                {
                    downloadUrlType = "getDownloadUrlSafe"
                }
                var tmpUrlData = await cfg_param.requireServer(downloadUrlType, reqParams)
                if (typeof tmpUrlData === "undefined") 
                {
                    GcadILog("getDownload: getDownloadUrl error.") 
                    return tmpBuf
                }           
                GcadILog("getDownload: tmpUrlData = ", tmpUrlData)
                if (tmpUrlData.storageType == GCAD_LOCALFS_STORAGE_TYPE) {
                    var reqUrl =  tmpUrlData.downloadUrl + reqParams 
                    var tmpArrayBuf = await cfg_param.downloadFromLocalfs(reqUrl, cfg_param.token);
                    tmpBuf = new Uint8Array(tmpArrayBuf);                                         
                } 
                else if (tmpUrlData.storageType == GCAD_OSS_ALI_STORAGE_TYPE || tmpUrlData.storageType == GCAD_OSS_AWS_STORAGE_TYPE) {
                    if (tmpUrlData.storageType) {
                        var tmptesturl = tmpUrlData.downloadUrl
                        tmpBuf = await cfg_param.downloadFromOSS(tmptesturl);
                        GcadILog("getDownload: fetchFromOSS tmpBuf=", tmpBuf)   
                    }
                    else {
                        GcadILog("getDownload: need coding. error")   // 如果是oss 就要再判断是不是cdn 是cdn直接下载 不是 走api
                    }                                                         
                }
                return tmpBuf
            };

            // 运行实际选择 目前在下载完成后wasm启动前
            cfg_param.manageVsDataPath = async () => {
                var percentageUsed = 0;

                cfg_param.rmDirPath("/data/tmp");
                cfg_param.mkdirTree("/data/tmp");
                cfg_param.mkdirTree('/data/tmp/' + cfg_param.document.did);
                
                if (navigator.storage && navigator.storage.estimate) {
                    const quota = await navigator.storage.estimate();
                    // quota.usage -> 已用字节数。 当前源已使用永久存储空间
                    // quota.quota -> 最大可用字节数。 默认配额估值太大，无意义 考虑使用组最大限制2G=2147483648
                    var siteStorageUnitLimit = 2147483648 * GCAD_IDBFS_USED_LIMIT / 100;
                    if (quota.quota < siteStorageUnitLimit) {
                        siteStorageUnitLimit = quota.quota;
                    }
                    percentageUsed = (quota.usage / siteStorageUnitLimit) * 100;
                    GcadILog(`您已使用可用存储的 ${percentageUsed}%。`);
                    const remaining = siteStorageUnitLimit - quota.usage;
                    GcadILog(`您最多可以再写入 ${remaining} 个字节。`);
                    cfg_param.local_vsDataStorageLimit = siteStorageUnitLimit;   // 此处若大于2G则使用2G， 此处计算使用率是整个浏览器存储使用率，不便利用
                } else {
                    cfg_param.local_vsDataStorageLimit = 2147483648 * GCAD_IDBFS_USED_LIMIT / 100;
                }
                var allSize = 0;
                // 只有当前did会更新flag 其他did目录可能过期需老化
                let fs = cfg_param.fs;
                var tmpstat = fs.readdir('/data/vsData');
                GcadILog("manageVsDataPath: tmpstat.length=", tmpstat.length);
                GcadILog("manageVsDataPath:  tmpstat=", tmpstat)
                for (var i = 0; i < tmpstat.length; i++) {
                    if (tmpstat[i] != '.' && tmpstat[i] != '..' && tmpstat[i] != cfg_param.document.did
                        && !tmpstat[i].endsWith(".dwg.gs") && !tmpstat[i].endsWith(".dwg") && !tmpstat[i].endsWith(".dwg.ocf4")) {
                        // 若是其他文件 则需要删除 会删除过去的dwl dwl2
                        if (cfg_param.fs.isFile(cfg_param.fs.stat(GCAD_VS_DATA_DIR + tmpstat[i]).mode)) {
                            GcadILog("manageVsDataPath: isFile **=", GCAD_VS_DATA_DIR + tmpstat[i]);
                            cfg_param.fs.unlink(GCAD_VS_DATA_DIR + tmpstat[i])
                        } else {
                            var flag = false
                            try {
                                cfg_param.fs.isFile(cfg_param.fs.stat(GCAD_VS_DATA_DIR + tmpstat[i] + '/keepAlive.flag').mode)
                                flag = cfg_param.isTimeoutKeepAlive(GCAD_VS_DATA_DIR + tmpstat[i])
                            } catch (e) {
                                //GcadILog("manageVsDataPath: KeepAlive is not exit"); 
                                flag = true
                            }
                            if (flag && tmpstat[i] != cfg_param.document.did) {
                                GcadILog("manageVsDataPath: need clear /data/vsData/", tmpstat[i])
                                cfg_param.rmDirPath(GCAD_VS_DATA_DIR + tmpstat[i])
                            }
                        }
                    } else if (tmpstat[i].endsWith(".dwg.gs") || tmpstat[i].endsWith(".dwg") || tmpstat[i].endsWith(".ocf4")) {
                        allSize = allSize + cfg_param.local_filesize(GCAD_VS_DATA_DIR + tmpstat[i])
                        //GcadILog("manageVsDataPath: allSize=", allSize)
                    }
                }
                // 计算总量,若总容量超过阈值,则删除最老的did; 正常老化处理后,依然超过使用阈值,不做处理
                /*
                if(allSize >= cfg_param.local_vsDataStorageLimit || percentageUsed > GCAD_IDBFS_USED_LIMIT){
                    GcadILog("manageVsDataPath: allSize is limit, need clear.")
                    var delSHA = 0
                    var oldDate = new Date()
                    for(var item in cfg_param.local_vsDataIndex)
                    {
                        if (item != cfg_param.document.sha1)
                        {
                            
                            GcadILog("manageVsDataPath: oldDate=", oldDate)
                            GcadILog("manageVsDataPath: cfg_param.local_vsDataIndex[item].time=", cfg_param.local_vsDataIndex[item].time)
                            var itemDate = new Date(cfg_param.local_vsDataIndex[item].time);
                            if (oldDate.getTime() >= itemDate.getTime())
                            {
                                //GcadILog("manageVsDataPath: allSize is limit, need clear.")
                                delSHA = cfg_param.local_vsDataIndex[item].sha1
                                oldDate = itemDate
                                GcadILog("manageVsDataPath: oldDate=", oldDate)
                            }
                        }
                    }
                    if (delSHA != 0 && delSHA != cfg_param.document.sha1)
                    {
                        GcadILog("manageVsDataPath: need clear.delSHA=", delSHA)
                        allSize = allSize - cfg_param.local_vsDataIndex[delSHA].size;
                        GcadILog("manageVsDataPath: allSize=", allSize)
                        fs.unlink(cfg_param.local_vsDataIndex[delSHA].path) 
                        delete  cfg_param.local_vsDataIndex[delSHA]
                        cfg_param.update_vsDataIndex(false, cfg_param); 
                    } 
                    else if (delSHA==cfg_param.document.sha1)
                    {
                        GcadILog("manageVsDataPath: need clear. delSHA=cfg_param.document.sha1")
                    }
                }
                */
                return new Promise((resolve, reject) => {
                    fs.syncfs(false, function (err) {
                        if (err) {
                            resolve(err);
                        }
                        else {
                            resolve(err);
                        }
                    });
                })
            };
            cb.eventBeginStage1();

            showMsg(GCAD_IDBFS_CHECK_BEGIN, '开始绑定本地数据!')
            await initFSFromIDB(m, cfg_param);
            showMsg(GCAD_IDBFS_CHECK_END, '绑定本地数据成功!')

            showMsg(GCAD_READ_LOCAL_DATA_BEGIN, '读取本地索引!')
            GcadILog("读取本地索引")
            let indexCtx = readLocalFileWithUtf8(m.FS, '/data/local_indexes.json');
            let local_sha1IndexCtx = readLocalFileWithUtf8(m.FS, '/data/local_sha1Index.json');
            var fileIndexes;
            let local_sha1Index;
            if (indexCtx && indexCtx != '') {
                fileIndexes = JSON.parse(indexCtx);//所有的文件索引
            }
            if (local_sha1IndexCtx && local_sha1IndexCtx != '') {
                local_sha1Index = JSON.parse(local_sha1IndexCtx);//所有的文件索引
                for (let key in local_sha1Index) {
                    local_sha1Index[key].path = cfg_param.path_tidy(local_sha1Index[key].path);
                }
            }
            if (!fileIndexes) {
                fileIndexes = {};
            }
            if (!local_sha1Index) {
                local_sha1Index = {};
            }

            let local_vsDataIndexCtx = readLocalFileWithUtf8(m.FS, '/data/local_vsDataIndex.json');
            let local_vsDataIndex;
            if (local_vsDataIndexCtx && local_vsDataIndexCtx != '') {
                local_vsDataIndex = JSON.parse(local_vsDataIndexCtx);//所有的文件索引
            }
            if (!local_vsDataIndex) {
                local_vsDataIndex = {};
            }
            cfg_param.local_vsDataIndex = local_vsDataIndex;

            cfg_param.initLocalvsDataIndex = async () => {
                return new Promise(async (resolve, reject) => {
                    let local_vsDataIndexCtx = await readLocalFileWithUtf8(m.FS, '/data/local_vsDataIndex.json')
                    let local_vsDataIndex;
                    if (local_vsDataIndexCtx && local_vsDataIndexCtx != '') {
                        local_vsDataIndex = JSON.parse(local_vsDataIndexCtx);//所有的文件索引
                    }
                    if (!local_vsDataIndex) {
                        local_vsDataIndex = {};
                    }
                    cfg_param.local_vsDataIndex = local_vsDataIndex;

                    function getObjectSize(obj) {
                        return Object.keys(obj).length; // 返回对象自身可枚举属性的数量
                    }
                    GcadILog("cfg_param.local_vsDataIndex.length=", getObjectSize(cfg_param.local_vsDataIndex))
                    GcadILog("读取本地显示数据索引结束")
                    resolve("本地显示数据索引读取完成");
                })
            }

            await cfg_param.initLocalvsDataIndex();
            GcadILog("读取本地索引结束")
            showMsg(GCAD_READ_LOCAL_DATA_END, '读取本地索引结束!');
            cfg_param.local_CopyFile = (fromPath, toPath, useSoftLink = true) => {//复制文件
                if (fromPath === toPath) return true;
                try {
                    m.FS.symlink(fromPath, toPath);
                    return true;
                }
                catch (e) {

                }
                return false;
            };
            cfg_param.local_RWCopyFile = (fromPath, toPath, useSoftLink = true) => {//复制文件
                if (fromPath === toPath) return true;
                try {
                    var tmpbuf = m.FS.readFile(fromPath, { encoding: 'utf8' });
                    m.FS.writeFile(toPath, tmpbuf);
                    return true;
                }
                catch (e) {

                }
                return false;
            };
            cfg_param.local_filesize = (path) => {//获取文件大小
                let s = m.FS.stat(path.replace('//', '/'));
                return m.FS.isFile(s.mode) ? s.size : 0;
            };
            cfg_param.local_existFile = (path) => {//文件是否存在
                try {
                    return cfg_param.fs.isFile(m.FS.stat(path.replace('//', '/')).mode);//
                } catch (e) {
                    return false;
                }
            };
            cfg_param.variant_replace = (str) => {
                var vr = cfg_param.var;
                let from = 0;
                res = "";
                let m = str.matchAll('\\$\\{[a-z|A-z|_][a-z|A-z|0-9]*\\}');
                for (const v of m) {
                    res += str.substr(from, v.index - from);
                    let key = v[0].substr(2, v[0].length - 3);
                    if (vr[key]) {
                        res += vr[key];
                    }
                    from = v.index + v[0].length;
                }
                res += str.substr(from, str.length - from);
                return res;
            };

            cfg_param.local_fileIndexes = fileIndexes;
            cfg_param.local_sha1Index = local_sha1Index;
            cfg_param.application.entry = 'gcadcloud'
            cfg_param.load_application = async (applicationName) => {
                return new Promise(async (resolve, reject) => {
                    GcadILog("加载应用:" + applicationName);
                    await loadEmscriptenModule(applicationName, cfg_param.cloudcanvas);
                    resolve("应用加载完成");
                })
            };
            cfg_param.execute_application = async (applicationName) => {
                return new Promise((resolve, reject) => {
                    var jsModulePromise = fetchTextLocal(applicationName + ".js")
                    Promise.all([jsModulePromise]).then(function ([jsModule]) {
                        self.eval(jsModule);
                        resolve("应用开始执行");
                    }).catch(function (error) {
                        console.error(error)
                        self.error = error;
                    });
                    GcadILog("execute_application.");
                })
            };
            cfg_param.setProcess = async (process, msg) => {
                if (msg || process > GCAD_OPEN_DWG_FINISH) {
                    if (process == GCAD_MAKE_OCF_FINISH + GCAD_OPEN_DWG_FINISH) {
                        GcadILog("setProcess big_fssync process=", process);
                        cb.setProcess(GCAD_OPEN_DWG_FINISH);
                        await cfg_param.big_fssync(false)
                        return
                    }
                    if (process > GCAD_OPEN_DWG_FINISH && GcadOpenDWGErrorMsg[process - GCAD_OPEN_DWG_FINISH]) {
                        //GcadILog("process=", process);
                        msg = GcadOpenDWGErrorMsg[process - GCAD_OPEN_DWG_FINISH]
                        cb.setProcess(GCAD_OPEN_DWG_FAILED, msg);   // 前段修改后 此处需设置返回-1
                        return
                    }
                    if (!msg) {
                        GcadILog("process=", process);
                        msg = "other error."
                        cb.setProcess(GCAD_OPEN_DWG_FAILED, msg);
                        return
                    }
                    cb.setProcess(GCAD_OPEN_DWG_FINISH);
                } else {
                    cb.setProcess(process);
                }
            };
            cfg_param.getFontListInfo = () => {
                return cfg_param.fontInfo
            };

            cfg_param.fetchFromServer = async (inputUrl, inputMethod, inputToken) => {
                return new Promise(async (resolve, reject) => {
                    try {
                        const response = await fetch(inputUrl, {
                            method: inputMethod,
                            headers: {
                                'Content-Type': 'application/json;charset=utf-8',
                                Authorization: inputToken,
                            },
                        })
                        if (response.ok) {
                            GcadILog('fetchFromServer : oooooooooooooook.');
                            resolve(response.arrayBuffer())
                        } else {
                            if (response.status === 404) {
                                GcadILog('fetchFromServer : err= 404, Not found.');
                            } else if (response.status === 500) {
                                GcadILog('fetchFromServer : err= 500, internal server error.');
                            } else {
                                GcadILog('fetchFromServer : err= other error, url invalid.');
                            }
                            resolve("fail")
                        }
                    } catch (e) {
                        GcadILog('fetchFromServer : fetch is fail, Promise rejected.');
                        GcadILog('fetchFromServer : e=', e);
                        resolve("fail")
                    }
                })
            };

            if (cfg_param.application.runEnv != "standalone") {
                if (__GSTAR_CLOUD_CAD__.instance.externalMount.token && __GSTAR_CLOUD_CAD__.instance.externalMount.token != '') {
                    cfg_param.token = __GSTAR_CLOUD_CAD__.instance.externalMount.token
                } else {
                    try {
                        var userInfo = JSON.parse(localStorage.getItem('user'))
                        cfg_param.token = userInfo.token
                    } catch (e) {
                        GcadILog("JSON.parse: get header token is invalid JSON.")
                        cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "get token failed.")
                        return
                    }
                }
                if (__GSTAR_CLOUD_CAD__.instance.externalMount.api_endpoint && __GSTAR_CLOUD_CAD__.instance.externalMount.api_endpoint != '') {
                    cfg_param.headerUrl = __GSTAR_CLOUD_CAD__.instance.externalMount.api_endpoint
                } else {
                    cfg_param.headerUrl = window.location.href.slice(0, window.location.href.indexOf('/cloudCAD.html'))
                }
                cfg_param.document.headerUrl = cfg_param.headerUrl.slice(0, cfg_param.headerUrl.lastIndexOf(':'))
            }
            cfg_param.fetchFromServerPost = async (inputUrl, inputMethod, inputToken, reqParam, onceSign) => {
                return new Promise( async (resolve, reject) => {
                    try {
                        const response = await fetch(inputUrl,{
                            method: inputMethod,
                            headers:{'onceSign': onceSign,
                               'Content-Type': 'application/json;charset=utf-8',
                                Authorization:  inputToken,
                            },
                            body: JSON.stringify(reqParam)
                        })
                        if (response.ok) {
                            GcadILog('fetchFromServerPost : oooooooooooooook.');
                            resolve(response.arrayBuffer())
                        }else{
                            if (response.status === 404) {
                                GcadILog('fetchFromServerPost : err= 404, Not found.');
                            } else if (response.status === 500) {
                                GcadILog('fetchFromServerPost : err= 500, internal server error.');
                            } else {
                                GcadILog('fetchFromServerPost : err= other error, url invalid.');
                            }
                            resolve("fail")       
                        }
                    } catch (e) {
                        GcadILog('fetchFromServerPost : fetch is fail, Promise rejected.');
                        GcadILog('fetchFromServerPost : e=', e);
                        resolve("fail")
                    }
                })
            };


            cfg_param.requireServerPost = async (reqType, reqParam, onceSign) => {
                tmpToken = cfg_param.token;
                tmpUrl = cfg_param.headerUrl + "/api/v2/_st/_file/_getOneRandKey" // + reqParam
                //body: JSON.stringify(data)
                tmpMethod = "POST"
                GcadILog('requireServerPost : tmpUrl=', tmpUrl);   
                GcadILog('requireServerPost : tmpMethod=', tmpMethod); 
                GcadILog('requireServerPost : onceSign=', onceSign); 
                GcadILog('requireServerPost : tmpToken=', tmpToken); 

                var retData = await cfg_param.fetchFromServerPost(tmpUrl, tmpMethod, tmpToken, reqParam, onceSign)
                //var retData = response.arrayBuffer()
                GcadILog('requireServerPost : oooooooooooooook. retData=', retData);
                if (retData == "fail") {
                    GcadILog('requireServerPost : fetch failed'); 
                } 
                else {
                    var retDataStr =  new TextDecoder().decode(retData)
                    if ( retDataStr == "illegal request") {
                        GcadILog('requireServerPost : getUrlSourceByKey fetch failed, illegal request'); 
                    } 
                    else {
                        GcadILog('requireServerPost : fetch Finished');
                        GcadILog('requireServerPost : retData=', retData); 
                        try {
                            testjson = JSON.parse(new TextDecoder().decode(retData))
                        } catch (e) {
                            GcadILog("requireServerPost : JSON.parse error, retData is invalid JSON.")
                        }
                        GcadILog('requireServerPost : testjson=', testjson); 
                        if ( reqType == "getOneRandKey") {
                            // jsonobj
                            return testjson.data
                        }
                    }
                }          
            }

            cfg_param.requireServer = async (reqType, reqParam) => {
                GcadILog('requireServer: reqType=', reqType);
                GcadILog('requireServer: reqParam=', reqParam);
                mToken = cfg_param.token;
                var headerUrl = cfg_param.headerUrl
                var needURL = {
                    "getOneRandKey":{"url":"/api/v2/_st/_file/_getOneRandKey","method":"post","data":{"onceSign":"4110"}},
                    "getDwgData": { "url": "/api/v2/_st/_file/_download", "method": "get", "data": { "fileId": "4110", "fileName": "sample.dwg" } },
                    "getDwgDataEn":{"url":"/api/v2/_st/_file/_xorEncrpty/_download","method":"get","data":{"fileId":"4110","fileName":"sample.dwg"}},
                    "getDownloadUrlSafe":{"url":"/api/v3/_st/_file/_download_url/safe","method":"get","data":{"fileId":"4097"}},
                    "getDownloadUrlExport":{"url":"/api/v3/_st/_file/_download_url/export","method":"get","data":{"fileId":"4097"}},
                    "getKVMeta": { "url": "/api/v2/_st/_file/_getKVMeta", "method": "get", "data": { "key": "11111111" } },
                    "getMeta": { "url": "/api/v2/_st/_file/_getMeta", "method": "get", "data": { "fileId": "4110", "metaKey": "gcad2d_gs_instruction" } },
                    "postTask": { "url": "/api/v2/_st/_task", "method": "post", "data": { "callback": "", "config": "", "fileId": "", "type": "", "version": "" } }
                };
                GcadILog("headerUrl=", headerUrl)
                mUrl = headerUrl + needURL[reqType].url + reqParam
                mMethod = needURL[reqType].method
                GcadILog('requireServer : mUrl=', mUrl);
                GcadILog('requireServer : mMethod=', mMethod);
                var retData = await cfg_param.fetchFromServer(mUrl, mMethod, mToken)

                if (retData == "fail") {
                    GcadILog('requireServer : fetch failed');
                } else {
                    if (reqType == "getDwgDataEn" || reqType == "getDwgData") {
                        return retData
                    }
                    var retDataStr = new TextDecoder().decode(retData)
                    if (retDataStr == "illegal request") {
                        GcadILog('requireServer : getUrlSourceByKey fetch failed, illegal request');
                    } else {
                        GcadILog('requireServer : fetch Finished');
                        GcadILog('requireServer : retData=', retData);
                        try {
                            testjson = JSON.parse(new TextDecoder().decode(retData))
                        } catch (e) {
                            GcadILog("requireServer : JSON.parse error, retData is invalid JSON.")
                        }
                        GcadILog('requireServer : testjson=', testjson);
                        if (reqType == "getMeta") {
                            GcadILog('requireServer : testjson.data.gcad2d_gs_instruction=', testjson.data.gcad2d_gs_instruction);
                            metainfo = JSON.parse(testjson.data.gcad2d_gs_instruction)
                            GcadILog('requireServer : metainfo=', metainfo);
                            myMetaInfo = metainfo.metaInfo[0];
                            GcadILog('requireServer : myMetaInfo=', myMetaInfo);
                            return myMetaInfo.history.V1.info.transition.taskStatus;
                        }
                        if ( reqType == "getKVMeta" || reqType == "getOneRandKey" || reqType == "getDownloadUrlSafe" || reqType == "getDownloadUrlExport" ) {
                            // jsonobj
                            return testjson.data
                        }
                    }
                }
            };

            function datatime() {
                Date.prototype.format = function (format) {
                    var o = {
                        "M+": this.getMonth() + 1, //month
                        "d+": this.getDate(), //day
                        "h+": this.getHours(), //hour
                        "m+": this.getMinutes(), //minute
                        "s+": this.getSeconds(), //second
                        "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
                        "S": this.getMilliseconds() //millisecond
                    }

                    if (/(y+)/.test(format)) {
                        format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
                    }

                    for (var k in o) {
                        if (new RegExp("(" + k + ")").test(format)) {
                            format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
                        }
                    }
                    return format;
                }
            }
            datatime();

            cfg_param.local_openFileIndex = [];  // 待开图 显示数据有多个
            cfg_param.local_vsDataStorageLimit = 134217728;  // 128M       
            cfg_param.compareDates = async (date1Str, date2Str) => {
                var date1 = new Date(date1Str);
                var date2 = new Date(date2Str);
                GcadILog("compareDates date1=", date1, "date2=", date2);
                if (date1.getTime() > date2.getTime()) {
                    GcadILog("date1 is later than date2");
                    return 1;
                } else if (date1.getTime() < date2.getTime()) {
                    GcadILog("date1 is earlier than date2");
                    return -1;
                } else {
                    GcadILog("date1 is the same as date2");
                    return 0;
                }
            };
            cfg_param.addVsDataIndex = async (newAddSize, strPATH, strSHA) => {
                var tmpObj2 = {};
                tmpObj2["path"] = strPATH;
                tmpObj2["sha1"] = strSHA;
                tmpObj2["size"] = newAddSize;
                var timesNow = new Date().format("yyyy-MM-dd hh:mm:ss.S");
                tmpObj2["time"] = timesNow;
                if (strPATH.endsWith(".dwg.gs") && cfg_param.local_vsDataIndex[cfg_param.document.sha1]) // 记录dwg对应的显示数据dwg.gs
                {
                    cfg_param.local_vsDataIndex[cfg_param.document.sha1].vsDataSha1 = strSHA;
                }
                cfg_param.local_vsDataIndex[strSHA] = tmpObj2;
                await cfg_param.update_vsDataIndex(false, cfg_param);
                GcadILog("addVsDataIndex add");
            };

            cfg_param.big_utime = (path, atime, mtime) => {
                window.FS.utime(path, atime, mtime)
            }

            cfg_param.big_addVsDataIndex = async (newAddSize, strPATH, strSHA, flag) => {
                var tmpObj2 = {};
                tmpObj2["path"] = strPATH;
                tmpObj2["sha1"] = strSHA;
                tmpObj2["size"] = newAddSize;
                var timesNow = new Date().format("yyyy-MM-dd hh:mm:ss.S");
                tmpObj2["time"] = timesNow;
                if (strPATH.endsWith(".dwg.gs") && cfg_param.local_vsDataIndex[cfg_param.document.sha1]) // 记录dwg对应的显示数据dwg.gs
                {
                    cfg_param.local_vsDataIndex[cfg_param.document.sha1].vsDataSha1 = strSHA;
                }
                cfg_param.local_vsDataIndex[strSHA] = tmpObj2;
                if (flag) {
                    await cfg_param.big_update_vsDataIndex(false, cfg_param);
                }
                GcadILog("addVsDataIndex add");
            };

            function sleep(time) {
                return new Promise((resolve) => setTimeout(resolve, time));
            };

            function hexStringToUint8Array(hexString) {
                // 分割为两位字符组（自动补全奇数长度字符串，如 "aaa" → "aa0a"）
                const hexPairs = hexString.match(/.{1,2}/g) || [];
                //GcadILog("hexStringToUint8Array: hexPairs=", hexPairs);
                return new Uint8Array(hexPairs.map(byte => parseInt(byte, 16)));
            }

            var kEnumTaskPending = 1
            var kEnumTaskExecuting = 2
            var kEnumTaskFinished = 3
            var kEnumTaskPartialFinished = 4
            var kEnumTaskFailed = 5
            var kEnumTaskCanceled = 6
            var waitTime = 1000 // 1000=1秒
            var reqNum = 20

            cfg_param.getCharDataTest = () => {
                var message = {}
                message.command = "getCharData"
                message.reqHeaderUrl = cfg_param.headerUrl
                message.token = cfg_param.token
                var tmpData = {}
                tmpData.requireStr = "A"
                tmpData.fontName = "SourceHanSansCN-Regular.ttf"
                GcadILog("sendCommand : tmpData=", tmpData)
                message.reqUrlData = tmpData  // "/gs/catalog"
                GcadILog("sendCommand : message=", message)
                cfg_param.workServer.postMessage(message)
            }

            cfg_param.setEncryptMode = () => {
                var message = {}
                message.command = "SetEncryptMode"
                message.mode = cfg_param.document.useEnMode
                GcadILog("sendCommand : message=", message)
                cfg_param.workServer.postMessage(message)
            }

            cfg_param.initEnModeAndKey = async () => {
                // 获取密钥 并解密 保存到idbfs 测试
                GcadILog("initEnModeAndKey : start") 

                var randKey           = '';
                var characters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                var charactersLength = characters.length;
                for (var i = 0; i < 20; i++) {
                    randKey += characters.charAt(Math.floor(Math.random() * charactersLength));
                }
                GcadILog("initEnModeAndKey : randKey=", randKey)
                GcadILog("initEnModeAndKey : randKey.length=", randKey.length)

                //var randKey = "sample.dwgsample.dwg" //todo len=20
                var did = cfg_param.document.did
                var reqDate = new Date().format("yyyy-MM-dd hh:mm:ss");
                var fileId = cfg_param.document.fileId
                var etag = cfg_param.document.sha1

                // 请求密钥信息
                var onceSign = "1234567890"
                var reqParams = {
                    "randKey":encodeURIComponent(randKey),
                    "did":did,
                    "reqDate":encodeURIComponent(reqDate),
                    "fileId":fileId,  
                    "etag":etag,
                }
                cfg_param.document.useEnMode = false 
                var tmpArrayBuf = await cfg_param.requireServerPost("getOneRandKey", reqParams, onceSign)                
                if (typeof tmpArrayBuf === "undefined") {
                    GcadELog("initEnModeAndKey : requireServer getOneRandKey error")
                    return 
                } 
                else {
                    GcadILog("initEnModeAndKey : tmpArrayBuf=", tmpArrayBuf)
                    if (typeof tmpArrayBuf.xorSwitch === "undefined") { // undefined
                        //cfg_param.document.useEnMode = false 
                    } 
                    else {
                       cfg_param.document.useEnMode = tmpArrayBuf.xorSwitch
                       cfg_param.setEncryptMode()
                    }
                } 

                // 计算密钥
                var tmpstr = encodeURIComponent(randKey) + '_' + did + '_' + encodeURIComponent(reqDate) + '_' + fileId + '_' + etag;
                const encoder = new TextEncoder(); 
                const encodedArray = encoder.encode(tmpstr); 
                var wordArray = cfg_param.CryptoJS.lib.WordArray.create(encodedArray.buffer)
                var tmpSha1 = cfg_param.CryptoJS.SHA1(wordArray).toString()
                GcadILog("initEnModeAndKey : tmpSha1=", tmpSha1) 
                const uint8ArrayShaData = encoder.encode(tmpSha1);
                // 需要将tmpArrayBuf.xorEncryptStr从16进制转为字符串 再进行异或

                var hexToStr = '';
                for (var i = 0; i < tmpArrayBuf.xorEncryptStr.length; i += 2) {
                    hexToStr += String.fromCharCode(parseInt(tmpArrayBuf.xorEncryptStr.substr(i, 2), 16));
                }
                GcadILog("initEnModeAndKey : hexToStr=", hexToStr) 

                const uint8ArrayRawData = encoder.encode(hexToStr);
                for (let i = 0; i < uint8ArrayRawData.length; i++) {
                    uint8ArrayRawData[i] = uint8ArrayRawData[i] ^ uint8ArrayShaData[i%uint8ArrayShaData.length]
                }
                const decoder = new TextDecoder();
                const decodedString = decoder.decode(uint8ArrayRawData);
                GcadILog("initEnModeAndKey : de decodedString=", decodedString) 
                cfg_param.document.useKey = decodedString  //此乃密钥
            }
            cfg_param.getAndStorageDwgData = async () => {
                // name  fileId  sha1 不为空
                var tmpLocalPath = GCAD_VS_DATA_DIR + cfg_param.document.sha1 + ".dwg"
                var exitSHAFile = false;
                exitSHAFile = cfg_param.local_existFile(tmpLocalPath)
                cfg_param.document.symlinkNeedCreat = 0
                cfg_param.document.useEnMode = false 
                await cfg_param.initEnModeAndKey();

                var lastSaveAsPath = "/data/tmp/lastsavepath.flag";

                GcadILog("getAndStorageDwgData cfg_param.local_vsDataIndex = ", cfg_param.local_vsDataIndex)

                if (exitSHAFile && cfg_param.local_vsDataIndex[cfg_param.document.sha1]
                    && cfg_param.local_vsDataIndex[cfg_param.document.sha1].sha1 == cfg_param.document.sha1
                    && cfg_param.local_vsDataIndex[cfg_param.document.sha1].path == tmpLocalPath
                    && cfg_param.local_vsDataIndex[cfg_param.document.sha1].size == cfg_param.document.size) {
                    GcadILog("getAndStorageDwgData dwg file exit, tmpLocalPath= ", tmpLocalPath)
                    var exitSymlinkFile = false;
                    var tmpPath = cfg_param.document.localVsRootPath + cfg_param.document.cloudPath
                    exitSymlinkFile = cfg_param.local_existFile(tmpPath)
                    if (!exitSymlinkFile) {
                        let cloudPathResult = cfg_param.document.cloudPath.split("/") // 3
                        var tmpPathTree = cfg_param.document.localVsRootPath
                        for (var k = 1; k < cloudPathResult.length - 1; k++) {
                            tmpPathTree = tmpPathTree + '/' + cloudPathResult[k]
                        }
                        GcadILog("getAndStorageDwgData symlink not exit, creat tmpPathTree=", tmpPathTree)
                        let fs = cfg_param.fs;
                        fs.mkdirTree(tmpPathTree)
                        var notUseSymlink = false
                        if (notUseSymlink) {
                            //cfg_param.local_RWCopyFile(tmpLocalPath, tmpPath, notUseSymlink)
                            var opts = {}
                            opts.flags = opts.flags || 0;
                            opts.encoding = opts.encoding || 'binary';
                            var tmpbuf = fs.readFile(tmpLocalPath, opts);
                            //var tmpBuf = new Uint8Array(tmpArrayBuf);
                            fs.writeFile(tmpPath, tmpbuf);
                            GcadELog("getAndStorageDwgData local_RWCopyFile tmpPath=", tmpPath)
                        }
                        else {
                            //fs.symlink(tmpLocalPath, tmpPath);
                            cfg_param.document.symlinkPath = tmpLocalPath
                            cfg_param.document.symlinkNeedCreat = 1
                        }
                        //fs.chmod(tmpLocalPath, 0777)
                        //fs.chmod(tmpPath, 0777) 
                        fs.writeFile(lastSaveAsPath, tmpLocalPath);
                        await fs_sync(false, cfg_param.fs);
                        cfg_param.document.localPath = tmpPath
                    }
                    else {
                        fs.writeFile(lastSaveAsPath, tmpLocalPath);
                        await fs_sync(false, cfg_param.fs);
                        cfg_param.document.symlinkPath = tmpLocalPath
                        cfg_param.document.symlinkNeedCreat = 2
                        cfg_param.document.localPath = tmpPath
                    }
                }
                else {
                    var tmpBuf = new Uint8Array();
                    tmpBuf = await cfg_param.getDownload(cfg_param.document.fileId);
                    var tmpSize = tmpBuf.length;
                    GcadILog("getAndStorageDwgData tmpSize = ", tmpSize)
                    if (tmpSize != cfg_param.document.size) {
                        GcadILog("getAndStorageDwgData dwg file size error.")
                        cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "input params dwg size error.")// 返回开图失败
                        return new Promise((resolve, reject) => resolve(false)); 
                    }
                    if ((cfg_param.document.useEnMode && tmpBuf[0] == 65 && tmpBuf[1] == 67 ) || 
                        (!cfg_param.document.useEnMode && tmpBuf[0] == 65 && tmpBuf[1] == 68 )){ // 加密模式获取明文则自己加密，出现则服务器异常;非加密模式出现密文则解密
                        GcadILog("getAndStorageDwgData: En  need  encrypt")
                        if(cfg_param.document.useEnMode) {
                            tmpBuf[1] = 68
                        } else {
                            tmpBuf[1] = 67
                        }
                        
                        // 1. 计算固定串
                        var BasePrimeArr = [311, 317, 13, 293, 31, 197, 251, 113, 283, 397, 179, 191, 223, 233, 43, 409,
                                    241, 229, 347, 157, 389, 337, 281, 379, 103, 269, 199, 167, 7, 37, 359, 3,
                                    139, 107, 239, 67, 127, 109, 17, 211, 59, 277, 181, 53, 11, 367, 19, 47,
                                    149, 307, 227, 313, 83, 61, 401, 79, 271, 151, 257, 23, 5, 263, 97, 163,
                                    331, 41, 137, 101, 89, 73, 59, 71, 353, 2, 173, 349, 43, 53, 503, 509, 
                                    307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379, 383, 389, 397, 
                                    401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 
                                    499, 503, 509, 521, 523, 541];
                        GcadILog("getAndStorageDwgData: BasePrimeArr.length=", BasePrimeArr.length) 
                        var tBufferIn = [];
                        for( var k = 0; k < BasePrimeArr.length; k++ ) {
                            tBufferIn[k*4+0] = BasePrimeArr[k] >> 24; //大端
                            tBufferIn[k*4+1] = BasePrimeArr[k] >> 16;
                            tBufferIn[k*4+2] = BasePrimeArr[k] >> 8;
                            tBufferIn[k*4+3] = BasePrimeArr[k];
                        }
                        GcadILog("getAndStorageDwgData: En tBufferIn=", tBufferIn) 
                        // 2. 计算T表: sha 1024-2048, 固定串, sha2048-3072
                        var tBufferBegin = tmpBuf.slice(1024, 2048); 
                        var wordArrayBegin = cfg_param.CryptoJS.lib.WordArray.create(tBufferBegin)
                        var tmpSha1BeginHexStr = cfg_param.CryptoJS.SHA1(wordArrayBegin).toString()//计算  
                        GcadILog("getAndStorageDwgData: En tmpSha1BeginHexStr=", tmpSha1BeginHexStr)      
                        var tBufferEnd = tmpBuf.slice(2048, 3072)
                        var wordArrayEnd = cfg_param.CryptoJS.lib.WordArray.create(tBufferEnd)
                        var tmpSha1EndHexStr = cfg_param.CryptoJS.SHA1(wordArrayEnd).toString()//计算
                        GcadILog("getAndStorageDwgData: En tmpSha1EndHexStr=", tmpSha1EndHexStr)  

                        var tableArrLen = tmpSha1BeginHexStr.length/2 + tBufferIn.length+ tmpSha1EndHexStr.length/2

                        var tmpSha1BeginArr = hexStringToUint8Array(tmpSha1BeginHexStr)
                        var tmpSha1EndArr = hexStringToUint8Array(tmpSha1EndHexStr)

                        var tableArr = new Uint8Array(tableArrLen)
                        for (var i = 0; i < tmpSha1BeginArr.length; i += 1) {
                            tableArr[i] = tmpSha1BeginArr[i];
                        }
                        var startPos = tmpSha1BeginHexStr.length/2
                        //GcadILog("getAndStorageDwgData: En startPos=", startPos) 
                        for (var i = 0; i < tBufferIn.length; i += 1) {
                            tableArr[startPos + i] = tBufferIn[i];
                        }
                        startPos = startPos + tBufferIn.length
                        //GcadILog("getAndStorageDwgData: En startPos=", startPos) 
                        for (var i = 0; i < tmpSha1EndArr.length; i += 1) {
                            tableArr[startPos + i] = tmpSha1EndArr[i];
                        }

                        // 3. 计算位置
                        var wordArrayTable = cfg_param.CryptoJS.lib.WordArray.create(tableArr)
                        var tmpSha1TableHexStr = cfg_param.CryptoJS.SHA1(wordArrayTable).toString()//计算 
                        GcadILog("getAndStorageDwgData: En tmpSha1TableHexStr=", tmpSha1TableHexStr) 
                        var tmpPos = 0;
                        for (var i = 0; i < tmpSha1TableHexStr.length; i += 1) {
                            tmpPos += parseInt(tmpSha1TableHexStr.substr(i, 1), 16);
                        }  
                        tmpPos = tmpPos & -4;
                        tmpPos = tmpPos + 3072;
                        var beginEncrptyPos = tmpPos;
                        GcadILog("getAndStorageDwgData: En beginEncrptyPos=", beginEncrptyPos) 

                        cfg_param.document.useEnMode = true
                        var keyStr = cfg_param.document.useKey
                        var encoder = new TextEncoder(); 
                        var keyArr = encoder.encode(keyStr);
                        var keyArrLen = keyArr.length
                        //GcadILog("getAndStorageDwgData: En keyArr=", keyArr)
                        GcadILog("getAndStorageDwgData: En keyArrLen=", keyArrLen)
                        // 4. 加密
                        for( var idx = beginEncrptyPos; idx < tmpSize; idx++ ){
                            tmpBuf[idx] ^= (keyArr[(idx-beginEncrptyPos)%(keyArrLen)] ^ tableArr[(idx-beginEncrptyPos)%(tableArrLen)]);
                        }
                        //GcadILog("getAndStorageDwgData tmpBuf = ", tmpBuf)
                    }

                    let fs = cfg_param.fs;
                    fs.writeFile(tmpLocalPath, tmpBuf);
                    var tmpPath = cfg_param.document.localVsRootPath + cfg_param.document.cloudPath
                    GcadELog("getAndStorageDwgData symlink tmpPath=", tmpPath)
                    let cloudPathResult = cfg_param.document.cloudPath.split("/") // 3
                    var tmpPathTree = cfg_param.document.localVsRootPath
                    for (var k = 1; k < cloudPathResult.length - 1; k++) {
                        tmpPathTree = tmpPathTree + '/' + cloudPathResult[k]
                    }
                    GcadILog("getAndStorageDwgData symlink tmpPathTree=", tmpPathTree)
                    fs.mkdirTree(tmpPathTree)
                    var notUseSymlink = false
                    if (notUseSymlink) {
                        //cfg_param.local_RWCopyFile(tmpLocalPath, tmpPath, notUseSymlink)
                        var opts = {}
                        opts.flags = opts.flags || 0;
                        opts.encoding = opts.encoding || 'binary';
                        var tmpbuf = fs.readFile(tmpLocalPath, opts);
                        //var tmpBuf = new Uint8Array(tmpArrayBuf);
                        fs.writeFile(tmpPath, tmpbuf);
                        GcadELog("getAndStorageDwgData local_RWCopyFile tmpPath=", tmpPath)
                    }
                    else {
                        //fs.symlink(tmpLocalPath, tmpPath);
                        cfg_param.document.symlinkPath = tmpLocalPath
                        cfg_param.document.symlinkNeedCreat = 1
                    }
                    //fs.chmod(tmpLocalPath, 0777)
                    //fs.chmod(tmpPath, 0777)                    
                    fs.writeFile(lastSaveAsPath, tmpLocalPath);
                    await fs_sync(false, cfg_param.fs);
                    await cfg_param.addVsDataIndex(tmpSize, tmpLocalPath, cfg_param.document.sha1);
                    cfg_param.document.localPath = tmpPath
                }
                cfg_param.statusDwgData = GCAD_DWG_DATA_READY;
                return new Promise((resolve, reject) => resolve(true));
            }
	    
	     cfg_param.getLicense = async () => {
                var tmpLicensePath = "/data/license.data"
                // del old license
                var exitFile = false;
                exitFile = cfg_param.local_existFile(tmpLicensePath)
                if (exitFile) {
                    GcadILog("getLicense: find old license, del it")
                    cfg_param.fs.unlink(tmpLicensePath)
                    await fs_sync(false, cfg_param.fs);
                }
                // download new license
                var url =  cfg_param.headerUrl + "/api/v2/sdk/license/export"
                GcadILog('getLicense: url=', url);       
                var retData = await cfg_param.fetchFromServer(url, "get", cfg_param.token)
                console.log("getLicense: retData=", retData)

                if (retData == "fail") {
                    GcadELog('getLicense: requireServer fetch failed'); 
                    return  new Promise((resolve, reject) => resolve(false));  
                } 
                else {
                    var retDataStr =  new TextDecoder().decode(retData)
                    if ( retDataStr == "illegal request") {
                        GcadELog('getLicense: requireServer fetch failed, illegal request'); 
                        return new Promise((resolve, reject) => resolve(false));  
                    } 
                    else {
                        var tmpBuf = new Uint8Array(retData);
                        var tmpSize = tmpBuf.length;
                        GcadILog("getLicense :  tmpSize = ", tmpSize)
                        if (tmpSize == 0) {
                            GcadELog('getLicense: getLicense failed'); 
                            return  new Promise((resolve, reject) => resolve(false));  
                        } 
                        let fs = cfg_param.fs;
                        fs.writeFile(tmpLicensePath, tmpBuf);
                        await fs_sync(false, cfg_param.fs);
                        GcadILog("getLicense :  end")
                    }      
                }
                GcadILog("getLicense :  end")
                return new Promise((resolve, reject) => resolve(true));
            }

            cfg_param.updateIndex = async (info) => {
                GcadILog("info=", info)
                return new Promise((resolve, reject) => resolve(true));
            }

            // 下载单个依赖文件
            cfg_param.findFile = (dependentFile, fop) => {
                GcadILog("findFile : dependentFile=", dependentFile)
                var message = {}
                message.command = "findFile"
                message.reqHeaderUrl = cfg_param.headerUrl
                message.token = cfg_param.token
                message.fileId = cfg_param.document.fileId
                message.dependentFile = dependentFile // "../xrefs/1.1.dwg"
                message.fop = fop
                GcadILog("findFile : message=", message)
                cfg_param.workServer.postMessage(message)
            }
            // 批量下载依赖文件
            cfg_param.findFiles = (dependentFiles, fop) => {
                GcadILog("findFiles : dependentFiles=", dependentFiles) //
                var message = {}
                message.command = "findFiles"
                message.reqHeaderUrl = cfg_param.headerUrl
                message.token = cfg_param.token
                message.fileId = cfg_param.document.fileId
                var list = dependentFiles.split(";"); // dependentFiles="../xrefs/1.1.dwg;../xrefs/1.1.1.dwg"
                message.dependentFile = list
                message.fop = fop
                GcadILog("findFiles : message=", message)
                cfg_param.workServer.postMessage(message)
            }

            cfg_param.postMessageVsData = (type) => {
                var message = {}
                message.command = "getAndStorageVsData"
                message.reqHeaderUrl = cfg_param.headerUrl
                message.token = cfg_param.token
                var tmpData = {}
                tmpData.fileId = cfg_param.document.fileId
                tmpData.version = cfg_param.document.version
                tmpData.sha1 = cfg_param.document.sha1
                GcadILog("sendCommand : tmpData=", tmpData)
                message.reqUrlData = tmpData  // "/gs/catalog"
                message.needReturnData = type
                GcadILog("sendCommand : message=", message)
                cfg_param.workServer.postMessage(message)
            }

            // only 测试用
            cfg_param.testDownloadFile = (fileName) => {
                GcadILog("testDownloadFile : fileName=", fileName)
                var opts = {}
                opts.flags = opts.flags || 0;
                opts.encoding = opts.encoding || 'binary';
                var tmpUint8Array = window.FS.readFile(fileName, opts); //todo
                let url = window.URL.createObjectURL(new Blob([tmpUint8Array.buffer], { type: "arraybuffer" }))
                const link = document.createElement('a');
                link.style.display = 'none';
                link.href = url;
                var name = fileName.split('/').pop();
                link.setAttribute('download', name);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
            cfg_param.testSetLogLevel = (index) => {
                cfg_param.document.logLevel = index
                var message = {}
                message.command = "testSetLogLevel"
                message.level = index
                GcadILog("sendCommand : message=", message)
                cfg_param.workServer.postMessage(message)
                GcadILog("testSetLogLevel : loglevel=", index)
            }

            // 先获取sid 再获取data并存储 管理
            cfg_param.getAndStorageVsData = async (reqkey, gsname) => {
                GcadILog('getAndStorageVsData: reqkey=', reqkey);

                // 检查是否已有显示数据
                var exitDwgGsFile = false;
                if (cfg_param.local_vsDataIndex && cfg_param.local_vsDataIndex[cfg_param.document.sha1]
                    && cfg_param.local_vsDataIndex[cfg_param.document.sha1].vsDataSha1) {
                    var tmpSHA = cfg_param.local_vsDataIndex[cfg_param.document.sha1].vsDataSha1;
                    if (cfg_param.local_vsDataIndex[tmpSHA] && cfg_param.local_vsDataIndex[tmpSHA].path) {
                        var tmpLocalPath = cfg_param.local_vsDataIndex[tmpSHA].path;
                        exitDwgGsFile = cfg_param.local_existFile(tmpLocalPath)
                        if (exitDwgGsFile) {
                            cfg_param.local_openFileIndex.push(cfg_param.local_vsDataIndex[tmpSHA]);
                            cfg_param.statusVsData = GCAD_VS_DATA_READY;
                            return new Promise((resolve, reject) => resolve(true));
                        }
                        else {
                            GcadILog('local_existFile: not exit,tmpLocalPath=', tmpLocalPath);
                            if (cfg_param.big_local_existFile(tmpLocalPath)) {
                                GcadILog('big_local_existFile: exit,tmpLocalPath=', tmpLocalPath);
                            } else {
                                GcadILog('big_local_existFile: not exit,tmpLocalPath=', tmpLocalPath);
                            }
                        }
                    }
                }
                else {
                    GcadILog('is exist vsData File: local_vsDataIndex not exit');
                }

                //异步worker请求显示数据
                cfg_param.postMessageVsData(false)
                return new Promise((resolve, reject) => resolve(true));
            };

            cfg_param.startOpenDWG = async (userDwgInfo) => {
                GcadILog("startOpenDWG.");
                var tmpdwgObj = {}
                var wordArray = cfg_param.CryptoJS.lib.WordArray.create(userDwgInfo.data)
                tmpdwgObj.sha1 = cfg_param.CryptoJS.SHA1(wordArray).toString()//计算
                tmpdwgObj.path = '/data/' + userDwgInfo.name + '.dwg'
                cfg_param.document.localPath = tmpdwgObj.path
                if (local_sha1Index && local_sha1Index[tmpdwgObj.sha1]
                    && cfg_param.big_local_existFile(local_sha1Index[tmpdwgObj.sha1].path)
                    && cfg_param.big_local_existFile(tmpdwgObj.path)) {
                    GcadILog("downloadFile: data from dwg file, file exit,not need write.");
                } else {
                    GcadILog("downloadFile: data from dwg file, writefile=", tmpdwgObj.path);
                    cfg_param.mkdirTree(cfg_param.getPath(tmpdwgObj.path));
                    cfg_param.writeFile(tmpdwgObj.path, new Uint8Array(userDwgInfo.data));
                    local_sha1Index[tmpdwgObj.sha1] = tmpdwgObj;
                    //local_fileIndexes[tmpdwgObj.path] = tmpdwgObj;
                }
                await cfg_param.big_fssync(false)
            };

            Module.onRuntimeInitialized = function () {
                if (Module.cfg_param.document.symlinkNeedCreat && Module.cfg_param.document.symlinkNeedCreat != 0) {
                    if (Module.cfg_param.document.symlinkNeedCreat == 2) {
                        window.FS.unlink(Module.cfg_param.document.localPath, function (err) {
                            if (err) {
                                GcadELog("onRuntimeInitialized fs.unlink err=", err)
                                reject(err);
                            } else {
                                GcadELog("onRuntimeInitialized fs.unlink tmpPath=", tmpPath)
                                resolve(err);
                            }
                        });
                    }
                    window.FS.symlink(Module.cfg_param.document.symlinkPath, Module.cfg_param.document.localPath);
                    //window.FS.chmod(Module.cfg_param.document.symlinkPath, 0777)
                    //window.FS.chmod(Module.cfg_param.document.localPath, 0777)   
                    Module.cfg_param.big_fssync(false)
                }
                Module.cfg_param.document.openMode = Module.cfg_param.statusDwgData + Module.cfg_param.statusVsData;
                if (Module.cfg_param.document.openMode == 0) {
                    // Module.cfg_param.setProcess(GCAD_DOWN_DWG_ERROR)
                    Module.cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "download dwg file failed.")
                }
                if(cfg_param.statusLicense == GCAD_LICENSE_NOT_READY)
                {
                    GcadELog("open dwg failed: download license file failed.");
                    Module.cfg_param.setProcess(GCAD_OPEN_DWG_FAILED, "download license file failed.") //此处非必需 底层还会检查证书，并通知前端
                }
                var vsFileInfo = {};
                vsFileInfo.main = ""
                vsFileInfo.ext_datafile = []
                GcadILog("Module.cfg_param.local_openFileIndex.length=", Module.cfg_param.local_openFileIndex.length);
                for (var j = 0; j < Module.cfg_param.local_openFileIndex.length; j++) {
                    var tmpVsFileObj = {}
                    tmpVsFileObj.size = Module.cfg_param.local_openFileIndex[j].size
                    tmpVsFileObj.sha1 = Module.cfg_param.local_openFileIndex[j].sha1
                    tmpVsFileObj.name = tmpVsFileObj.sha1 + '.dwg.gs'
                    tmpVsFileObj.path = GCAD_VS_DATA_DIR + tmpVsFileObj.name
                    vsFileInfo.ext_datafile.push(tmpVsFileObj)
                }
                if (Module.cfg_param.local_openFileIndex.length != 0) {
                    Module.cfg_param.document.vsFileInfo = vsFileInfo
                }
                GcadILog("Module.cfg_param.document=", Module.cfg_param.document);
                var s = Module.allocateUTF8(JSON.stringify(Module.cfg_param.document));
                Module._set_user_data_string(s);
                Module._free(s);
                Module.cfg_param.statusSetDocument = true
            }
            Module.cfg_param = cfg_param;
            showMsg(GCAD_LOAD_APPLICATION_DATA_BEGIN, '开始进程序引导过程!');
            gcad_boot(cfg_param, cb);
        });
    }
};

var loadPrintPreviewPromise = null
function loadPrintPreview(path) {
  return new Promise((resolve, reject) => {
    var script = document.createElement('script')
    script.src = `${path}PreviewModule.js` + '?v=' + (window.sdkver || Date.now())
    script.type = "text/javascript"
    document.body.appendChild(script)
    script.onerror = reject;
    script.onload = function () {
      resolve()
    }
  })
}
