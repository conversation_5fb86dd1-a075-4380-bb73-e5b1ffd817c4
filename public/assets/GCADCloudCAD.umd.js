!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).GCADCloudCAD=t()}(this,(function(){"use strict";const e=function(){var e={},t={},n=-1,r="*";function i(e,t,n){try{e(t,n)}catch(r){setTimeout(function(e){return function(){throw e}}(r),0)}}function o(e,t,n){e(t,n)}function u(e,n,r,u){var s,a=t[n],c=u?o:i;if(t.hasOwnProperty(n))for(s in a)a.hasOwnProperty(s)&&c(a[s],e,r)}function s(e){var n=String(e);return Boolean(t.hasOwnProperty(n)&&function(e){var t;for(t in e)if(e.hasOwnProperty(t))return!0;return!1}(t[n]))}function a(e,t,n,i){var o=function(e,t,n){return function(){var i=String(e),o=i.lastIndexOf(".");for(u(e,e,t,n);-1!==o;)o=(i=i.substr(0,o)).lastIndexOf("."),u(e,i,t,n);u(e,r,t,n)}}(e="symbol"==typeof e?e.toString():e,t,i),a=function(e){for(var t=String(e),n=s(t)||s(r),i=t.lastIndexOf(".");!n&&-1!==i;)i=(t=t.substr(0,i)).lastIndexOf("."),n=s(t);return n}(e);return!!a&&(!0===n?o():setTimeout(o,0),!0)}return e.publish=function(t,n=void 0){return a(t,n,!1,e.immediateExceptions)},e.publishSync=function(t,n=void 0){return a(t,n,!0,e.immediateExceptions)},e.subscribe=function(e,r){if("function"!=typeof r)return!1;e="symbol"==typeof e?e.toString():e,t.hasOwnProperty(e)||(t[e]={});var i="uid_"+String(++n);return t[e][i]=r,i},e.subscribeAll=function(t){return e.subscribe(r,t)},e.subscribeOnce=function(t,n){var r=e.subscribe(t,(function(){e.unsubscribe(r),n.apply(this,arguments)}));return e},e.clearAllSubscriptions=function(){t={}},e.clearSubscriptions=function(e){var n;for(n in t)t.hasOwnProperty(n)&&0===n.indexOf(e)&&delete t[n]},e.countSubscriptions=function(e){var n,r=0;for(n in t)t.hasOwnProperty(n)&&0===n.indexOf(e)&&r++;return r},e.getSubscriptions=function(e){var n,r=[];for(n in t)t.hasOwnProperty(n)&&0===n.indexOf(e)&&r.push(n);return r},e.unsubscribe=function(n){var r,i,o,u="string"==typeof n&&(t.hasOwnProperty(n)||function(e){var n;for(n in t)if(t.hasOwnProperty(n)&&0===n.indexOf(e))return!0;return!1}(n)),s=!u&&"string"==typeof n,a="function"==typeof n,c=!1;if(!u){for(r in t)if(t.hasOwnProperty(r)){if(i=t[r],s&&i[n]){delete i[n],c=n;break}if(a)for(o in i)i.hasOwnProperty(o)&&i[o]===n&&(delete i[o],c=!0)}return c}e.clearSubscriptions(n)},e}();return async function({token:t,element:n,param:r={}}){if(r.cadServer&&r.cadServer.endsWith("/")&&(r.cadServer=r.cadServer.slice(0,-1)),(r={cadServer:"https://cloudapi.gstarcad.com",fileId:"",fileName:"",...r}).cadServer||(r.cadServer="https://cloudapi.gstarcad.com"),!t)return alert(`参数异常：token=${t}`);if(!n)return alert(`参数异常：element=${n}`);if(!r.fileId)return alert(`参数异常：param.fileId=${r.fileId}`);const i=document.getElementById(n);if(!i)return alert(`参数异常：id=${n} dom不存在！`);window.addEventListener("message",(function(t){t.origin===r.cadServer&&t.data.action&&e.publish(t.data.action,t.data.payload)}));const o=function(e){const t=document.createElement("iframe");return t.setAttribute("style","width:100%;height:100%;"),t.setAttribute("allowfullscreen","true"),t.setAttribute("frameborder","0"),e.appendChild(t),t}(i);try{await function(e,t){return new Promise(((n,r)=>{function i(){return e.contentWindow?(e.removeEventListener("load",i),n(!0)):(e.removeEventListener("load",i),r(null))}e.addEventListener("load",i),e.src=`${t}/cloudCAD.html`}))}(o,r.cadServer)}catch(s){return alert("加载失败")}function u(e){o.contentWindow.postMessage(e,r.cadServer)}return e.subscribe("close",((e,{msg:t})=>{var n;o&&(null==(n=o.contentWindow)||n.close(),i.removeChild(o))})),u({action:"start",payload:{...r,token:t}}),{on:(t,n)=>e.subscribe(t,n),off:t=>{e.unsubscribe(t)},call:(t,n)=>{const r=+new Date+"";return new Promise(((i,o)=>{e.subscribeOnce(`call.${r}`,((e,t)=>{"resolve"===t.status?i(t.result):o(t.result)})),u({action:"call",eid:r,payload:{fn:t,params:n}})}))}}}}));
