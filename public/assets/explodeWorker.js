self.onmessage = function(event) {
  const { level, explodeX, explodeY, explodeZ, magnitude, explodeCenter, explodeNodes } = event.data;
  const matrices = [];
  for (const node of explodeNodes) {
      if (node && explodeCenter) {
          const delta = subtractPoints(calculateCenter(node.bbox.min, node.bbox.max), explodeCenter);
          const scaledDelta = scalePoint(delta, magnitude * (node.level / level));

          if (!explodeX) scaledDelta.x = 0;
          if (!explodeY) scaledDelta.y = 0;
          if (!explodeZ) scaledDelta.z = 0;

          const translationMatrix = createTranslationMatrix(scaledDelta);
          const tempMatrix = multiplyMatrices(node.netMatrix.m, translationMatrix)
          const tempMatrix2 = multiplyMatrices(tempMatrix, node.netMatrixInverse.m)
          const resultMatrix = multiplyMatrices(node.matrix.m, tempMatrix2)
          matrices.push(resultMatrix);
      }
  }
  // 返回计算结果
  self.postMessage(matrices);
};

// 计算中心点
function calculateCenter(minPoint, maxPoint) {
  const centerX = (minPoint.x + maxPoint.x) / 2;
  const centerY = (minPoint.y + maxPoint.y) / 2;
  const centerZ = (minPoint.z + maxPoint.z) / 2;

  return {
      x: centerX,
      y: centerY,
      z: centerZ
  };
}

// 点相减
function subtractPoints(pointA, pointB) {
  return {
      x: pointA.x - pointB.x,
      y: pointA.y - pointB.y,
      z: pointA.z - pointB.z
  };
}

// 点缩放
function scalePoint(point, scale) {
  return {
      x: point.x * scale,
      y: point.y * scale,
      z: point.z * scale
  };
}

// 创建平移矩阵
function createTranslationMatrix(delta) {
  // 初始化为单位矩阵
  const m = new Array(16).fill(0);
  m[0] = 1; 
  m[5] = 1;
  m[10] = 1;
  m[15] = 1;
  // 设置平移分量
  m[12] = delta.x; // X 平移
  m[13] = delta.y; // Y 平移
  m[14] = delta.z; // Z 平移
  return m
}

// 矩阵相乘
function multiplyMatrices(matrixA, matrixB) {
  const result = new Float32Array(16);
  // 实现矩阵相乘的逻辑
  for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
          result[i * 4 + j] = matrixA[i * 4] * matrixB[j] +
                              matrixA[i * 4 + 1] * matrixB[j + 4] +
                              matrixA[i * 4 + 2] * matrixB[j + 8] +
                              matrixA[i * 4 + 3] * matrixB[j + 12];
      }
  }
  return result;
}

