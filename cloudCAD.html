<!doctype html>
<html lang="en-us">
<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0" />
  <title>cloudCAD</title>
  <style>
    html {
      background-color: #000 !important;
    }
    html,
    body {
      padding: 0;
      margin: 0;
      overflow: hidden;
      height: 100%;
    }
  </style>
</head>

<body>
  <div id="wcadWrap"></div>
  <script type="text/javascript" src="/assets/CloudCAD/boot.js"></script>
  <script type="text/javascript" src="/assets/CloudCAD/cadboot.js"></script>
  <script type="text/javascript" src="/assets/CloudCAD/GStarCloudCAD.js"></script>
  <script type="text/javascript" src="/assets/CloudCAD/jszip.min.js"></script>
  <script type="text/javascript" src="/assets/crypto-js.min.js"></script>
  <script type="text/javascript" src="/assets/CloudCADBoot.umd.js"></script>
  <script type="module">
    import '/src/app/common/execTime.js'
    const {record} = window.__GSTAR_CLOUD_CAD__.execTime
    record('html script start')
    let cloudCAD
    window.addEventListener('load', async (e) => {
      let _url = window.location.href.replace(/\+/g, '%2B')
  	  _url = _url.replace(/#/g, '%23')
      const curUrl = new URL(_url)
      let params = {}
      curUrl.searchParams.forEach((v, k) => {
        try {
          params[k] = JSON.parse(v)
        } catch (e) {
          params[k] = v
        }
      })
      // 有时候传过来的fileId是 id
      try {
        if (!params['fileId']) params['fileId'] = Number(params.id)
      } catch(err) {}

      if (!params.fileName) {
        params.fileName = params.name
      }
      
      // console.log('curUrl.searchParams', params)

      // 如果是直接传递fileId，认为是页面跳转已经有足够的参数。否则等待message启动
      if (params['fileId']) {
        try {
          record('前端初始化(CloudCADBoot)')
          cloudCAD = await main(params)
          record('调用cadboot GstarCADBoot.start')
          cloudCAD.start()
        } catch(err) {
          console.error(err)
          alert(err.message || JSON.stringify(err))
        }
      }
    })
    function isInIframe() {
      try {
        return window.self !== window.top;
      } catch (e) {
        return true;
      }
    }
    function safeCloseWindow() {
      window.close();
      // 防止立即关闭前 JS 还没反应，稍等检测
      setTimeout(() => {
        try {
          if (!window.closed) {
            alert("此页面无法被自动关闭，请手动关闭浏览器标签页。");
          }
        } catch (e) {
          alert("此页面无法被自动关闭，请手动关闭浏览器标签页。");
        }
      }, 300)
    }
    function postMsgResponse(eid, payload) {
      window.parent.postMessage({
        action: `_sendMessageResponse.${eid}`,
        payload
      }, '*')
    }
    function serializeError(error) {
      return {
        __isSerializedError: true,
        name: error.name,
        message: error.message,
        stack: error.stack,
      };
    }
    window.addEventListener( "message",
      async (e)=>{
        // console.log('收到消息', e)
        // console.log('isInIframe', isInIframe())
        if (e.data) {
          switch(e.data.action) {
            case 'cloudCADPublish':
              if (cloudCAD) {
                cloudCAD.pubSub.publish(e.data.payload)
              }
              break
            case 'call':{
              try {
                if (!cloudCAD) throw new Error('cloudCAD not ready')
                let result = undefined
                const { fn, params } = e.data.payload
                const fnPath = fn.split('.');
                // 从 cloudCAD 中逐层取出方法
                let method = cloudCAD;
                for (let i = 0; i < fnPath.length - 1; i++) {
                    method = method[fnPath[i]];
                }
                const methodName = fnPath[fnPath.length - 1];

                if (typeof method[methodName] !== 'function') {
                  throw new Error(`Method ${fn} not found or is not a function`)
                }
                result = await method[methodName](params)
                postMsgResponse(e.data.eid, {
                  status: 'resolve',
                  result
                })
              } catch (err) {
                postMsgResponse(e.data.eid, {
                  status: 'reject',
                  result: serializeError(err)
                })
              }
              break
            }
            case 'main':
              // console.log('收到main', e.data.payload)
              try {
                cloudCAD = await main(e.data.payload)
                postMsgResponse(e.data.eid, {
                  status: 'resolve',
                  result: null
                })
              } catch(err) {
                postMsgResponse(e.data.eid, {
                  status: 'reject',
                  result: serializeError(err)
                })
              }
              break
            case 'start':
              // console.log('收到start', e.data.payload)
              try {
                cloudCAD.start(e.data.payload)
                postMsgResponse(e.data.eid, {
                  status: 'resolve',
                  result: null
                })
              } catch(err) {
                postMsgResponse(e.data.eid, {
                  status: 'reject',
                  result: serializeError(err)
                })
              }
              break
            case 'notice.reEdit': // 不是外部调用，则自己重载页面
              if (!isInIframe()) window.location.reload()
              break
            case 'notice.exit': // 不是外部调用，则自己关闭页面
              if (!isInIframe()) safeCloseWindow()
              break
          }
        }
      },	false);

    async function main(params) {
      let cloudCAD = null
      try {
        cloudCAD = await window.CloudCADBoot({
          ...params,
          wrapId: 'wcadWrap',
          documentCloudPath: '',
          iconUrl: `${import.meta.env.VITE_PUBLIC_PATH}/assets/CloudCAD/icon`,
          baseUrl: `${import.meta.env.VITE_PUBLIC_PATH}/assets/CloudCAD/`,
          fetch_listUrl: `${import.meta.env.VITE_PUBLIC_PATH}/assets/CloudCAD/fetch_list.json`,
          s1Url: `${import.meta.env.VITE_PUBLIC_PATH}/assets/CloudCAD/s1_need.json`,
          s2Url: `${import.meta.env.VITE_PUBLIC_PATH}/assets/CloudCAD/s2_need.json`,
          externalMount: {},
          isDownloadFile: false // 是否前端下载图纸文件
        })
      } catch(err) {
        if (!isInIframe()) {
          alert(err.message)
        }
        window.parent.postMessage({
          action: 'loadError',
          payload: err
        }, '*')
        throw err
      }
      if (cloudCAD) {
        cloudCAD.pubsub.subscribe('app.wasmMsg.loadProcess', async (ename, {process}) => {
          // payload = {
          //   process: number
          // }
          window.parent.postMessage({
            action: 'loadProcess',
            payload: {
              process
            }
          }, '*')
        })
        cloudCAD.pubsub.subscribe('external.interface.error', async (ename, payload) => {
          window.parent.postMessage({
            action: 'interfaceError',
            payload: payload
          }, '*')
        })
        // 埋点,开图完成
        cloudCAD.pubsub.subscribe('cloudCAD.event.loadFinish', async (ename, payload) => {
          // payload = {
          //   date,
          //   did,
          //   loadTime
          // }
          record('开图成功')
          window.parent.postMessage({
            action: 'loadFinish',
            payload: payload
          }, '*')
        })
        cloudCAD.pubsub.subscribe('cloudCAD.event.loadError', async (ename, payload) => {
          // payload = {
          //   did,
          //   date,
          //   msg
          // }
          window.parent.postMessage({
            action: 'loadError',
            payload: payload
          }, '*')
        })
        // 埋点, 主动保存
        cloudCAD.pubsub.subscribe('cloudCAD.event.saveVersion', async (ename, payload) => {
          // payload = {
          //   date,
          //   did,
          //   result: true // 保存成功或失败
          // }
          window.parent.postMessage({
            action: 'saveVersion',
            payload: payload
          }, '*')
        })
        // 埋点, 自动保存
        cloudCAD.pubsub.subscribe('cloudCAD.event.autoSaveVersion', async (ename, payload) => {
          // payload = {
          //   date,
          //   did,
          //   result: true // 保存成功或失败
          // }
          window.parent.postMessage({
            action: 'autoSaveVersion',
            payload: payload
          }, '*')
        })
        // 埋点, 命令执行
        cloudCAD.pubsub.subscribe('cloudCAD.event.executeCommand', async (ename, payload) => {
          // payload = {
          //   date,
          //   did,
          //   command
          // }
          window.parent.postMessage({
            action: 'executeCommand',
            payload: payload
          }, '*')
        })
      }

      return cloudCAD
    }
 </script>
</body>

</html>