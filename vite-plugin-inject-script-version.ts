import type { Plugin } from 'vite'
import fs from 'fs'

export default function injectScriptVersionPlugin(): Plugin {
  const version = JSON.parse(fs.readFileSync('./package.json', 'utf-8')).version

  return {
    name: 'vite-plugin-inject-script-version',
    enforce: 'post',
    transformIndexHtml(html) {
      return html.replace(
        /<script\s+((?!type="module")[^>])*src="([^"]+\.js)"[^>]*><\/script>/g,
        (match, attrs, src) => {
          const hasQuery = src.includes('?')
          const separator = hasQuery ? '&' : '?'
          return match.replace(src, `${src}${separator}v=${version}`)
        }
      )
    }
  }
}
