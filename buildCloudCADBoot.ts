import { defineConfig, loadEnv } from "vite";
import { resolve } from "path";
import react from '@vitejs/plugin-react'

export default defineConfig((mode) => {
    console.log(mode);
    const env = loadEnv(mode.mode, process.cwd());
    const isBuild = mode.command === "build";
    return {
      publicDir: false,
      plugins: [
        react({
          babel: {
            presets: [
              [
                '@babel/preset-env',
                {
                  targets: 'Chrome 57',
                  modules: false,
                },
              ],
            ],
          },
        })
      ],
        resolve: {
            alias: {
                "@": resolve(__dirname, "src"),
            },
        },
        optimizeDeps: {
            // include: ["element-plus/dist/locale/zh-cn.mjs", "element-plus/dist/locale/en.mjs"],
        },
        build: {
          target: 'es2015',
          emptyOutDir: false,
          // sourcemap: 'inline',
          sourcemap: false,
          lib: {
            entry: `src/app/CloudCAD/index.ts`,
            name: 'CloudCADBoot',
            formats: ['umd'],
            // output filename;
            fileName: "assets/CloudCADBoot",
          },
          // chunkSizeWarningLimit: 2000,
          cssCodeSplit: true, //css 拆分
          // sourcemap: false, //不生成sourcemap
          minify: 'terser', //是否禁用最小化混淆，esbuild打包速度最快，terser打包体积最小。
          // assetsInlineLimit: 5000, //小于该值 图片将打包成Base64
          terserOptions: {
              compress: {
                  drop_console: true,
                  drop_debugger: true
              }
          },
          rollupOptions: {
          },
        },
        define: {
          '__SOCKETIO_MODE__': JSON.stringify('webWorker'),
          '__CLIENT_VERSION__': JSON.stringify(process.env.npm_package_version),
        }
    };
});
