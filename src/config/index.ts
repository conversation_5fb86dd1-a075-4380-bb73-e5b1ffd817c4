/** 考虑这块读取配置修改全局变量，1种方式是后端调用接口获取，2种方式是配置模板读取 */

import axios from "axios";
let config: object = {};

const setConfig = (cfg?: unknown) => {
    config = Object.assign(config, cfg);
};

const getConfig = (): GlobalConfigs => {
    return config;
};

/** 获取项目动态全局配置 */
const getGlobalConfig = (): Promise<any> => {
    window.$globalConfig = getConfig();
    return axios({
        method: "get",
        url: `${import.meta.env.VITE_BASE_URL}/globalConfig.json`,
    })
    .then(({ data: config }) => {
        console.log("获取配置参数: ", config)
        let $config = window.$globalConfig;
        // 自动注入项目配置
        if ($config && typeof config === "object") {
            $config = Object.assign($config, config);
            window.$globalConfig = $config;
            // 设置全局配置
            setConfig($config);
        }
        return $config;
    })
    .catch(() => {
        throw "public目录下缺少globalConfig.json文件";
    });
};

export { getConfig, setConfig, getGlobalConfig };
