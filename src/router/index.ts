import {
    createRouter,
    createWebHistory,
    createWebHashHistory,
} from "vue-router";
import type { Router } from "vue-router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { constantRoutes, asyncRoutes } from "./route";
import { useStoreRouter } from "@/stores/router";
import { useUserStore } from "@/stores/user";
import { checkRoute } from "@/utils/router";
import { redirectRoot } from "@/utils/const";

const getHistoryMode = (publicPath: string = import.meta.env.VITE_BASE_URL) => {
    return import.meta.env.VITE_ROUTER_HISTORY === "hash"
        ? createWebHashHistory(publicPath)
        : createWebHistory(publicPath);
};

/** 创建路由实例 */
let router: Router = createRouter({
    history: getHistoryMode(),
    routes: constantRoutes,
});

const handleBefore = async (to, from, next) => {
  // login 和 install的放行逻辑在各自路由中hook处理了

  NProgress.configure({ showSpinner: false });
  NProgress.start();

  const userStores = useUserStore();
  const routerStores = useStoreRouter();
  const menu = userStores.menu;

  if (["/sdk"].includes(to.path)) {
      next();
  }

  // 如果没有token,同时访问的不是 "/login", "/install"，跳转登录页
  if (!userStores.token && !["/login", "/install"].includes(to.path)) {
      next({ path: "/login", replace: true });
  }

  // 如果用户没有任何menu权限,同时访问的不是 "/login", "/empty", "/install"，跳转空页面
  if (
      menu.length == 0 &&
      !["/login", "/empty", "/install"].includes(to.path)
  ) {
      next({ path: "/empty", replace: true });
      return;
  }
  // 检查是否可以访问路由，无权限跳403
  if (checkRoute(to.path, router, asyncRoutes)) {
      next({ path: "/403" });
      return;
  }
  // 判断是否第一次刷新，由于vuerouter的addroute第一次不生效，需要额外next一次路由
  if (isFirstRresh) {
      isFirstRresh = false;
      next({ ...to, replace: true });
      return;
  }

  if (to.query["_blank"] == "1") {
      // 如果query带有_blank=1，则使用window.open打开新页签
      const href = to.fullPath.replace("&_blank=1", "");
      NProgress.done();
      next(false);
      setTimeout(() => {
          const w = window.open(href, "_blank");
          (w as any).focus();
      }, 0);
  } else {
      // 记录当前路由与面包屑更新
      routerStores.setActivePath(to.path);
      routerStores.setBreadcrumbList(to.matched);
      // 判断首页默认路由path是否和动态路由第一个的path一直，如果不一致则修改
      if (
          to.path === redirectRoot &&
          routerStores.redirectRoot !== redirectRoot
      ) {
          next({ path: routerStores.redirectRoot });
      } else {
          // 正常情况有权限直接放行
          next();
      }
  }

  NProgress.done();
}

const handleError = (error, to) => {
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    window.location.href = to.fullPath
  }
}

/** 重置路由 */
export function resetRouter(publicPath: string = import.meta.env.VITE_BASE_URL) {
  console.log('publicPath publicPath', publicPath)
    const newRouter: Router = createRouter({
        history: getHistoryMode(publicPath),
        routes: constantRoutes,
    });
    router = newRouter
    router.beforeEach(handleBefore)
    router.onError(handleError)
    router.options.routes = []
}

let isFirstRresh = true;



// 路由加载前
router.beforeEach(handleBefore);

router.onError(handleError)

export default router;
