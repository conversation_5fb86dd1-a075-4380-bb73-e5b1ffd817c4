import type { RouteRecordRaw } from "vue-router";
import { useUserStore } from "@/stores/user";
import { redirectRoot } from "@/utils/const";
import { ascendRoute } from "@/utils/router";
import { checkIsInstall } from "@/utils";
import { $t } from '@/locales';

const Layout = () => import("/src/views/layout/index.vue");

/** 常驻路由 */
export const constantRoutes: RouteRecordRaw[] = [
    {
        path: "/",
        component: Layout,
        name: "root",
        redirect: "/login",
        meta: {
            isKeepAlive: true,
        },
        children: [],
    },
    // {
    //     path: "/home", // 后期做数据统计之类的可视化页面
    //     name: "home",
    //     redirect: "/files/file",
    //     component: () => import("@/views/layout/default.vue"),
    //     meta: {
    //         title: "首页",
    //         isKeepAlive: true,
    //         roles: ["admin", "common"],
    //         icon: "iconfont icon-shouye",
    //         isBtn: true,
    //     },
    //     children: [
    //         {
    //             path: "/files/file",
    //             name: "filesFile",
    //             component: () => import("@/views/files/file/index.vue"),
    //             props: (route) => ({ query: route.query.id }),
    //             meta: {
    //                 title: "文件管理",
    //                 isKeepAlive: true,
    //                 roles: ["admin"],
    //                 icon: "Folder",
    //             },
    //         },
    //     ],
    // },
    {
        path: "/install", // 系统安装引导页
        name: "install",
        component: () => import("/src/views/system/install/index.vue"),
        meta: {
            title: "系统安装",
            isKeepAlive: true,
            roles: ["admin", "common"],
            icon: "iconfont icon-shouye",
            isBtn: false,
        },
        beforeEnter: async (to, from, next) => {
            // 校验是否安装
            const isInstall = await checkIsInstall();
            if (isInstall) next(redirectRoot);
            else {
                next();
            }
        },
    },
    {
        path: "/login",
        name: "login",
        component: () => import("/src/views/login/index.vue"),
        meta: {
            title: "登录",
        },
        beforeEnter: async (to, from, next) => {
            // 目前没有校验token，只是单纯的判断前端是否存储token，如果有则放行跳转到首页
            const userStores = useUserStore();
            if (userStores.token) next(redirectRoot);
            else next();
        },
    },
    {
        path: "/sdk",
        name: "sdk",
        component: () => import("/src/views/system/sdk/index.vue"),
        meta: {
            title: "样例SDK",
            // isKeepAlive: true,
            // icon: "HelpFilled",
        },
    },
    {
        path: "/app/:aname", // 系统安装引导页
        name: "app",
        component: () => import("/src/app/index.vue"),
    },
    {
        path: "/preview", // 预览页
        name: "preview",
        component: () => import("/src/components/preview/index.vue"),
    },
    {
        path: "/redirect",
        component: Layout,
        meta: {
            hidden: true,
        },
        children: [
            {
                path: "/redirect/:path(.*)",
                component: () => import("/src/views/redirect/index.vue"),
            },
        ],
    },
    {
        path: "/403",
        component: () => import("/src/views/error-page/403.vue"),
        meta: {
            hidden: true,
        },
    },
    {
        path: "/404",
        component: () => import("/src/views/error-page/404.vue"),
        meta: {
            hidden: true,
        },
        alias: "/:pathMatch(.*)*",
    },
    {
        path: "/empty",
        name: "empty",
        component: () => import("/src/views/layout/empty.vue"),
        meta: {
            hidden: true,
        },
    },
];

const modules: Record<string, any> = import.meta.glob(
    ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
    {
        eager: true,
    }
);

/**
 * 动态路由 后端auth控制
 */
let asyncRoutes: RouteRecordRaw[] = [];

Object.keys(modules).forEach((key) => {
    asyncRoutes.push(modules[key].default);
});

asyncRoutes = ascendRoute(asyncRoutes);

export { asyncRoutes };
