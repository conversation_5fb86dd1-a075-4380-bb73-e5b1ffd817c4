import type { RouteRecordRaw } from "vue-router";

const files: RouteRecordRaw = {
    path: "/files",
    name: "files",
    redirect: "/files/file",
    component: () => import("/src/views/layout/default.vue"),
    meta: {
        title: "文件管理",
        rank: "0",
        isKeepAlive: true,
        roles: ["admin", "common"],
        icon: "Files",
    },
    children: [
        {
            path: "/files/file",
            name: "filesFile",
            component: () => import("/src/views/files/file/index.vue"),
            props: (route) => ({ query: route.query.id }),
            meta: {
                title: "文件管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Folder",
            },
        },
        {
            path: "/files/template",
            name: "fileTemplate",
            component: () => import("/src/views/files/template/index.vue"),
            meta: {
                title: "模板管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Files",
            },
        },
        {
            path: "/files/font",
            name: "fileFont",
            component: () => import("/src/views/files/font/index.vue"),
            meta: {
                title: "字体管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "EditPen",
            },
        },
        {
            path: "/files/material",
            name: "material",
            component: () => import("/src/views/files/material/index.vue"),
            meta: {
                title: "材质管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Coin",
            },
        },
        {
            path: "/files/part",
            name: "part",
            component: () => import("/src/views/files/part/index.vue"),
            meta: {
                title: "零件管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Tools",
            },
        },
        {
            path: "/files/recycled",
            name: "filesRecycled",
            component: () => import("/src/views/files/recycled/index.vue"),
            meta: {
                title: "回收站",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "DeleteFilled",
            },
        },
    ],
};

export default files;
