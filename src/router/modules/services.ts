import type { RouteRecordRaw } from "vue-router";

const services: RouteRecordRaw = {
    path: "/services",
    name: "services",
    redirect: "/services/setting/instance",
    component: () => import("/src/views/layout/default.vue"),
    meta: {
        title: "应用管理",
        rank: "1",
        isKeepAlive: true,
        roles: ["admin", "common"],
        icon: "iconfont icon-shouye",
    },
    children: [
        {
            path: "/services/setting/instance",
            name: "servicesSettingInstance",
            component: () => import("/src/views/services/instance/index.vue"),
            meta: {
                title: "实例(节点)管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Platform",
            },
        },
        {
            path: "/services/setting/application",
            name: "servicesSettingInsApplication",
            component: () => import("/src/views/services/application/index.vue"),
            meta: {
                title: "应用管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Operation",
            },
        },
        {
            path: "/services/setting/taskType",
            name: "servicesSettingTaskType",
            component: () => import("/src/views/services/taskType/index.vue"),
            meta: {
                title: "任务类型管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Tickets",
            },
        },
        {
            path: "/services/setting/taskScheduler",
            name: "servicesSettingTaskScheduler",
            component: () => import("/src/views/services/taskScheduler/index.vue"),
            meta: {
                title: "计划任务",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Calendar",
            },
        },
        {
            path: "/services/execute/task",
            name: "servicesExecuteTask",
            component: () => import("/src/views/services/task/index.vue"),
            // props: (route) => ({ id: route.query.id }),
            meta: {
                title: "任务管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Document",
            },
        },
    ],
};

export default services;
