import type { RouteRecordRaw } from "vue-router";

const system: RouteRecordRaw = {
    path: "/system",
    name: "system",
    redirect: "/system/basic",
    component: () => import("/src/views/layout/default.vue"),
    meta: {
        title: "系统设置",
        rank: "3",
        isKeepAlive: true,
        roles: ["admin", "common"],
        icon: "iconfont icon-shouye",
    },
    children: [
        {
            path: "/system/basic",
            name: "systemBasic",
            component: () => import("/src/views/system/basic/index.vue"),
            meta: {
                title: "基本信息",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Avatar",
            },
        },
        {
            path: "/system/task",
            name: "systemTask",
            component: () => import("/src/views/system/task/index.vue"),
            meta: {
                title: "任务设置",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Tools",
            },
        },
        {
            path: "/system/database",
            name: "systemDatabase",
            component: () => import("/src/views/system/database/index.vue"),
            meta: {
                title: "数据库设置",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "TrendCharts",
            },
        },
        {
            path: "/system/storage",
            name: "systemStorage",
            component: () => import("/src/views/system/storage/index.vue"),
            meta: {
                title: "存储设置",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "FolderOpened",
            },
        },
        {
            path: "/system/fbackup",
            name: "systemFbackup",
            component: () => import("/src/views/system/fbackup/index.vue"),
            meta: {
                title: "备份与恢复",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "CopyDocument",
            },
        },
        {
            path: "/system/thirdParty",
            name: "systemThirdParty",
            component: () => import("/src/views/system/thirdParty/index.vue"),
            meta: {
                title: "三方接入",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Tickets",
            },
        },
        {
            path: "/system/sdk",
            name: "systemSdk",
            component: () => import("/src/views/system/sdk/index.vue"),
            meta: {
                title: "样例SDK",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "HelpFilled",
            },
        },
        {
            path: "/system/thirdParty/log",
            name: "thirdPartyLog",
            component: () => import("/src/views/system/thirdPartyLog/index.vue"),
            meta: {
                title: "三方访问日志",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Memo",
            },
        },
        {
            path: "/system/log",
            name: "systemLog",
            component: () => import("/src/views/system/log/index.vue"),
            meta: {
                title: "系统操作日志",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Reading",
            },
        },
        {
            path: "/system/openDWGConf",
            name: "openDWGConf",
            component: () => import("/src/views/system/openDWGConf/index.vue"),
            meta: {
                title: "2D 览图设置",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Operation",
            },
        },
        {
            path: "/system/cloudCADConf",
            name: "cloudCADConf",
            component: () => import("/src/views/system/cloudCADConf/index.vue"),
            meta: {
                title: "云原生设置",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "ScaleToOriginal",
            },
        },
        {
            path: "/system/license",
            name: "systemLicense",
            component: () => import("/src/views/system/license/index.vue"),
            meta: {
                title: "授权许可证",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Postcard",
            },
        },
        {
            path: "/system/authorizationRecords",
            name: "authorizationRecords",
            component: () => import("/src/views/system/authorizationRecords/index.vue"),
            meta: {
                title: "编辑授权使用记录",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "DataAnalysis",
            },
        },
        {
            path: "/system/webSdkCertificate",
            name: "webSdkCertificate",
            component: () => import("/src/views/system/webSdkCertificate/index.vue"),
            meta: {
                title: "WEB SDK证书导入",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Wallet",
            },
        },

    ],
};

export default system;
