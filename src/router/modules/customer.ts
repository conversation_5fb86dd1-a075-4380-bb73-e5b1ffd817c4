import type { RouteRecordRaw } from "vue-router";

const customer: RouteRecordRaw = {
    path: "/customer",
    name: "customer",
    redirect: "/customer/index",
    component: () => import("/src/views/layout/default.vue"),
    meta: {
        title: "用户管理",
        rank: "2",
        isKeepAlive: true,
        roles: ["admin", "common"],
        icon: "iconfont icon-shouye",
    },
    children: [
        {
            path: "/customer/index",
            name: "customerUser",
            component: () => import("/src/views/customer/user/index.vue"),
            meta: {
                title: "用户管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "User",
            },
        },
        {
            path: "/customer/group",
            name: "customerGroup",
            component: () => import("/src/views/customer/group/index.vue"),
            meta: {
                title: "用户组管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "UserFilled",
            },
        },
        {
            path: "/customer/auth",
            name: "customerAuth",
            component: () => import("/src/views/customer/auth/index.vue"),
            meta: {
                title: "权限策略",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Guide",
            },
        },
        {
            path: "/customer/fileAuth",
            name: "customerFileAuth",
            component: () => import("/src/views/customer/file/auth.vue"),
            meta: {
                title: "文件权限管理",
                isKeepAlive: true,
                roles: ["admin"],
                icon: "Management",
            },
        }
    ],
};

export default customer;
