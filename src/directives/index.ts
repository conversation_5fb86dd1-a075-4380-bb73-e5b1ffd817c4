import type { App, Directive } from "vue";
import { emitter, type Events } from "@/utils/mitt";
import { getMenuAuth } from "@/utils/permission";

const directives = {
  // 解决element-plus 按钮点击后没有失焦的问题
  noBlur: {
    mounted(el, binding) {
      el.addEventListener("click", (event: any) => {
        event.target.blur();
        if (event.target.nodeName === "SPAN") {
          event.target.parentNode.blur();
        }
      });
    },
  } as Directive,
  // 自动为省略文本添加title属性
  ellipsis: {
    mounted(el, binding) {
      // 如果元素没有title属性，则添加元素的文本内容作为title
      if (!el.title && el.textContent) {
        el.title = el.textContent.trim();
      }
      // 如果绑定了值，使用绑定的值作为title
      if (binding.value) {
        el.title = binding.value;
      }
    },
    updated(el, binding) {
      // 更新时重新设置title
      if (binding.value) {
        el.title = binding.value;
      } else if (!el.title && el.textContent) {
        el.title = el.textContent.trim();
      }
    },
  } as Directive,
  permission: {
    // 目前权限设计有点问题,按钮没有完全区分,所以这块做了兼容处理
    mounted(el, binding) {
      const { value } = binding;
      const { name, routeName, item } = value;
      // 目前只有文件列表页需要动态判断按钮
      if (routeName !== "filesFile") return;
      const hasPermission = getMenuAuth(name, item);
      if (!hasPermission) {
        el.style.display = "none";
      }
    },
  } as Directive,
  event: {
    mounted(el, binding) {
      const { arg, value } = binding;
      emitter.on(arg as keyof Events, (...args) => {
        value(...args);
      });
    },
    unmounted(el, binding) {
      const { arg } = binding;
      emitter.off(arg as keyof Events);
    },
  } as Directive,
  drag: {
    mounted(el: any, binding: any) {
      const oDiv = el; // 当前元素
      const minTop = oDiv.getAttribute("drag-min-top");
      const ifMoveSizeArea = 20;
      oDiv.onmousedown = (e: any) => {
        let target = oDiv;
        while (
          window.getComputedStyle(target).position !== "absolute" &&
          target !== document.body
        ) {
          target = target.parentElement;
        }

        document.onselectstart = () => {
          return false;
        };
        if (!target.getAttribute("init_x")) {
          target.setAttribute("init_x", target.offsetLeft);
          target.setAttribute("init_y", target.offsetTop);
        }

        const initX = parseInt(target.getAttribute("init_x"));
        const initY = parseInt(target.getAttribute("init_y"));

        // 鼠标按下，计算当前元素距离可视区的距离
        const disX = e.clientX - target.offsetLeft;
        const disY = e.clientY - target.offsetTop;
        document.onmousemove = (e) => {
          // 通过事件委托，计算移动的距离
          // 因为浏览器里并不能直接取到并且使用clientX、clientY,所以使用事件委托在内部做完赋值
          let l = e.clientX - disX;
          let t = e.clientY - disY;
          const { marginTop: mt, marginLeft: ml } =
            window.getComputedStyle(target);
          // 计算移动当前元素的位置，并且给该元素样式中的left和top值赋值
          if (l < 0) {
            l = 0;
          }
          if (t < 0) {
            t = 0;
          }
          if (l >= document.documentElement.clientWidth - target.offsetWidth) {
            l = document.documentElement.clientWidth - target.offsetWidth;
          }
          if (
            t >=
            document.documentElement.clientHeight - target.offsetHeight
          ) {
            t = document.documentElement.clientHeight - target.offsetHeight;
          }

          target.style.left = l - parseInt(ml) + "px";
          target.style.top = (t < minTop ? minTop : t) - parseInt(mt) + "px";

          if (
            Math.abs(l - initX) <= ifMoveSizeArea ||
            Math.abs(t - initY) <= ifMoveSizeArea
          ) {
            target.removeAttribute("dragged");
          } else {
            target.setAttribute("dragged", "");
          }
        };
        document.onmouseup = (e) => {
          document.onmousemove = null;
          document.onmouseup = null;
          document.onselectstart = null;
        };
        // return false不加的话可能导致黏连，拖到一个地方时div粘在鼠标上不下来，相当于onmouseup失效
        return false;
      };
    },
  },
};

/** 挂载自定义指令 */
export function loadDirectives(app: App) {
  for (const [key, value] of Object.entries(directives)) {
    app.directive(key, value);
  }
}
