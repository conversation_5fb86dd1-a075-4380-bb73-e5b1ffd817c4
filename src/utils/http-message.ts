/** 统一处理http error 错误信息，后期考虑国际化需要翻译后端的errorCode */
import { isNetworkError } from "./httpUtil";
import type { AxiosError } from "axios";
import type { ResponseData } from "@/model/public";
import { $t } from '@/locales'

export function getErrorMessage(error: AxiosError) {
    const { response, message } = error;
    if (message) {
        // 一些公共错误的统一处理
        if (message.indexOf("timeout") != -1) {
            return $t("网络超时")
        } else if (isNetworkError(message)) {
            return $t("网络连接错误")
        }
    }
    if (response && response.data) {
        const { msg = "", code } = response.data as ResponseData;
        // 目前msg是通过后端直接返回，这块如果需要做国际化的话，应该考虑前端来实现，后端只需要返回定义好的code,前端统一处理
        if (code) {
            const txt = $t(`resCode.${code}`);
            if (txt !== `resCode.${code}`) {
                // 由于前后端不能完全保持一致
                return txt;
            } else return msg || $t("未知错误")
        }
        if (msg) return msg;
        // 是否返回statusText？
        if (response.statusText) return response.statusText;
    }
    return $t("未知错误")
}
