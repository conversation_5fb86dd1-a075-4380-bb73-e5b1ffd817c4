import type { FileItem } from "@/model/file";
import type { ResponseData } from "@/model/public";
import type { Emitter } from "mitt";
import mitt from "mitt";

export type Events = {
    detailViewShow: { data: object; title?: string; width?: string };
    detailViewShow2: {
        data: object;
        title?: string;
        width?: string;
        drawerFooter?: any;
    };
    createGroupDialogShow: { data: Recordable; isUp: boolean };
    batchResultDialogShow: {
        data: Recordable[];
        title?: string;
        width?: string;
        batchApi?: (id: string) => Promise<ResponseData | any>;
    };
    importDialogShow: { type: string };
    previewShowDialog: FileItem;
    refreshFile: string;
    clearAll: string;
    "app.open": {
        aname: string;
        data: Recordable;
    };
    "app.close": {
        aname: string;
    };
    [ename:string]: any;
    loginSuccessful: any;
    enterPage: any;
};

export const emitter: Emitter<Events> = mitt<Events>();
