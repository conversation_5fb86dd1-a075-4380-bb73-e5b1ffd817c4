import pubsub from './pubsub.js';
import { getConfig } from '@/config';
import WebWorker from "./worker?worker&inline";

// 默认 API 类型定义
interface ApiEndpoints {
  file: string;
  material: string;
  part: string;
  font: string;
  template: string;
  modify: string;
  storage: string;
  askUploadType: string;
  postFileInfo: string;
}

// 上传参数类型
interface UploadOptions {
  file: File;
  fileId?: string;
  etag?: string;
  api?: any;
  type?: number;
  host?: string;
  folderId?: number;
  replace?: boolean;
  path?: string;
  token?: string;
  userTag?: string;
  tagType?: number;
  lastModify?: string;
  currModify?: string;
  baseId?: number;
  srcStorageId?: number;
  attribute?: string;
}

// Worker 计算返回的事件类型
interface WorkerMessage {
  type: "progress" | "done" | "error";
  progress?: number;
  sha1?: string;
  error?: any;
}

// 上传数据结构
interface UploadData {
  etag?: string;
  uploadUrl?: string;
  storageType?: number
  uid?: string
}

// 进度事件类型
interface ProgressEventCustom extends ProgressEvent {
  loaded: number;
  total: number;
}

const defaultApi: ApiEndpoints = {
  file: "/api/v2/_st/_file/_upload",
  material: "/api/v2/_st/_material/_upload",
  part: "/api/v2/_st/_part/_upload",
  font: "/api/v2/_st/_font/_upload",
  template: "/api/v2/_st/_file/_uploadTemplateFile",
  modify: '/api/v2/_st/_filemodify/_uploadModifyRecord',
  storage: '/api/v2/_st/_storage/_upload',
  askUploadType: '/api/v3/_st/_file/_preUploadFileByEtag',
  postFileInfo: "/api/v3/_st/_file/_createFileByEtag"
};

export default class UploadManager {
  private file: File;
  private fileId: string;
  private etag: string;
  private worker: Worker;
  private api: ApiEndpoints;
  private type: number;
  private folderId?: number;
  private replace: boolean;
  private path: string;
  private token: string;
  private host: string;
  private userTag: string;
  private tagType: number;
  private lastModify: string;
  private currModify: string;
  private baseId: number;
  private srcStorageId: number;
  private attribute: string;
  private others?: any

  constructor(options: UploadOptions) {
    const {
      file,
      api = defaultApi,
      fileId = "",
      type = 1, // 1:数据卷(文件管理) 4:缩略图,5:显示数据,6:批注,9:临时数据,10:日志 12:模板 13:材质 14:零件
      etag = '',
      folderId,
      replace = false,
      path = "",
      token = '',
      userTag = '',
      tagType = null as unknown as number,
      lastModify = "",
      currModify = "",
      baseId = null as unknown as number,
      srcStorageId = null as unknown as number,
      attribute = "",
      host = "",
      ...others
    } = options

    this.file = file || null;
    this.fileId = fileId;
    this.etag = etag;
    this.worker = new WebWorker();
    this.api = api;
    this.type = type;
    this.folderId = folderId;
    this.replace = replace;
    this.path = path;
    this.token = token;
    this.host = host;
    this.userTag = userTag
    this.tagType = tagType
    this.lastModify = lastModify;
    this.currModify = currModify;
    this.baseId = baseId;
    this.srcStorageId = srcStorageId;
    this.attribute = attribute;
    this.others = others;

    console.log("others", others, options)

    if (!this.host) this.getHost()
  }

  getHost() {
    const config = getConfig()
    this.host = config?.api_endpoint || ''
    console.log("getHost", config)
  }


  async start(uid: string): Promise<any> {
    console.log(this.file, this.worker)
    if (!this.file) return
    try {
      const sha1Hash = this.etag ? this.etag : await this.computeSHA1(this.file);
      console.log("SHA-1 计算完成：", sha1Hash);

      const resData = await this.uploadFileEtag({ etag: sha1Hash })
      const uploadFileEtagData = resData.data
      console.log("上传文件etag完成：", uploadFileEtagData);

      if (uploadFileEtagData.quickTrans) {
        pubsub.publish("success", { ...uploadFileEtagData, uid });
        return resData
      }

      const { uploadUrl } = uploadFileEtagData

      if (uploadFileEtagData.storageType === 1) {
        const response = await this.uploadFile({ etag: sha1Hash, uploadUrl, uid });
        const postFileInfoRes = await this.postFileInfo({ etag: sha1Hash, storageType: uploadFileEtagData.storageType, })
        pubsub.publish("success", { ...postFileInfoRes, uid });
        return postFileInfoRes;
      } else {
        const response = await this.uploadFileToOss({ etag: sha1Hash, uploadUrl: uploadFileEtagData.uploadUrl, uid });
        const postFileInfoRes = await this.postFileInfo({ etag: sha1Hash, storageType: uploadFileEtagData.storageType })
        pubsub.publish("success", { ...postFileInfoRes, uid });
        return postFileInfoRes;
      }
    } catch (error) {
      console.error("上传失败:", error);
      throw error;
    }
  }

  private computeSHA1(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = () => {
        const arrayBuffer = reader.result as ArrayBuffer;
        this.worker.postMessage({ fileData: arrayBuffer, preSize: 0 });

        this.worker.onmessage = (event: MessageEvent<{ type: string; sha1?: string; progress?: number; error?: any }>) => {
          if (event.data.type === "progress") {
            console.log(`计算进度：${event.data.progress?.toFixed(2)}%`);
          } else if (event.data.type === "done") {
            resolve(event.data.sha1 || "");
          } else if (event.data.type === "error") {
            reject(event.data.error);
          }
        };

        this.worker.onerror = (error) => {
          reject(error);
        };
      };

      reader.onerror = (error) => {
        reject(error);
      };

      reader.readAsArrayBuffer(file);
    });
  }

  on(eventName: string, callback: (evt: any) => void) {
    pubsub.subscribe(eventName, callback);
  }

  private progress(evt: ProgressEventCustom, uid?: string) {
    const { loaded, total } = evt
    const res = { loaded, total, uid }
    // console.log("progress", evt, res)
    pubsub.publish("progress", res);
  }

  private async uploadFileEtag(data: UploadData): Promise<any> {
    // const form = new FormData();
    // form.append("folderId", this.folderId ? this.folderId.toString() : "");
    // form.append("replace", this.replace.toString());
    // form.append("etag", data.etag);
    // form.append("fileName", this.file.name);
    // form.append("fileSize", this.file.size.toString());
    // form.append("path", this.path);

    let params = {
      folderId: this.folderId ? this.folderId : "",
      replace: this.replace,
      etag: data.etag,
      fileName: this.file.name,
      fileSize: this.file.size,
      path: this.path,
      ...this.others
    }

    console.log("this.others", this.others, params)

    if (this?.tagType) (params as any).tagType = this.tagType
    if (this?.userTag) (params as any).userTag = this.userTag


    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open("POST", `${this.host}${this.api.askUploadType}`, true);

      xhr.setRequestHeader("Authorization", `${this.token}`);
      xhr.setRequestHeader("Content-Type", `application/json`);


      xhr.onload = () => {
        // console.log(111, xhr)
        if (xhr.readyState === 4 && xhr.status === 200) {
          const data = JSON.parse(xhr.responseText)
          if (data.code === 0) {
            resolve(data);
          } else {
            reject(data)
          }
          // console.log(xhr)
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => {
        reject(new Error("Upload failed due to a network error"));
      };

      xhr.send(JSON.stringify(params));
    });
  }

  private async uploadFile(data: UploadData): Promise<any> {
    // console.log("uploadFile", data)
    const form = new FormData();
    // form.append("folderId", this.folderId ? this.folderId.toString() : "");
    // form.append("replace", this.replace.toString());
    // form.append("digest", data.etag);
    // form.append("etag", data.etag);
    // form.append("file_type", "");
    form.append("file", this.file);
    // form.append("attach_to", "0");

    const params = {
      replace: this.replace,
      digest: data.etag,
      etag: data.etag,
      file_type: this.type,
      attach_to: 0
    }


    form.append('attribute', JSON.stringify(params));

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open("POST", `${data.uploadUrl}`, true);

      xhr.setRequestHeader("Authorization", `${this.token}`);

      // 上传进度监听
      xhr.upload.onprogress = (evt: ProgressEventCustom) => {
        console.log("上传进度", "uploadFile", evt, data?.uid)
        if (evt.lengthComputable) {
          this.progress(evt, data?.uid);
        }
      };

      xhr.onload = () => {
        // console.log(111, xhr)
        if (xhr.readyState === 4 && xhr.status === 200) {
          const data = JSON.parse(xhr.responseText)
          // console.log(data)
          if (data.code === 0) {
            resolve(data);
          } else {
            reject(data);
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => {
        reject(new Error("Upload failed due to a network error"));
      };

      xhr.send(form);
    });
  }
  private async uploadFileToOss(data: UploadData): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open("PUT", `${data.uploadUrl}`, true);
      xhr.setRequestHeader("Content-Type", 'application/octet-stream');

      // 上传进度监听
      xhr.upload.onprogress = (evt: ProgressEventCustom) => {
        console.log("上传进度", "uploadFile", evt, data?.uid)
        if (evt.lengthComputable) {
          this.progress(evt, data?.uid);
        }
      };

      xhr.onload = () => {
        // console.log(111, xhr)
        if (xhr.readyState === 4 && xhr.status === 200) {
          resolve(true)
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => {
        reject(new Error("Upload failed due to a network error"));
      };

      xhr.send(this.file);
    });
  }
  private async postFileInfo(data: UploadData): Promise<any> {
    // const form = new FormData();
    // form.append("replace", this.replace.toString());
    // form.append("etag", data.etag);
    // form.append("fileSize", this.file.size.toString());
    // form.append("fileName", this.file.name.toString());
    // form.append("folderId", this.folderId ? this.folderId.toString() : "");
    // form.append("path", this.path);
    // form.append("storageType", data.storageType);

    const params = {
      replace: this.replace,
      etag: data.etag,
      fileSize: this.file.size,
      fileName: this.file.name,
      folderId: this.folderId ? this.folderId : "",
      path: this.path,
      storageType: data.storageType,
      ...this.others
    }

    if (this?.tagType) (params as any).tagType = this.tagType
    if (this?.userTag) (params as any).userTag = this.userTag

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open("POST", `${this.host}${this.api.postFileInfo}`, true);

      xhr.setRequestHeader("Authorization", `${this.token}`);
      xhr.setRequestHeader("Content-Type", `application/json`);
      // xhr.setRequestHeader("Accept", `application/json`);

      // 上传进度监听
      // xhr.upload.onprogress = (evt: ProgressEventCustom) => {
      //   console.log("上传进度", "postFileInfo", evt)
      //   if (evt.lengthComputable) {
      //     this.progress(evt);
      //   }
      // };

      xhr.onload = () => {
        // console.log(111, xhr)
        if (xhr.readyState === 4 && xhr.status === 200) {
          const data = JSON.parse(xhr.responseText)
          // console.log(data)
          if (data.code === 0) {
            resolve(data);
          } else {
            reject(data);
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => {
        reject(new Error("Upload failed due to a network error"));
      };

      xhr.send(JSON.stringify(params));
    });
  }
}
