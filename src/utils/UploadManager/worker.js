import { readBlock } from '@/utils/sha1/StreamUtil'
import { createSha1 } from '@/utils/sha1/js-sha1-origin'

const CHUNK_SIZE = 1024 * 1024 * 10; // 10MB 分块
const PROGRESS_EMIT_STEP = 0.2; // 每增加 0.2% 触发进度回调

self.onmessage = async (event) => {
  const { fileData, preSize } = event.data;

  if (!(fileData instanceof ArrayBuffer)) {
    self.postMessage({ type: "error", error: "Invalid file data, expected ArrayBuffer" });
    return;
  }

  const hash = createSha1();
  const blob = new Blob([fileData]); // 转回 Blob 以便 readBlock 处理

  let progress = 0;
  let lastProgress = 0;

  await readBlock(
    blob,
    CHUNK_SIZE,
    (buf, loaded, total) => {
      hash.update(buf);

      progress = (loaded * 100) / total;
      if (progress - lastProgress >= PROGRESS_EMIT_STEP) {
        self.postMessage({ type: "progress", progress });
        lastProgress = progress;
      }
    },
    () => false, // 不支持外部中断
  );

  self.postMessage({ type: "done", sha1: hash.hex() });
};