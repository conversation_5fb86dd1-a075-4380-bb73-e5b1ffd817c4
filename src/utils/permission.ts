/**
 * 权限bit对应表
 * 1:  查看            -> 文件管理
 * 2:  添加            -> 文件管理
 * 4:  删除            -> 文件管理
 * 8:  文件授权         -> 文件管理
 * 16: 删除自己的       -> 文件管理
 * 64: 管理            -> 应用管理
 * 256: 管理            -> 用户管理
 * 1024: 管理           -> 系统设置
 */
import { useUserStore } from "@/stores/user";
import { storeToRefs } from "pinia";

export const default_user_bits = [1, 2, 64];

/** 目前后端返回的是中文, 所以这样处理,待后续修改为英文key,然后重构 */
export const update_menus = ['重命名', '移动', '拷贝']
export const recycled_menus = ['还原', '删除']
export const font_menus = ['下载', '删除']
const permission_combination = {
    '重命名': '编辑',
    '拷贝': '查看',
    '移动': '删除',
} as any

/** 获取按钮权限 */
export const getMenuAuth = (name: string, item?: Recordable) => {
    let hasPermission = false;
    const userStores = useUserStore();
    const { auth, id } = storeToRefs(userStores);

    if (auth.value && auth.value.length > 0) {
        const data = auth.value.find(v => v.menu === '文件管理');
        if (data) {
            if (name === '删除') {
                for (let i = 0; i < data.auths.length; i++ ) {
                    const auth = data.auths[i]
                    if (auth.name === name) {
                        // 如果有删除则直接跳出循环,判断有权限
                        hasPermission = true
                        break;
                    }
                    if (auth.name === '删除自己的') {
                        // 如果是删除自己
                        if (item && item.ownerId) {
                            if (id.value === item.ownerId) {
                                hasPermission = true
                                break;
                            }
                        }
                    }
                }
            } else {
                hasPermission = data.auths.some((r: Recordable) => r.name === (permission_combination[name] || name));
            }
        }
    }
    return hasPermission
}