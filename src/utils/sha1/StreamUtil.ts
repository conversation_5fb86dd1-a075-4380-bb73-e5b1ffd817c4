/** @format */
export {readBlock, readStream, getArrayBufferFromBlob}

async function readStream(readable: any, onData: (arg0: any) => any, getStopFlag: () => any) {
  // let errored = false
  for await (const chunk of readable) {
    if (getStopFlag()) {
      readable.destroy()
      throw new Error('stopped')
    }
    try {
      await onData(chunk)
    } catch (e) {
      readable.destroy()
      throw e
    }
  }
}

/* istanbul ignore next */
async function readBlock(blob: Blob, chunkSize: number, onChunkData: { (buf: any, loaded: number, total: number): void; (arg0: Uint8Array, arg1: number, arg2: any): any }, getStopFlag: () => any) {
  getStopFlag = getStopFlag || (() => false)

  const size = blob.size
  const len = Math.ceil(size / chunkSize)
  let start = 0
  let end
  const fileReader = new FileReader()
  for (let i = 0; i < len; i++) {
    if (getStopFlag()) {
      throw new Error('stopped')
    }
    start = i * chunkSize
    end = Math.min(start + chunkSize, size)
    const buf = await getArrayBufferFromBlob(blob.slice(start, end), fileReader) as any
    await onChunkData(new Uint8Array(buf), end, size)
  }
}
/* istanbul ignore next */
function getArrayBufferFromBlob(blob: Blob, fileReader: any) {
  return new Promise((a, b) => {
    // var fileReader = new FileReader()
    fileReader.onload = (e: any) => a(e.target.result)
    fileReader.onerror = (e: any) => b(e.target.error)
    fileReader.readAsArrayBuffer(blob)
  })
}

