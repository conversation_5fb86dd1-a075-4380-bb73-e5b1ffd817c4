/**
 * 目前浏览器计算sha1使用串行，目前只用于应用安装
 * 传输列表这块待后续要求性能时添加优化
 */
import crypto from 'crypto'
import * as fs from 'fs'
import { readBlock } from './StreamUtil.js'
import { createSha1 } from './js-sha1-origin'

export {
  // for 浏览器js
  calcFileSha1, // 串行
}

const CHUNK_SIZE = 1024 * 1024 * 10
const PROGRESS_EMIT_STEP = 0.2 // 进度超过多少,回调onProgress

/// for 浏览器js
async function calcFileSha1(file: File, preSize: number, onProgress: (progress: number) => void, getStopFlag: any) {
  onProgress = onProgress || (prog => {
    //
  })
  getStopFlag = getStopFlag || (() => false)

  const blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice

  const hash = createSha1()

  let blob

  if (preSize) {
    // 预秒传只需计算前1000KB
    blob = blobSlice.call(file, 0, preSize)
  } else {
    //计算整个文件的
    blob = file
  }

  let progress = 0
  let last_progress = 0
  await readBlock(
    blob,
    CHUNK_SIZE,
    (buf: any, loaded: number, total: number) => {
      hash.update(buf)

      // 进度
      progress = (loaded * 100) / total
      if (progress - last_progress >= PROGRESS_EMIT_STEP) {
        try {
          onProgress(progress)
        } catch (e) {
          console.error(e)
        }
        last_progress = progress
      }
    },
    getStopFlag,
  )
  return hash.hex()
}
