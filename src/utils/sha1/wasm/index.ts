export {
  // for WASM
  calcFileWASMSha1,
}

async function calcFileWASMSha1(file: File, onProgress: (progress: number) => void, getStopFlag: any) {
  return new Promise<any>((resolve, reject) => {
    const worker = new Worker(new URL('./sha1-worker.js', import.meta.url))
    worker.addEventListener('message', (event) => {
      const { sha1Hash } = event.data;
      console.log('SHA-1 Hash:', sha1Hash);
      worker.terminate();
      resolve(sha1Hash);
    });
    worker.postMessage({ fileData: file });
  });
}
