/**
 * @Description 使用wasm计算sha1,缺少进度,分片等,内存释放,待优化
 * js 和 wasm 通信的过程就是：先把想要传递的数据序列化成 ArrayBuffer
 * 然后把 buffer 写入 Memory 并且把数据的起始位置、数据大小传给 wasm 模块
 * 在 wasm 中根据位置取到这块 buffer，最后把 buffer 反序列化成自己想要的类型
 */
importScripts(`${import.meta.env.VITE_PUBLIC_PATH}/assets/md5.js`);

self.addEventListener('message', async (event) => {
  const { fileData } = event.data;
  const md5 = self.cwrap('sha1', null, ['number', 'number'])
  try {
    const arrayBuffer = await readFileAsArrayBuffer(fileData);

    const { ptr: inputPtr, buffer: inputBuffer } = await allocateMemory(arrayBuffer.byteLength);
    const ctx = new Uint8Array(arrayBuffer);
    inputBuffer.set(ctx);

    const { ptr: outputPtr, buffer: outputBuffer } = await allocateMemory(40);
    md5(inputBuffer.byteOffset, outputBuffer.byteOffset, inputBuffer.byteLength);

    const sha1Hash = Array.from(outputBuffer).map((v) => String.fromCharCode(v)).join('');
    self.postMessage({ sha1Hash });

    freeMemory(inputPtr);
    freeMemory(outputPtr);
  } catch (error) {
    console.error('错误:', error);
  }
});

// 读取文件buffer
function readFileAsArrayBuffer(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);

    reader.readAsArrayBuffer(file);
  });
}

// 分配内存
function allocateMemory(size) {
  return new Promise((resolve, reject) => {
    function checkIsInit() {
      if (self.runtimeInitialized) {
        const ptr = self._malloc(size);
        const buffer = new Uint8Array(self.HEAPU8.buffer, ptr, size);
        resolve({ ptr, buffer })
      } else {
        setTimeout(() => checkIsInit(), 1);
      }
    }
    checkIsInit();
  });
}

function freeMemory(ptr) {
  self._free(ptr);
}
