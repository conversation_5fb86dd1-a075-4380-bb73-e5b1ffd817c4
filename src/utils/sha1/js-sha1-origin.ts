/** @format */

import * as Sha1 from 'js-sha1'

function ready() {
  //
}

export {ready, sha1, createSha1}

// 一次性计算
function sha1(buff: any) {
  return Sha1.create().update(buff).hex().toUpperCase()
}

function createSha1() {
  const hash = Sha1.create()

  return {
    update(buff: any) {
      hash.update(buff)
    },
    getH() {
      const h = []
      for (let i = 0; i < 5; i++) {
        const val = hash[`h${i}`] >>> 0
        h.push(val)
      }
      return h
    },
    hex() {
      return hash.hex().toLowerCase()
    },
  }
}
