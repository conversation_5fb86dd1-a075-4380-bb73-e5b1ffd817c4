import type { LocaleSetting, LocaleType } from "@/model/public";
import { $t } from '@/locales';
// 请求头-内容类型
export const ContentType = {
  JSON: "application/json",
  FORM: "application/x-www-form-urlencoded",
  UPLOAD: "multipart/form-data",
  BINARY: "application/octet-stream",
  SHEET: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
};
// 本地存储类型
export const StorageType = {
  COOKIE: "cookie",
  SESSION: "sessionStorage",
  LOCAL: "localStorage",
};
// 白名单 比如登录页
export const whiteList = ["/login"];

// 白名单菜单,用于临时测试
export const whiteMenuList = [
  "thirdPartyLog",
  "systemLog",
  "systemSdk",
  "systemLicense",
  "openDWGConf",
  "fileTemplate"
];

// 不能览图的文件类型
export const notAllowPreviewExt = [
  // "slddrw",
  // "catdrawing",
  // "fbx",
  // "dae",
  // "3dxml",
  "ocf",
  "xcgm",
];

// 是否socket使用加解密
export const enabled_socket_security = true;

// 支持全局socket通知的组件路径
export const enableSocketPathList = ["/app/Preview2d", "/app/Preview3d", "/files/file"];

// 没有登录权限的code,用于跳转到登录页
export const noPermissionCode = [401, 400401, 400402];

// 上传状态
export const UPLOAD_STATUS = {
  BEFORE: 0,
  SUCCESS: 1,
  FAIL: 3,
  IN: 4,
};

export const QUICK_MENU_MAX_COUNT = 5;

export const redirectRoot = "/files/file";

// logo 可上传文件类型
export const logoAccept = ".jpg,.jpeg,.png,.bmp";

// logo 上传tip
export const getUploadLogoTip = () =>
  $t("允许系统自定义logo，支持 jpg、jpeg、png、bmp格式的图片，图片大小不超过5M");

// 图章上传tip
export const getUploadStampTip = () => $t("支持png格式，200px*200px，最多支持10个图章图标")

// favicon 可上传文件类型
export const faviconAccept = ".ico";

// favicon 上传tip
export const getUploadFaviconTip = () =>
  $t("允许系统自定义favicon，支持ico格式的图片，图片大小不超过5M。");

// 用户组树形根目录
export const getTreeRoot = () => ({
  code: "gstar-root-zy",
  name: $t("浩辰图纸管理系统"),
});

export const getEnableStatus = () => [$t("禁用"), $t("启用")]; // 位运算来进行转换

// 任务类型类别
export const getTaskTypeCategory = () => [
  {
    label: $t("前端任务"),
    value: 1,
  },
  {
    label: $t("普通后台任务"),
    value: 4,
  },
  {
    label: $t("系统后台任务"),
    value: 5,
  },
  {
    label: $t("系统后台定时任务"),
    value: 6,
  },
];

// 任务状态
export const getTaskStatus = () => [
  $t("已就绪"),
  $t("执行中"),
  $t("已完成"),
  $t("部分完成"),
  $t("已失败"),
  $t("已取消"),
  // "已终止",
];

// 任务优先级
export const getTaskPriority = () => [
  $t("最低"),
  $t("低"),
  $t("略低"),
  $t("正常"),
  $t("略高"),
  $t("高"),
  $t("最高"),
];

export const LOCALE: { [key: string]: LocaleType } = {
  ZH_CN: "zh_CN",
  EN_US: "en",
};

export const localeSetting: LocaleSetting = {
  locale: LOCALE.EN_US,
};

export const getLocaleList = () => [
  {
    text: $t("简体中文"),
    event: LOCALE.ZH_CN,
  },
  {
    text: "English",
    event: LOCALE.EN_US,
  },
];

// logo和favicon大小限制
export const MAX_LOGO_FAVICON_LIMIT = 1024 * 1024 * 5;

// 2d开图水印设置提示
export const getWaterTip = () => $t('建议10个字符以内')

// 2d开图图章格式
export const stampAccept = ".png";

export const GSTAR_SDK_DYNAMIC_PW =
  "hMYdQJrL60s0f4RBzzJd_x1fq0j_bcu3rSi0QXhosx69cL1gCQwKW8bchP6DYUjZ";

export const avatar =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJ8AAACfBAMAAAAVNRbTAAAAIVBMVEXEzeDk6PHh5e/Hz+Le4+7P1ubT2ejX3era4OzM1OTJ0ePBGcCPAAAC90lEQVRo3u2az08TQRTHJ65U7cnvuNLVE4sIeNuIyo+Ta1INngoCesTSaLzZIgl4KuVg5NREuK8Kif6XJmLyVNyZvt1vIiH9/AGfzMx7M/vmzZohQ4YMOTME31fXVlc/sXT338X4yfQOQ7fQghA9Kz+8Hn4n3Cjr28WfTPdL+a708Df1UtGV9eNMejbGaSaKG6uygJwx3sO/sQWTJ0iRQ1Rs0xwhl1pWZIC7EBjJU4kdQruuF67AxW3OjIVIvYojMZyo51yBmzvqbQc3Na3wAG5spoxJBx7ecrJQuKUTfoGPUZ2wDR+hTgg/GTEL9dv5MgRKmO/CT1cjXGKPsM0WytlFOm9S+GkofNVBhH1NTRPDi03YQs75L0RsYe3Mj5AuDA09yv8zD6spWRikijVkHQ6jbOFNQz5gH6qEL+FlTCV8Di/XdOUmvNxQCS/By3WV8CJ7hBcUQWEJu2xhg7yGNiGnTc2oCGJFkDl776lSOAcP0ifgfARsQ38ZJd9Gl+Fk0hjuIu4b8n15n3yjt5tGTdPhG3+SGUO8MFtpn3IuU1FSqHeYcr7xQqVFOhjESDoYZNLIwZMz+qJuvaAwyKlwwr4pyJI7a2i5PVq8S+xs9fF6knVjuGFeNIVpk6dsDtjCClsYpGSh+cwWjjiEvDhPlhHOsYWzjr3MShy+sEYXJmRhlJGDEmbktLGNma+G+nL2IS36nZo5zKvB9oye+Vevtx/xSmIT/Hqnpn2bv8HJR/XBlcKJrSfaMsTHVKbxNeFA/2K/ID4X4ZsBp33cwoBsDWIM1noYFLvl9x1upxiccMeXLSebQ2F87/S9GIcD9S2t2kQB7EZusnRQiDAnH+edPn2GH/cApnGkhxJM9U/Fo4NSPE6ctaoeu6h7/VW/yjVRmonMCA9ilKfOGaAQNtw9Gj0SlxVQiBK5gXHoSsOVw5RU+iQazJDIDyRHoBFm8hMHh01pSXGYPKmiGUiVtwwme/K+T5szqNjEgMs6WzjGFl4dCofCcyn8Adlp2JUaUsvTAAAAAElFTkSuQmCC";

export const unknown_icon =
  "data:image/svg+xml;base64,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";

// 上传文件类型
export const upload_file_type = {
  THUMBNAIL: 4, // 缩略图
  SHOW_DATA: 5, // 显示数据
  NOTE: 6, // 批注
  LOG: 10, // 日志
};

export const TaskTypeMaps = [
  "DWGVisualization", // 1
  "SaveAsDWG", // 2
  "SaveAsPDF", // 3
  "MakeDWGThumbnail", // 4
  "MakeDWGBigThumbnail", // 5
  "BuildingVisualization", // 6
  "MakeBuildingThumbnail", // 7
  "ConvertPDF", // 8
  "SelfCheck", // 9
  "ManufacturingVisualization", // 10
  "MakeManufacturingThumbnail", // 11
  "Decompression", // 12
  "dataclean", // 13
  "DataClearTimeOutData", // 14
  "GetDWGInfo", // 15
  "MakeDWGIncrMerge", // 16
  "ConvertDWF", // 17
  "MFGSkpVisualization", // 18
  "MakeMFGSkpThumbnail", // 19
  "backuptask", // 20
  "restart", // 未知
];

// 制造业
export const manufacturing = [
  "3dm",
  "art",
  "c3s",
  "catdrawing",
  "catpart",
  "catshape",
  "ccd",
  "cfg",
  "cwr",
  "dcm",
  "ed",
  "edz",
  "emn",
  "emp",
  "exp",
  "iam",
  "idw",
  "ipt",
  "model",
  "plmxml",
  "ptf",
  "pvs",
  "pvz",
  "pwd",
  "sdt",
  "session",
  "slddrw",
  "smg",
  "stpxz",
  "stpz",
  "xar",
  "xmt_bin",
  "xmt_txt",
  "xpr",
  "z3",
  "sldprt",
  "sldasm",
  "par",
  "asm",
  "psm",
  "prt",
  "prt*",
  "asm*",
  "ipt(V6-V2021)",
  "iam(V11-V2021)",
  "model",
  "CATPart",
  "CATProduct",
  "cgr",
  "3dxml",
  "stp",
  "step",
  "sat",
  "sab",
  "igs",
  "iges",
  "jt",
  "x_t",
  "x_b",
  "vda",
  "xcgm",
  "3mf",
  "skp",
  "fbx",
  "dae",
  "hsf",
  "bcf",
  "prc",
  "pts",
  "ptx",
  "u3d",
  "wrl",
  "wrml",
  "xyz",
  "dlv",
  "neu",
  "xas",
  "mf1",
  "arc",
  "nuv",
  "ifczip",
  "xmt",
  "stp.z",
  "glb",
  "unv",
  "pkg"
];

// 文件类型
export const fileTypeMap = {
  "2D": ["dwg", "dwt", "dxf", "dws", "ocf"],
  "3D": [
    "prt*",
    "asm*",
    "ipt(V6-V2021)",
    "iam(V11-V2021)",
    "CATPart",
    "CATProduct",
    "rvt",
    "par",
    "emp",
    "emn",
    "idw",
    "ipt",
    "iam",
    "igs",
    "cfg",
    "prt",
    "ptf",
    "z3",
    "cwr",
    "smg",
    "sldasm",
    "sldprt",
    "catpart",
    "asm",
    "xar",
    "xpr",
    "psm",
    "pwd",
    "pft",
    "ed",
    "edz",
    "pvs",
    "pvz",
    "art",
    "sdt",
    "dcm",
    "ccd",
    "c3s",
    "jt",
    "sat",
    "sab",
    "iges",
    "igs",
    "obj",
    "x_t",
    "x_b",
    "xmt_txt",
    "xmt_bin",
    "plmxml",
    "step",
    "stp",
    "stpz",
    "stpxz",
    "vda",
    "stl",
    "ifc",
    "model",
    "rfa",
    "swstd",
    "cgr",
    "fbx",
    "catproduct",
    "3dm",
    "3dxml",
    "dae",
    "sam",
    "xcgm",
    "asat",
    "asab",
    "exp",
    "session",
    "zae",
    "ifzip",
    "idm",
    "p_b",
    "xmp_bin",
    "p_t",
    "xmp_txt",
    "stpx",
    "slddrw",
    "3mf",
    "catdrawing",
    "gltf",
    "mf",
    "skp",
    "nwd",
    "hsf",
    "bcf",
    "prc",
    "pts",
    "ptx",
    "u3d",
    "wrl",
    "wrml",
    "xyz",
    "dlv",
    "neu",
    "xas",
    "mf1",
    "arc",
    "nuv",
    "ifczip",
    "xmt",
    "stp.z",
    "glb",
    "unv",
    "pkg"
  ],
  pdf: ["pdf"],
  ppt: [
    "pptx",
    "ppt",
    "pot",
    "potx",
    "pps",
    "ppsx",
    "dps",
    "dpt",
    "pptm",
    "potm",
    "ppsm",
  ],
  word: ["doc", "docx", "dotx", "docm", "dotm", "rtf"],
  zip: ["zip", "rar", "tgz", "7z", "gz", "tar"],
  excel: ["et", "xls", "xlt", "xlsx", "xlsm", "xltx", "xltm", "csv"],
  txt: ["txt"],
  md: ["md"],
  image: [
    "jpg",
    "jpeg",
    "bmp",
    "png",
    "gif",
    "heic",
    "webp",
    "tiff",
    "livp",
    "svg",
    "jfif",
  ],
  video: [
    "mp4",
    "mov",
    "ts",
    "dat",
    "avi",
    "mts",
    "flv",
    "swf",
    "mpg",
    "3gp",
    "wmv",
    "mkv",
    "rmvb",
    "vob",
    "f4v",
    "rm",
    "m2ts",
    "m4v",
    "mpeg",
    "asf",
    "wmz",
    "webm",
    "wm",
    "pmp",
    "mpga",
    "dv",
  ],
  audio: [
    "mp3",
    "wav",
    "amr",
    "m4a",
    "wma",
    "aac",
    "3gpp",
    "ogg",
    "mid",
    "aif",
    "flac",
    "ra",
    "mp2",
    "ram",
    "ac3",
    "ape",
  ],
  font: ["ttf", "ttc", "otf", "shx"],
};

// cad文件缩略图尺寸
export enum THUMB_SIZE {
  SMALL = '64x48',
  MIDDLE = '128x96',
  LARGE = '256x192'
}

// 2D 3D览图支持功能点集合
export const getFunctionPoint = () => ({
  layer: $t('图层'),
  layout: $t('布局'),
  viewport: $t('视口相关'),
  measures: $t('测量'),
  save: $t('保存'),
  watermark: $t('水印'),
  notes: $t('批注'),
  comparison: $t('图纸对比'),
  stack: $t('叠图'),
  layerextractImg: $t('图层提取'),
  examine: $t('审图'),
  rotate: $t('旋转'),
  explode: $t('爆炸'),
  section: $t('剖切'),
  structuretree: $t('结构树')
})
