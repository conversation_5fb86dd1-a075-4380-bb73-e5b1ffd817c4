// import { getConfig } from '@/config';
import type { CrumbsListItem, FileItem } from '@/model/file';
import { checkInstall } from '@/services/system';
import { useStoreConfig } from '@/stores/config';
import { useUserStore } from '@/stores/user';
import CryptoJS from 'crypto-js';
import workerCrypto from './workerCrypto.mjs'
import md5 from 'crypto-js/md5';
import { Base64 } from 'js-base64';
import { storeToRefs } from 'pinia';
import { fileTypeMap, manufacturing } from './const';
import { ETASKTYPE } from '@/model/task';
import { $t } from '@/locales'
const SIZE_UNITS = ['B', 'KB', 'MB', 'GB', 'TB'];
const getWeeks = () => [$t('一'), $t('二'), $t('三'), $t('四'), $t('五'), $t('六'), $t('日')];
type fileType =
  | '3D'
  | '2D'
  | 'pdf'
  | 'ppt'
  | 'word'
  | 'zip'
  | 'excel'
  | 'txt'
  | 'image'
  | 'video'
  | 'audio'
  | 'font'
  | 'md';

// 生成uuid
export function guid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;

    return v.toString(16);
  });
}

// 获取文件名后缀
export const getExt = (fileName: string) => {
  if (!fileName) {
    return '';
  }
  if (fileName.match(/\.prt\.[^.]+$/)) {
    return 'prt*';
  }
  if (fileName.match(/\.asm\.[^.]+$/)) {
    return 'asm*';
  }
  const extStart = fileName.lastIndexOf('.');
  const ext = fileName.substring(extStart + 1, fileName.length).toLowerCase();
  return ext;
};

// 获取文件名
export const getShortName = (fileName: string) => {
  if (!fileName) {
    return '';
  }
  const extStart = fileName.lastIndexOf('.');
  if (extStart >= 0) return fileName.substring(0, extStart);
  else {
    return fileName;
  }
};

// 文件大小换算

export const formatSize = (
  value: number | string,
  forceInt = false
): string => {
  if (value) {
    let index = 0;
    const srcSize = typeof value == 'number' ? value : parseFloat(value);
    index = Math.floor(Math.log(srcSize) / Math.log(1024));
    const size = srcSize / Math.pow(1024, index);
    //  保留的小数位数
    if (SIZE_UNITS[index] !== 'B' && !forceInt) {
      return size.toFixed(2) + SIZE_UNITS[index];
    } else return size.toFixed(0) + SIZE_UNITS[index];
  }
  return '0B';
};

// 状态转换 目前不确定是否可以兼容所有
export const formatStatus = (status: string | number, pre = false): string => {
  const code = typeof status === 'number' ? status : parseInt(status);
  switch (code) {
    case 0:
      return pre ? $t('正常') : 'primary';
    case 1:
      return pre ? $t('禁用') : 'danger';
    default:
      return pre ? $t('正常') : 'primary';
  }
};

// 展示节点状态
export const formatNodeStatus = (
  status: string | number,
  pre = false
): string => {
  const code = typeof status === 'number' ? status : parseInt(status);
  switch (code) {
    case 1:
      return pre ? $t('已连接') : 'status-0';
    case 2:
      return pre ? $t('未连接') : 'status-1';
    case 3:
      return pre ? $t('未绑定') : 'status-2';
    default:
      return pre ? $t('正常') : 'status-0';
  }
};

/**
 * 获取文件SHA-1的值
 * @params file 上传的 file
 */
export function getFileSha1(
  file: File,
  cb?: (progress: number) => void
): Promise<string> {
  return new Promise((resolve, reject) => {
    // const chunkSize = 1024 * 64;
    // let currentChunk = 0;
    // const chunks = Math.ceil(file.size / chunkSize);
    // const sha1 = CryptoJS.algo.SHA1.create();
    // const blobSlice =
    //   File.prototype.slice ||
    //   File.prototype?.mozSlice ||
    //   File.prototype?.webkitSlice;

    // const fileReader = new FileReader();

    // fileReader.onload = function (e) {
    //   const data = CryptoJS.enc.Latin1.parse(e?.target?.result);
    //   sha1.update(data);

    //   currentChunk += 1;
    //   if (currentChunk < chunks) {
    //     loadNext();
    //   } else {
    //     const res =
    //       (sha1.finalize().toString(CryptoJS.enc.Hex) as string) || '';
    //     resolve(res);
    //   }
    // };

    // function loadNext() {
    //   const start = currentChunk * chunkSize,
    //     end = start + chunkSize >= file.size ? file.size : start + chunkSize;
    //   if (cb && typeof cb === 'function') {
    //     const result = start / file.size;
    //     cb(result);
    //   }
    //   fileReader.readAsBinaryString(blobSlice.call(file, start, end));
    // }

    // loadNext();

    const fr = new FileReader()
    fr.readAsArrayBuffer(file)
    fr.onerror = (err) => {
      console.error("FileReader onerror", err)
    }
    fr.onloadend = (e) => {
      const buf = e.target?.result
      if (!buf) return reject('readAsArrayBuffer error!')
      

      workerCrypto.cryptoDigest(buf, 'SHA-1')
      .then(resolve)
      .catch(reject)
    }
    
  });
}

// 十六位十六进制数作为密钥
const SECRET_KEY = CryptoJS.enc.Utf8.parse('gstarcad');
// 十六位十六进制数作为密钥偏移量
const SECRET_IV = CryptoJS.enc.Utf8.parse('gstarcad');

/**
 * 加密方法
 * @param data - 数据
 */
export function encrypt(data: any, key: string) {
  const dataHex = CryptoJS.enc.Utf8.parse(data);
  const encrypted = CryptoJS.AES.encrypt(
    dataHex,
    CryptoJS.enc.Utf8.parse(key),
    {
      iv: CryptoJS.enc.Utf8.parse(key.substring(0, 16)),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    }
  );
  return encrypted.toString();
}

/**
 * 解密方法
 * @param data - 数据
 */
export function decrypt(data: any, key: string) {
  try {
    const decrypt = CryptoJS.AES.decrypt(data, CryptoJS.enc.Utf8.parse(key), {
      iv: CryptoJS.enc.Utf8.parse(key.substring(0, 16)),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    const result = CryptoJS.enc.Utf8.stringify(decrypt).toString();
    return JSON.parse(result);
  } catch (e) {
    console.log('解密socket数据失败：', e);
  }
}

/** 密码MD5加密 */
export function encryptByMd5(password: string) {
  return md5(password).toString();
}

// 由于lodash需要引入的包过多,而且用到的地方不多,所以手写比较好

/** 防抖 */
export function debounce<A extends Array<any>, R = void>(
  func: (..._args: A) => R,
  wait: number,
  immediate?: boolean
) {
  let timeout: null | NodeJS.Timeout = null;

  return (..._args: A) => {
    const context = debounce;
    const args = _args;

    if (timeout) clearTimeout(timeout); // timeout 不为null
    if (immediate) {
      const callNow = !timeout; // 第一次会立即执行，以后只有事件执行后才会再次触发
      timeout = setTimeout(function () {
        timeout = null;
      }, wait);
      if (callNow) {
        func.apply(context, args);
      }
    } else {
      timeout = setTimeout(function () {
        func.apply(context, args);
      }, wait);
    }
  };
}

// 对象数据过滤传递给详情页
export const formatObjectData = (
  val: any,
  showKeys: string[] = [],
  formatData: Recordable = {}
) => {
  const data: { key: string; value: any; isHtml?: boolean }[] = [];
  if (!val) return data;
  for (const [key, value] of Object.entries(val)) {
    if (showKeys.includes(key)) {
      data.push({
        key,
        value: formatData[key] ? formatData[key](value) : value,
      });
    } else {
      continue;
    }
  }
  return data;
};

// 下载
export const downloadByData = (
  data: BlobPart,
  filename: string,
  mime?: string
) => {
  const blob = new Blob([data], {
    type: mime || 'application/octet-stream',
  });
  const urlObject = window.URL || window.webkitURL || window;
  const blobURL = urlObject.createObjectURL(blob);
  const tempLink = document.createElement('a');
  tempLink.style.display = 'none';
  tempLink.href = blobURL;
  tempLink.setAttribute('download', filename);
  if (typeof tempLink.download === 'undefined') {
    tempLink.setAttribute('target', '_blank');
  }
  document.body.appendChild(tempLink);
  tempLink.click();
  // ElMessage({
  //   type: "success",
  //   message: `下载成功`,
  // });
  document.body.removeChild(tempLink);
  urlObject.revokeObjectURL(blobURL);

  // const _utf = "\uFEFF";
  // const blob = new Blob([_utf + data], {
  //     type: mime || "application/octet-stream", // 自己需要的数据格式
  // });
  // window.navigator.msSaveBlob(blob, filename);
};

export const downloadByUrl = (url: string, prefix = '&') => {
  const userStores = useUserStore();
  const a = document.createElement('a');
  const { api_endpoint: host } = window.$globalConfig;
  a.href = `${host}${import.meta.env.VITE_GLOB_API_URL_PREFIX as any}/${
    import.meta.env.VITE_GLOB_API_URL_VERSION as any
  }${url}${prefix}Authorization=${userStores.token}`;
  a.style.display = 'none';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

// 禁用时间 不能超过当前时间
export const disabledDate = (time: Date | string, type: string) => {
  if (type === 'date') {
    return (time as Date).getTime() < Date.now() - 8.64e7;
  } else {
    const arr = [];
    const len = type === 'hour' ? 24 : 60;
    const value =
      type === 'hour'
        ? new Date().getHours()
        : type === 'minute'
        ? new Date().getMinutes()
        : new Date().getSeconds();
    for (let i = 0; i < len; i++) {
      if (new Date(time).getTime() < Date.now()) {
        if (value <= i) continue;
        arr.push(i);
      }
    }
    return arr;
  }
};

const iz = (a: number): string => {
  return a < 10 ? `0${a}` : `${a}`;
};

export const getCurrentTime = (type = 0) => {
  // 0 获取当前时间， 1 获取当天0点时间， 2获取当天23点59分59秒
  const date = new Date();
  const Y = date.getFullYear(); // 获取系统的年；
  const M = iz(date.getMonth() + 1); // 获取系统月份，由于月份是从0开始计算，所以要加1
  const D = iz(date.getDate()); // 获取系统日
  const H = iz(date.getHours()); // 获取系统小时
  const m = iz(date.getMinutes()); // 获取系统分
  const s = iz(date.getSeconds()); // 获取系统秒
  switch (type) {
    case 0:
      return Y + '-' + M + '-' + D + ' ' + H + ':' + m + ':' + s;
    case 1:
      return Y + '-' + M + '-' + D + ' ' + '00' + ':' + '00' + ':' + '00';
    case 2:
      return Y + '-' + M + '-' + D + ' ' + '23' + ':' + '59' + ':' + '59';
    default:
      return Y + '-' + M + '-' + D + ' ' + H + ':' + m + ':' + s;
  }
};

// 获取外部js的全局对象
export const getGlobalObject = (scriptUrl: string, objName: string) => {
  return new Promise((resolve, reject) => {
    const tag = document.getElementsByTagName('script');
    for (const i of tag) {
      if (i.src.includes(scriptUrl)) {
        return resolve((window as any)[objName]);
      }
    }
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = withVersion(scriptUrl);
    script.onerror = reject;
    document.body.appendChild(script);
    script.onload = () => {
      resolve((window as any)[objName] as any);
    };
  });
};

// 输出webclient版本
declare const __CLIENT_VERSION__: string;
export function withVersion(url: string): string {
  const sep = url.includes('?') ? '&' : '?'
  return `${url}${sep}v=${__CLIENT_VERSION__}`
}

// 表单校验必填项
export const getRequireRules = (items: Recordable[], prefix = '') => {
  const rule: Recordable = {};
  if (!items || items.length <= 0) return;
  items.forEach((item) => {
    rule[item.key] = [
      {
        required: true,
        message: `请${item.action || $t('输入')}${prefix}${$t(
          `formKey.${item.key}`
        )}`,
        trigger: 'blur',
      },
      {
        required: true,
        message: `请${item.action || $t('输入')}${prefix}${$t(
          `formKey.${item.key}`
        )}`,
        trigger: 'change',
      },
    ];
  });
  return rule;
};

export const isType = (item: FileItem, type: fileType) => {
  const extension = getExt(item.name);
  if (Object.prototype.hasOwnProperty.call(fileTypeMap, type)) {
    return fileTypeMap[type].includes(extension.toLowerCase());
  }
  return false;
};

// 获取文件icon名称
export const getFileIconName = (item: FileItem) => {
  let name = 'unknow';
  if (item.fileType === 1) {
    name = 'folder';
  } else {
    const extension = getExt(item.name);
    for (const [key, value] of Object.entries(fileTypeMap)) {
      if (value.includes(extension)) return key;
    }
  }
  return name;
};

// 校验系统是否安装
export const checkIsInstall = async () => {
  // 校验是否安装
  let isInstall = true;
  // const config = getConfig();
  const config = window.$globalConfig;
  
  if (Object.prototype.hasOwnProperty.call(config, 'server_is_install')) {
    isInstall = !!config.server_is_install;
  } else {
    try {
      await checkInstall();
    } catch (e: any) {
      if (e.code && e.code === 500000) {
        isInstall = false;
      }
    }
  }
  return isInstall;
};

/**
 * url 参数转义
 * + %2B
 * 空格 %20
 * / %2F
 * ? %3F
 * # %23
 * ...
 */
export const escapedUrl = (url: string) => {
  // 下载文件需要转义特殊字符
  let _url = url.replace(/\+/g, '%2B'); // "+" 转义
  _url = url.replace(/#/g, '%23'); // "#"
  return _url;
};

// 判断俩个数组是否相同
export const isEqualByArrays = (arr1: string[], arr2: string[]) => {
  const result =
    arr1.length === arr2.length &&
    arr1.every((a) => arr2.some((b) => a === b)) &&
    arr2.every((_b) => arr1.some((_a) => _a === _b));
  return result;
};

// query参数编码
export const encodeQueryParams = (params: {
  [key: string]: string | any;
}): string => {
  return Base64.encode(
    JSON.stringify(Object.keys(params).map((k) => params[k]))
  );
};

// query参数解码
export const decodeQueryParams = (
  paramsStr: string
): { [key: string]: string } => {
  const val = JSON.parse(Base64.decode(paramsStr));
  return val;
};

// 判断是否是制造业文件
export const isManufacturing = (name?: string) => {
  const file_extension = getExt(name || '');
  return manufacturing.some(
    (v: string) => v.toLowerCase() === file_extension.toLowerCase()
  );
};

/**
 * 获取制造业文件格式对应的任务类型
 * 目前制造业几十种文件格式共用一个安装包，
 * 后期会为每种文件格式提供一个单独的应用，
 * 此方法在后台更改完后还会进行更改，
 * 目前只对skp文件使用单独的任务类型
 */
export const getMFGType = (name?: string) => {
  let taskType = ETASKTYPE.MANUFACTURING
  const file_extension = getExt(name || '');
  if (file_extension.toLowerCase() === 'skp') {
    taskType = ETASKTYPE.MANUFACTURING_SKP
  }
  return taskType
};

// 改变fav
export const changeFavicon = () => {
  let favicon = document.querySelector('link[rel="icon"]') as HTMLAnchorElement;
  if (favicon !== null) {
    const configStores = useStoreConfig();
    const { site_favicon, site_host } = storeToRefs(configStores);
    favicon.href = `${site_host.value}/${site_favicon.value}`;
  } else {
    favicon = document.createElement('link') as unknown as HTMLAnchorElement;
    favicon.rel = 'icon';
    favicon.href = '/favicon.ico';
    document.head.appendChild(favicon);
  }
};

/**
 * 格式化系统策略时间
 * @param val  (月份（1-12） 日（1-31） 星期（1-7）小时（0-23）分钟（0-59）秒(0-59) 用下划线_分割)
 * @example execPolicy 0_12_0_22_00_00' 每月12号 22:00:00执行)
 * @returns
 */
export const formatExecPolicyTime = (val: string) => {
  let msg = '';
  if (val) {
    const data = val.split('_');
    // 获取时分秒
    const dayTime = `${iz(parseInt(data[3]))}:${iz(parseInt(data[4]))}:${iz(
      parseInt(data[5])
    )}`;
    if (parseInt(data[2])) {
      // 如果设置了周
      const weeks = getWeeks();
      msg += $t('每周{week}', { week: weeks[parseInt(data[2]) - 1] });
    } else {
      // 判断月份
      msg += parseInt(data[0])
        ? $t('每年第{month}月的', { month: parseInt(data[0]) })
        : parseInt(data[1])
        ? $t('每月')
        : '';
      // 判断日
      msg += parseInt(data[1]) ? $t('{day}号', { day: parseInt(data[1]) }) : $t('每天');
    }
    msg += dayTime;
    msg += $t('执行');
  }
  return msg;
};

export const getBreadcrumbs = (items: CrumbsListItem[]) => {
  if (items && items.length > 0) {
    return items.map((item) => {
      const queryParams = {
        folderId: Number(item.folderId),
        direction: 0,
        limit: 10,
        searchName: '',
        searchBeginTime: '',
        searchEndTime: '',
        fileType: 0,
        sortField: 0,
        ascOrDesc: 'desc',
        lastFileId: 0,
        lastFileSize: 0,
        lastFileModifyTime: '',
        lastFileName: '',
        reqPage: 1,
        reqPageIdArr: ''
      }
      return {
        title: item.folderName,
        href: item.folderId === 2 ? 'files/file' : `files/file?params=${encodeQueryParams(queryParams)}`,
      };
    });
  }
};

/**
 * @description: 判断系统类型
 * @return {*}
 */
export const systemType = () => {
  const agent = navigator.userAgent.toLowerCase()
  const windows = agent.indexOf("win") > -1 || agent.indexOf("wow") > -1
  const Linux = String(navigator.platform).indexOf("Linux") > -1
  const mac = /macintosh|mac x/i.test(agent)
  const iOS = !!agent.match(/\(i[^;]+;( u;)? cpu.+mac os x/)
  const android = agent.indexOf('android') > -1 || agent.indexOf('adr') > -1

  if (windows) return "Windows"
  if (Linux && !android) return "Linux"
  if (mac) return "iOS"
  if (iOS) return "iOS"
  if (android) return "Android"
}

/**
 * 
 * @param url 
 * @param queryData 
 */
export const openUrlFn = (url: string, queryData: { [k: string]: [v: any] } | null) => {
  // const link = document.createElement('a');
  const dom = document.getElementById("openNewWindow")
  const search = new URLSearchParams()
  if (queryData) {
    for (const [key, value] of Object.entries(queryData)) {
      if(key === "_blank") continue
      search.set(key, value as unknown as string)
    }

    // link.href = `${url}?${search}`;
    (dom as any).href = `${url}?${search}`;
  } else {
    // link.href = url;
    (dom as any).href = url;

  }
     
  // link.target = '_blank';
  // console.log('link', link)
  // document.body.appendChild(link);
  // link.click();

    dom?.click();
    (dom as any).href = "";
  // document.body.removeChild(link);
}