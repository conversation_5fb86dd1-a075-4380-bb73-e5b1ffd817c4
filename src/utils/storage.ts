import { StorageType } from "./const";
export function get(key: string, storage: string) {
    if (!key) return "";
    switch (storage) {
        case StorageType.SESSION:
            return sessionStorage.getItem(key);
        case StorageType.LOCAL:
            return localStorage.getItem(key);
        default:
            return localStorage.getItem(key);
    }
}
export function set(key: string, value: string | object, storage: string) {
    if (!key) return "";
    if (typeof value === "object") value = JSON.stringify(value);
    switch (storage) {
        case StorageType.SESSION:
            sessionStorage.setItem(key, value);
            break;
        case StorageType.LOCAL:
            localStorage.setItem(key, value);
            break;
        default:
            localStorage.set(key, value);
            break;
    }
}
export function remove(key: string, storage: string) {
    if (!key) return "";
    switch (storage) {
        case StorageType.SESSION:
            sessionStorage.removeItem(key);
            break;
        case StorageType.LOCAL:
            localStorage.removeItem(key);
            break;
        default:
            localStorage.removeItem(key);
            break;
    }
}
