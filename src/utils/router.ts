import type { Router, RouteRecordRaw } from "vue-router";
import { whiteMenuList } from "./const";

/** 按照路由中meta下的rank等级升序来排序路由 */
export const ascendRoute = (arr: any[]) => {
    return arr.sort(
        (a: { meta: { rank: number } }, b: { meta: { rank: number } }) => {
            return a?.meta.rank - b?.meta.rank;
        }
    );
};

/** 判断是否是在动态路由中存在且没有权限访问 */
export const checkRoute = (
    path: string,
    router: Router,
    asyncRoutes: RouteRecordRaw[]
) => {
    let r = false;
    for (let i = 0; i < asyncRoutes.length; i++) {
        const route = asyncRoutes[i];
        if (route.path === path && !router.hasRoute(route.name || "")) {
            r = true;
            return;
        }
        if (route.children) {
            const subRouteHas = route.children.find((v) => v.path === path);
            if (subRouteHas && !router.hasRoute(subRouteHas.name || "")) {
                r = true;
                return;
            }
        }
    }
    return r;
};

/** 判断是否拥有路由权限 */
export const hasPermission = (menu: Recordable[], route: RouteRecordRaw) => {
    if (route.meta) {
        return menu.find((m) => m.title === route.meta?.title);
    } else {
        return [];
    }
};

/** 过滤路由 */
export const filterAsyncRoutes = (
    routes: RouteRecordRaw[],
    menu: Recordable[]
) => {
    const res: RouteRecordRaw[] = [];
    if (!menu) return res;
    routes.forEach((route: RouteRecordRaw) => {
        const r = { ...route };
        const hasAuthMenu = hasPermission(menu, r);
        if (whiteMenuList.includes(r.name as string)  || hasAuthMenu) {
            if (r.children) {
                r.children = filterAsyncRoutes(
                    r.children,
                    hasAuthMenu?.children ?? []
                );
            }
            // 路由重定向重新赋值
            r.redirect = r.children && r.children[0].path;
            res.push(r);
        }
    });
    return res;
};

/** 根据后端menu字段重新生成规范路径 */
export const formatAsyncRoutes = (arrRoutes: Array<RouteRecordRaw>) => {};
