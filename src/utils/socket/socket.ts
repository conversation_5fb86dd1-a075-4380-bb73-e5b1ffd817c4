import * as createSocket from "socket.io-client";
import CryptoJS from 'crypto-js';
import { enabled_socket_security } from "../const";
import { getGlobalConfig } from "@/config";

export const CENOTICE_PREFIX = 'ceNotice.'

export type TResCallback = (resData: any) => void
export type TCallback = (ename: string, data: any, topic: any) => void
export interface IResCallbacks {
  [trans_id: string | number]: TResCallback
}
export interface ICallBacks {
    [ename: string]: TCallback[];
}

export interface ICENoticeData {
  data: any
  file_id: number
  msg_type: string
  room_id: string
  trans_id: string
}

export interface ISubEventData {
    trans_id: number;
    event: string;
    topic: string;
    param: {
        [key: string]: any;
    };
    sender?:{
        [key: string]: any;
    };
    message: {
      [key: string]: any;
    }
}

export interface IConnectParams {
  token: string // 认证token
  url: string // socket连接地址
  key: string //aes key
  uid: string // 用户id（唯一标识即可）
  uname: string // 用户名（理论可以任意填或uid即可）
  did: string // 设备id （用浏览器指纹）
  conn_type: number // 连接类型 0: 普通链接 1：单人链接 2：多人链接
  fileId?: string | number // 文件id
  client_id?: string // 客户端id
  apply_id?: boolean // 是否要返回设备id
  browser_id?: string // 设备id
}

export default class Socket {
  _socket: any;
  _callbacks: ICallBacks;
  _resCallbacks: IResCallbacks;
  _processedList: Set<string | number>;
  _connectParams: IConnectParams;
  conn_id: string | null;
  _singleResCallbacks?: any
  private _emitList: {
    ename: string
    args: string
    isEmited: boolean
  }[]
  _reConnect: boolean
  reConnectInterval: number
  constructor() {
    this._socket = null;
    this.conn_id = null;
    this._reConnect = true
    this.reConnectInterval = 40000 // 40s
    this._callbacks = {};
    this._resCallbacks = {};
    this._singleResCallbacks = {}
    // 初始化待提交队列
    this._emitList = []
    this._connectParams = {
      token: '',
      url: '/',
      key: '',
      uid: '',
      uname: '',
      did: '',
      conn_type: 0,
      fileId: ""
    };
    // 已处理的事件id
    this._processedList = new Set();
  }
  connect(params: IConnectParams) {
    if(!this.reConnect) return
    this._connectParams = params
    console.log("socket connect", this._connectParams, this.reConnect)
    if (!this.isConnected()) {
          this._socket = createSocket.connect(this._connectParams.url, {
              reconnection: false,
              transports: ["websocket", "polling"],
              query: {
                Authorization: this._connectParams.token,
                uid: this._connectParams.uid,
                username: this._connectParams.uname,
                did: this._connectParams.did,
                tm: +new Date(),
                conn_type: this._connectParams.conn_type,
                  // file_id: 0
                file_id: this._connectParams?.fileId || 0,
                client_id: this._connectParams?.client_id || '',
                apply_id: this._connectParams?.apply_id || false,
                browser_id: this._connectParams?.browser_id || ''
              },
              withCredentials: false,
              rejectUnauthorized: false
          });

        const originalOnevent = this._socket.onevent;

        this._socket.onevent = (packet: any) => {
          const args = packet.data || [];
          originalOnevent.call(this._socket, packet); // Call the original handler
          const eventName = args[0]; // The event name is the first argument
          let eventData = args.slice(1)[0]; // The rest are event data
          console.log("eventData", args, eventData)

          // 非subscribe事件的转发
          const _handleSubscribe = this._handleSubscribe.bind(this)
          _handleSubscribe(eventData)
          // 踢出编辑事件
          const raiseRule = 'file.edit.raise'
          if (raiseRule === eventName) {
            this.close()
            return
          }
          // 登录失效事件
          const eTokenInvalid = 'user.token.invalid'
          if (eTokenInvalid === eventName) {
            this.close()
            return
          }

          // ce.rooms.notice.converted 二进制不需要解密
          if (this._connectParams.key && eventName !== 'connect.reponse') {
            // if (eventName.includes('ce.rooms.notice.converted')) {}
            eventData = decrypt(eventData, this._connectParams.key)
          }
          console.log(`socket client receive---: ${eventName}`, eventData, eventData?.conn_id);
          this.setConnectId(eventData?.conn_id)

          if (eventData?.message) {
            eventData.message = JSON.parse(eventData.message)
            console.log(this)
          }

         
          
          // 房间相关 事件，含通知事件
          const regRoom = /^(ce|send|file\.edit\.single|license\.exchange\.browser)\..*/
          if (regRoom.test(eventName)) {
            // 通知事件单独处理
            if (eventName.includes('ce.rooms.notice')) {
              this._ceNotice(eventName, eventData)
              return
            }
            this._resSubscribe(eventData)
          }
        }

        this._socket.on("connect", this._handleConnect.bind(this));
        this._socket.on("disconnect", this._handleDisConnect.bind(this));
        this._socket.on("connect_error", this._handleConnectError.bind(this));
        this._socket.on("subscribe", this._handleSubscribe.bind(this));
        this._socket.on("broadcast", this._handleSubscribe.bind(this));

        this._socket.on("connect.reponse", this.handleConnRes.bind(this))
      } else {
          console.log("已连接，无需重连", this._connectParams, this._socket);
      }
  }
  close() {
    this.reConnect = false
    this._emitList = []
    if (this._socket) {
      this._socket.close()
      this._socket = null
    }
  }
  private _set_connection() {
    this.emit("set_connection", {
      trans_id: createTransId(),
      uid: this._connectParams.uid,
      username: this._connectParams.uname,
      did: this._connectParams.did,
      tm: +new Date(),
      conn_type: this._connectParams.conn_type,
      file_id: this._connectParams?.fileId || 0,
    })

    console.log("set_connection", {
      trans_id: createTransId(),
      uid: this._connectParams.uid,
      username: this._connectParams.uname,
      did: this._connectParams.did,
      tm: +new Date(),
      conn_type: this._connectParams.conn_type,
      file_id: this._connectParams?.fileId || 0,
    })
  }
 
  setConnectId(id: string) {
    if (id) {
      this.conn_id = id
      console.log("id", this)
      this._handlePostConnectId(id)
    }
  }
  _handlePostConnectId(id: string) {
    console.log("id", id)
  }
  isConnected() {
    return this._socket && this._socket.connected;
  }
  // 
  // socket订阅消息，进行分发
  _handleSubscribe(eventData: ISubEventData) {
      const _aseKey = this._connectParams.key
      if (_aseKey) {
          eventData = decrypt(eventData, _aseKey)
      }
      console.log('socket eventData', eventData)
      // 非正常格式不进行处理
      if (!eventData.trans_id && !eventData.event) {
          console.error("subscribe/broadcast 异常信息", eventData);
      }
      if (!this._isProcessed(eventData.trans_id)) {
          // 将事件加入已处理记录
          this._addProcessed(eventData.trans_id);

          // 匹配订阅事件
          const ename = eventData.event;
          const resParam = eventData.param;
          const topic = eventData.topic;
          const matchList = this._getMatchList(ename);
          // console.log('socketio事件：', ename, ' 匹配事件列表 ', matchList)

          matchList.forEach((_matchName) => {
              if (this._callbacks[_matchName]) {
                  this._callbacks[_matchName].forEach((cb) =>
                      cb.call(this, ename, resParam, topic)
                  );
              }
          });
      }
  }
  // socket连接成功cb
  _handleConnect(e: any) {
    this.connStatusChange(this.isConnected())
    console.log("socketio连接成功", this._connectParams);
    // this._set_connection()

    // setTimeout(() => {
      // ports.forEach((port) => {
      //     port.postMessage('refreshGlobalConfig');
      // });
      // 连接成功将相关订阅消息未发送的发送并置为发送
      console.log('连接成功，this._emitList', this._emitList)
      this._emitList.forEach(emit => {
        if (!emit.isEmited) {
          this.emit(emit.ename, emit.args)
          console.log('发送之前的订阅事件：', emit.ename, emit.args)
          emit.isEmited = true
        }
      })
    // }, 3000)
  }
  // socket断连cb
  _handleDisConnect(reason: string) {
    this.connStatusChange(this.isConnected())
    console.log("_handleDisConnect", reason);
    // 断联将相关订阅消息置为未发送，待重连后发送
    this._emitList.forEach(emit => {
      emit.isEmited = false
    })
    // 尝试重连
    if (this.reConnect) {
      if (this._socket) {
        setTimeout(() => {
          this._socket.connect()
        }, this.reConnectInterval)
        console.warn('socket reconnecting')
      } else {
        console.error('socket reconnect error: socket is null')
      }
    }
  }
  // socket连接错误cb
  _handleConnectError(e: any) {
    this.connStatusChange(this.isConnected())
    console.log("socket连接错误", e.message, e);
    // 尝试重连
    if (this.reConnect) {
      if (this._socket) {
        setTimeout(() => {
          this._socket.connect()
        }, this.reConnectInterval)
        console.warn('socket reconnecting')
      } else {
        console.error('socket reconnect error: socket is null')
      }
    }
  }
  // 业务上连接结果，成功或者失败
  handleConnRes(payload: any) {
    console.log("业务上连接结果", payload);
  }
  on(
      ename: string,
      callback: TCallback,
      topic?: string
  ) {
      if (typeof ename !== 'string') {
        const params:any = ename
        ename = params.ename
        callback = params.callback
        topic = params.topic
      }
      // 本地订阅
      if (!this._callbacks[ename]) this._callbacks[ename] = [];
      this._callbacks[ename].push(callback);

      const emitData = {
        event: ename,
        topic: topic || "",
      }
      // 通知服务端我们监听的事件
      this.emit("subscribe", emitData)
  }
  off(ename: string, callback?: TCallback) {
    if (typeof ename !== 'string') {
      const params:any = ename
      ename = params.ename
      callback = params.callback
    }
      // 本地取消订阅
      if (callback) {
          if (!this._callbacks[ename]) return;

          for (let i = 0; i < this._callbacks[ename].length; i++) {
              if (this._callbacks[ename][i] === callback) {
                  this._callbacks[ename].splice(i, 1);
                  i--;
              }
          }
      } else {
          this._callbacks[ename] = [];
      }

      // 获取取消订阅数据
      const data = {
        event: ename,
        topic: '',
      }
      // 通知服务端取消
      this.emit("unsubscribe", data)
  }
  emit(ename: string, args: any, isEmited?: boolean) {
    if (typeof ename !== 'string') {
      const params:any = ename
      ename = params.ename
      args = params.args
      isEmited = params.isEmited
    }

    args.tm = Date.now()
    console.log('socket client emit+++', ename, args)

    const isConnected = this.isConnected()

    if (isEmited === undefined) {
      if (!this._emitList.find((v) => v.args === args && v.ename === ename)) {
        this._emitList.push({ename, args, isEmited: isConnected})
      }
    }
    if (!isConnected) return
    
    const emitData = !enabled_socket_security ? JSON.stringify(args) : encryptData(args, this._connectParams.key)
    this._socket?.emit(ename, emitData);
  }
  // 获取事件名匹配列表
  _getMatchList(ename: string) {
      const arr = ename.split(".");
      const matchList = [];
      for (const i in arr) {
          let str = "";
          for (let k = 0; k <= parseInt(i); k++) {
              if (k != 0) str += ".";
              str += arr[k];
          }
          matchList.push(str);
      }
      return matchList;
  }
  // 加入已处理id到列表
  _addProcessed(eid: string | number) {
    if (eid !== undefined && eid !== null) {
      // 已处理id缓存一万，超过则清空
      if (this._processedList.size > 10000) this._processedList.clear();
      this._processedList.add(eid);
    }
  }
  // 检查是否已处理
  _isProcessed(eid: string | number) {
    if (eid !== undefined && eid !== null) {
      return this._processedList.has(eid);
    } else {
      return false
    }
  }
  // 连接状态变化
  connStatusChange(isConnected: boolean) {
    if (!isConnected) this.handleDisConnect({})
  }
  // 接受socket响应监听
  _resSubscribe(eventData: ISubEventData) {
    console.log('socket _resSubscribe', eventData)
    // 非正常格式不进行处理
    if (!eventData.trans_id && !eventData.event) {
      console.error("subscribe/broadcast 异常信息", eventData);
    }

    if (eventData?.message?.trans_id) {
      console.log(this._processedList, eventData.message.trans_id)
      if (!this._isProcessed(eventData.message.trans_id)) {
        this._addProcessed(eventData?.message?.trans_id);
        console.log("收到编辑页返回后的回调", this._resCallbacks)
        this._handleResSubscribe(eventData?.message?.trans_id, eventData)
        // if (this?._resCallbacks[eventData?.message?.trans_id]) {
        //   const cb = this._resCallbacks[eventData?.message?.trans_id]
        //   delete this._resCallbacks[eventData?.message?.trans_id]
        //   cb.call(this, eventData)
        // }
      }
    }

    if (!this._isProcessed(eventData.trans_id)) {
      // 将事件加入已处理记录
      this._addProcessed(eventData.trans_id);
      console.log(this._resCallbacks)
      // 优先匹配 响应 事件
      if (this._resCallbacks[eventData.trans_id]) {
        const cb = this._resCallbacks[eventData.trans_id]
        delete this._resCallbacks[eventData.trans_id]
        cb.call(this, eventData)
      }
    }

   
  }
  // 请求响应模式
  request(reqData: any) {
    return new Promise((resolve, reject) => {
      const trans_id = createTransId()
      const {ename, ...args} = reqData

      const cb:TResCallback = (data: any) => {
        console.log('socket request respones data', data)
        resolve(data)
      }

      this._resCallbacks[trans_id] = cb
      const mergeArgs = {trans_id, ...args}
      console.log('socket request args', this, mergeArgs)
      this.emit(ename, mergeArgs)
    })
  }

  _handleResSubscribe(id: string, eventData:ICENoticeData) {
    console.log(id, eventData)
  }

  // 房间通知事件处理
  _ceNotice(ename: string, eventData: ICENoticeData) {
    const {
      data,
      file_id,
      msg_type,
      room_id,
      trans_id,
    } = eventData
    const matchEName = CENOTICE_PREFIX+msg_type
    let resParam:any
    if (msg_type.includes('ce.rooms.notice.converted')) {
      let editor, base64Data, commitId, command, position, lastCommitId
      switch (msg_type) {
        case 'ce.rooms.notice.converted.map': {
          base64Data = data.map
          break
        }
        case 'ce.rooms.notice.converted.dwi': {
          base64Data = data.dwi
          break
        }
      }
      editor = data.editor
      commitId = data.commit_id
      command = data.command
      position = data.position
      lastCommitId = data.last_commit_id
      // 校验数据
      if (base64Data) {
        // 由于base64需要转换
        const buffer = Uint8Array.from(atob(base64Data as string), c => c.charCodeAt(0))
        resParam = {
          data: buffer,
          commitId,
          editor,
          command,
          position,
          lastCommitId
        }
      }
    } else {
      resParam = data
    }
    if (this._callbacks[matchEName]) {
      this._callbacks[matchEName].forEach((cb) =>
        cb.call(this, ename, resParam, '')
      );
    }
  }
  // 断联回调
  handleDisConnect(payload: any) {

  }
  get reConnect() {
    return this._reConnect
  }
  set reConnect(v: boolean) {
    this._reConnect = v
    this.setReConnect(v)
  }
  setReConnect(v: boolean) {
    
  }
  setInsideReConnect(v: boolean){
    this.reConnect = v
  }
}


export function createTransId() {
  return Date.now() + '-' + Math.floor(Math.random() * 10000000)
}

// aes加密data
export function encryptData(data: Object, aeskey:string) {
  try {
    return encrypt(JSON.stringify(data), aeskey || '')
  } catch (e) {
    console.error('socket encryptData error', e)
    return ''
  }
}

// socket的数据，是否加解密
export function getSocketData(ename: string, topic?: any, aeskey?:string) {
    if (!enabled_socket_security) {
        // 不采用加解密
        return {
            event: ename,
            topic: topic || "",
        }
    } else {
        // 采用加解密
        const result = encrypt(JSON.stringify({
            event: ename,
            topic: topic || "",
        }), aeskey || '')
        return result
    }
}

/**
 * 加密方法
 * @param data - 数据
 */
export function encrypt(data: any, key: string) {
  const dataHex = CryptoJS.enc.Utf8.parse(data);
  const encrypted = CryptoJS.AES.encrypt(
    dataHex,
    CryptoJS.enc.Utf8.parse(key),
    {
      iv: CryptoJS.enc.Utf8.parse(key.substring(0, 16)),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    }
  );
  return encrypted.toString();
}

/**
 * 解密方法
 * @param data - 数据
 */
export function decrypt(data: any, key: string) {
  try {
    const decrypt = CryptoJS.AES.decrypt(data, CryptoJS.enc.Utf8.parse(key), {
      iv: CryptoJS.enc.Utf8.parse(key.substring(0, 16)),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    const result = CryptoJS.enc.Utf8.stringify(decrypt).toString();
    try {
      const data = JSON.parse(result)
      return data
    } catch (err) {
      console.warn("解密socket数据不是json:  ", result)
      return result
    }
  } catch (e) {
    console.log('解密socket数据失败：', e);
  }
}