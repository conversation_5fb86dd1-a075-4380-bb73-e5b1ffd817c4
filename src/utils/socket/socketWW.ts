import Socket, { createTransId } from './socket'
import type { IConnectParams, ISubEventData, IResCallbacks, TResCallback } from './socket'

import { getGlobalConfig } from "@/config";
import WebWorker from "./socketWebWorker?worker&inline";


// 使用共享worker建立socketio长联
export default class SocketByWebWorker extends Socket {
  private _worker: Worker;
  constructor() {
    super()

    this._worker = new WebWorker();
    this._worker.onmessage = (ev) => {

      console.log('ww worker message', ev.data)
      
      if (ev.data === 'refreshGlobalConfig') {
        getGlobalConfig()
      } else if (ev.data.setSocketAttr) { // 修改socket属性
        const {key, value} = ev.data.setSocketAttr as {key: string, value: any};
        console.log('设置socket属性', ev.data.setSocketAttr);
        (this as any)[key] = value
      } else if (ev.data.disConnect) { // 断连"file.edit.raise"
        this.handleDisConnect(ev.data.disConnect)
      } else if (ev.data.connectMsg) { // 长连连接信息，成功或失败
        this.handleConnRes(ev.data.connectMsg)
      } else if (ev.data.eventData?.event_type) { // 普通数据消息
        ev.data.eventData.event = ev.data.eventData.event_type
        ev.data.eventData.param = ev.data.eventData
        this._handleSubscribe(ev.data.eventData);
      } else if (ev.data.eventData?.event) { // 普通数据消息
        this._handleSubscribe(ev.data.eventData);
      } else if (ev.data.resEventData) { // 请求响应的消息
        const eventData = ev.data.resEventData
        if (this._resCallbacks[eventData.trans_id]) {
          const cb = this._resCallbacks[eventData.trans_id]
          delete this._resCallbacks[eventData.trans_id]
          cb.call(this, eventData)
        }
      } else {
        localStorage.setItem("connectionInfo", JSON.stringify(ev.data.eventData));
      }
    };
    console.log("SocketByWebWorker");
  }
  connect(params: IConnectParams) {
    console.log('SocketByWebWorker connect1')
    this._connectParams = params
    this._worker.postMessage({
      ename: "connect",
      params,
    });
  }
  // socket订阅消息，进行分发
  _handleSubscribe(eventData: ISubEventData) {
    if (!eventData) return;
    console.log("subscribe/broadcast receive msg", eventData);
    // 非正常格式不进行处理
    if (!eventData.trans_id && !eventData.event) {
      return console.error("subscribe/broadcast 异常信息", eventData);
    }
    const ename = eventData.event;
    const resParam = eventData.param;
    if (eventData.sender && eventData.sender.username) {
      resParam.uid = eventData.sender.uid
      resParam.username = eventData.sender.username
    }
    const topic = eventData.topic;
    const matchList = this._getMatchList(ename);
    // console.log('socketio事件：', ename, ' 匹配事件列表 ', matchList)

    matchList.forEach((_matchName) => {
      if (this._callbacks[_matchName]) {
        this._callbacks[_matchName].forEach((cb) =>
            cb.call(this, ename, resParam, topic)
        );
      }
    });
  }
  emit(ename: string, args: any) {
    console.log("emit", ename, args);
    this._worker.postMessage({
      ename: "emit",
      params: {
        ename,
        args,
      },
    });
  }
  request(reqData: any) {
    return new Promise((resolve, reject) => {
      const trans_id = createTransId()
      const {ename, ...args} = reqData

      const cb:TResCallback = (data: any) => {
        // console.log('socket request respones data', data)
        resolve(data)
      }

      this._resCallbacks[trans_id] = cb
      const mergeArgs = {trans_id, ...args}
      console.log('socket request args', mergeArgs)
      this.emit(ename, mergeArgs)
    })
  }

  close() {
    this.reConnect = false
    this._worker.postMessage({
      ename: "close",
      params: {
      },
    })
  }
}