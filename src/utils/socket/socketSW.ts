import Socket, {createTransId} from './socket'
import type { IConnectParams, ISubEventData, TResCallback } from './socket'
import {withVersion} from "@/app/common/utils"
import { getGlobalConfig } from "@/config";
// import SharedWorker from "./SocketSharedWorker?sharedworker";

// Shared Worker 代码
// const workerCode = `
// import Socket, { decrypt } from './socket';
// import type { ISubEventData } from './socket';

// const ports = new Set<MessagePort>();

// class SocketSharedWorker extends Socket {
//   _handleSubscribe(eventData: ISubEventData) {
//     const _aseKey = this._connectParams.key;
//     if (_aseKey) {
//         eventData = decrypt(eventData, _aseKey);
//     }
//     console.log('socketSW eventData', eventData);
//     // 非正常格式不进行处理
//     if (!eventData.trans_id && !eventData.event) {
//         console.error("subscribe/broadcast 异常信息", eventData);
//     }
//     // 如果未处理过消息，防重复消息处理
//     if (!this._isProcessed(eventData.trans_id)) {
//         // 将事件加入已处理记录
//         this._addProcessed(eventData.trans_id);
//         // port发出消息
//         ports.forEach((port) => {
//             port.postMessage({ eventData });
//         });
//     }
// }

// const socket = new SocketSharedWorker();

// // eslint-disable-next-line
// (self as unknown as SharedWorkerGlobalScope).onconnect = (event) => {
//     const port = event.ports[0];
//     ports.add(port);
//     console.log("连接成功 ports", ports, event.ports);
//     port.onmessage = (message) => {
//       const { ename, params } = message.data;
//       if ((socket as any)[ename]) (socket as any)[ename](params);
//     };
// };
// `;


// 使用共享worker建立socketio长联
export default class SocketBySharedWorker extends Socket {
  private _worker: SharedWorker;
  constructor() {
    super()
    this._worker = new SharedWorker(withVersion(`${import.meta.env.VITE_BASE_URL}/assets/ShareWorker.umd.js`));
    this._worker.port.start();
    this._worker.port.onmessage = (ev) => {
      const regTransform = /^file.edit.single\./
         
      console.log("ev.data", ev.data)
      if (ev.data === 'refreshGlobalConfig') {
        getGlobalConfig()
      } else if (ev.data.setSocketAttr) { // 修改socket属性
        const {key, value} = ev.data.setSocketAttr as {key: string, value: any};
        (this as any)[key] = value
      } else if (ev.data?.type === "singleEdit") {
        console.log(this._singleResCallbacks, this._singleResCallbacks)
       
        const cb = this._singleResCallbacks[ev.data.transId]
          delete this._singleResCallbacks[ev.data.transId]
          cb.call(this, ev.data?.eventData)
      } else if (ev.data?.conn_id) {
        console.log("存储conn_id", ev.data?.conn_id)
        localStorage.setItem("conn_id", ev.data?.conn_id);

      } else if (regTransform.test(ev.data?.eventData?.event_type)) { 
        ev.data.eventData.event = ev.data?.eventData?.event_type
        this._handleSubscribe(ev.data.eventData);
      } else if (ev.data.eventData?.event) {
        this._handleSubscribe(ev.data.eventData);
      } else if (ev.data.resEventData) { // 请求响应的消息
        console.log("请求响应的消息", ev.data.resEventData)
        const eventData = ev.data.resEventData
        if (this._resCallbacks[eventData.trans_id]) {
          const cb = this._resCallbacks[eventData.trans_id]
          delete this._resCallbacks[eventData.trans_id]
          cb.call(this, eventData)
        }
      } else {
        localStorage.setItem("connectionInfo", JSON.stringify(ev.data.eventData));
      }
    };
    console.log("SocketBySharedWorker");
  }
  connect(params: IConnectParams) {
    console.log('SocketBySharedWorker connect1')
    this._connectParams = params
    this.setInsideReConnect(true)
    this._worker.port.postMessage({
      ename: "connect",
      params,
    });
  }
  getConnectId() {
    this._worker.port.postMessage({
      ename: "getConnectId",
      params: {},
    });
  }
  // socket订阅消息，进行分发
  _handleSubscribe(eventData: ISubEventData) {
    if (!eventData) return;

    if (!this._isProcessed(eventData.trans_id)) {
      // 将事件加入已处理记录
      this._addProcessed(eventData.trans_id)

      console.log("subscribe/broadcast receive msg", eventData);
      // 非正常格式不进行处理
      if (!eventData.trans_id && !eventData.event) {
        return console.error("subscribe/broadcast 异常信息", eventData);
      }
      const ename = eventData.event;
      const resParam = eventData?.message || eventData.param;
      if (eventData.sender && eventData.sender.username) {
        resParam.uid = eventData.sender.uid
        resParam.username = eventData.sender.username
      }
      const topic = eventData.topic;
      const matchList = this._getMatchList(ename);
      // console.log('socketio事件：', ename, ' 匹配事件列表 ', matchList)

      matchList.forEach((_matchName) => {
        if (this._callbacks[_matchName]) {
          this._callbacks[_matchName].forEach((cb) =>
              cb.call(this, ename, resParam, topic)
          );
        }
      })
    } else {
      console.log('重复事件', eventData)
    }
    
    
    
  }
  emit(ename: string, args: any) {
    this._worker.port.postMessage({
      ename: "emit",
      params: {
        ename,
        args,
      },
    });
  }

  // 请求响应模式
  request(reqData: any) {
    return new Promise((resolve, reject) => {
      const trans_id = createTransId()
      const {ename, ...args} = reqData

      const cb:TResCallback = (data: any) => {
        console.log('socket request respones data', data)
        resolve(data)
      }

      this._resCallbacks[trans_id] = cb
      const mergeArgs = {trans_id, ...args}
      console.log('socket request args', this, mergeArgs)
      this.emit(ename, mergeArgs)
    })
  }

   // 单人编辑请求
  singleRequest(reqData: any) {
    return new Promise((resolve, reject) => {
      const trans_id = createTransId()
      const { ename,  message = {}, ...args } = reqData
      message.trans_id = trans_id

      const strMessage = JSON.stringify(message)
      console.log('socket request data', trans_id, strMessage)

      const cb:TResCallback = (data: any) => {
        console.log('socket request respones data', data)
        resolve(data)
      }

      this._singleResCallbacks[trans_id] = cb
      const mergeArgs = { ename, message: strMessage, ...args }
      console.log('singleRequest socket request args', mergeArgs, this._singleResCallbacks)
      this.request(mergeArgs).then((res: any) => { 
        console.log('singleRequest socket request res', res)
        if (res.code != 0) {
          reject(res)
        }
      }).catch(err => { 
        reject(err)
      })
      // this.emit(ename, mergeArgs)

    })
  }

  close() {
    console.log('this._callbacks', this._callbacks)
    this._worker.port.postMessage({
      ename: "close",
      params: {
      },
    })
  }

  setInsideReConnect(v: boolean): void {
    this._worker.port.postMessage({
      ename: "setInsideReConnect",
      params: v,
    })
  }
}