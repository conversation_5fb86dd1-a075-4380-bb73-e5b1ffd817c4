import SocketByWebWorker from "./socketWW";
import SocketBySharedWorker from "./socketSW";

export { SocketByWebWorker, SocketBySharedWorker };

// __SOCKETIO_MODE__模式   webWorker  或  sharedWorker
declare const __SOCKETIO_MODE__: string;
// 全局单例，优先使用共享worker建立socketio长联，不兼容则主线程长连
const socket = window.SharedWorker && __SOCKETIO_MODE__ === 'sharedWorker'
    ? new SocketBySharedWorker()
    : new SocketByWebWorker();
export default socket;