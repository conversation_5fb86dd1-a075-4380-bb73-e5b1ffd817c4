import Socket, { decrypt, CENOTICE_PREFIX } from './socket'
import type { ISubEventData, ICENoticeData } from './socket'

class SocketWebWorker extends Socket {
  _handleSubscribe(eventData: ISubEventData) {
    console.log('socketWW eventData 1', eventData)
    const _aseKey = this._connectParams.key
    if (_aseKey) {
        eventData = decrypt(eventData, _aseKey)
    }
    console.log('socketWW eventData', eventData)
    // 非正常格式不进行处理
    if (!eventData.trans_id && !eventData.event) {
        console.error("subscribe/broadcast 异常信息", eventData);
    }
    // 如果未处理过消息，防重复消息处理
    if (!this._isProcessed(eventData.trans_id)) {
        console.log("_handleSubscribe _addProcessed", eventData.trans_id)
        // 将事件加入已处理记录
        this._addProcessed(eventData.trans_id);
        // port发出消息
        self.postMessage({ eventData });
    }
  }
  _ceNotice(ename: string, eventData: ICENoticeData) {
    const {
      data,
      file_id,
      msg_type,
      room_id,
      trans_id,
    } = eventData
    const matchEName = CENOTICE_PREFIX+msg_type
    let resParam:any
    if (msg_type.includes('ce.rooms.notice.converted')) {
      console.log('接收到增量数据', data)
      let editor, base64Data, commitId, command, position, lastCommitId
      switch (msg_type) {
        case 'ce.rooms.notice.converted.map': {
          base64Data = data.map
          break
        }
        case 'ce.rooms.notice.converted.dwi': {
          base64Data = data.dwi
          break
        }
      }
      editor = data.editor
      commitId = data.commit_id
      command = data.command
      position = data.position
      lastCommitId = data.last_commit_id
      // 校验数据
      if (base64Data) {
        // 由于base64需要转换
        const buffer = Uint8Array.from(atob(base64Data as string), c => c.charCodeAt(0))
        resParam = {
          data: buffer,
          commitId,
          editor,
          command,
          position,
          lastCommitId
        }
      }
    } else {
      resParam = data
    }
    self.postMessage({ eventData: {
      event: matchEName,
      param: resParam,
      topic: ''
    } });
  }
  _resSubscribe(eventData: ISubEventData) {
    console.log("_resSubscribe", eventData)
    if (!eventData.trans_id && !eventData.event) {
      console.error("subscribe/broadcast 异常信息", eventData);
    }
    
    console.log("_resSubscribe 1", this._processedList, this._isProcessed(eventData.trans_id))

    if (!this._isProcessed(eventData.trans_id)) {
      console.log("_resSubscribe _addProcessed", eventData.trans_id)

      // 将事件加入已处理记录
      this._addProcessed(eventData.trans_id);

      self.postMessage({ resEventData: eventData });
    }
  }
  handleConnRes(payload: any) {
    super.handleConnRes(payload)
    self.postMessage({ connectMsg: payload })
  }
  // 断联回调
  handleDisConnect(payload: any) {
    super.handleDisConnect(payload)
    self.postMessage({ disConnect: payload })
  }
  setReConnect(v: boolean) {
    console.log('发出修改属性消息')
    self.postMessage({ setSocketAttr: {
      key: 'reConnect',
      value: v
    } })
  }
}

const socket = new SocketWebWorker();

// eslint-disable-next-line
(self as WorkerGlobalScope).self.onmessage = (message) => {
  const { ename, params } = message.data;
  if ((socket as any)[ename]) (socket as any)[ename](params);
};
