import Socket, { decrypt } from './socket'
import type { ISubEventData } from './socket'

const ports = new Set<MessagePort>();

class SocketSharedWorker extends Socket {
  _handleSubscribe(eventData: ISubEventData) {
    const _aseKey = this._connectParams.key
    if (_aseKey) {
        eventData = decrypt(eventData, _aseKey)
    }
    console.log('socketSW eventData', eventData, this)
    // 非正常格式不进行处理
    if (!eventData.trans_id && !eventData.event) {
        console.error("subscribe/broadcast 异常信息", eventData);
    }
    // 如果未处理过消息，防重复消息处理
    // if (!this._isProcessed(eventData.trans_id)) {
        // 将事件加入已处理记录
        // this._addProcessed(eventData.trans_id);
        // port发出消息
        ports.forEach((port) => {
            port.postMessage({ eventData });
        });
    // }
  }

  _resSubscribe(eventData: ISubEventData) {
    if (!eventData.trans_id && !eventData.event) {
      console.error("subscribe/broadcast 异常信息", eventData);
    }

    if (eventData?.message?.trans_id) {
      console.log(this._processedList, eventData.message.trans_id)
      if (!this._isProcessed(eventData.message.trans_id)) {
        this._addProcessed(eventData?.message?.trans_id);
        console.log("收到编辑页返回后的回调", this._resCallbacks)
        this._handleResSubscribe(eventData?.message?.trans_id, eventData)
        // if (this?._resCallbacks[eventData?.message?.trans_id]) {
        //   const cb = this._resCallbacks[eventData?.message?.trans_id]
        //   delete this._resCallbacks[eventData?.message?.trans_id]
        //   cb.call(this, eventData)
        // }
      }
    }
    
    if (!this._isProcessed(eventData.trans_id)) {
      // 将事件加入已处理记录
      this._addProcessed(eventData.trans_id);

      ports.forEach((port) => {
        port.postMessage({ resEventData: {...eventData} });
      });

    }
  }

  _handlePostConnectId(conn_id:string) {
    ports.forEach((port) => {
      console.log("发起存储conn_id", conn_id)
      port.postMessage({ conn_id });
    });
  }

  _handleResSubscribe(transId: string, eventData: any) {
    console.log("_handleResSubscribe", eventData)
    ports.forEach((port) => {
      port.postMessage({ transId, type: "singleEdit", eventData });
    });
  }

  setReConnect(v: boolean) {
    console.log('发出修改属性消息')
    ports.forEach((port) => {
      port.postMessage({ setSocketAttr: {
        key: 'reConnect',
        value: v
      } })
    })
  }

  // connStatusChange(flag: boolean) {
  //   if (!flag) {
  //     ports.forEach((port) => {
  //       port.postMessage({ type: "singleEdit", connectErr: true });
  //     });
  //   }
    
  // }
}

const socket = new SocketSharedWorker();

// eslint-disable-next-line
(self as unknown as SharedWorkerGlobalScope).onconnect = (event) => {
    const port = event.ports[0];
    ports.add(port);
    console.log("连接成功 ports", ports, event.ports);
    port.onmessage = (message) => {
      const { ename, params } = message.data;
      if ((socket as any)[ename]) (socket as any)[ename](params);
    };
};

