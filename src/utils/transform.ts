/** 转换任务相关方法 */

import { EFILEMATEKEY } from "@/model/file";
import { ETASKTYPE } from "@/model/task";

const pdfTaskConfigParams = [
    "paperWidth", // 纸张宽度
    "paperHeight", // 纸张高度
    "colorType", // 1 黑白 -1 灰度 32 彩色
    "scale", // 图纸缩放比例
    "scopeType", // 范围类型 0-当前视图 1-全图 2-自定义窗口
    "pt1X", // 第一点
    "pt1Y",
    "pt2X", // 第二点
    "pt2Y",
];

const upperString = (val: string) => {
    return val.slice(0, 1).toUpperCase() + val.slice(1);
};

export const getTransformPDFTaskConfig = (params: Recordable) => {
    const config: Recordable = {};
    if (params) {
        pdfTaskConfigParams.forEach((key) => {
            if (key === "scopeType") {
                config[key] = params.iScapeType;
            } else if (key === "colorType") {
                config[key] = params.iHaveColor;
            } else {
                const _key = "d" + upperString(key);
                config[key] = params[_key];
            }
        });
    }
    return config;
};

export const getTaskType = (metaKey: string) => {
    switch (metaKey) {
        case EFILEMATEKEY.DWG_META:
            return ETASKTYPE.CONVERT_OCF;
        case EFILEMATEKEY.SAVE_DWG_META:
            return ETASKTYPE.SAVE_DWG;
        case EFILEMATEKEY.SAVE_PDF_META:
            return ETASKTYPE.SAVE_PDF;
        default:
            return ETASKTYPE.CONVERT_OCF;
    }
};
