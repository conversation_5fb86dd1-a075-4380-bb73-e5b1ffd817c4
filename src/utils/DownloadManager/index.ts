interface ApiEndpoints {
  download: string
}

const defaultApi = {
  download: "/api/v3/_st/_file/_download_url/export"
};

async function getConfig(host?: string) {
  if ((window as any).$globalConfig) {
    // 如果已经存在，直接返回
    return (window as any).$globalConfig;
  } else {
    try {
      const result = await fetch(`${host || ''}/globalConfig.json`);
      const data = await result.json();
      console.log(data);
      (window as any).$globalConfig = data;
      return data
    } catch (error) {
      console.log(error);
      return '';
    }
  }
}

export default class DownloadManager {
  private fileId: string | number | null
  private fileName: string
  private api: ApiEndpoints
  private host: string
  private token: string
  private storageId?: string | number | null

  constructor(params: { fileId?: string | number | null, token: string, fileName?: string, storageId?: string | number | null, host?: string }) {
    this.fileId = params?.fileId || ""
    this.fileName = params?.fileName || ""
    this.api = defaultApi
    this.host = params?.host || ""
    this.token = params.token
    this.storageId = params?.storageId || null
    this.getHost()
  }

  async getHost() {
    if (!this.host) {
      const config = await getConfig()
      console.log('config', config)
      this.host = config?.api_endpoint || ''
    }
  }

  async start() {
    try {
      const { downloadUrl, storageType } = await this.downloadFile()
      console.log({ downloadUrl, storageType })
      if (storageType === 1) {
        await this.downloadStorageByUrl({ url: `${downloadUrl}?${this.fileId ? 'fileId=' + this.fileId : ''}&${this.storageId ? 'storageId=' + this.storageId : ''}`, storageType })
      } else {
        await this.downloadByUrl({ url: downloadUrl, storageType })
      }
    } catch (error) {
      console.log(error)
    }
  }

  async getPreviewData() {
    const isPreview = true;
    try {
      const { downloadUrl, storageType } = await this.downloadFile()
      console.log({ downloadUrl, storageType })
      if (storageType === 1) {
        const blob = await this.downloadStorageByUrl({ url: `${downloadUrl}?${this.fileId ? 'fileId=' + this.fileId : ''}&${this.storageId ? 'storageId=' + this.storageId : ''}`, storageType, isPreview })
        return {
          data: blob,
          fileId: this.fileId,
          storageId: this.storageId
        }
      } else {
        const blob = await this.downloadByUrl({ url: downloadUrl, storageType, isPreview })
        return {
          data: blob,
          fileId: this.fileId,
          storageId: this.storageId
        }
      }
    } catch (error) {
      console.log(error)
      return {
        data: null,
        fileId: this.fileId,
        storageId: this.storageId
      }
    }
  }

  private async downloadFile(): Promise<any> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open("GET", `${this.host}${this.api.download}?${this.fileId ? 'fileId=' + this.fileId : ''}&${this.storageId ? 'storageId=' + this.storageId : ''}`, true);

      xhr.setRequestHeader("Authorization", `${this.token}`);
      xhr.setRequestHeader("Content-Type", "application/json");

      xhr.onload = () => {
        console.log(111, xhr)
        if (xhr.readyState === 4 && xhr.status === 200) {
          const data = JSON.parse(xhr.responseText)
          console.log(data)
          if (data.code === 0) {
            resolve(data.data);
          } else {
            reject(data);
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => {
        reject(new Error("Upload failed due to a network error"));
      };

      xhr.send();
    });
  }

  private downloadByUrl = (data: any) => {
    fetch(data.url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Server replied HTTP code: ${response.status}`);
        }
        return response.blob(); // 将响应转换为 blob
      })
      .then(blob => {
        if (data.isPreview) return blob
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = this.fileName; // 设置下载文件的名字
        document.body.appendChild(link); // 此步骤确保链接存在于文档中
        link.click(); // 模拟点击下载链接
        link.remove(); // 完成后移除链接
        console.log("Download completed!");
      })
      .catch(error => {
        console.error("Error during download:", error);
      });
  };

  private downloadStorageByUrl = (data: any) => {
    return new Promise((resolve, reject) => {

      fetch(data.url, {
        headers: {
          "Authorization": this.token
        }
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`Server replied HTTP code: ${response.status}`);
          }
          return response.blob(); // 将响应转换为 blob
        })
        .then(blob => {
          if (data.isPreview) {
            resolve(blob)
            return
          }

          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = this.fileName; // 设置下载文件的名字
          document.body.appendChild(link); // 此步骤确保链接存在于文档中
          link.click(); // 模拟点击下载链接
          link.remove(); // 完成后移除链接
          console.log("Download completed!");
          resolve(null)
        })
        .catch(error => {
          console.error("Error during download:", error);
          reject(error)
        });
    })
  };
}