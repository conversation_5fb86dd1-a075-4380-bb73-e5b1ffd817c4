import { ElMessage, type MessageParamsWithType } from 'element-plus'
// 私有属性，只在当前文件可用
const showMessage = Symbol('showMessage')

type msgType = 'success' | 'warning' | 'info' | 'error'

export default class gstarMessage {
    success (options: MessageParamsWithType, single = true) {
        this[showMessage]('success', options, single)
    }
    warning(options: MessageParamsWithType, single = true) {
        this[showMessage]('warning', options, single)
    }
    info(options: MessageParamsWithType, single = true) {
        this[showMessage]('info', options, single)
    }
    error(options: MessageParamsWithType, single = true) {
        this[showMessage]('error', options, single)
    }
    [showMessage] (type: msgType, options: MessageParamsWithType, single: boolean) {
        if (single) {
            // 判断当前页是否有el-message标签，如果没有则执行弹窗操作
            if (document.getElementsByClassName('el-message').length === 0) {
              ElMessage[type](options)
            }
        } else {
          ElMessage[type](options)
        }
    }
}