import Fingerprint2 from 'fingerprintjs2';
import socket from "@/utils/socket"; // 包含用户aes key信息
import EditIngDialog from './webComponents/EditIngDialog'
import '@vaadin/dialog'
import t from '../../app/common/i18n'

interface CloudCadUtilsParams {
  serverHost: string
  token: string
  shareWorker?: any
  uid: string
  uname: string
  deviceId?: string
  editName?: string | null
  globalConfig?: any
  socket?: any
  editIngDialog?: any
  callback?: any
  conn_type?: number // 连接类型 0：普通链接 1：单人链接 2：多人链接
}

interface EditParams {
  breadcrumbList: string  // "[{\"title\":\"文件库\",\"href\":\"files/file\"},{\"title\":\"钢琴(5).dwg\"}]"
  did: string
  uname: string
  fileName: string
  fileId: string
  token: string
  enableHeader: boolean
}

interface WaitingResponseFilesId {
  [key: number]: boolean;
}

class CloudCadUtils {
  token: string;
  shareWorker: any;
  serverHost: string
  uid: string
  uname: string
  deviceId: string
  editName: string | null
  editSocketId: number | null
  globalConfig: any
  socket: any
  editIngDialog: any
  callback: any
  conn_type?: number // 连接类型 0：普通链接 1：单人链接 2：多人链接
  fileItem: any
  requestIntervalNum = 0
  timeNum = 60
  waitingResponseFilesId: WaitingResponseFilesId

  constructor(params: CloudCadUtilsParams) {
    console.log("params", params)
    this.serverHost = params?.serverHost || ''
    this.token = params?.token || ''
    this.shareWorker = params?.shareWorker || {}
    this.uid = params?.uid || ''
    this.uname = params?.uname || ''
    this.conn_type = params?.conn_type || 0
    this.deviceId = ""
    this.editName = null
    this.globalConfig = {}
    this.socket = params?.socket || socket
    this.editSocketId = null
    this.fileItem = {}
    this.requestIntervalNum = 0
    this.timeNum = 60
    this.waitingResponseFilesId = {}

    this.initGlobalConfig()
    this.callback = params?.callback || function () { console.log() };
    this.editIngDialog = new EditIngDialog()
    this.initSocketEvents()

  }

  async initGlobalConfig() {
    try {
      const config = await fetch(`${this.serverHost}/globalConfig.json`).then((res) => res.json())
      this.globalConfig = config
      
      console.log('globalConfig', config)
      this.callback("initSuccess")
    } catch (error) {
      this.callback(error)
      console.log(`init error: ${error}`)
    }
  }

  initSocketEvents() {
    // this.socket.on("transform.single.connect.editor.response", (ename, data) => {
    //   console.log(111111, ename, data)
    // })
  }

  // socket连接
  initSocket() {
    this.socket.connect({
      token: this.token,
      url: this.globalConfig.socketIo_url,
      key: this.globalConfig.key,
      uid: this.uid,
      uname: this.uname,
      did: this.deviceId,
      conn_type: this.conn_type
    })
    this.onUpload()
  }

  // 绑定上传方法
  onUpload() {
    this.socket.on("file.edit.single.snatch.save.progress", (ename: string,data: any) => { 
      console.log("upload progress", ename, data)
      const messageData = JSON.parse(data)

      if (messageData?.deviceId === this.deviceId) {
        if (messageData.progressPercent === "start") {
          this.showWiteSavingNoActionDialog()
        } else if (messageData.progressPercent >= 100) { 
          // this.showWiteFirstDialog()
        } else {
          this.editIngDialog.updateProcess(messageData.progressPercent)
        }
      }
    })
  }

  onuUnlockFile() { 
    this.socket.on("file.edit.unlock", (ename: string,data: any) => { 
      console.log("file.edit.unlock", ename, data)
      this.editIngDialog?.closeDialog()
      this.requestEditDrawing(this.fileItem)
    })
  }

  successHandler() { 
    this.editIngDialog.closeDialog()
    this.requestEditDrawing(this.fileItem)
  }

  previewCloudCAD() {
    console.log('previewCloudCAD')
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`当前可以进入编辑页，请点击 确定 按钮继续`))
    this.editIngDialog.setOkText(t("确定"))
    this.editIngDialog.setCancelText(t("取消"))
    this.editIngDialog.setCancelCallback(() => this.editIngDialog.closeDialog())
    this.editIngDialog.setOkCallback(() => {
      this.editIngDialog.closeDialog()
      this.openUrlFn(`${import.meta.env.VITE_BASE_URL}/cloudCAD.html`, {...this.fileItem, enableHeader: true})
    })
    this.editIngDialog.openDialog()
  }

  // 请求编辑
  async requestEditDrawing(editParams: any, isMandatory = false) {
    this.fileItem = editParams
    console.log("params?.socket", this.socket)
    // this.socket.getConnectId()
    this.deviceId = (await this.createDeviceId()) as string;
    this.fileItem.did = this.deviceId;
    const params = {
      devId: this.deviceId,
      fileId: editParams.fileId
    }
    
    this.initSocket()
    
    const headers = {
      "Authorization": this.token
    }

    // 发起开图请求
    fetch(`${this.globalConfig.api_endpoint}/api/v2/_edit/_state?fileId=${editParams.fileId}`, {
      method: "get",
      headers
    }).then(async (response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    }).then(async (result) => {
      console.log("resData", result)
      if (result.code !== 0) {
        throw new Error(`/_edit/_state error: ${JSON.stringify(result)}`)
      }
      if (!result.data.locked) { // 没锁定
        this.previewCloudCAD()
        return
      } else {
        const { editor, editSessionId } = result.data
        // 从服务端拿到编辑者信息
        this.editName = editor
        this.editSocketId = editSessionId
  
        
        if (this.editSocketId) {
          if (isMandatory) { // 如果是强制抢占，显示强制抢占框
            this.showMandatoryDialog()
          } else { // 正常单人询问抢占流程
            this.showWiteFirstDialog()
          }
        } else {
          this.snatchRequest(editParams)
        }

      }

    })
    .catch((error) => {
      console.error("There was a problem with the fetch operation:", error);
      this.showNetErrDialog()
    })
  }

  // 强制枷锁
  async snatchRequest(editParams: any) {
    this.fileItem = editParams
    console.log("params?.socket", this.socket)
    this.socket.getConnectId()
    this.deviceId = (await this.createDeviceId()) as string;
    this.fileItem.did = this.deviceId;
    const params = {
      devId: this.deviceId,
      fileId: editParams.fileId
    }
    
    this.initSocket()
    
    const headers = {
      "Content-Type": "application/json",
      "Authorization": this.token
    }

    // 发起开图请求
    fetch(`${this.globalConfig.api_endpoint}/api/v2/_edit/_force_unlock`, {
      method: "POST",
      headers,
      body: JSON.stringify(params)
    }).then(async (response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    }).then(async (result) => {
        console.log("resData", result)
      
      if (result.code === 0) {
        this.previewCloudCAD()
        return
      } 
    })
    .catch((error) => {
      console.error("There was a problem with the fetch operation:", error);
      this.showNetErrDialog()
    })
  }

  cancelEdit() { 
    if(this.waitingResponseFilesId[this.fileItem.fileId]) this.waitingResponseFilesId[this.fileItem.fileId] = false;

    // this.request("ce.transform.single.cancel.edit")
    this.editIngDialog.closeDialog()
  }

  // 显示正在编辑中 询问是否要中断命令
  showEditingAskDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`该图有正在执行的命令，是否要强制中断操作并保存？请注意，中断后会丢弃部分修改`))
    this.editIngDialog.setCancelText(t("取消"))
    this.editIngDialog.setOkText(t("强制中断"))
    this.editIngDialog.setOkCallback(this.forceRequestEndEdit.bind(this))
    this.editIngDialog.setCancelCallback(this.cancelEdit.bind(this))
    this.editIngDialog.openDialog()
  }

  // 提示正在有人抢占编辑
  showFirstDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`该图正在被抢占编辑权限，是否强制抢占编辑权限？注意强制抢占权限存在编辑者保存内容丢失的风险！`))
    this.editIngDialog.setCancelText(t("取消"))
    this.editIngDialog.setCancelCallback(this.cancelEdit.bind(this))
    this.editIngDialog.setOkText(t("强制抢占"))
    this.editIngDialog.setOkCallback(this.forceRequestEndEdit.bind(this))
    this.editIngDialog.openDialog() 
  }

  // 显示初次开图有占用弹窗
  showWiteFirstDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`{editName} 正在编辑，是否要抢占编辑权限？`, { editName: this.editName }))
    this.editIngDialog.setOkText(t("结束编辑"))
    this.editIngDialog.setCancelText(t("取消"))
    this.editIngDialog.setCancelCallback(this.cancelEdit.bind(this))
    this.editIngDialog.setOkCallback(this.requestEndEdit.bind(this))
    this.editIngDialog.openDialog()
  }

  requestInterval(cb: () => void) {
    if (this.requestIntervalNum) {
      clearInterval(this.requestIntervalNum)
    }

    this.requestIntervalNum = setInterval(() => {
      this.timeNum = this.timeNum - 1
      if (this.timeNum <= 0) {
        console.log("requestIntervalNum")
        cb()
        clearInterval(this.requestIntervalNum)
        this.timeNum = 60
      }
    }, 1000)
  }

  // 结束编辑请求
  requestEndEdit() {
    const showWiteResponseDialog = this.showWiteResponseDialog.bind(this)
    showWiteResponseDialog()

    this.waitingResponseFilesId[this.fileItem.fileId] = true

    this.request("file.edit.single.snatch.ask").then((result: any) => {
      console.log("file.edit.single.snatch.ask -", result)
      if( Object.prototype.hasOwnProperty.call(this.waitingResponseFilesId, this.fileItem.fileId) && this.waitingResponseFilesId[this.fileItem.fileId] === false) return
      if (result?.message?.status === "ok") {
        this.successHandler()
      } else if (result?.message?.status === "editing") {
        this.showNoWayDialog()
      } else if (result?.message?.status === "no") {
        this.showBRejectDialog()
      } else if (result?.message?.status === "busy") {
        this.showFirstDialog()
      } else {
        this.showNetErrDialog()
      }

    }).catch((err: any) => {
      console.log("err", err)
      this.showNetErrDialog()
    });
  }

  // 网络错误弹窗
  showNetErrDialog() {
    this.editIngDialog.closeDialog()
    // this.editIngDialog.setTipText(`您的网络错误或者对方网络异常，请检查网络后重试`)
    // this.editIngDialog.setCancelText("取消")
    // this.editIngDialog.setCancelCallback(this.cancelEdit.bind(this))
    // this.editIngDialog.openDialog()

    this.snatchRequest(this.fileItem)
  }

  // 超时进入强制编辑弹窗
  showOutTimeDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`对方长时间未响应，是否强制抢占编辑权限？`))
    this.editIngDialog.setOkText(t("强制抢占"))
    this.editIngDialog.setCancelText(t("取消"))
    this.editIngDialog.setCancelCallback(this.cancelEdit.bind(this))
    this.editIngDialog.setOkCallback(this.forceRequestEndEdit.bind(this))
    this.editIngDialog.openDialog()
  }

  // 有正在执行的命令 无法抢占弹窗
  showNoWayDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`{editName} 用户命令正在执行中，无法被抢占`, { editName: this.editName }))
    this.editIngDialog.setOkText(t("强制抢占"))
    this.editIngDialog.setCancelText(t("取消"))
    this.editIngDialog.setCancelCallback(this.cancelEdit.bind(this))
    this.editIngDialog.setOkCallback(this.forceRequestEndEdit.bind(this))
    this.editIngDialog.openDialog()
  }

  // 强制抢占编辑弹窗
  showMandatoryDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`当前图纸正在被其他用户编辑中，如需进入编辑，请点击继续编辑。此操作将导致其他用户退出编辑状态。`))
    this.editIngDialog.setOkText(t("继续编辑"))
    this.editIngDialog.setCancelText(t("取消"))
    this.editIngDialog.setCancelCallback(this.cancelEdit.bind(this))
    this.editIngDialog.setOkCallback(this.forceRequestEndEdit.bind(this))
    this.editIngDialog.openDialog()
  }

  // 被拒绝强制编辑弹窗
  showBRejectDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`{editName} 拒绝了您的抢占请求，是否强制抢占编辑权限？注意强制抢占权限存在编辑者保存内容丢失的风险！`, { editName: this.editName }))
    this.editIngDialog.setOkText(t("强制抢占"))
    this.editIngDialog.setCancelText(t("取消"))
    this.editIngDialog.setCancelCallback(this.cancelEdit.bind(this))
    this.editIngDialog.setOkCallback(this.forceRequestEndEdit.bind(this))
    this.editIngDialog.openDialog()
  }

  // 显示等待编辑用户是否同意被抢占弹窗
  showWiteResponseDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`正在等待 {editName} 响应，剩余 `, { editName: this.editName }))
    this.editIngDialog.setCount(30)
    this.editIngDialog.setCancelText(t("取消"))
    this.editIngDialog.setCancelCallback(this.cancelEdit.bind(this))

    const timeOutParam = {
      setOkText: t("强制编辑"),
      okCallback: this.forceRequestEndEdit.bind(this),
      cancelText: t("继续等待"),
      cancelCallback: this.showWiteResponseDialog.bind(this),
      tipText: t(`对方长时间未响应，是否强制进入编辑状态或继续等待？`)
    }
    this.editIngDialog.setTimeoutCallback(this.showTimeoutDialog.bind(this, timeOutParam))
    this.editIngDialog.openDialog()
  }

  // 强制结束编辑请求
  forceRequestEndEdit() {
    this.request("file.edit.single.force.snatch")
    this.editSocketId = null
    this.successHandler()
    // .then((result:any) => {
    //   console.log("result - file.edit.single.force.snatch", result)
    //   if (result?.message?.status === "ok") {
    //     this.successHandler()
    //   } else if (result?.message?.status === "busy") {
    //     this.showFirstDialog()
    //   } else {
    //     this.showNetErrDialog()
    //   }
    // }).catch((err: any) => {
    //   console.log("err", err)
    //   this.showNetErrDialog()
    // });
  }

  // 发送请求等待响应
  showWiteDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`正在等待 {editName} 响应，剩余 `, { editName: this.editName }))
    this.editIngDialog.setOkText("")
    this.editIngDialog.setCount(60)
    this.editIngDialog.openDialog()
  }

  // 显示等待保存提示弹窗 注：发起询问的强制编辑请求 等对方响应
  showWiteSaveDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`{editName} 正在对他的修改进行保存，请等待 `, { editName: this.editName }))
    this.editIngDialog.setCount(30)
    this.editIngDialog.setOkCallback(this.showWiteResponseDialog.bind(this))
    this.editIngDialog.openDialog()
  }

  // 显示等待保存提示弹窗 不可操作
  showWiteSavingNoActionDialog() {
    this.editIngDialog.closeDialog()
    this.editIngDialog.setTipText(t(`{editName} 正在对他的修改进行保存，请等待 `, { editName: this.editName }))
    this.editIngDialog.setOkText("")
    this.editIngDialog.openDialog()
  }

  // 超时弹窗
  showTimeoutDialog(params: any) {
    const {setOkText, okCallback = () => {}, cancelText, cancelCallback = () => {}, tipText, count = 0} = params
    console.log('超时弹窗', params)
    this.editIngDialog.closeDialog()
    setTimeout(() => {
      this.editIngDialog.setTipText(tipText)
      this.editIngDialog.setOkText(setOkText)
      this.editIngDialog.setOkCallback(okCallback)
      this.editIngDialog.setCancelText(cancelText)
      this.editIngDialog.setCancelCallback(cancelCallback)
      this.editIngDialog.setCount(count)
      this.editIngDialog.openDialog()
    }, 200)
  }

  // 抢占中-等待弹框
  showSnatchingDialog() {
    console.log('抢占中-弹窗')
    this.editIngDialog.closeDialog()
    setTimeout(() => {
      this.editIngDialog.setTipText(t('进入编辑中，请稍等......'))
      this.editIngDialog.setOkText('')
      this.editIngDialog.setCancelText('')
      this.editIngDialog.openDialog()
    }, 200)
  }

  // 创建浏览器指纹
  createDeviceId () {
    return new Promise((resolve) => {
      Fingerprint2.get((components:any) => { // 参数只有回调函数时，默认浏览器指纹依据所有配置信息进行生成
        const values = components.map((component:any) => component.value); // 配置的值的数组
        const randomStr = (window.crypto && window.crypto.randomUUID) ? window.crypto.randomUUID() : Math.random() + ''
        values.push(randomStr)
        const res = Fingerprint2.x64hash128(values.join(''), 31); // 生成浏览器指纹
        resolve(res)
      })
    })
  }

  request(event_type: string) {
    return new Promise((resolve, reject) => {
      const conn_id = localStorage.getItem("conn_id") || ''
          
      const params = {
        ename: "send.targeted.message",
        receiver_conn_id: this.editSocketId,
        sender_conn_id: conn_id,
        event_type,
        message: {
          sender_name: this.uname,
          deviceId: this.deviceId
        }
      }
      console.log("params promiseWrap", params, this.socket)
      this.socket.singleRequest(params).then((res:any) => {
        resolve(res)
      }).catch((err: any) => {
        reject(err)
      })
    })
  }

  /**
 * 
 * @param url 
 * @param queryData 
 */
  openUrlFn = (url: string, queryData: EditParams | null) => {
    // const link = document.createElement('a');
    let dom = document.getElementById("openNewWindow")
    if (!dom) {
      dom = document.createElement('a')
      dom.setAttribute('id', 'openNewWindow')
      dom.setAttribute('target', '_blank')
      dom.setAttribute('style', 'display: none;')
      dom.setAttribute('rel', 'noopener noreferrer')
      document.body.appendChild(dom)
    }
    const search = new URLSearchParams()
    if (queryData) {
      for (const [key, value] of Object.entries(queryData)) {
        if(key === "_blank") continue
        search.set(key, value as unknown as string)
      }

      (dom as any).href = `${url}?${search}`;
    } else {
      // link.href = url;
      (dom as any).href = url;

    }
      dom?.click();
      (dom as any).href = "";
  }

  getBreadcrumbs = (items: any[]) => {
    if (items && items.length > 0) {
      return items.map((item) => {
        const queryParams = {
          folderId: Number(item.folderId),
          direction: 0,
          limit: 10,
          searchName: '',
          searchBeginTime: '',
          searchEndTime: '',
          fileType: 0,
          sortField: 0,
          ascOrDesc: 'desc',
          lastFileId: 0,
          lastFileSize: 0,
          lastFileModifyTime: '',
          lastFileName: '',
          reqPage: 1,
          reqPageIdArr: ''
        }
        return {
          title: item.folderName,
          href: item.folderId === 2 ? 'files/file' : `files/file?params=${encodeQueryParams(queryParams)}`,
        };
      });
    }
  };
}

export const editEntry = (cloudCadUtilsParams:CloudCadUtilsParams, fileId: string, isSingle = true) => {
  const cloudCadUtils = new CloudCadUtils(cloudCadUtilsParams)
  const client_id = localStorage.getItem('client_id') || '';
  const browser_id = localStorage.getItem('gstarDeviceId') || '';

  // 添加弹框，暂只用于点击编辑
  const dialog = document.createElement('vaadin-dialog')
  let dialogText = ''
  const dialogCancelText = t('取消')
  const dialogOkText = ''
  const handleDialogConfirm = () => {}
  const handleDialogCancel = () => {
    dialog.opened = false
  }

  dialog.noCloseOnEsc = true
  dialog.renderer = (root) => {
    if (!root.firstElementChild) {
      const container = document.createElement('div');
      container.classList.add('dialog-container');
      container.innerHTML = `
        <style>
          .dialog-container {
            border-box: box-sizing;
          }
          .dialog-text {
            height: 50px;
          }

          .dialog-btn-container {
            display: flex;
            justify-content: flex-end;
            grid-gap: 10px;
          }
        </style>
        <div class="dialog-text">
          <p class="dialog-text">${dialogText}</p>
        </div>
        <div class="dialog-btn-container">
        ${dialogCancelText ? `<vaadin-button theme="error" id="cancel-btn">${dialogCancelText}</vaadin-button>` : ""}
        ${dialogOkText ? `<vaadin-button theme="primary" id="confirm-btn">${dialogOkText}</vaadin-button>` : ""}
        </div>
        `;
      root.appendChild(container);

      // 添加按钮点击事件
      const confirmBtn = dialogOkText ? container.querySelector('#confirm-btn') : null;
      const cancelBtn = dialogCancelText ? container.querySelector('#cancel-btn') : null;

      confirmBtn?.addEventListener('click', () => {
        handleDialogConfirm()
      });

      cancelBtn?.addEventListener('click', () => {
        handleDialogCancel()
      });
    }
  };
  dialog.noCloseOnOutsideClick = true
  document.body.appendChild(dialog)
  // 判断是否可以进入编辑
  fetch(`${cloudCadUtilsParams.serverHost}/api/v2/browserid/_count?client_id=${client_id}&browser_id=${browser_id}`, {
    method: 'GET',
    headers: {
      Authorization: cloudCadUtilsParams.token
    }
  })
  .then(res => res.json())
  .then(({ data }) => {
    console.log('设备数量信息', data)
    if (!data.allow_entry) {
      return Promise.reject(t('已达到当前时段最大用户链接数'))
    }
  })
  // 判断当前图纸是否有正在编辑
  .then(() => {
    if (isSingle) {
      console.log('进入单人编辑流程', cloudCadUtils)
      cloudCadUtils.requestEditDrawing({
        enableHeader: true,
        fileId: Number(fileId)
      })
      console.log('end 单人编辑流程')
    } else {
      cloudCadUtils.previewCloudCAD()
    }
  })
  .catch(err => {
    dialogText = err
    dialog.opened = true
    console.error(err)
  })
}

export default CloudCadUtils