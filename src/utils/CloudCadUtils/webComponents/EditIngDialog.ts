import '@vaadin/button';
import '@vaadin/dialog';

class EditIngDialog {
  dialogOpened: boolean;
  tipText: string;
  timeoutId: number | null = null;
  ntervalId: number | null = null;
  countBack: number;
  count: number;
  okText: string;
  okCallback: () => void;
  cancelText: string;
  cancelCallback: () => void;
  timeoutCallback: () => void;
  dialog: any;
  process: string;
  
  constructor() {
    this.tipText = '';
    this.timeoutId = null;
    this.ntervalId = null;
    this.countBack = 0;
    this.count = 0;
    this.okText = '';
    this.okCallback = () => { };
    this.cancelText = '';
    this.cancelCallback = () => { };
    this.timeoutCallback = () => { };
    this.process = "";
    this.dialog = document.createElement('vaadin-dialog');
    this.dialog.noCloseOnEsc = true;
  }

  // protected override render() {
  //   // const {dialogFooterRenderer, dialogRenderer } = import('@vaadin/dialog/lit.js');

  //   return html`
  //     <vaadin-dialog
  //     modeless
  //       .opened="${this.dialogOpened}"
  //       @opened-changed="${(event: DialogOpenedChangedEvent) => { console.log('change');  this.closeDialog(event.detail.value) }}"
  //       ${this.countBack ? dialogRenderer(() => html`${this.tipText}, ${this.count}s`, [this.tipText,this.count]) : dialogRenderer(() => html`${this.tipText}`, [this.tipText])}
  //       ${dialogFooterRenderer(
  //         () => {
  //           if (this.okText && this.cancelText) {
  //             return html`
  //               <vaadin-button theme="primary" @click="${this.clickOpenDialog}" style="margin-right: auto;">
  //                 ${this.okText}
  //               </vaadin-button>
  //               <vaadin-button theme="tertiary" @click="${this.closeDialog}">${this.cancelText}</vaadin-button>
  //             `
  //           } else if (this.okText && !this.cancelText) {
  //             return html`
  //               <vaadin-button theme="primary" @click="${this.clickOpenDialog}" style="margin-right: auto;">
  //                 ${this.okText}
  //               </vaadin-button>
  //             `
  //           } else if (!this.okText && this.cancelText) {
  //             return html`
  //               <vaadin-button theme="tertiary" @click="${this.closeDialog}">${this.cancelText}</vaadin-button>
  //             `
  //           } else {
  //             return html``
  //           }
  //         },
  //         []
  //       )}
  //     ></vaadin-dialog>
  //   `;
  // }

  renderDialog() {
    this.dialog.renderer = (root: HTMLElement) => {
      if (!root.firstElementChild) {
        const container = document.createElement('div');
        container.classList.add('dialog-container');
        container.innerHTML = `
          <style>
            .dialog-container {
              border-box: box-sizing;
            }
            .dialog-text {
              height: 50px;
            }

            .dialog-btn-container {
              display: flex;
              justify-content: flex-end;
              grid-gap: 10px;
            }
          </style>
          <div class="dialog-text">
            <p class="dialog-text">${this.tipText} ${this.count ? `<span id="time-count">, ${this.count}s</span>` : ""} </p>
            ${ this.process ? `<p class="dialog-text" id="process-text">${this.process}</p>` : ``}
          </div>
          <div class="dialog-btn-container">
          ${this.cancelText ? `<vaadin-button theme="error" id="cancel-btn">${this.cancelText}</vaadin-button>` : ""}
          ${this.okText ? `<vaadin-button theme="primary" id="confirm-btn">${this.okText}</vaadin-button>` : ""}
          </div>
          `;
        root.appendChild(container);

        // 添加按钮点击事件
        const confirmBtn = this.okText ? container.querySelector('#confirm-btn') as HTMLButtonElement : null;
        const cancelBtn = this.cancelText ? container.querySelector('#cancel-btn') as HTMLButtonElement : null;

        confirmBtn?.addEventListener('click', () => {
          this.confirmClick()
        });

        cancelBtn?.addEventListener('click', () => {
          this.cancelClick() 
        });
      }
    };

    this.dialog.noCloseOnOutsideClick = true

    document.body.appendChild(this.dialog);
  }

  updateProcess(str: string) { 
    this.process = str
    const dom = document.getElementById("process-text")
    if (dom) {
      dom.innerHTML = `, ${this.process}%`
    }
  }

  updateText() {
    const dom = document.getElementById("time-count")
    if (dom) {
      dom.innerHTML = `, ${this.count}s`
    }
  }

  setTipText(tipText: string) {
    this.tipText = tipText
   }

  setOkText(okText: string) {
    this.okText = okText
  }

  setCancelText(cancelText: string) {
    this.cancelText = cancelText
  }

  setCount(count: number) {
    this.count = this.countBack = count
  }

  setOkCallback(cb: () => void) {
    this.okCallback = cb
  }

  setCancelCallback(cb: () => void) {
    this.cancelCallback = cb
    
  }

  setTimeoutCallback(cb: () => void) {
    this.timeoutCallback = cb
    
  }

  _clearInterval() {
    if (this.timeoutId) {
      clearInterval(this.timeoutId)
      this.count = this.countBack
      this.timeoutId = null
    }
  }

  startTimeout() {
    this._clearInterval()
    this.timeoutId = setInterval(() => {
      if (this.count > 0) {
        console.log(this.count)
        this.count--
        this.updateText()
      } else {
        this._clearInterval()
        console.log(this.timeoutCallback)
        this.timeoutCallback && this.timeoutCallback()
      }
    }, 1000)
  }

  openDialog() {
    console.log("Opening dialog", this.countBack, this.count);
    this.renderDialog()
    this.dialog.opened = true
    this.countBack && this.startTimeout()
  }

  confirmClick() {
    this.okCallback && this.okCallback()
  }

  cancelClick() {
    this.cancelCallback && this.cancelCallback()
  }

  closeDialog() {
    console.log("Closing dialog")
    this._clearInterval()
    this.dialog.opened = false
    this.tipText = ''
    this.okText = ''
    this.cancelText = ''
    this.process = ''
    this.count = this.countBack = 0
    this.okCallback = () => {}
    this.cancelCallback = () => {}
    this.timeoutCallback = () => { }
  }
}

export default EditIngDialog;
