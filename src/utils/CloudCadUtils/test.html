<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <script type="module">
    import CloudCadUtils from './index.js';

    const initParams = {
      serverHost: "https://gcad-dev-cn.51ake.com:8384",
    }

    const utils = new CloudCadUtils(initParams);
    console.log(utils)
  </script>
</head>
<body>

  <!-- <style>
    #edit-dialog {
      position: fixed;
      top: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.4);
      z-index: 9999;
      width: 100vw;
      height: 100vh;
    }

    #edit-dialog .dialog-content {
      width: 300px;
      height: auto;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 10 10 0 0 rgba(0, 0, 0, 0.4);
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      box-sizing: border-box;
      padding: 20px;
    }

    #edit-dialog .dialog-tips {
      font-size: 16px;
      color: #333;
      text-align: center;
    }

    #edit-dialog .dialog-time {
      font-size: 16px;
      color: #333;
      text-align: center;
    }

    #edit-dialog .dialog-footer {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
    }

    .dialog-footer .dialog-action {
      cursor: pointer;
      user-select: none;
      border: 1px solid #dcdfe6;
      box-sizing: border-box;
      white-space: nowrap;
      font-size: 14px;
      border-radius: 4px;
      padding: 2px 14px;
      text-align: center;
      color: #606266;
    }

    .dialog-footer .dialog-end-edit {
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
    }

    .dialog-end-edit:hover{
      opacity: 0.8;
    }
  </style>

  <div id="edit-dialog">
    <div class="dialog-content">
      <p class="dialog-tips">正在等待xxxx响应</p>
      <p class="dialog-time">50s</p>
      <footer class="dialog-footer">
        <span class="dialog-action dialog-close" onclick="closeEditDialog()">关闭</span>
        <span class="dialog-action dialog-end-edit" onclick="closeEditDialog()">结束编辑</span>
      </footer>
    </div>
  </div> -->
 
</body>
</html>