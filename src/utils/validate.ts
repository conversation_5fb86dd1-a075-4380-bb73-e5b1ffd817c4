/** 待确认规则添加正则校验 */

// 校验用户名
export function validateUserName(val: string) {
    if (!val) return true;
    const pattern = /^[a-zA-Z]\w{1,127}$/;
    return pattern.test(val);
}
// 校验邮箱
export function validateEmail(val: string) {
    if (!val) return true;
    const pattern = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
    return pattern.test(val);
}
// 校验手机号
export function validatePhone(val: string) {
    if (!val) return true;
    const pattern =
        /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/;
    return pattern.test(val);
}
// 校验密码
export function validatePassWord(val: string) {
    return val.length <= 32 && val.length >= 6;
}
// 校验文件夹/文件 名称
export function validateFileName(val: string) {}

// 校验用户组编号
export function validateGroupCode(val: string) {
    if (!val) return true;
    const pattern = /^[a-zA-Z]\w{0,31}$/;
    return pattern.test(val);
}
