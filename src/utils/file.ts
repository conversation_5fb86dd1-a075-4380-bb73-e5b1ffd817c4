import { FILETYPE, type CrumbsListItem, type FileItem } from "@/model/file";
import { useUserStore } from "@/stores/user";
import router from "@/router";
import { emitter } from "@/utils/mitt";
import { getBreadcrumbs, getExt, isType, openUrlFn } from ".";
import { useFile } from '@/hooks/useFile'
import { notAllowPreviewExt } from "./const";
import { getSDKAuth, checkLogin } from "@/services/auth";
import { getBrowserIdCount, getFileInfo } from "@/services/file";
import { decrypt, escapedUrl } from '@/utils';
import { createDeviceid } from '@/app/common/utils';
import { ElMessage } from 'element-plus';
import { $t } from '@/locales';
const { api_endpoint: host, openDrawTab } = window.$globalConfig;
const userStores = useUserStore();

// 处理文件列表点击事件
export const gotoItem = (item: FileItem, cb: any) => {
  switch (item.fileType) {
    case FILETYPE.FOLDER:
    case FILETYPE.ZIP:
    case FILETYPE.TAR_GZ:
    case FILETYPE.RAR:
    case FILETYPE.TAR:
      if (cb && typeof cb === "function") cb();
      break;
    default:
      preview(item);
      break;
  }
};

export const getPreviewUrl = (item: FileItem, type = "", size = '') => {
  if (type === "cad") {
    let imgId = ''
    try {
      if (item.thumbnail) {
        const parsedThumbnail = JSON.parse(item.thumbnail)
        if (typeof parsedThumbnail === 'object') {
          imgId = parsedThumbnail[size] ?? ''
        } else if (typeof parsedThumbnail === 'number') {
          imgId = item.thumbnail
        }
      }
    } catch (error) {
      console.log(error)
    }
    // 如果是cad相关图纸
    return `${host}/api/v2/_st/_storage/_download?Authorization=${userStores.token}&storageId=${imgId}`;
  } else {
    let url = `${host}${import.meta.env.VITE_GLOB_API_URL_PREFIX as any}/${
      import.meta.env.VITE_GLOB_API_URL_VERSION as any
    }${`/_st/_file/_download?fileName=${item.name}&fileId=${
      item.fileId || item.id
    }`}${"&"}Authorization=${userStores.token}&preview=1`;

    if (item.isHistory) {
      url += `&sid=${item.sid}&version=${item.version}&history=1`;
    }
    return escapedUrl(url);
  }
};

const getAuth = async () => {
  try {
    const res = await getSDKAuth();
    const key = useUserStore().key
    const resData = decrypt(res.data, key || "")
    return resData || "";
  } catch (e) {
    console.log(e);
    return "";
  }
};

export const preview = async (item: FileItem, isReplace?: boolean) => {
  const ext = getExt(item.name);
  if (notAllowPreviewExt.includes(ext)) {
    // 如果是不支持的文件类型，则直接返回
    ElMessage({
      type: "error",
      message: `该文件类型暂未支持`,
    });
    return;
  }
  const params = {
    fileId: item.fileId || item.id,
    fileName: item.name,
    etag: item.etag,
    version: item.version,
    hideFunction: item.hideFunction
  } as Recordable;
  if (item.isHistory) {
    // 如果是历史版本列表里的文件，则携带如下参数
    params.sid = item.sid;
    params.history = 1;
  }
  const { canPreview }  = useFile(item)
  const isLogin = await checkLogin()
  if (isLogin.code !== 0) return
  // 如果不是历史列表跳转（直接文件列表预览）获取最新的版本更新params
  if (!item.isHistory) {
    // 获取最新文件信息（版本、etag）更新params
    const fileInfo = await getFileInfo(item.fileId || item.id)
    // console.log('fileInfo', fileInfo)
    params.etag = fileInfo.data.FileInfo.etag
    params.version = fileInfo.data.FileInfo.version
  }

  if (isType(item, "2D")) {
    const data = await getAuth();
    if (data && data["2DFileFormat"] && data["2DJSSDKFormat"]) {
      if (
        data["2DFileFormat"]
          .map((str: string) => str.toLowerCase())
          .includes(ext)
      ) {
        if (isReplace) {
          router.replace({
            name: 'app',
            params: { aname: 'Preview2d' },
            // path: `/app/Preview2d`,
            query: params,
          });
        } else {
          openUrlFn(`${import.meta.env.VITE_BASE_URL}/preview2d.html`, Object.assign(
            params,
            openDrawTab === "new" ? { _blank: 1 } : {}
          ))
        }
        return;
      }
    }
    ElMessage({
      type: "error",
      message: `该文件类型未获得授权`,
    });
  } else if (isType(item, "3D")) {
    const data = await getAuth();
    if (data && data["3DFileFormat"] && data["3DJSSDKFormat"]) {
      if (
        data["3DFileFormat"]
          .map((str: string) => str.toLowerCase())
          .includes(ext)
      ) {
        // 如果是3D文件
        openUrlFn(`${import.meta.env.VITE_BASE_URL}/preview3d.html`, Object.assign(
          params,
          openDrawTab === "new" ? { _blank: 1 } : {}
        ))
        return;
      }
    }
    ElMessage({
      type: "error",
      message: `该文件类型未获得授权`,
    });
  } else if (canPreview.value) {
    // 目前只支持PDF和图片预览功能
    const previewURL = getPreviewUrl(item);
    if (openDrawTab === "new") {
      const params = {fileId: item.fileId || item.id, name: item.name, url: previewURL, _blank: 1 }
      router.push({
        path: `${import.meta.env.VITE_BASE_URL}/preview`,
        query: params as any,
      });
    } else {
      emitter.emit("previewShowDialog", { ...item, url: previewURL });
    }
  } else {
    ElMessage({
      type: "error",
      message: `暂不支持打开该类型文件`,
    });
  }
};



/**
 * @description 点击在线编辑
 * @param disabledEdit 
 * @param item 
 * @param crumbsList 
 * @param isLastCrumbName 
 * @returns 
 */
interface editFileParamsType {
  disabledEdit: boolean,
  item: FileItem,
  bsList: CrumbsListItem[],
  isLastCrumbName?: boolean,
  cloudCadUtils?: any 
}
export const editFile = async (params: editFileParamsType) => {
  const { disabledEdit, item, bsList, isLastCrumbName, cloudCadUtils } = params;
  if (disabledEdit) return ElMessage.error($t('文件在线编辑功能未获得授权'))
  
  console.log("item", {...item})
  
  /** 获取面包屑数据 */
  const queryData: any = {...item, _blank: 1 }
  const list: any = getBreadcrumbs(bsList) || []
  isLastCrumbName && list.push({title: item.name})
  queryData.breadcrumbList = (JSON.stringify(list))
  console.log("queryData", queryData)


  // const did = await createDeviceid()
  // queryData.did = did
  try {
    if (!(window?.$globalConfig?.isCooperate)) { // 如果isCooperate === false单人编辑,则需要请求编辑走抢占编辑逻辑，多人协同编辑则不走此逻辑
      // await requestEditFile({
      //   fileId: item.id,
      //   devId: did ?? ''
      // })

      // const params = {
      //   breadcrumbList: queryData.breadcrumbList,
      //   fileName: queryData.name,
      //   fileId: queryData.id,
      //   enableHeader: true
      // }
      const client_id = localStorage.getItem('client_id') || '';
      const browser_id = localStorage.getItem('gstarDeviceId') || '';

      const browserIdCountData = await getBrowserIdCount({client_id, browser_id})
      console.log("browserIdCount", browserIdCountData)
      if (browserIdCountData.code === 0) {
        if (browserIdCountData.data.allow_entry) {
          cloudCadUtils.requestEditDrawing({...queryData, enableHeader: true, fileId: queryData.id})
        } else {
          ElMessage({
            type: "error",
            message: $t(`已达到当前时段最大用户链接数`),
          });
        }
      }

    }
    // openUrlFn("/cloudCAD.html", {...queryData, enableHeader: true})
  } catch (error: any) {
    if (error.code === 440600) {
      const data = {
        queryData,
        editor: error?.data?.editor ?? ''
      }
      emitter.emit("showEditPermissionDialog", { data });
    }
  }
}