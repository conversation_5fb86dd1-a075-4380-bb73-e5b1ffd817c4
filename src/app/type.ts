// sdk初始化参数
export interface IGCADPARAMS {
  vendorcode: string; //必须，开发者的编码.(由服务器指定生成，参见后端部分）
  element: string; //必须，指定要在哪儿显示图形
  param: {
    //均是可选部分，具体使用细节见后 param参数使用
    wrapId?: string; // 兼容WebCAD参数
    fileName?: string; // 可选，显示的文件名称。（该值会参与文件校验）当不指定时，开发商服务器必须采用传递方式1
    etag?: string; // 可选，文件的Etag标识数据。（该值会参与文件校验）当不指定时，且开发商服务器不采用方式1时 将不会对文件内容完整性做检查。
    size?: number | string; // 可选，文件的大小。（该值会参与文件校验）当不指定时，且开发商服务器不采用方式1时 将不会对文件内容大小做检查。
    cadServer?: string; // 浩辰CAD服务器地址。 可选，当不指定是： https://cloud.gstarcad.com/v2/api
    fileToken?: string; // 可选。文件信息。用于从数据服务器获取文件。（该值会参与文件校验）
    userToken?: string; // 用户的信息。用于从数据服务器获取文件。（该值不会参与文件校验） 可选。
    fileId?: string; // 可选。仅用于后端 PUSH方式。此FILEID是开发商服务器向浩辰服务器PUSH文件时返回的文件ID
    fileDownLoadUrl?: string;

    version?: string;

    languagePath?: string; // 可选，界面语言字典url，默认中文，可以设置其他字典，参考en.json
    treeLanguagePath?: string; // 可选，结构树语言字典url，将结构树节点中关键字替换，参考tree-zh.json
    hideFunction?: boolean | string; // 可选，隐藏功能按钮，默认false
  };
}
// 元数据，包含二维和三维
export interface IMetaData {
  brepStorageIds?: number[]
  info?: { //基本显示数据
    transition: { //任务
      taskId: number | string, //任务id
      taskStatus: number, //任务状态
      type: number, //0-单文件 1-分片
    }
    etag: string //文件摘要SHA-1
  }
  "main"?: {
    storageId: string, //主文件id
    storageName: string //主文件名
  }, //文件存储id
  "slice"?: {
    storageId: string, //分片文件id
    storageName: string //分片文件名
  }[],
  transitionList?: {
    layoutName: string; // 布局名
    taskStatus: number; // 任务状态
    taskId: string | number; // 任务id
    storageId: string; // 资源storageId
    storageName: string; // 资源storage 名
  }[];
}
// 转换任务状态
export enum ETASKSTATUS {
  pending = 1, // 已就绪
  executing, // 执行中
  finished, // 已完成
  partialFinished, // 部分完成
  failed, // 已失败
  canceled, // 已取消
}
// 任务优先级
export enum ETASKPRIORITY {
  lowest = 1, // 最低
  low, // 低
  lower, // 略低
  normal, // 正常
  higher, // 略高
  high, // 高
  highest // 最高
}
export interface IFSMResult {
  done: boolean;
  next?: TFSMFunction;
  nextParams?: any;
  result?: {
    state: boolean;
    data?: any;
  };
}
// 函数式状态机
export type TFSMFunction = (data: any) => Promise<IFSMResult>
export interface IMataVersion {
  version: string // 数据结构版本
  data: IMetaData
  history?: any
}
// 任务类型
export enum ETASKTYPE {
  CONVERT_OCF = "DWGVisualization", // 转换ocf文件
  SAVE_DWG = "SaveAsDWG", // 保存dwg
  SAVE_PDF = "SaveAsPDF", // 保存pdf
  THUMBNAIL_2D = "MakeDWGThumbnail", // 生成2D缩略图
  THUMBNAIL_BIG_2D = "MakeDWGBigThumbnail", // 生成2D大缩略图
  CONVERT_VSF = "BuildingVisualization", // 转换vsf文件
  THUMBNAIL = "MakeBuildingThumbnail", // 生成缩略图
  CONVERT_PDF = "ConvertPDF", // 转换pdf文件
  SELF_CHECK = "SelfCheck", // 自我检测
  MANUFACTURING = "ManufacturingVisualization",
  MANUFACTURING_THUMBNAIL = "MakeManufacturingThumbnail",
  MANUFACTURING_SKP = "MFGSkpVisualization",
  CONVERT_3D_OCF = "dwg2ocf3d", // 转换3d ocf文件
  CONVERT_RVT = "rvt2ocf3", // 转换rvt文件
  BACKUP = "backuptask", // 备份
  DATA_ARRANGE = "dataclean", // 数据整理
  RESTART = "restart", // 重启服务
  CONVERT_DWF = "ConvertDWF", // 转换dwf文件
  DATA_CLEAR_TIMEOUT_DATA = "DataClearTimeOutData"
}