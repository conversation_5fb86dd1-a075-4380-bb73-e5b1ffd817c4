const apiList = {
  "getFontInfoList": {
    url: getUrl('/_st/_font/_fontInfoList'),
    method: 'get',
    data: {folderId: 7, limit: 1000000}
  },
  "getFontMatrixList": {
    url: getUrl('/_st/_font/_fontMatrix'),
    method: 'get',
    data: {folderId: 7, limit: 1000000, requiredFont: '', requiredStr: ''}
  },
  "getFileInfoByPath": {
    url: getUrl('/_st/_file/_getFileInfoByPath'),
    method: 'get',
    data: {filePath: ''}
  },
  "downloadFileSource": {
    url: getUrl('/_st/_file/_download'),
    method: 'get',
    data: {fileName: '', fileId: ''}
  }
}

function getUrl(api: string) {
  const { api_endpoint: host } = window.$globalConfig
  const url = `${host}${import.meta.env.VITE_GLOB_API_URL_PREFIX as string}/${
    import.meta.env.VITE_GLOB_API_URL_VERSION as string
  }${api}`
  return url
}

export default JSON.stringify(apiList)