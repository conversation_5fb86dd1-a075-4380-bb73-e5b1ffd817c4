// 纯函数国际化解决方案，用于 src/app 和 src/utils/CloudCadUtils 目录下的代码
// 不依赖 Vue 或其他组件，可以单独打包

import zhLocale from '../../locales/zh'
import enLocale from '../../locales/en'

// 语言映射
const messages = {
  zh: zhLocale,
  en: enLocale,
}

// 获取当前语言
export const getCurrentLocale = (): 'zh' | 'en' => {
  // 从 localStorage 获取语言设置
  try {
    const localeStore = localStorage.getItem('locale-store')
    if (localeStore) {
      const parsed = JSON.parse(localeStore)
      return parsed.locale === 'zh_CN' ? 'zh' : 'en'
    }
  } catch (e) {
    console.warn('Failed to get locale from localStorage:', e)
  }
  
  // 默认返回中文
  return 'zh'
}

// 纯函数翻译方法
export const t = (key: string, params?: Record<string, any>): string => {
  const locale = getCurrentLocale()
  const message = messages[locale]
  
  // 获取翻译文本
  let text = (message as any)[key] || key
  
  // 处理参数替换
  if (params && typeof text === 'string') {
    Object.keys(params).forEach(paramKey => {
      const regex = new RegExp(`\\{${paramKey}\\}`, 'g')
      text = text.replace(regex, String(params[paramKey]))
    })
  }
  
  return text
}

// 导出默认的翻译函数
export default t
