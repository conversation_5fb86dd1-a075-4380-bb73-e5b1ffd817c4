(function() {
    if (!window.__GSTAR_CLOUD_CAD__) {
        window.__GSTAR_CLOUD_CAD__ = {};
    }

    if (!window.__GSTAR_CLOUD_CAD__.execTime) {
        window.__GSTAR_CLOUD_CAD__.execTime = {
            data: [],
            record(key, level = 0) {
                const timestamp = Date.now();
                window.__GSTAR_CLOUD_CAD__.execTime.data.push({ key, level, timestamp });
            },
            print() {
                let previousTimestamps = {};

                window.__GSTAR_CLOUD_CAD__.execTime.data.forEach((entry, index) => {
                    const {log} = window.console
                    const { key, level, timestamp } = entry;
                    const indent = '  '.repeat(level);
                    let timeDiff = '';

                    if (previousTimestamps[level] !== undefined) {
                        timeDiff = ` (+${timestamp - previousTimestamps[level]}ms)`;
                    }

                    // Print time difference on a separate line
                    if (timeDiff) {
                        log(`%c${indent}${timeDiff}`, 'color: gray;');
                    }

                    const color = level === 0 ? 'color: blue;' : 'color: green;';
                    const symbol = level === 0 ? '❯' : '├';
                    log(`%c${indent}${symbol} ${key}`, color);

                    previousTimestamps[level] = timestamp;

                    // If the next entry is at a lower level, clear higher levels
                    if (window.__GSTAR_CLOUD_CAD__.execTime.data[index + 1] && window.__GSTAR_CLOUD_CAD__.execTime.data[index + 1].level <= level) {
                        for (let i = level + 1; i in previousTimestamps; i++) {
                            delete previousTimestamps[i];
                        }
                    }
                });
            }
        };
    }
})();