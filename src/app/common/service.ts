import axios from 'axios';
import { promiseWrap } from '../common/utils';
import type { SocketByWebWorker } from '../../utils/socket';
import { sleep } from '../../utils/httpUtil';
import CryptoJS from 'crypto-js';
import { IUserInfo } from '../CloudCAD/types';

import UploadManager from "@/utils/UploadManager"
// import { useUserStore } from "@/stores/user";
// const userStores = useUserStore();

interface IHttpConfig {
  api_endpoint: string;
  token: string;
}

export const createHttpRequest = (httpConfig: IHttpConfig) => {
  const requestConfig = {
    baseURL: `${httpConfig.api_endpoint}/api/v2`,
    headers: {
      Authorization: httpConfig.token,
    },
  };
  // 请求编辑
  const h_requestEdit = (data: any) => {
    return axios({
      ...requestConfig,
      url: `/_edit/_request`,
      method: 'post',
      data: data
    }).then(res => res.data)
  };
  // 获取云CAD配置
  const h_getCADConfig = () => {
    return axios({
      ...requestConfig,
      url: `/_system/_gcadcloud_info`,
      method: 'get',
    }).then((res: any) => res.data);
  };
  // 获取文件信息
  const h_getFileInfo = (fileId: string) => {
    return axios({
      ...requestConfig,
      url: `/_st/_file/${fileId}`,
      method: 'get',
    }).then((res: any) => res.data);
  };
  // 根据文件信息下载文件

  const h_downloadFileByFileInfo = (params: {
    reqFrom: string;
    fileName: string;
    fileId: string | number;
  }) => {

    const escapedUrl = (url: string) => {
      // 下载文件需要转义特殊字符
      let _url = url.replace(/\+/g, '%2B'); // "+" 转义
      _url = url.replace(/#/g, '%23'); // "#"
      return _url;
    }

    const url = `/_st/_file/_download?fileName=${params.fileName}&fileId=${params.fileId}&reqFrom=${params.reqFrom}`;
    const translate_url = escapedUrl(url)

    return axios({
      ...requestConfig,
      url: translate_url,
      method: 'get',
      responseType: 'arraybuffer',
    }).then((res) => res.data);
  };
  // 上传文件
  // const h_uploadFile = (data: any, cb: (progressEvent: any) => void) => {
  //   const form = new FormData();
  //   form.append('folderId', data.folderId ? data.folderId.toString() : '');
  //   form.append('replace', data.replace ? data.replace.toString() : '');
  //   form.append('etag', data.etag);
  //   form.append('file', data.file);
  //   if (data.userTag) form.append('userTag', data.userTag);
  //   if (data.tagType) form.append('tagType', data.tagType)
  //   if (data.path) form.append('path', data.path);
  //   return axios({
  //     ...requestConfig,
  //     headers: {
  //       ...requestConfig.headers,
  //       'Content-Type': 'multipart/form-data',
  //     },
  //     // ignore: true,
  //     url: '/_st/_file/_upload',
  //     method: 'post',
  //     data: form,
  //     onUploadProgress: cb,
  //   }).then((res: any) => res.data);
  // };
  const h_uploadFile = async (data: any, cb: (progressEvent: any) => void) => {
    const upload = new UploadManager({
      file: data.file,
      folderId: data.folderId || '',
      type: 1,
      replace: data.replace,
      token: httpConfig.token,
      host: httpConfig.api_endpoint,
      tagType: data?.tagType || "",
      userTag: data?.userTag || ""
    })
    console.log("h_uploadFile", data)

    const res = await upload.start("");

    return res
  }
  // 上传增量
  const h_uploadModifyRecord = (params: any) => {
    const formData = new FormData();
    // todo 需要补充etag参数
    // 。。。
    for (const key in params) {
      formData.append(key, params[key]);
    }
    return axios({
      ...requestConfig,
      headers: {
        ...requestConfig.headers,
        'Content-Type': 'multipart/form-data',
      },
      // ignore: true,
      url: '/_st/_filemodify/_uploadModifyRecord',
      method: 'post',
      data: formData,
    }).then((res: any) => {
      if (res.data.code !== 0) return Promise.reject(res.data)
      return res.data
    });
  };

  // 自动保存
  const h_autoSave = async (data: any) => {
    if (data.isShot) {
      return new Promise((resolve, reject) => {
        // 如果是快照
        h_uploadFile(
          {
            folderId: data.folderId,
            replace: true,
            file: data.file,
          },
          (evt) => {
            // const progressPercent = ((evt.loaded / evt.total) * 100).toFixed(2);
            // if (parseInt(progressPercent) === 100) {
            //   resolve(true);
            // }
          }
        ).then((res) => {
          if (res.code !== 0) {
            if ((window as any).__GSTAR_CLOUD_CAD__) {
              (window as any).__GSTAR_CLOUD_CAD__.instance.pubsub.publish('external.interface.error', {
                type: 0,
                code: res.code,
                msg: res.msg
              })
            }
            throw new Error(res.msg)
          } else {
            resolve(true)
          }
        })
          .catch((e) => {
            reject(false);
          });
      });
    } else {
      const [err, result] = await promiseWrap(
        h_uploadModifyRecord({
          fileId: data.fileId,
          etag: data.etag,
          baseId: data.baseId || 0,
          srcStorageId: data.sid || 0,
          lastModify: data.lastsaveIncrVersion,
          currModify: data.cursaveIncrVersion,
          baseCommitID: data.baseCommitID,
          command: data.cmd,
          position: JSON.stringify(data.curPt),
          did: data.did,
          file: data.file,
        })
      );
      if (err) {
        console.log('上传增量失败', err);
        throw new Error('上传增量失败');
      }

      return result.data;
    }
  };

  // 通过sid下载资源
  const h_downloadFileBySid = (
    params: {
      storageId: string | number;
      fileName?: string;
      Authorization?: string;
      type?: number | string;
    },
    type?: 'arraybuffer' | 'json' | 'text' | 'blob' | 'document' | 'stream'
  ) => {
    return axios({
      ...requestConfig,
      url: '/_st/_storage/_download',
      method: 'get',
      responseType: type || 'arraybuffer',
      params,
    }).then((res) => res.data);
  };

  // 批量获取用户头像
  const h_batchUserInfo = (ids: string) => {
    return axios({
      ...requestConfig,
      url: '/_user/_batch_get',
      method: 'get',
      params: {
        ids,
      },
    }).then((res) => res.data?.data);
  };

  // 下载图片
  const h_downloadImage = (key: string) => {
    return axios({
      ...requestConfig,
      url: '/_image',
      method: 'get',
      responseType: 'arraybuffer',
      params: {
        key,
      },
    }).then((res) => res.data);
  };

  // 通过taskId获取文件ID
  const h_getFileIdByTaskId = async (taskId: string) => {
    try {
      const result = await axios({
        ...requestConfig,
        url: '/_st/_file/_queryFileIdByTaskId',
        method: 'get',
        params: {
          taskId,
        },
      });
      return result?.data;
    } catch (error) {
      console.log('getFileIdByTaskId err', error);
      throw new Error(
        '_queryFileIdByTaskId error response:' + JSON.stringify(error)
      );
    }
  };

  // 根据fileUrl获取fileId
  const h_getFileIdByFileUrl:(params: {
    userToken?: string;
    fileName?: string;
    etag?: string;
    size?: string | number;
    fileDownLoadUrl?: string;
    fileToken?: string;
  }) => Promise<string> = async ({
    userToken,
    fileName: fileName,
    etag: etag,
    size: size,
    fileDownLoadUrl: fileUrl,
    fileToken,
  }: {
    userToken?: string;
    fileName?: string;
    etag?: string;
    size?: string | number;
    fileDownLoadUrl?: string;
    fileToken?: string;
  }) => {
    type TSTATUS = 0 | 1 | 2;

    const thirdPartyParams: any = {
      fileDownLoadUrl: fileUrl,
    };
    if (userToken) thirdPartyParams.userToken = userToken;
    if (fileName) thirdPartyParams.fileName = fileName;
    if (etag) thirdPartyParams.etag = etag;
    if (size) thirdPartyParams.size = size;
    if (fileToken) thirdPartyParams.fileToken = fileToken;
    try {
      const _res = await axios({
        ...requestConfig,
        url: `/_st/_file/_thirdPartyOpenDrawingByUrl`,
        method: 'post',
        data: thirdPartyParams,
      });
      const res = _res.data
      if (res.data && (res.data.taskId || res.data.taskId === 0)) {
        let taskInfo: { status: TSTATUS; fileId: number | string } = {
          status: 0,
          fileId: '',
        };
        while (taskInfo.status === 0) {
          await sleep(3000);
          try {
            const taskData = await h_getFileIdByTaskId(res.data.taskId);
            if (!taskData.data) {
              throw new Error('getFileIdByTaskId error: ' + JSON.stringify(taskData))
            }
            taskInfo = taskData.data
            console.log('taskInfo', taskInfo);
          } catch (err) {
            console.log('taskInfo err', err);
            throw err;
          }
        }
        switch (taskInfo.status) {
          case 1 as TSTATUS:
            return taskInfo.fileId + '';
          case 2 as TSTATUS:
            throw new Error('download error: ' + JSON.stringify(taskInfo));
          default:
            throw new Error('unknown status: ' + JSON.stringify(taskInfo));
        }
      } else {
        throw new Error(
          'thirdPartyOpenDrawingByUrl error res:' + JSON.stringify(res)
        );
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  // 获取元数据
  const h_getMeta = async (params: {
    fileId: number | string;
    metaKey: string;
  }) => {
    const res = await axios({
      ...requestConfig,
      url: '/_st/_file/_getMeta',
      method: 'get',
      params,
    });
    return res?.data;
  };

  // 设置元数据
  const h_setMeta = async (data: {
    fileId: number | string;
    metaKey: string;
    value: string;
    version: string;
  }) => {
    const res = await axios({
      ...requestConfig,
      url: '/_st/_file/_setMeta',
      method: 'post',
      data,
    });
    return res?.data;
  };

  // 查询任务信息
  const h_getTaskInfoById = async (taskId: string | number) => {
    const res = await axios({
      ...requestConfig,
      url: `/_task/${taskId}`,
      method: 'get',
    });
    return res?.data;
  };

  // 创建任务
  const h_initTask = async (params: {
    fileId: string | number;
    type: string;
    version?: string;
    config?:
    | {
      [key: string]: any;
    }
    | string;
  }) => {
    if (!params.config) params.config = {};
    params.config = JSON.stringify(params.config);
    params.fileId = parseInt(params.fileId as string);
    const res = await axios({
      ...requestConfig,
      url: `/_task`,
      method: 'post',
      data: params,
    });
    return res.data;
  };

  // 提升任务优先级
  const h_setTaskPriority = async (params: {
    id: number;
    priority: number;
  }) => {
    const data = params;
    return await axios({
      ...requestConfig,
      url: `/_task/${data.id}/_priority`,
      method: 'put',
      data,
    });
  };

  // 检查文件是否需要重新转换
  const h_isNeedReConvert = async (
    fileId: number | string,
    version: string,
    metaKey: string
  ) => {
    const res = await axios({
      ...requestConfig,
      url: `/_st/_file/_isNeedReConvert`,
      method: 'get',
      params: {
        fileId,
        version,
        metaKey,
      },
    });
    return res?.data
  };

  // 获取历史版本列表
  const h_getHistoryTagList = async (data: {
    status: 0 | 1 | 2, // 0:未完成归并的，1：归并完成的，2:全部
    fileId: number,
    sign: string
  }) => {
    const res = await axios({
      ...requestConfig,
      url: `/_st/_fileTag/_getFileHistoryTag`,
      method: 'get',
      params: { ...data },
    });
    return res?.data
  };

  // 添加历史版本
  const h_addHistoryTag = async (data: {
    baseStorageId: number,
    fileId: number,
    lastRecordId: number,
    userTag: string,
  }) => {
    const res = await axios({
      ...requestConfig,
      url: `/_st/_fileTag/_addFileHistoryTag`,
      method: 'post',
      data: { ...data },
    });
    console.log('h_addHistoryTag res', res)
    if (res?.data.code !== 0) return Promise.reject(res?.data)
    return res?.data
  };

  // 删除历史版本
  const h_deleteHistoryTag = async (data: {
    fileId: number,
    userTag: string,
    verId: number
  }) => {
    const res = await axios({
      ...requestConfig,
      url: `/_st/_fileTag/_delFileHistoryTag`,
      method: 'post',
      data: { ...data },
    });
    return res?.data
  };

  // 获取文件夹文件列表（向下翻页）
  const h_getFileListByDrop = async (params: any) => {
    const res = await axios({
      ...requestConfig,
      url: `/_st/_fd/_getFileList`,
      method: 'get',
      params,
    });
    return res?.data
  };

  // 通过文件路径获取文件信息
  const h_getFileInfoByPath = async (path: string) => {
    const res = await axios({
      ...requestConfig,
      url: `/_st/_file/_getFileInfoByPath`,
      method: 'get',
      params: {
        filePath: path
      },
    });
    return res?.data
  };

  // 获取授权许可证
  const h_getLicense = async () => {
    const res = await axios({
      ...requestConfig,
      url: "/license_info",
      method: 'get',
    });
    return res?.data
  };

  // 上传文件到storage
  const h_uploadFileByStorageId = async (params: any) => {
    const formData = new FormData();
    for (const key in params) {
      formData.append(key, params[key]);
    }
    const res = await axios({
      ...requestConfig,
      headers: {
        ...requestConfig.headers,
        'Content-Type': 'multipart/form-data',
      },
      url: `/_st/_storage/_upload`,
      method: 'post',
      data: formData,
    });
    return res?.data;

    // const upload = new UploadManager({
    //   file: params.file,
    //   type: 'storage',
    //   attribute: params.attribute,
    //   token: userStores.token
    // })

    // const res = await upload.start();

    // return res?.data
  };

  // 创建书签
  const h_createMark = async (params: {
    center: string;
    etag: string;
    fileId: number;
    name: string;
    scale: number;
    id: string;
  }) => {
    const res = await axios({
      ...requestConfig,
      url: `/_st/_file/_setBookMark`,
      method: 'post',
      data: params,
    });
    return res?.data;
  };

  // 获取书签
  const h_getMarks = async (params: { etag: string; fileId: number }) => {
    const res = await axios({
      ...requestConfig,
      url: `/_st/_file/_getBookMark`,
      method: 'get',
      params,
    });
    return res?.data;
  };

  // 删除书签
  const h_deleteMarks = async (params: {
    delId: string;
    etag: string;
    fileId: number;
  }) => {
    const res = await axios({
      ...requestConfig,
      url: `/_st/_file/_delBookMark`,
      method: 'delete',
      params,
    });
    return res?.data;
  };

  // 获取2D开图配置信息
  const h_getOpenDWGConfig = async (data: any) => {
    const res = await axios({
      ...requestConfig,
      url: `/_system/_open_drawing_config/_getlist`,
      method: 'get',
      params: data,
    });
    return res?.data;
  };

  // 获取当前用户信息
  const h_getSelfUserInfo = async (): Promise<IUserInfo> => {
    let res: any = null
    if (requestConfig.headers.Authorization) {
      try {
        res = await axios({
          ...requestConfig,
          url: `/_user/_self_info`,
          method: 'get'
        });
      } catch (err) { /* empty */ }
    }
    if (!res || res?.data?.code !== 0) {
      // 获取失败，尝试获取本地用户信息
      const userStr = localStorage.getItem('user');
      let userInfo: IUserInfo
      try {
        if (!userStr) throw new Error()
        userInfo = JSON.parse(userStr)
      } catch (err) {
        return Promise.reject('getUserInfo error!')
      }
      return userInfo
    }
    return (res?.data?.data?.token) ? (res?.data?.data) as IUserInfo : { ...(res?.data?.data), token: httpConfig.token }
  };

  // 获取sdk授权信息
  const h_getSDKAuth = async (key: string) => {
    const res = await axios({
      ...requestConfig,
      url: `/_license/_jssdkformatinfo`,
    })
    const resData = decrypt(res?.data?.data, key || "")
    return resData;
  }

  const decrypt = (data: any, key: string) => {
    try {
      const decrypt = CryptoJS.AES.decrypt(data, CryptoJS.enc.Utf8.parse(key), {
        iv: CryptoJS.enc.Utf8.parse(key.substring(0, 16)),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      const result = CryptoJS.enc.Utf8.stringify(decrypt).toString();
      return JSON.parse(result);
    } catch (e) {
      console.log('解密socket数据失败：', e);
    }
  }

  return {
    h_requestEdit,
    h_getCADConfig,
    h_getSelfUserInfo,
    h_getLicense,
    h_getFileInfoByPath,
    h_getFileListByDrop,
    h_deleteHistoryTag,
    h_addHistoryTag,
    h_getHistoryTagList,
    h_getFileInfo,
    h_autoSave,
    h_downloadFileBySid,
    h_batchUserInfo,
    h_downloadImage,
    h_getFileIdByFileUrl,
    h_getMeta,
    h_setMeta,
    h_getTaskInfoById,
    h_initTask,
    h_setTaskPriority,
    h_isNeedReConvert,
    h_downloadFileByFileInfo,
    h_uploadFileByStorageId,
    h_createMark,
    h_getMarks,
    h_deleteMarks,
    h_getOpenDWGConfig,
    h_uploadModifyRecord,
    h_uploadFile,
    h_getSDKAuth
  };
};
interface ISocketRequestCommonParams {
  uid: string;
  username: string;
  did: string;
  ename: string;
  room_id: string;
  file_id: number | string;
  [key: string]: any;
}
export const createSocketRequest = (
  socket: SocketByWebWorker,
  state: any,
  socketReqParams: any
) => {
  const socketRequest = async (
    params: ISocketRequestCommonParams,
    socket: SocketByWebWorker
  ) => {
    const [error, res] = await promiseWrap(socket.request(params));
    if (error) {
      throw error;
    }
    if (res.code !== 0) {
      throw new Error(JSON.stringify(res));
    }
    return res;
  };

  // 加入房间
  const s_joinRoom = async () => {
    const joinRoomReq = socketRequest(
      { ...socketReqParams, ename: 'ce.rooms.join' },
      socket
    );
    const [errJoinRoom, joinRes]: any = await promiseWrap(joinRoomReq);
    if (errJoinRoom) {
      console.error('加入房间失败', errJoinRoom);
      throw errJoinRoom;
    }

    // 更新房间id
    socketReqParams.room_id = joinRes.room_id;
    // 成功返回房间id
    return joinRes.room_id;
  };
  // 获取图纸
  const s_getFileInfo = async () => {
    const getFile = socketRequest(
      { ...socketReqParams, ename: 'ce.rooms.get.file' },
      socket
    );
    const [err, res]: any = await promiseWrap(getFile);
    if (err) {
      console.error('获取图纸文件失败', err);
      throw err;
    }
    // base_commit_id
    state.baseCommitID = res.base_commit_id;
    state.docSid = res.storage_id;
    // 成功返回图纸资源id
    return {
      docSid: res.storage_id,
      type: res.type,
    };
  };

  interface IMember {
    did: string;
    tid: string;
    tm: number;
    uid: string;
    username: string;
  }
  // 获取房间内成员列表
  const s_getMembers = async (): Promise<IMember[]> => {
    const [err, res]: any = await promiseWrap(
      socketRequest(
        { ...socketReqParams, ename: 'ce.rooms.number.list' },
        socket
      )
    );
    if (err) {
      console.error('获取房间成员失败', err);
      throw err;
    }

    // 成功返回成员列表
    return res.members;
  };
  // 增量提交事件
  const s_submitIncr = async (data: {
    cmd: string;
    curPt: { x: number; y: number; z: number };
    recordId: number;
  }) => {
    const [err, res]: any = await promiseWrap(
      socketRequest(
        {
          ...socketReqParams,
          ename: 'ce.rooms.submit.increment',
          incr_data: [
            {
              id: data.recordId,
              command: data.cmd,
              position: JSON.stringify(data.curPt),
            },
          ],
        },
        socket
      )
    );
    if (err) {
      console.error('增量事件发送失败', err);
      throw err;
    }

    // 增量事件返回
    return res;
  };
  // 获取增量
  const s_getIncr = async (data: {
    commitId: string | number;
    lastCommitId: string | number;
  }) => {
    const [err, res]: any = await promiseWrap(
      socketRequest(
        {
          ...socketReqParams,
          ename: 'ce.rooms.get.incr',
          commit_id: data.commitId,
          last_commit_id: data.lastCommitId,
        },
        socket
      )
    );
    if (err) {
      console.error('获取增量失败', err);
      throw err;
    }

    // 增量事件返回
    return res;
  };

  // 保存版本
  const s_saveVersion = async (tag: string) => {
    const [err, res]: any = await promiseWrap(
      socketRequest(
        { ...socketReqParams, ename: 'ce.rooms.save.version', tag },
        socket
      )
    );
    if (err) {
      console.error('保存版本失败', err);
      throw err;
    }

    // 增量事件返回
    return res;
  };

  return {
    s_joinRoom,
    s_getFileInfo,
    s_getMembers,
    s_submitIncr,
    s_saveVersion,
    s_getIncr,
  };
};
