import Fingerprint2 from 'fingerprintjs2';
import { GLOBALCONFIG_PATH, DRAWVIEWPATH_1, DRAWVIEWPATH_2, DRAWVIEWPATH, DBNAME, DBVERSION, OBJSTORE} from '../CloudCAD/constant'
import socket from '@/utils/socket';
import { ETASKTYPE } from '../type';
import workerCrypto from '@/utils/workerCrypto.mjs'
import { Base64 } from 'js-base64';
import t from './i18n'
// import '@vaadin/confirm-dialog'
// import '@vaadin/notification'

// 输出webclient版本
declare const __CLIENT_VERSION__: string;
export function withVersion(url: string): string {
  const sep = url.includes('?') ? '&' : '?'
  return `${url}${sep}v=${__CLIENT_VERSION__}`
}

export const polyfill = () => {
  // polyfill WeakRef
  if (!window.WeakRef) {
    (window as any).WeakRef = function (a:any) {
      return {
        deref: function() {
          return a
        }
      }
    }
  }
}

export const promiseWrap = (promise: Promise<any>) => promise.then(data => [null, data]).catch(err => [err])

// 制造业格式列表
export const manufacturing = [
  "3dm",
  "art",
  "c3s",
  "catdrawing",
  "catpart",
  "catshape",
  "ccd",
  "cfg",
  "cwr",
  "dcm",
  "ed",
  "edz",
  "emn",
  "emp",
  "exp",
  "iam",
  "idw",
  "ipt",
  "model",
  "plmxml",
  "ptf",
  "pvs",
  "pvz",
  "pwd",
  "sdt",
  "session",
  "slddrw",
  "smg",
  "stpxz",
  "stpz",
  "xar",
  "xmt_bin",
  "xmt_txt",
  "xpr",
  "z3",
  "sldprt",
  "sldasm",
  "par",
  "asm",
  "psm",
  "prt",
  "prt*",
  "asm*",
  "ipt(V6-V2021)",
  "iam(V11-V2021)",
  "model",
  "CATPart",
  "CATProduct",
  "cgr",
  "3dxml",
  "stp",
  "step",
  "sat",
  "sab",
  "igs",
  "iges",
  "jt",
  "x_t",
  "x_b",
  "vda",
  "xcgm",
  "3mf",
  "skp"
];

// 创建浏览器指纹
export const createDeviceid = ():Promise<string> => {
  return new Promise((resolve, reject) => {
    Fingerprint2.get((components:any) => { // 参数只有回调函数时，默认浏览器指纹依据所有配置信息进行生成
      const values = components.map((component:any) => component.value); // 配置的值的数组
      const randomStr = (window.crypto && window.crypto.randomUUID) ? window.crypto.randomUUID() : Math.random() + ''
      values.push(randomStr)
      const res: string = Fingerprint2.x64hash128(values.join(''), 31); // 生成浏览器指纹
      resolve(res)
    })
  })
}

// indexdb 删除写入方法
export function openDatabase(databaseName: string, version: number) {
  return new Promise((resolve: (value: IDBDatabase) => void, reject) => {
    const request = indexedDB.open(databaseName, version);

    request.onupgradeneeded = function (event: any) {
      const db = event.target.result;
      const transaction = event.target.transaction;

      let fileStore;

      if (db.objectStoreNames.contains(OBJSTORE)) {
        fileStore = transaction.objectStore(OBJSTORE);
      } else {
        fileStore = db.createObjectStore(OBJSTORE);
      }

      if (!fileStore.indexNames.contains('timestamp')) {
        fileStore.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };

    request.onsuccess = function (event: any) {
      const db:IDBDatabase = event.target.result;
      resolve(db);
    };

    request.onerror = function (event: any) {
      reject(event.target.error);
    };
  });
}
export function writeArrayBuffer(key: string, data: any, db: IDBDatabase) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([OBJSTORE], 'readwrite');
    const objectStore = transaction.objectStore(OBJSTORE);

    transaction.oncomplete = function () {
      resolve(db);
    };

    transaction.onerror = function (event) {
      reject(transaction.error);
    };
    const addRequest = objectStore.add({ timestamp: new Date(), mode: 33279, contents: data }, key);

    addRequest.onsuccess = function () {
      resolve(db);
    };

    addRequest.onerror = function (event) {
      reject(addRequest.error);
    };
  });
}
export function writeDir(key: string, db: IDBDatabase) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([OBJSTORE], 'readwrite');
    const objectStore = transaction.objectStore(OBJSTORE);

    transaction.oncomplete = function () {
      resolve(db);
    };

    transaction.onerror = function (event) {
      reject(transaction.error);
    };
    const addRequest = objectStore.add({ timestamp: new Date(), mode: 16895 }, key);

    addRequest.onsuccess = function () {
      resolve(db);
    };

    addRequest.onerror = function (event) {
      reject(addRequest.error);
    };
  });
}
export function fuzzyDelete(keyword: string, db: IDBDatabase) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([OBJSTORE], 'readwrite');
    const objectStore = transaction.objectStore(OBJSTORE);
    transaction.oncomplete = function () {
      resolve(db);
    };

    transaction.onerror = function (event) {
      reject(transaction.error);
    };

    objectStore.openCursor().onsuccess = function (event: any) {
      const cursor = event.target.result
      if (cursor) {
        // 在这里实现模糊匹配的逻辑
        if (cursor?.key?.includes(keyword)) {
          cursor.delete();
        }
        cursor.continue();
      }
    };
  });
}
// 图纸数据写入indexdb
export const writeDocBuffer = async (documentBuffer: ArrayBuffer, path: string) => {
  
  // 提前写入图纸信息
  const db = await openDatabase(DBNAME, DBVERSION)
  // 模糊删除
  await fuzzyDelete(DRAWVIEWPATH, db)

  // 写入新图纸
  try {
    await writeDir(DRAWVIEWPATH_1, db)
    await writeDir(DRAWVIEWPATH_2, db)
  } catch (err) {
    console.warn(err)
  }
  const sampleArrayBuffer = new Uint8Array(documentBuffer);
  await writeArrayBuffer(path, sampleArrayBuffer, db)
  db.close()
}

export const showToast = (message: string, duration = 2000, position = 'top-center', theme = 'primary') => {
  // 如果在iframe中则任务是外部对接，不进行toast提示
  if (isInIframe()) return
  import('@vaadin/notification')
  const notification = document.createElement('vaadin-notification') as any;
  notification.duration = duration;
  notification.position = position;
  
  notification.renderer = function (root: HTMLElement) {
    if (!root.firstElementChild) {
      const container = document.createElement('div');
      container.innerHTML = `<p style="margin: 0; padding: 0;">${message}</p>`;
      root.appendChild(container);
    
      // 这里直接访问 notification-card 并添加 theme
      const notificationCard = container.closest('vaadin-notification-card');
      if (notificationCard) {
        notificationCard.setAttribute('theme', theme);
      }
    }

  };

  notification.open();
  document.body.appendChild(notification);
}

export const errorTips = (msg: string | Error, duration = 3000) => {
  const _msg = typeof msg === 'object' ? msg.message : msg
  
  showToast(_msg, duration, 'top-center', 'error')
}
// 获取外部js的全局对象
export const getGlobalObject = (scriptUrl: string, objName: string) => {
  return new Promise((resolve, reject) => {
    const tag = document.getElementsByTagName('script');
    for (const i of tag) {
      if (i.src.includes(scriptUrl)) {
        return resolve((window as any)[objName]);
      }
    }
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = withVersion(scriptUrl);
    script.onerror = reject;
    document.body.appendChild(script);
    script.onload = () => {
      resolve((window as any)[objName] as any);
    };
  });
};
/**
 * @description 获取url参数
 * @param query query string
 * @returns object
 */
export function getQueryObj() {
  const params = new URLSearchParams(window.location.search);
  const obj: { [key: string]: any } = {};
  for (const [key, value] of params.entries()) {
    obj[key] = value;
  }
  return obj;
}
/**
 * @description 从localStorage获取用户信息
 * @returns user object
 */
export function getUserInfo() {
  const user = localStorage.getItem('user');
  return user ? JSON.parse(user) : null;
}
/**
 * @description 从localStorage获取token
 * @returns token string
 */
export function getToken() {
  const token = getUserInfo()?.token;
  return token;
}
/**
 * @description 获取配置信息,包含api地址等
 */
export async function getConfig(host?: string) {
  if ((window as any).$globalConfig) {
    // 如果已经存在，直接返回
    return (window as any).$globalConfig;
  } else {
    try {
      const result = await fetch(`${host || ''}/globalConfig.json`);
      return await result.json();
    } catch (error) {
      console.log(error);
      return '';
    }
  }
}
/**
 * @description 错误处理函数
 * @param token token
 */
export function errorHandler(token: string | null, error: any) {
  // 第一步：校验token是否存在
  if (!token) {
    // token不存在，跳转到登录页面
    window.location.href = '/login';
  } else {
    // 错误提示
  }
}
/**
 * @description socket连接
 */
export async function connectSocket(socketIo_url: string, key: string, token: string) {
  const did = await createDeviceid() as string
  const userInfo = getUserInfo();
  socket.connect({
    url: socketIo_url,
    token: token,
    key: key,
    uid: userInfo?.id || '',
    uname: userInfo?.user_name || '',
    did,
  });
}

// 获取文件名后缀
export const getExt = (fileName: string) => {
  if (!fileName) {
    return '';
  }
  if (fileName.match(/\.prt\.[^.]+$/)) {
    return 'prt*';
  }
  if (fileName.match(/\.asm\.[^.]+$/)) {
    return 'asm*';
  }
  const extStart = fileName.lastIndexOf('.');
  const ext = fileName.substring(extStart + 1, fileName.length).toLowerCase();
  return ext;
};

// 判断是否是制造业文件
export const isManufacturing = (name?: string) => {
  const file_extension = getExt(name || '');
  return manufacturing.some(
    (v: string) => v.toLowerCase() === file_extension.toLowerCase()
  );
};

/**
 * 获取制造业文件格式对应的任务类型
 * 目前制造业几十种文件格式共用一个安装包，
 * 后期会为每种文件格式提供一个单独的应用，
 * 此方法在后台更改完后还会进行更改，
 * 目前只对skp文件使用单独的任务类型
 */
export const getMFGType = (name?: string) => {
  let taskType = ETASKTYPE.MANUFACTURING
  const file_extension = getExt(name || '');
  if (file_extension.toLowerCase() === 'skp') {
    taskType = ETASKTYPE.MANUFACTURING_SKP
  }
  return taskType
};

/**
 * 获取文件SHA-1的值
 * @params file 上传的 file
 */
export function getFileSha1(
  file: File,
  cb?: (progress: number) => void
): Promise<string> {
  return new Promise((resolve, reject) => {
    const fr = new FileReader()
    fr.readAsArrayBuffer(file)
    fr.onloadend = (e) => {
      const buf = e.target?.result
      if (!buf) return reject('readAsArrayBuffer error!')
      

      workerCrypto.cryptoDigest(buf, 'SHA-1')
      .then(resolve)
      .catch(reject)
    }
    
  });
}

export const combineBreadcrumb = (breadcrumb: string, path: string) => {
  const breadcrumbParts = breadcrumb.split('/');
  let p_path = '';
  for (let i = 0; i < path.length; i++) {
    if (path[i] === '\\') {
      p_path += '/'
    } else {
      p_path += path[i]
    }
  }
  const pathParts = p_path.split('/');

  for (const part of pathParts) {
    if (part === '..') {
      // 如果是 '..', 则移除面包屑的最后一个部分
      breadcrumbParts.pop();
    } else if (part !== '.') {
      // 如果不是 '.', 则添加到面包屑中
      breadcrumbParts.push(part);
    }
  }

  return breadcrumbParts.join('/');
}

// query参数编码
export const encodeQueryParams = (params: {
  [key: string]: string | any;
}): string => {
  return Base64.encode(
    JSON.stringify(Object.keys(params).map((k) => params[k]))
  );
};

export const getBreadcrumbs = (items: any[]) => {
  if (items && items.length > 0) {
    return items.map((item, index) => {
      const queryParams = {
        folderId: Number(item.folderId),
        direction: 0,
        limit: 10,
        searchName: '',
        searchBeginTime: '',
        searchEndTime: '',
        fileType: 0,
        sortField: 0,
        ascOrDesc: 'desc',
        lastFileId: 0,
        lastFileSize: 0,
        lastFileModifyTime: '',
        lastFileName: '',
        reqPage: 1,
        reqPageIdArr: ''
      }

      return {
        title: item.folderName as string,
        href: item.folderId === 2 ? 'files/file' : (index === (items.length - 1) ? ""  :`files/file?params=${encodeQueryParams(queryParams)}`),
      };
    });
  } else {
    return []
  }
};

export const closePage = (payload: {msg: string}) => {
  window.parent.postMessage({ action: 'close', payload }, '*')
  window.close()
}

export const getQuery = (key: string) => {
  const url = new URL(window.location.href)
  const v = url.searchParams.get(key)
  return v
}

export const setQuery = (key: string, value: string) => {
  const url = new URL(window.location.href)
  url.searchParams.set(key, value)
  history.replaceState(null, "", url)
}

export function createInactivityWatcher(timeoutInSeconds: number, onTimeoutCallback: () => void) {
  let lastActivityTime = Date.now();
  let intervalId: any;

  const events = ['mousemove', 'keydown', 'scroll', 'touchstart'];

  function updateLastActivity() {
    lastActivityTime = Date.now();
  }

  function checkInactivity() {
    const now = Date.now();
    if ((now - lastActivityTime) > timeoutInSeconds * 1000) {
      stopWatching();
      onTimeoutCallback();
    }
  }

  function handleVisibilityChange() {
    if (document.visibilityState === 'visible') {
      checkInactivity();
    }
  }

  function startWatching() {
    events.forEach(event => window.addEventListener(event, updateLastActivity));
    document.addEventListener('visibilitychange', handleVisibilityChange);
    updateLastActivity(); // 初始化时间戳
    intervalId = setInterval(checkInactivity, 1000); // 每秒检查一次
  }

  function stopWatching() {
    events.forEach(event => window.removeEventListener(event, updateLastActivity));
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    clearInterval(intervalId);
  }

  // 初始化监控
  startWatching()

  return {
    stop: stopWatching,
    reset: updateLastActivity,
  };
}

let cachedConfirmDialog: any = null;
let currentConfirmHandler = null;
let currentRejectHandler = null;
export function showConfirmDialog(dialogParams: {
  headerTitle?: string,
  confirmText?: string,
  rejectText?: string,
  msg?: string,
  confirmCB?: () => void,
  rejectCB?: () => void,
}) {
  const {
    headerTitle = '',
    confirmText = '',
    rejectText = '',
    confirmCB,
    rejectCB,
    msg
  } = dialogParams;

  if (!cachedConfirmDialog) {
    import('@vaadin/confirm-dialog')
    cachedConfirmDialog = document.createElement('vaadin-confirm-dialog') as any;
    
    cachedConfirmDialog.noCloseOnEsc = true;
    document.body.appendChild(cachedConfirmDialog);
  }

  // 更新属性与回调
  cachedConfirmDialog.setAttribute("style", "z-index: 999999 !important;");
  cachedConfirmDialog.setAttribute('header', headerTitle);
  cachedConfirmDialog.setAttribute('message', msg);
  cachedConfirmDialog.setAttribute('confirm-text', confirmText);
  cachedConfirmDialog.setAttribute('reject-text', rejectText);
  cachedConfirmDialog.setAttribute('reject-button-visible', rejectText ? 'true' : 'false');

  if (currentConfirmHandler) {
    cachedConfirmDialog.removeEventListener('confirm', currentConfirmHandler);
  }
  if (currentRejectHandler) {
    cachedConfirmDialog.removeEventListener('reject', currentRejectHandler);
  }

  // 新事件处理函数
  currentConfirmHandler = () => {
    confirmCB?.();
    cachedConfirmDialog.opened = false;
  };
  currentRejectHandler = () => {
    rejectCB?.();
    cachedConfirmDialog.opened = false;
  };

  // 绑定新回调
  cachedConfirmDialog.addEventListener('confirm', currentConfirmHandler);
  cachedConfirmDialog.addEventListener('reject', currentRejectHandler);

  cachedConfirmDialog.opened = true;
}

export function mallocStr(str: string) {
  const Module = (window as any).Module;
  const strBufferCommitFailId = new TextEncoder().encode(str);
  const strCommitFailId = Module._malloc(strBufferCommitFailId.length + 1);
  Module.HEAPU8.set(strBufferCommitFailId, strCommitFailId);
  Module.HEAPU8[strCommitFailId + strBufferCommitFailId.length] = 0;

  return strCommitFailId
}

export function isInIframe() {
  try {
    return window.self !== window.top;
  } catch (e) {
    return true;
  }
}
export function safeCloseWindow() {
  window.close();
  // 防止立即关闭前 JS 还没反应，稍等检测
  setTimeout(() => {
    try {
      if (!window.closed) {
        alert(t("此页面无法被自动关闭，请手动关闭浏览器标签页。"));
      }
    } catch (e) {
      alert(t("此页面无法被自动关闭，请手动关闭浏览器标签页。"));
    }
  }, 300)
}