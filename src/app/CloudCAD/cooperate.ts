import { createSocketRequest, createHttpRequest } from '../common/service'
import { ERUNING_STATUS } from './constant'
import { writeDocBuffer, errorTips, promiseWrap, showToast } from '../common/utils'
import {CENOTICE_PREFIX}  from "../../utils/socket/socket"
import SocketByWorker from "../../utils/socket/socketWW"
import t from '../common/i18n'

declare let window: any & typeof globalThis;
interface IIncrData {data: Uint8Array, commitId: number|string, editor: number|string, isIncr: boolean, command: string, lastCommitId: number|string
  position: string
  ignoreMiss?: boolean
}

export default async (params:any, wc:any, cloudCAD:any, socket: SocketByWorker) => {
  const { h_autoSave, h_downloadFileBySid, h_batchUserInfo, h_downloadImage } = createHttpRequest(params)
  // 开图期间的状态
  const state: {[key:string]: any} = {
    cadIsLoaded: false,
    baseCommitID: null,
    incrSid: null,
    incrRecordId: null,
    docSid: null,
    did: null,
    runingStatus: ERUNING_STATUS.NO_JOIN_ROOM
  }
  const userInfo = params.userInfo
  // socket请求响应相关的通用参数
  const socketReqParams = {
    uid: userInfo.id,
    username: userInfo.user_name,
    did: params.did,
    ename: '',
    room_id: '',
    file_id: Number(params.fileId)
  }
  // socket 请求响应接口
  const { s_joinRoom, s_getFileInfo, s_getMembers, s_submitIncr, s_saveVersion, s_getIncr, } = createSocketRequest(socket, state, socketReqParams)

  // 初始化服务端连接
  const initConnection = async () => {
    // if (errnum === 0) {
    //   errnum = 1
    //   console.log('模拟加入房间失败')
    //   throw new Error('加入房间失败')
    // }
    // 未加入房间，则加入房间
    if (state.runingStatus < ERUNING_STATUS.IN_ROOM) {
      await s_joinRoom()
      state.runingStatus = ERUNING_STATUS.IN_ROOM
    }

    // 未下载文件，则获取下载文件
    if (state.runingStatus < ERUNING_STATUS.GET_FILE_OK) {
      const {docSid, type} = await s_getFileInfo()
      // if (errnum === 1) {
      //   errnum = 2
      //   console.log('模拟获取文件信息失败')
      //   throw new Error('获取文件信息失败')
      // }
      const docBuffer = await h_downloadFileBySid({storageId: docSid, type})
      // if (errnum === 2) {
      //   errnum = 3
      //   console.log('模拟下载文件失败')
      //   throw new Error('下载文件失败')
      // }
      await writeDocBuffer(docBuffer, params.documentLocalPath)

      state.runingStatus = ERUNING_STATUS.GET_FILE_OK
    }
  }

  const handleInitError = () => {
    wc.wc_errorRetry.tips = t('初始化失败，请稍后再试！')
    wc.wc_errorRetry.isopen = 'true'
    return new Promise((resolve, reject) => {
      // 错误重试回调
      wc.wc_errorRetry.handleErrorRetry = async () => {
        const [err] = await promiseWrap(initConnection())
        if (!err) {
          wc.wc_errorRetry.isopen = 'false'
          resolve(true)
        }
      }
    })
  }
  // 图纸重新渲染
  const handleRebase = () => {
    location.reload()
  }

  try {
    await initConnection()
  } catch(err) {
    console.error('协作连接失败/获取文件失败', err)
    wc.wc_loading.setAttribute('isopen', 'false')
    // 连接获取图纸错误进入异常重试处理, 重试不成功不会向下走
    await handleInitError()
    wc.wc_loading.setAttribute('isopen', 'true')
  }

  // 获取成员列表
  const userListCache:any = {}
  const updateUserList = () => {
    // 筛出需要获取用户信息的用户ids
    const getUserInfoByIds = (idList: string[]) => {
      return Array.from(new Set(idList)).filter((v) => userListCache[v] === undefined).join(',')
    }
    s_getMembers().then(async (memList) => {
      console.log('房间成员', memList)
      const idList = memList.map(mem => mem.uid)
      const ids = getUserInfoByIds(idList)
      if (ids) {
        const userList = await h_batchUserInfo(ids)
        console.log('房间用户信息列表', userList)
        // 补充头像缓存
        for (const userinfo of userList) {
          if (userinfo.avatar) {
            try {
              const buf = await h_downloadImage(userinfo.avatar);
              const url = URL.createObjectURL(new Blob([buf]));
              userinfo.avatarImg = url
            } catch (err) { /* empty */ }
          } else {
            userinfo.avatarBgColor = `${Math.floor(Math.random() * 255) + ''}, ${Math.floor(Math.random() * 255) + ''}, ${Math.floor(Math.random() * 255) + ''}`
          }
          if (!userListCache[userinfo.id]) userListCache[userinfo.id] = userinfo
        }
      }
      // 组装数据给显示组件
      const userList = memList.map(mem => {
        return {
          ...mem,
          avatar: userListCache[mem.uid]?.avatarImg || '',
          avatarBgColor: userListCache[mem.uid]?.avatarBgColor || '255,255,255'
        }
      })
      wc.wc_userAvatar.userList = userList
    }).catch(err => {
      console.error('获取房间成员失败,3s后自动重试', err)
      setTimeout(() => {
        updateUserList()
      }, 3000)
    })
  }
  updateUserList()

  // 订阅增量通知
  let incrTemp: {
    [key: number|string]: IIncrData
  } = {} // 增量暂存
  // 待合并增量数据列表
  let incrMergeList:IIncrData[] = []
  // 缺失增量逻辑
  const reqIncr = (() => {
    let state = 'free' // free getting
    let retry = 0 // 失败重试次数

    function req(params: {commitId: string | number, lastCommitId: string | number}) {
      state = 'getting'
      s_getIncr(params).then((res) => {
        state = 'free'
        retry = 0
        console.warn('------------------=======', params, res)
        if (res.code === 0) {
          res.list.forEach((incr:any, i:number) => {
            const isIncr = incr.dwi ? true : false
            const buffer = Uint8Array.from(atob(incr.dwi || incr.map), c => c.charCodeAt(0))
            const resParam:IIncrData = {
              data: buffer,
              isIncr,
              commitId: incr.commit_id,
              editor: incr.editor,
              command: incr.command,
              position: incr.position,
              lastCommitId: incr.last_commit_id
            }
            if (i !== res.list.length - 1) {
              resParam.ignoreMiss = true
            }
            handleIncrMerge(resParam)
          })
        }
      }).catch(err => {
        if (retry < 2) {
          retry++
          req(params)
        } else {
          state = 'free'
          retry = 0
        }
      })
    }

    return (params: {commitId: string | number, lastCommitId: string | number}) => {
      if (state === 'free') {
        console.log('111111111111实际请求缺失增量', params)
        req(params)
      }
    }

  })()
  // 收到增量数据回调
  const handleIncrMerge = (data: IIncrData) => {
    console.log('处理增量数据', data)
    const { commitId } = data
    // 如果已经初始化好了直接给云CAD合并
    if (state.cadIsLoaded) {
      incrMergeList.push(data)
    } else {
      incrTemp[commitId] = data
    }
  }

  socket.on(CENOTICE_PREFIX+'ce.rooms.notice.converted.map', (ename, data) => {
    data.isIncr = false
    handleIncrMerge(data)
    
  })
  socket.on(CENOTICE_PREFIX+'ce.rooms.notice.converted.dwi', (ename, data) => {
    data.isIncr = true
    const { editor, command, position, commitId, lastCommitId } = data
    let msg = `${userListCache[editor].user_name} `
    if (position) msg += t(`在{position}`, { position })
    msg += t(`执行了 {command} 命令`, { command })
    // console.warn(msg)
    showToast(msg)
    if (window.__mockIncrMiss && commitId % 3 >= 1) {
      console.warn('跳过底层合并', commitId, lastCommitId)
      return
    }
    handleIncrMerge(data)
  })
  // 订阅合并失败提示
  socket.on(CENOTICE_PREFIX+'ce.rooms.notice.merge.failed', (ename, data) => {
    // rebase
    errorTips(t('增量合并失败'))
    handleRebase()
  })
  // 订阅进入房间
  socket.on(CENOTICE_PREFIX+'ce.rooms.notice.join', (ename, data) => {
    updateUserList()
  })
  socket.on(CENOTICE_PREFIX+'ce.rooms.notice.leave', (ename, data) => {
    updateUserList()
  })
  // 长连断联处理
  socket.connStatusChange = (isConnected: boolean) => {
    if (!isConnected) {
      wc.wc_loading.tips = t('连接断开，重连中...')
      wc.wc_loading.isopen = 'true'
    } else {
      wc.wc_loading.isopen = 'false'
      // rebase
      handleRebase()
    }
  }

  // 增量上传处理
  cloudCAD.externalMount.uploadModifyRecord = async (data:any) => {
    data.baseCommitID = state.baseCommitID
    data.did = state.did
    data.folderId = params.folderId
    data.fileId = params.fileId
    data.sid = state.docSid
    let [err, res] = await promiseWrap(h_autoSave(data))
    console.log('增量处理结果', res, err)
    if (err) {
      throw err
    }

    // const incrSid = state.incrSid = res?.storageId
    // if (!incrSid) {
    //   throw new Error('增量上传成功，但没有storageId')
    // }
    const recordId = state.incrRecordId = res?.recordId;

    if (!recordId) {
      throw new Error(t('增量上传成功，但没有recordId'))
    }

    const uploadEventParams:any = {
      cmd: data.cmd,
      curPt: data.curPt,
      recordId: recordId
    };
    [err] = await promiseWrap(s_submitIncr(uploadEventParams))
    if (err) {
      throw err
    }
    
    return recordId
  }

  // 增量上传回调函数
  cloudCAD.externalMount.uploadModifyRecordCallback = (data: any) => {
    // isSave true 单人编辑抢占权限 保存成功，关闭窗口
    // if(data.isSave) 
  }
  // 保存版本处理
  cloudCAD.externalMount.saveVersion = async (data:any) => {
    data.sid = state.incrSid
    data.fileId = params.fileId
    const [err, res] = await promiseWrap(s_saveVersion(data.tag))

    if (err) {
      errorTips(err)
      return Promise.reject(err)
    }
    return true
  }


  // 需要在cloudcad启动完成后执行
  const cooperateCallback = () => {
    // 协同编辑回调
    state.cadIsLoaded = true

    // 开图成功后，告知底层图纸的baseCommitId
    console.warn("Module._gcloud_init_Commitid======", state.baseCommitID)
    try {
      const resInitCommitId = window.Module._gcloud_Init_Commitid(state.baseCommitID)
      if (!resInitCommitId) {
        console.warn("Module._gcloud_init_Commitid fail ", state.baseCommitID)
      }
    } catch(err) {
      console.warn("Module._gcloud_init_Commitid err ", err)
    }
    // 合并启动过程中接收的增量数据
    if (Object.keys(incrTemp).length > 0) {
      const incrList = Object.keys(incrTemp).filter((commitId) => commitId > state.baseCommitID).map(commitId => incrTemp[commitId])
      for (const incr of incrList) {
        handleIncrMerge(incr)
      }
      incrTemp = {}
    }

    // 底层增量合并轮询
    setInterval(() => {
      if ((window as any).Module && (window as any).Module?._gcloud_Callback_IncrMerge && incrMergeList.length) {
        if (!cloudCAD.isEditing) {
          console.log('0000000000000000000000执行合并', incrMergeList)
          incrMergeList.forEach(mergeData => {
            console.log('0000000000000000000000执行合并中', mergeData.data, mergeData.data.length, mergeData.isIncr);
            // 将js buffer存入wasm内存, 进行参数传递
            const Module = (window as any).Module;
            const ptr_buffer = Module._malloc(mergeData.data.length);
            Module.HEAPU8.set(mergeData.data, ptr_buffer);
            try {
              const mergeRes = Module._gcloud_Callback_IncrMerge(ptr_buffer, mergeData.data.length, mergeData.isIncr, mergeData.commitId, mergeData.lastCommitId)
              console.log('77777777777777', mergeRes)
              if (mergeRes > 0 && !mergeData.ignoreMiss) {
                console.log('111111111111缺失增量', mergeRes, mergeData.commitId)
                reqIncr({
                  commitId: mergeData.commitId,
                  lastCommitId: mergeRes
                })
              }
            } catch(err) {
              console.error('本地增量合并异常', mergeData.data, mergeData.isIncr)
            }
            Module._free(ptr_buffer);
          })
          incrMergeList = []
          console.log('0000000000000000000000执行完成')
        } else {
          console.log('0000000000000000000000有待合并数据', incrMergeList.length)
        }
      }
    }, 1000)
  }

  return cooperateCallback
}