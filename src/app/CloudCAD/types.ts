export interface ICADPARAMS {
  wrapId: string
  token: string
  did: string
  fileId: string
  folderId: string
  fileName: string
  documentCloudPath: string
  documentLocalPath: string
  iconUrl: string
  baseUrl: string
  fetch_listUrl: string
  s1Url: string
  s2Url: string
  isCooperate: boolean
  isDownloadFile: boolean
  enableHeader: boolean
  showCreateVersionButton?: boolean
  showSaveStatus?: boolean
  hideLoading: boolean
  uid?: number
  logLevel?: number
  wc: {
    [key: string]: HTMLElement
  }
  externalMount: {
    [key: string]: any
  },
  breadcrumbList: {
    title: string
    href: string
  }[]
}

// 外部接口错误
export enum EExternalInterfaceError {
  saveSnapshot,
}

export interface IGlobalConfig {
  api_endpoint: string;
  key?: string;
  openDrawTab: string;
  isCooperate?: boolean
  server_is_install?: boolean;
  socketIo_url: string;
}

export interface IUserInfo {
  version: number;
  id: string;
  token: string;
  user_name: string;
  fullname: string;
  groups: Recordable[];
  email: string;
  phone: string;
  avatar: string;
  avatarUrl?: string;
  key?: string;
  auth: {
    auths: Recordable[];
    id: number;
    menu: string;
  }[];
  menu: Recordable[];
}

export interface IFileInfo {
  lastModifyTime: string;
  etag: string;
  size: number;
  metaId: number;
  sid: number;
  thumbnail: string;
  id: number;
  createTime: string;
  parentId: number;
  name: string;
  ownerId: string;
  ownerName: string;
  lastEditor: string;
  permType: number;
  fileType: number;
  version: string;
  specialFunctions: string[];
  fileHaveAlter: boolean;
  noteHaveAlter: boolean;
  enable: number;
  userTag: string;
  currEditUser: string;
}
// 文件类型
export const FILETYPE = {
  FOLDER: 1, // 文件夹
  DWG: 2,
  RVT: 3,
  TAR_GZ: 4,
  ZIP: 5,
  PDF: 6,
  DWT: 7,
  DWS: 8,
  PNG: 11,
  RAR: 12,
  TAR: 13,
  BMP: 23,
  JPEG: 24,
  SVG: 25,
  STL: 79,
  TTF: 200,
  TTC: 201,
  OTF: 202,
  SHX: 203,
  DWF: 54,
};
export const FILE_TYPE_NAME = {
  [FILETYPE.FOLDER]: 'folder',
  [FILETYPE.DWG]: 'dwg',
  [FILETYPE.RVT]: 'rvt',
  [FILETYPE.TAR_GZ]: 'tar.gz',
  [FILETYPE.ZIP]: 'zip',
  [FILETYPE.PDF]: 'pdf',
  [FILETYPE.DWT]: 'dwt',
  [FILETYPE.DWS]: 'dws',
  [FILETYPE.PNG]: 'png',
  [FILETYPE.RAR]: 'rar',
  [FILETYPE.TAR]: 'tar',
  [FILETYPE.BMP]: 'bmp',
  [FILETYPE.JPEG]: 'jpeg',
  [FILETYPE.SVG]: 'svg',
  [FILETYPE.STL]: 'stl',
  [FILETYPE.TTF]: 'ttf',
  [FILETYPE.TTC]: 'ttc',
  [FILETYPE.OTF]: 'otf',
  [FILETYPE.SHX]: 'shx',
  [FILETYPE.DWF]: 'dwf'
}