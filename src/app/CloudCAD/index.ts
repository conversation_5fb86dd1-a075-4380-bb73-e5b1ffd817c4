/* eslint-disable @typescript-eslint/no-empty-function */
import { DRAWVIEWPATH } from './constant'
import { errorTips, createDeviceid, promiseWrap, getConfig, showToast, getBreadcrumbs, closePage, getQuery, setQuery, createInactivityWatcher, showConfirmDialog, mallocStr } from '../common/utils'
import WCLoading from './webComponents/Loading'
import WCUserAvatar from './webComponents/UserAvatar'
import ErrorRetry from './webComponents/ErrorRetry'
import cooperate from './cooperate'
import single from './single'
import { createHttpRequest } from '../common/service'
import SocketByWorker from "../../utils/socket/socketWW"

import initCad, { createCADExternalMount, getIndexedDBValue, showCannotEditDialog } from './initCad'
import { EExternalInterfaceError } from './types'
import type { ICADPARAMS, IGlobalConfig } from './types'
import { isNetworkError } from '@/utils/httpUtil'
import { editEntry } from "@/utils/CloudCadUtils"
import '../common/style.scss'
import t from '../common/i18n'

import pubsub from './pubsub.js'

declare let window: any & typeof globalThis;
// 输出webclient版本
declare const __CLIENT_VERSION__: string;
// 初始化web component组件
const initWC = (wrap: HTMLElement) => {
  // 用户头像组件
  const wc_userAvatar = new WCUserAvatar()
  wrap.appendChild(wc_userAvatar)
  // 挂载loading组件
  const wc_loading = new WCLoading()
  wrap.appendChild(wc_loading)
  // 挂在错误重连组件
  const wc_errorRetry = new ErrorRetry()
  wrap.appendChild(wc_errorRetry)

  return {
    wc_userAvatar,
    wc_loading,
    wc_errorRetry,
  }
}

const startGStarCloudCAD = async (initParams: ICADPARAMS) => {
  const {record: recordTime} = window.__GSTAR_CLOUD_CAD__.execTime
  recordTime('CloudCADBoot start', 1)
  window.sdkver = __CLIENT_VERSION__
  // 地址栏带有cannotEditCode 表示是页面已失效
  const cannotEditCode = getQuery("cannotEditCode")
  if (cannotEditCode) {
    showCannotEditDialog(cannotEditCode as any)
    return
  }

  // 监听页面不活动
  let inactivityWatcher: { stop: () => void, reset: () => void } | null = null
  const startTime = +new Date()
  // 创建设备id
  const did = (await createDeviceid()) as string
  // 方便调试验证，将did放到全局变量
  document.body.setAttribute('data-did', did)

  const globalConfig: IGlobalConfig = await getConfig()

  const { h_getSelfUserInfo } = createHttpRequest({
    token: initParams.token,
    api_endpoint: globalConfig.api_endpoint
  })

  const userInfo = await h_getSelfUserInfo()
  const { h_getFileInfo, h_downloadImage, h_getCADConfig, h_uploadFile, h_requestEdit, h_addHistoryTag } = createHttpRequest({
    token: userInfo.token,
    api_endpoint: globalConfig.api_endpoint
  })
  recordTime('CloudCADBoot h_getSelfUserInfo finish', 1)

  // 注册beforeUnload,离开页面逻辑
  beforeCallback({
    id: initParams.fileId,
    token: userInfo.token,
    devId: did,
    globalConfig
  })

  let fileInfoRes: any
  try {
    const { data } = await h_getFileInfo(initParams.fileId)
    fileInfoRes = data
  } catch (err) {
    console.error(t('获取文件信息失败'), err)
    throw new Error(t('获取文件信息失败, 请返回文件列表重试！'))
  }
  recordTime('CloudCADBoot h_getFileInfo', 1)

  const { FileInfo: fileInfo, crumbsList } = fileInfoRes

  crumbsList.unshift({
    folderId: 2,
    name: t('文件库')
  })

  // web component 组件
  const wc = initWC(document.body)
  recordTime('CloudCADBoot initWC finish', 1)
  // socket连接文件加锁
  const socket = new SocketByWorker()

  let socketDisconnectInProcess = false // socket断连处理中
  // 监听断联
  socket.handleDisConnect = () => {
    if (!socketDisconnectInProcess) { // 如果socket断连中，则不再处理
      socketDisconnectInProcess = true
      if (!socket.reConnect) { // 如果socket是主动断联补充连，则不进行处理
        return
      }

      // 断连界面禁止操作
      wc.wc_loading.setAttribute('tips', t('连接断开，重连中...'))
      wc.wc_loading.setAttribute('isopen', 'true')
      // 等待新的连接结果
      socket.handleConnRes = (payload: any) => {
        console.log('socket连接结果', payload);
        // 判断成功或者失败
        if (payload.code === 0) {
          console.log('socket连接成功, 尝试加锁')
          editLock().then((_res) => {
            wc.wc_loading.setAttribute('tips', '')
            wc.wc_loading.setAttribute('isopen', 'false')
          }).catch(err => {
            wc.wc_loading.setAttribute('tips', err || t('加锁失败{error}, 请返回文件列表重试！', { error: err ? `(${err})` : '' }))
          })
        } else {
          wc.wc_loading.setAttribute('tips', t('连接失败{msg}, 请返回文件列表重试！', { msg: payload.msg ? `(${payload.msg})` : '' }))
        }

        socketDisconnectInProcess = false // 重置断连处理状态
      }
    }

  }

  // 监听被踢出事件
  const onRaise = () => {
    socket.on("file.edit.raise", (ename, data) => {
      wc.wc_loading.setAttribute('tips', '')
      wc.wc_loading.setAttribute('isopen', 'false')
      wc.wc_loading = null

      setQuery("cannotEditCode", '2')
      showCannotEditDialog('2')
    })
  }

  // 监听token失效
  socket.on("user.token.invalid", (ename, data) => {
    wc.wc_loading.setAttribute('tips', '')
    wc.wc_loading.setAttribute('isopen', 'false')
    wc.wc_loading = null

    setQuery("cannotEditCode", '3')
    showCannotEditDialog('3')
  })

  // 监听服务器要求断联事件
  const forceSave = (saveFlag: string) => {
    // 由于保存的触发和实际保存是两个地方，由此通过触发传递 saveFlag 在保存完成后事件传出 saveFlag 来知晓此次保存流程的完成
    return new Promise((resolve, reject) => {
      const str = JSON.stringify({
        saveFlag
      })

      pubsub.subscribeOnce(`app.saveFinish.${saveFlag}`, (ename: string, payload: any) => {
        // 判断保存结果
        if (payload.saveResult) {
          return resolve(true)
        } else {
          return reject(payload)
        }
      })
      try {
        const malloc = mallocStr(str)
        window.Module._gcloud_data_commit(true, malloc)
      } catch (err) {
        return reject(err)
      }
    })
  }

  // 保存成功逻辑
  function saveSuccess() {
    wc.wc_loading.setAttribute('isopen', 'false')
    // 主动断连
    socket.close()

    showConfirmDialog({
      headerTitle: t('通知'),
      confirmText: t('退出'),
      msg: t('服务器已断开联接，修改的数据已保存成草稿，可以重新进入编辑继续之前工作。'),
      confirmCB: () => {
        window.parent.postMessage({
          action: `notice.exit`,
          payload: {
          }
        }, '*')
        wc.wc_loading.setAttribute('tips', t('退出中...'))
        wc.wc_loading.setAttribute('isopen', 'true')
      }
    })
  }
  const handleServerDisConn = async (saveSuccess: Function) => {

    // 取消不活动的监听
    inactivityWatcher?.stop?.()

    wc.wc_loading.setAttribute('tips', t('数据保存中，请勿关闭程序'))
    wc.wc_loading.setAttribute('isopen', 'true')

    // 检查是否有正在执行的命令 true :无，flase：有
    const isEditIng = !window.Module._gcloud_isQuiescent()
    console.log('handleServerDisConn')
    // 检查是否需要保存
    const flag = (window.Module && window.Module._gcloud_isDbMod && window.Module._gcloud_isDbMod()) || false
    // 保存
    if (flag) {
      // 强制保存
      try {
        const saveFlag = 'forceSave_' + new Date()
        await forceSave(saveFlag)
      } catch (saveFailData) {
        console.warn(t('保存失败！'), saveFailData)
        // 保存失败重试
        // 保存失败弹框
        const showSaveFailConfirm = (saveFailData: any) => {
          showConfirmDialog({
            headerTitle: t('通知'),
            confirmText: t('重试'),
            rejectText: t('退出'),
            msg: t('服务器已断开联接，自动保存数据失败，请重试保存或退出编辑'),
            confirmCB: () => {
              const { saveFunc, saveParams } = saveFailData
              saveFunc(saveParams)
                .then(saveSuccess)
                .catch((_saveFailData: any) => {
                  showSaveFailConfirm(_saveFailData)
                })
            },
            rejectCB: () => {
              wc.wc_loading.setAttribute('isopen', 'false')
              // 主动断连
              socket.close()
              window.parent.postMessage({
                action: `notice.exit`,
                payload: {
                }
              }, '*')
              wc.wc_loading.setAttribute('tips', t('退出中...'))
              wc.wc_loading.setAttribute('isopen', 'true')
            }
          })
        }
        return showSaveFailConfirm(saveFailData)
      }
    }
    

    saveSuccess()
  }
  socket.on("file.edit.machine.recycling", () => handleServerDisConn(saveSuccess))

  // 编辑加锁
  function editLock() {
    const gstarDeviceId = localStorage.getItem('gstarDeviceId')

    return new Promise((resolve, reject) => {
      let client_id = localStorage.getItem('client_id')
      if (!client_id) {
        client_id = new Date().getTime() + ''
        localStorage.setItem('client_id', client_id)
      }
      h_requestEdit({
        fileId: fileInfo.id,
        devId: did ?? '',
        browser_id: gstarDeviceId,
        client_id,
      }).then(result => {
        if (result.code === 0) {
          console.log(t('编辑加锁成功'), result)
        } else {
          const { editor, editSessionId, uid } = result.data
          console.warn('编辑加锁失败, 判断是否是其他用户加的锁', result)
          if ((uid !== userInfo.id?.toString()) && (uid !== initParams.uid?.toString())) { // 编辑加锁失败，并且加锁人不是自己
            const errMsg = result.msg || ''
            return reject(errMsg)
          }
        }
        return resolve(true)
      }).catch(err => {
        // 加锁请求异常
        console.error(err)
        return reject('')
      })
    })
  }

  let client_id: any = null

  function socketConnect() {
    return new Promise((resolve, reject) => {
      const gstarDeviceId = localStorage.getItem('gstarDeviceId')

      // 连接成功回调
      socket.handleConnRes = (payload: any) => {
        console.log('socket连接结果', payload);
        onRaise()
        // 判断成功或者失败
        if (payload.code === 0) {
          console.log('socket连接成功, 尝试加锁')
          if (payload?.browser_id) {
            localStorage.setItem('gstarDeviceId', payload?.browser_id)
          }
          editLock().then((_res) => {
            resolve(true) // 长连和加锁同时成功
          }).catch(err => {
            reject(err)
          })
        } else {
          return reject(payload.msg)
        }
      }

      socket.connect({
        token: userInfo.token,
        url: globalConfig.socketIo_url,
        key: userInfo.key || globalConfig.key || '',
        uid: userInfo.id || initParams.uid?.toString() || '',
        uname: userInfo.user_name,
        did: did,
        conn_type: 1,
        fileId: initParams.fileId,
        apply_id: gstarDeviceId ? false : true,
        client_id,
        browser_id: gstarDeviceId || ""
      })
    })
  }
  try {
    client_id = localStorage.getItem('client_id')

    if (!client_id) {
      client_id = new Date().getTime() + ''
      localStorage.setItem('client_id', client_id)
    }

    await socketConnect()
  } catch (msg: any) {
    console.error(t('socket连接/加锁失败'), msg)
    socket.close()
    throw new Error(t('连接失败, 请返回文件列表重试！ {msg}', { msg }))
  }
  recordTime('CloudCADBoot socketConnect finish', 1)

  // 补全用户头像
  try {
    if (userInfo.avatar) {
      const buf = await h_downloadImage(userInfo.avatar);
      const url = URL.createObjectURL(new Blob([buf]));
      userInfo.avatarUrl = url
    }
  } catch (err) { /* empty */ }

  recordTime('CloudCADBoot h_downloadImage finish', 1)

  console.log(t('用户信息'), userInfo)

  if (!initParams.hideLoading) wc.wc_loading.setAttribute('isopen', 'true')

  initParams.documentLocalPath = `${DRAWVIEWPATH}${did}.dwg`

  // 获取面包屑
  const breadcrumbList = getBreadcrumbs(crumbsList)
  recordTime('CloudCADBoot getBreadcrumbs finish', 1)
  breadcrumbList[0].title = t("文件库")

  // cfg_param 补充云CAD编辑配置参数
  let cadConfig = {
    edit_type: 'single',
    save_classify: 1, // 1是按快照保存,0是按增量保存
    force_disconnect_timeout: 20 * 60, // 超时未操作主动断联时间s
    single: {
      save_cycle: 354,
      save_type: 0
    }
  }
  try {
    const { data } = await h_getCADConfig()
    if (data && data.edit_type) {
      cadConfig = { ...cadConfig, ...data }
    } else {
      throw new Error(t('获取云CAD配置失败'))
    }
  } catch (err) {
    console.error(t('获取云CAD配置失败'), err)
  }
  recordTime('CloudCADBoot h_getCADConfig finish', 1)

  const externalMount = createCADExternalMount(globalConfig, userInfo, fileInfo)

  const [errInitCad, { cloudCAD, cfg_param, invoke_callBack }] = await promiseWrap(initCad({
    ...initParams,
    did,
    wc,
    breadcrumbList,
    externalMount: {
      ...externalMount,
      ...(initParams.externalMount)
    },
    fileName: fileInfo.name,
    fileId: Number(fileInfo.id),
    ...fileInfo
  }, userInfo))

  if (errInitCad) {
    // 云CAD插件初始化失败就不用走其他的了
    throw new Error(t('cloudCAD init error!'))
  }

  recordTime('CloudCADBoot initCad finish', 1)

  // cloudCAD增加 openEditPage 方法
  // 单人编辑抢占逻辑所需参数
  const cloudCadUtilsParams = {
    serverHost: globalConfig.api_endpoint,
    token: userInfo.token,
    uid: userInfo.id,
    uname: userInfo.user_name
  }
  cloudCAD.openEditPage = (fileId: any) => {
    console.log(t('点击了编辑'))
    const isSingle = cadConfig.edit_type === 'single'
    editEntry(cloudCadUtilsParams, fileId, isSingle)

  }

  // 证书校验回调 验证不通过的话关闭socket
  cloudCAD.pubsub.subscribe('app.wasmMsg.CertStatus', (msg: any, data: any) => {
    console.log('app.wasmMsg.CertStatus', data)
    if (data.type !== 0) {
      socket.close()
    }
  })

  cloudCAD.did = did
  // 底层图纸相关参数补齐
  let cloudPath
  try {
    const breadcrumbList = getBreadcrumbs(crumbsList)
    cloudPath = breadcrumbList.slice(1).map((v: any) => v.title).join('/');
    if (cloudPath && !cloudPath.startsWith('/')) cloudPath = '/' + cloudPath;
    console.log(t('单人编辑cloudPath'), cloudPath)
  } catch (e) {
    console.error(t('cloudPath 从url parse失败'), e)
    cloudPath = '/xxx1.dwg'
  }
  cfg_param.document.cloudPath = initParams.documentCloudPath || cloudPath
  cfg_param.document.localPath = initParams.documentLocalPath || ''
  cfg_param.document.fileId = initParams.fileId
  cfg_param.document.sha1 = fileInfo.etag
  cfg_param.document.did = did
  cfg_param.document.version = fileInfo.version
  cfg_param.document.size = fileInfo.size
  cfg_param.document.sid = fileInfo.sid
  cfg_param.document.metaId = fileInfo.metaId
  cfg_param.document.folderId = fileInfo.parentId
  cfg_param.document.uploadInterval = cadConfig.single.save_cycle
  cfg_param.document.uploadType = cadConfig.save_classify === 1 ? 'snapshot' : 'increment'


  if (cadConfig.save_classify === 1) { // 如果是快照，则保存版本走快照
    cloudCAD.externalMount.saveVersion = (params: any) => {
      return new Promise((resolveMain, rejectMain) => {
        console.log('主动保存', params, cadConfig)
        // eslint-disable-next-line no-async-promise-executor
        const uploadFullFunc = new Promise(async (resolve, reject) => {

          let savePath = window.Module.UTF8ToString(window.Module._gcloud_download_saveAs(false))

          if (savePath) {
            if (savePath === 'CmdRunning') {
              return resolve(t('命令执行中！'))
            }
          } else {
            return reject(new Error('saveAs exception savePath: ' + savePath))
          }

          const error = await (window as any)?.Module?.cfg_param.big_fssync(false)
          if (error) return resolve('Module?.cfg_param.big_fssync error: ' + error)

          let idbRes: any = await getIndexedDBValue(savePath)

          console.log('uploadFullData --- idbRes', idbRes, savePath)

          if (!idbRes) {
            return resolve('getIndexedDBValue error!')
          }

          // 创建 File 对象
          const blob = new Blob([idbRes.contents], {
            type: 'application/octet-stream',
          })
          const file = new File([blob], fileInfo.name)

          h_uploadFile(
            {
              folderId: fileInfo.parentId,
              replace: true,
              file: file,
              tagType: 1,
              userTag: params.tag,
            },
            (evt: any) => {
            }
          )
            .then(res => {
              console.log('h_uploadFile --- res', res)
              if (res.code !== 0) {
                cloudCAD.pubsub.publish('external.interface.error', {
                  type: EExternalInterfaceError.saveSnapshot,
                  code: res.code,
                  msg: res.msg
                })
                throw new Error(res.msg)
              } else {
                resolve(true)
              }
            })
            .catch((e: any) => {
              reject(e)
            })
        })
        // 内部错误为then res 非 true，接口错误为catch e
        uploadFullFunc.then((res) => {
          if (res === true) {
            showToast(t('保存版本成功'), 3000, 'top-center', 'success')
            resolveMain(res)
          } else { // 错误提示
            showToast(t('保存版本失败！'), 3000, 'top-center', 'error')
            rejectMain(res)
          }
        }).catch((e: Error) => {
          console.error(t('保存版本失败！'), e.message)
          // 一些公共错误的统一处理
          let errorMessage = e.message || (e as any).msg
          // if (errorMessage.indexOf("timeout") != -1) {
          //   errorMessage = "网络超时";
          // } else if (isNetworkError(errorMessage)) {
          //   errorMessage = "网络连接错误";
          // }
          showToast(errorMessage, 3000, 'top-center', 'error')
          rejectMain(new Error(errorMessage))
        })
      })
    }
  } else { // 增量保存
    cloudCAD.externalMount.saveVersion = (params: any) => {
      return new Promise((resolve, reject) => {
        // 先调用底层有没有需要保存的进行增量保存
        const str = JSON.stringify({
          type: 'addHistoryTag',
          userTag: params.tag,
        })
        function mallocStr(str: string) {
          const Module = (window as any).Module;
          const strBufferCommitFailId = new TextEncoder().encode(str);
          const strCommitFailId = Module._malloc(strBufferCommitFailId.length + 1);
          Module.HEAPU8.set(strBufferCommitFailId, strCommitFailId);
          Module.HEAPU8[strCommitFailId + strBufferCommitFailId.length] = 0;

          return strCommitFailId
        }

        let res = false

        try {
          const malloc = mallocStr(str)
          res = window.Module._gcloud_data_commit(false, malloc)
          console.warn('gcloud_data_commit', res)
        } catch (e) {
          console.error('gcloud_data_commit error', e)
          return reject(t('保存增量异常！'))
        }

        if (res) { // 如果增量保存成功，则先上传增量，等待增量上传完成在打tag
          pubsub.subscribeOnce('gcloud.upload.finish', (ename: any, payload: any) => {
            if (payload.result === 'ok') {
              h_addHistoryTag({
                baseStorageId: Number(fileInfo.sid),
                fileId: Number(fileInfo.id),
                lastRecordId: payload.recordId, // 使用上传完成的recordId
                userTag: params.tag,
              }).then(() => {
                resolve(true)
              }).catch((error: any) => {
                // errorTips('创建历史版本失败')
                reject(t('创建历史版本失败 {error}', { error: JSON.stringify(error) }))
              })
            } else {
              reject(t('保存失败！{errorMsg}', { errorMsg: payload.errorMsg }))
            }
          })
        } else { // 增量保存失败
          h_addHistoryTag({
            baseStorageId: Number(fileInfo.sid),
            fileId: Number(fileInfo.id),
            lastRecordId: params.recordId,
            userTag: params.tag,
          }).then(() => {
            resolve(true)
          }).catch((error: any) => {
            // errorTips('创建历史版本失败')
            reject(t('创建历史版本失败 {error}', { error: JSON.stringify(error) }))
          })
        }

      })

    }
  }

  recordTime('CloudCADBoot cfg_param finish', 1)

  let completedCallback = () => { }
  const allParams = {
    ...initParams,
    did,
    cfg_param,
    invoke_callBack,
    userInfo,
    token: userInfo.token,
    fileInfo,
    cadConfig,
    ...globalConfig,
  }
  console.log(t('编辑-所有参数'), allParams)
  if (cadConfig.edit_type !== 'single') {
    const [errCooperate, cooperateCallback] = await promiseWrap(cooperate(allParams, wc, cloudCAD, socket))
    if (errCooperate) {
      console.error(t('初始化失败'), errCooperate)
      if (!initParams.hideLoading) wc.wc_loading.setAttribute('isopen', 'true')
      throw new Error(t('协同连接失败！'))
      return
    }

    completedCallback = cooperateCallback
  } else {
    const [errSingle, singleCallback] = await promiseWrap(single(allParams, wc, cloudCAD, socket))
    if (errSingle) {
      console.error(t('初始化失败'), errSingle)
      if (!initParams.hideLoading) wc.wc_loading.setAttribute('isopen', 'true')
      throw new Error(t('单人编辑初始化失败！'))
    }
  }

  recordTime('CloudCADBoot 单人/协同逻辑补充 finish', 1)

  // 监听崩溃刷新，刷新页面并保持did不变
  cloudCAD.pubsub.subscribe('app.needRefresh', (payload: any) => {
    console.warn('前端收到崩溃刷新请求', payload)
    const url = new URL(window.location.href)
    url.searchParams.delete('did')
    url.searchParams.append('did', did)
    console.log(t('拼接请求'), url.href)
    window.location.href = url.href
  })

  // 再次重写自动保存和主动保存的函数，增加保存成功与失败的回调
  const saveVersion = cloudCAD.externalMount.saveVersion
  const uploadModifyRecord = cloudCAD.externalMount.uploadModifyRecord

  cloudCAD.externalMount.saveVersion = (params: any) => {
    return new Promise((resolve, reject) => {
      saveVersion(params)
        .then((res: any) => {
          cloudCAD.pubsub.publish('cloudCAD.event.saveVersion', {
            date: +new Date(),
            did,
            result: true
          })
          resolve(res)
        })
        .catch((err: any) => {
          cloudCAD.pubsub.publish('cloudCAD.event.saveVersion', {
            date: +new Date(),
            did,
            result: false,
            err
          })
          reject(err)
        })
    })
  }
  cloudCAD.externalMount.uploadModifyRecord = (params: any) => {
    return new Promise((resolve, reject) => {
      uploadModifyRecord(params)
        .then((res: any) => {
          // 底层透传前端携带参数存在
          if (params.param) {
            try {
              const param = JSON.parse(params.param)
              if (param.saveFlag) {
                pubsub.publish(`app.saveFinish.${param.saveFlag}`, { saveResult: true })
              } else {
                console.warn('uploadModifyRecord Exception param: ', params.param)
              }
            } catch (err) {
              console.warn('uploadModifyRecord Exception param: ', params.param)
            }
          }
          cloudCAD.pubsub.publish('cloudCAD.event.autoSaveVersion', {
            date: +new Date(),
            did,
            result: true
          })
          resolve(res)
        })
        .catch((err: any) => {
          // 底层透传前端携带参数存在
          if (params.param) {
            try {
              const param = JSON.parse(params.param)
              if (param.saveFlag) {
                pubsub.publish(`app.saveFinish.${param.saveFlag}`, {
                  saveResult: false,
                  saveParams: params,
                  saveFunc: cloudCAD.externalMount.uploadModifyRecord
                })
              } else {
                console.warn('uploadModifyRecord Exception param: ', params.param)
              }
            } catch (err) {
              console.warn('uploadModifyRecord Exception param: ', params.param)
            }
          }
          cloudCAD.pubsub.publish('cloudCAD.event.autoSaveVersion', {
            date: +new Date(),
            did,
            result: false
          })
          reject({
            saveResult: false,
            saveParams: params,
            saveFunc: cloudCAD.externalMount.uploadModifyRecord
          })
        })
    })
  }
  // 初次开图成功后，发出消息
  cloudCAD.pubsub.subscribe('app.wasmMsg.FIRST_RENDER_FINISH_TIME', () => {
    cloudCAD.pubsub.publish('cloudCAD.event.loadFinish', {
      date: +new Date(),
      did,
      loadTime: +new Date() - startTime,
      fileSize: fileInfo.size
    })
  })
  // 命令触发
  cloudCAD.pubsub.subscribe('app.wasmMsg.CMDUSE', (ename: any, payload: any) => {
    cloudCAD.pubsub.publish('cloudCAD.event.executeCommand', {
      date: +new Date(),
      did,
      command: payload
    })
  })

  // 启动cad
  // const startCAD = () => {
  //   return new Promise((resolve, reject) => {
  //     cloudCAD.externalMount.finish = resolve
  //     window.GstarCADBoot.start(cfg_param, invoke_callBack);
  //   })
  // }
  // await startCAD()

  function startFinishCallback() {
    // 开图成功后的处理
    completedCallback()
  
    // 主动未操作超时处理
    function timeoutWithoutOperation() {
      wc.wc_loading.setAttribute('isopen', 'false')
      // 主动断连
      socket.close()
      // 弹框
      showConfirmDialog({
        headerTitle: t('通知'),
        confirmText: t('继续编辑'),
        rejectText: t('退出编辑'),
        msg: t('当前页面长时间没有操作，暂停页面编辑。'),
        confirmCB: () => {
          window.parent.postMessage({
            action: `notice.reEdit`,
            payload: {
            }
          }, '*')
          wc.wc_loading.setAttribute('tips', t('请稍候...'))
          wc.wc_loading.setAttribute('isopen', 'true')
        },
        rejectCB: () => {
          window.parent.postMessage({
            action: `notice.exit`,
            payload: {
            }
          }, '*')
          wc.wc_loading.setAttribute('tips', t('退出中...'))
          wc.wc_loading.setAttribute('isopen', 'true')
        }
      })
    }
    inactivityWatcher = createInactivityWatcher(cadConfig.force_disconnect_timeout, () => handleServerDisConn(timeoutWithoutOperation))
  }

  cloudCAD.externalMount.finish = startFinishCallback

  cloudCAD.start = () => {
    window.GstarCADBoot.start(cfg_param, invoke_callBack)
  }

  recordTime('CloudCADBoot end finish', 1)

  return cloudCAD
}

const beforeCallback = (params: { id: string, token: string, devId: string, globalConfig: IGlobalConfig }) => {
  // const { id, token, devId, globalConfig } = params
  // // 如果是多人编辑  则不需要离开发送leave
  // if (globalConfig.isCooperate) return

  // window.addEventListener('beforeunload', (event: any) => {
  //   if (event.currentTarget.performance.navigation.type !== 1) {
  //     // 关闭图纸的时候需要释放编辑锁
  //     leaveEdit()
  //   }
  // })

  // const leaveEdit = () => {
  //   if ((window as any).navigator.sendBeacon) {
  //     const api_endpoint = `${globalConfig.api_endpoint}/api/v2/_edit/_leave?Authorization=${token}`;
  //     navigator.sendBeacon(api_endpoint, JSON.stringify({
  //       fileId: Number(id),
  //       devId
  //     }));
  //   } else {
  //     fetch(`${globalConfig.api_endpoint}/api/v2/_edit/_leave`, {
  //       headers: {
  //         Authorization: token
  //       },
  //       method: 'POST',
  //       body: JSON.stringify({
  //         fileId: Number(id),
  //         devId
  //       })
  //     })
  //   }
  // }
}

export default startGStarCloudCAD



