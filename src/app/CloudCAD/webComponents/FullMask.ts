
const templateStr = `
<style>
:host {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .3);
  z-index: 999999;
  display: none;
  justify-content: center;
  align-items: center;
}
</style>
`

export default class GCADFullMask extends HTMLElement {
  static observedAttributes = ["isopen"];
  constructor() {
    super()
    const _DOM_template = document.createElement('template');
    _DOM_template.innerHTML = templateStr
    this.attachShadow({ mode: 'open' }).appendChild(_DOM_template.content.cloneNode(true));

  }
  get isopen(): string | null {
    return this.getAttribute('isopen')
  }
  set isopen(v: string) {
    this.setAttribute('isopen', v)
  }
  attributeChangedCallback(name: string, oldVal: any, newVal: any) {
    if (name === 'isopen') {
      this.setAttribute('style', `display:${this.isopen === 'true' ? 'flex' : 'none'};`)
    }
  }
}

// my-element
window.customElements.define('gcad-full-mask', GCADFullMask)
