import FullMask from './FullMask'

const templateStr = `
<style>
.box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.animat-wrap {
  width: 150px;
  height: 150px;
  position: relative;
}
.loading {
  background-image:  url(${import.meta.env.VITE_PUBLIC_PATH}/assets/imgs/loading.png);
  background-repeat: no-repeat;
  width: 150px;
  height: 150px;
  background-size: 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  animation: loadingRotate 6s infinite linear;
}
.logo {
  background-image:  url(${import.meta.env.VITE_PUBLIC_PATH}/assets/imgs/loading_logo.png);
  background-repeat: no-repeat;
  width: 75px;
  height: 75px;
  background-size: 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.tips {
  flex: 0 0 50px;
  height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.tips-text {
  color: white;
}
@keyframes loadingRotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
<section class="box">
  <div class="animat-wrap">
    <div class="loading"></div>
    <div class="logo"></div>
  </div>
  <div class="tips">
    <p class="tips-text"></p>
  </div>
</section>
`

export default class Loading extends FullMask {
  static observedAttributes = [...FullMask.observedAttributes, "tips"];
  _DOM_tips: HTMLElement
  _DOM_tips_text: HTMLElement
  constructor() {
    super()

    const _DOM_template = document.createElement('template');
    _DOM_template.innerHTML = templateStr

    this.shadowRoot?.appendChild(_DOM_template.content.cloneNode(true))

    this._DOM_tips = this.shadowRoot?.querySelector('.tips') as HTMLElement
    this._DOM_tips_text = this.shadowRoot?.querySelector('.tips-text') as HTMLElement
  }
  get tips() {
    return this.getAttribute('tips') || ''
  }
  set tips(v: string) {
    this.setAttribute('tips', v)
  }
  attributeChangedCallback(name: string, oldVal: any, newVal: any) {
    super.attributeChangedCallback(name, oldVal, newVal);
    if (name === 'isopen' && newVal !== 'true') {
      this._DOM_tips_text.innerText = ''
    }
    if (name === 'tips') {
      this._DOM_tips_text.innerText = newVal
    }
  }
}

// my-element
window.customElements.define('gcad-loading', Loading)
