import Loading from './Loading'

const templateStr = `
<style>
.btn[disabled] {
  pointer-events:none;
  cursor: wait;
}
</style>
<button class="btn">重试</button>
`

export default class ErrorRetry extends Loading {
  static observedAttributes = [...Loading.observedAttributes];
  _DOM_btn: HTMLButtonElement
  _handleErrorRetry: (e: Event) => void
  _retryInterval: number
  constructor() {
    super()

    const _DOM_template = document.createElement('template');
    _DOM_template.innerHTML = templateStr

    // this.shadowRoot?.appendChild(_DOM_template.content.cloneNode(true))
    this._DOM_tips.appendChild(_DOM_template.content.cloneNode(true))
    this._DOM_btn = this.shadowRoot?.querySelector('.btn') as HTMLButtonElement
    this._retryInterval = 3

    this._handleErrorRetry = (e: Event) => {
      this.disableInterval()
      this.handleErrorRetry()
    }
  }
  // 上层重写
  handleErrorRetry() {
  }
  // 禁用定时
  private disableInterval() {
    this._DOM_btn.setAttribute('disabled', 'disabled')
    let num = this._retryInterval

    this._DOM_btn.innerText = `重试(${num})`

    const timer = setInterval(() => {
      --num;
      if (num <= 0) {
        clearInterval(timer)
        this._DOM_btn.removeAttribute('disabled')
        this._DOM_btn.innerText = `重试`
      } else {
        this._DOM_btn.innerText = `重试(${num})`
      }
    }, 1000)
  }
  // dom插入完成hook
  connectedCallback() {
    this._DOM_btn.addEventListener('click', this._handleErrorRetry)
  }
  // 组件销毁时hook
  disconnectedCallback() {
    this._DOM_btn.removeEventListener('click', this._handleErrorRetry)
  }
}

// my-element
window.customElements.define('gcad-error-retry', ErrorRetry)
