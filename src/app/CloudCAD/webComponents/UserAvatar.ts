const template = `
<style>
:host {
  position: absolute;
  top: 5px;
  right: 260px;
  width: auto;
  height: 32px;
  z-index: 2;
}
.list {
  width: 100%;
  height: 100%;
  min-width: 22px;
  display: flex;
}
.item {
  flex: 0 0 22px;
  height: 22px;
  width: 22px;
  margin-left: -8px;
  border-radius: 50%;
  background-color: transparent;
  border: 1px solid rgba(255,255,255,.3);
  overflow: hidden;
}
img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.default-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  text-align: center;
  font-size: 22px;
  line-height: 22px;
  cursor: default;
}
</style>
<div class="list">
</div>
`

interface IUserInfo {
  username: string
  avatar: string
  did: string
  avatarBgColor: string
}
type TUserList = IUserInfo[]

export default class UserAvatar extends HTMLElement {
  template: HTMLTemplateElement
  _DOM_list: HTMLElement
  _userList: TUserList
  constructor() {
    super()

    this.template = document.createElement('template');
    this.template.innerHTML = template
    this.attachShadow({ mode: 'open' }).appendChild(this.template.content.cloneNode(true));

    this._DOM_list = this.shadowRoot?.querySelector('.list') as HTMLElement

    this._userList = []
  }

  get userList() {
    return this._userList
  }

  set userList(list) {
    this._userList = list
    this.updateList(list)
  }

  static get observedAttributes() {
    return ['userList'];
  }

  updateList(userlist: TUserList) {
    const listDOM = userlist.map(userInfo => {
      return `
        <div class="item" title="${userInfo.username}">
          ${
            userInfo.avatar ?
            `<img src="${userInfo.avatar}" />` :
            `<div class="default-avatar" style="background-color:rgb(${userInfo.avatarBgColor});">
              ${userInfo.username.split('')[0]}
            </div>`
          }
        </div>
      `
    }).join()
    this._DOM_list.innerHTML = listDOM
  }
  disconnectCallback() {
    this.template.remove()
  }
}

// my-element
window.customElements.define('gcad-user-avatar', UserAvatar)
