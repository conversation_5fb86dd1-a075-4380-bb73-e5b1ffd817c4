import { createHttpRequest } from '../common/service'
import { writeDocBuffer, showToast, promiseWrap, closePage, setQuery, mallocStr } from '../common/utils'
import SocketByWorker from "../../utils/socket/socketWW"
import '@vaadin/confirm-dialog'
import '@vaadin/dialog';
import { dialog } from 'mdui/functions/dialog.js';
import { getIndexedDBValue, showCannotEditDialog } from './initCad'
import t from '../common/i18n'

// import UploadManager from "@/utils/UploadManager"
// import { useUserStore } from "@/stores/user"

// const userStores = useUserStore();


const askEditDialogTime = 30 // 20s
let
  saveDialog: any = null, // 保存提示弹窗
  askEditDialog: any = null, // 询问抢占弹窗
  askEditDialogTimer: any = null, // 询问抢占弹窗计时器
  forcerEndEditorDialog: any = null, // 强制抢占弹窗
  saveStep1Cb = () => { },
  saveStep2Cb = () => { },
  saveStep3Cb = () => { },
  currentData: any = null,
  isStatus = false  // 是否有人抢占编辑

export default async (params: any, wc: any, cloudCAD: any, socket: SocketByWorker) => {
  const { h_downloadFileByFileInfo, h_uploadFile, h_uploadModifyRecord } = createHttpRequest(params)
  clearTime = params?.cadConfig?.wait_timeout || 60

  // 询问是否同意抢占逻辑
  socket.on("file.edit.single.snatch.ask", (ename, data) => {
    if (isStatus) {
      waitEditorNotice(data)
      return
    }

    console.log("recive ============ 1", data)
    // 检查是否有正在执行的命令 true :无，flase：有
    const isEditIng = window.Module._gcloud_isQuiescent()
    if (!isEditIng) {
      saveStep1Cb = createSaveStep1Cb(data, "editing")
      saveStep1Cb()
      return
    }
    currentData = data

    const message = data?.message && JSON.parse(data?.message)
    console.log("message -1", message)

    if (!askEditDialog) {
      // 弹框提示用户关闭当前编辑页
      let _DOM_confirm = document.createElement('vaadin-confirm-dialog') as any
      _DOM_confirm.setAttribute('header-title', t('您已无法编辑文件！'))
      _DOM_confirm.setAttribute('confirm-text', t('是'))
      _DOM_confirm.setAttribute('reject-text', t('否'))
      _DOM_confirm.setAttribute('reject-button-visible', '')
      _DOM_confirm.innerText = t(`{senderName} 要结束你的编辑，是否退出编辑。`, { senderName: message?.sender_name })
      _DOM_confirm.noCloseOnEsc = true

      _DOM_confirm.addEventListener('confirm', () => {
        if (askEditDialogTimer) {
          clearInterval(askEditDialogTimer)
          askEditDialogTimer = null
        }
        _DOM_confirm.remove()
        _DOM_confirm = null
        askEditDialog = null
        handleConfirm(currentData)
      })
      _DOM_confirm.addEventListener('reject', () => {
        if (askEditDialogTimer) {
          clearInterval(askEditDialogTimer)
          askEditDialogTimer = null
        }
        _DOM_confirm.remove()
        _DOM_confirm = null
        askEditDialog = null
        handleReject(currentData)
      })

      askEditDialog = _DOM_confirm
      document.body.appendChild(_DOM_confirm)
    } else {
      askEditDialog.innerText = t(`{senderName} 要结束你的编辑，是否退出编辑。`, { senderName: message?.sender_name })
    }

    askEditDialog.opened = true
    isStatus = true

    // 计时器
    if (askEditDialogTimer) {
      clearInterval(askEditDialogTimer)
      askEditDialogTimer = null
    }
    askEditDialogTimer = function () {
      let time = askEditDialogTime
      return setInterval(() => {
        if (askEditDialog) {
          if (time <= 0) {
            clearInterval(askEditDialogTimer)
            askEditDialogTimer = null
            // 倒计时结束，默认回否
            handleReject(currentData)
            askEditDialog.opened = false
            askEditDialog.setAttribute('reject-text', t(`否`))
            askEditDialog.remove()
            askEditDialog = null
          } else {
            askEditDialog.setAttribute('reject-text', t(`否({time})`, { time: time-- }))
          }
        } else {
          clearInterval(askEditDialogTimer)
          askEditDialogTimer = null
        }
      }, 1000)
    }()
  })

  // 强制结束编辑
  socket.on("file.edit.single.force.snatch", (ename, data) => {
    const message = data?.message && JSON.parse(data?.message)
    goCannotEditDialog(t(`{senderName} 已剥夺您的编辑权限，您被迫退出编辑页面`, { senderName: message?.sender_name }))
  })

  socket.on('file.edit.robbed', (ename, data) => {
    if (params.fileId == data.fileId) {
      console.error('编辑权限被强占', data.fileId)
      // goCannotEditDialog()
      // // 弹框提示用户关闭当前编辑页
      // const _DOM_confirm = document.createElement('vaadin-confirm-dialog') as any
      // _DOM_confirm.setAttribute('header-title', '您已无法编辑文件！')
      // _DOM_confirm.setAttribute('confirm-text', '关闭')
      // _DOM_confirm.innerText = `文件编辑权限已转由用户xxx获取，如需编辑请前往文件列表页重新进入编辑。`
      // _DOM_confirm.noCloseOnEsc = false

      // _DOM_confirm.addEventListener('confirm', () => {
      //   window.close()
      // })
      // _DOM_confirm.addEventListener('cancel', () => {
      //   window.close()
      // })


      // _DOM_confirm.opened = true
      // document.body.appendChild(_DOM_confirm)
    }
  })
  const fileInfo = params.fileInfo
  console.log('单人编辑fileInfo', fileInfo)

  if (params.isDownloadFile) {
    const documentBuffer = await h_downloadFileByFileInfo({
      fileName: fileInfo.name,
      fileId: fileInfo.id,
      reqFrom: 'cloudcad'
    })

    await writeDocBuffer(documentBuffer, params.documentLocalPath)
  }

  cloudCAD.externalMount.uploadModifyRecord = async (data: {
    cmd: string;
    curPt: {
      x: number;
      y: number;
      z: number;
    };
    file: File;
    baseId: number;
    isShot: boolean;
    lastsaveIncrVersion: string;
    cursaveIncrVersion: string;
    param?: string;
  }) => {
    const timeStart = Date.now()
    console.log('触发上传', data, timeStart)
    let param = null
    if (data?.param) {
      param = JSON.parse(data.param)
      if (param?.type === "single") showSaveDialog()
    }

    if (data.isShot) {
      return new Promise((resolve, reject) => {
        // const upload = new UploadManager({
        //   file: data.file, 
        //   etag: data.etag,
        //   type: "file",
        //   folderId: fileInfo.parentId,
        //   replace: true,
        //   token: userStores.token,
        //   tagType: 2
        // })

        // upload.start().then((res:any) => {
        //   console.log("11111111111111111111111", res)
        //   if (res.code === 0) {
        //     if (param && param.type === "single") {
        //       closeSaveDialog()
        //       switch (param.step) { 
        //         case "1":
        //           console.log("同意保存")
        //           saveStep1Cb()
        //           saveStep3Cb()
        //           isStatus = false
        //           goViewHtml()
        //           break;

        //         case "2":
        //           console.log("强制保存")
        //           saveStep2Cb()
        //           saveStep3Cb()
        //           isStatus = false
        //           goViewHtml()
        //           break;
        //       }
        //     }
        //     resolve(true)
        //   }
        // }).catch((err: any) => {
        //   console.log("上传快照错误", err)
        //   if (data?.param) {
        //     closeSaveDialog()
        //     setProcess("err", JSON.stringify(e))
        //   }
        //   reject(false);
        // })

        // 如果是快照
        h_uploadFile(
          {
            folderId: fileInfo.parentId,
            replace: true,
            file: data.file,
            tagType: 2,
          },
          (evt: any) => {
            console.log("上传快照", evt)
            // const progressPercent = ((evt.loaded / evt.total) * 100).toFixed(2);
            // setProcess(progressPercent)

            // if (parseInt(progressPercent) === 100) {

            // } 
          }
        ).then(res => {
          const timeEnd = Date.now();
          console.log('上传完成', timeEnd)
          if (res.code == 0) {

            if (param && param.type === "single") {
              closeSaveDialog()
              switch (param.step) {
                case "1":
                  console.log("同意保存")
                  saveStep1Cb()
                  saveStep3Cb()
                  isStatus = false
                  goCannotEditDialog()
                  break;

                case "2":
                  console.log("强制保存")
                  saveStep2Cb()
                  saveStep3Cb()
                  isStatus = false
                  goCannotEditDialog()
                  break;
              }
            }
            resolve(true);
          } else {
            reject(res)
          }
        }).catch((e: any) => {
          console.log("上传快照错误", e)
          if (param?.type === "single") {
            closeSaveDialog()
            setProcess("err", JSON.stringify(e))
          }
          reject(false);
        });
      });
    } else {
      console.log('上传增量')
      // const upload = new UploadManager({
      //   fileId: fileInfo.id,
      //   etag: data.etag,
      //   type: "modify",
      //   baseId: data.baseId || 0,
      //   srcStorageId: fileInfo.sid || 0,
      //   lastModify: data.lastsaveIncrVersion,
      //   currModify: data.cursaveIncrVersion,
      //   token: userStores.token,
      //   file: data.file
      // })

      const [err, result] = await promiseWrap(
        h_uploadModifyRecord({
          fileId: fileInfo.id,
          baseId: data.baseId || 0,
          srcStorageId: fileInfo.sid || 0,
          lastModify: data.lastsaveIncrVersion,
          currModify: data.cursaveIncrVersion,
          file: data.file,
        })

        // upload.start()
      );
      if (data?.param) {
        closeSaveDialog()
        setTimeout(() => {
          setProcess("100")
        }, 0)
      }

      if (err) {
        console.log('上传增量失败', err);
        if (err?.code === 400681) {
          setProcess("err", t(`文件编辑权限已转由用户{err?.data?.editor}获取，如需编辑请前往文件列表页重新进入编辑。`, { editor: err?.data?.editor }))
        }
        throw new Error('upload error');
      }

      if (result && data?.param) {
        console.log("h_uploadModifyRecord res")
        goCannotEditDialog()
      }

      return result.data?.recordId;
    }
  }

  // 询问是否同意被抢占弹窗的确认
  const handleConfirm = (data: any) => {
    saveStep1Cb = createSaveStep1Cb(data, "ok")
    saveStep3Cb = createSaveStep3Cb(data)

    try {
      // 检查是否需要保存
      const flag = (window.Module && window.Module._gcloud_isDbMod && window.Module._gcloud_isDbMod()) || false
      if (flag) {
        setProcess("start")
        const str = JSON.stringify({
          type: "single",
          step: "1"
        })
        const malloc = mallocStr(str)
        window.Module._gcloud_data_commit(true, malloc)
      } else {
        saveStep1Cb()
        saveStep3Cb()
        isStatus = false
        goCannotEditDialog()
      }
    } catch (err) { // 异常统一按不保存处理
      saveStep1Cb()
      saveStep3Cb()
      isStatus = false
      goCannotEditDialog()
    }
  }

  // 询问是否同意被抢占弹窗的拒绝
  const handleReject = (data: any) => {
    isStatus = false
    saveStep1Cb = createSaveStep1Cb(data, "no")
    saveStep1Cb()
  }
  // 跳转开图页
  const goCannotEditDialog = (msg: string | undefined = undefined) => {
    socket.close()
    setQuery('cannotEditCode', '1')
    // 弹框提示用户关闭当前编辑页
    showCannotEditDialog('1', msg)
  }

  // 正在保存弹窗
  function showSaveDialog() {
    if (!saveDialog) {
      let flag = true
      // 弹框提示用户关闭当前编辑页
      const _DOM_confirm = document.createElement('vaadin-dialog') as any

      _DOM_confirm.renderer = (root: HTMLElement) => {
        if (!root.firstElementChild && flag) {
          flag = false
          _DOM_confirm.setAttribute('header-title', t('无法编辑文件！'))


          // 自定义内容
          const customContent = document.createElement('div');
          customContent.style.width = '400px'
          customContent.classList.add('dialog-container');
          customContent.innerText = t(`正在保存您的编辑，请稍候...`)
          root.appendChild(customContent)
        }
      }

      _DOM_confirm.noCloseOnEsc = true
      _DOM_confirm.noCloseOnOutsideClick = true
      saveDialog = _DOM_confirm
      document.body.appendChild(saveDialog)

    }


    saveDialog.opened = true
  }

  function closeSaveDialog() {
    if (saveDialog) {
      saveDialog.opened = false
    }
  }

  function setProcess(progressPercent: string, error?: string) {
    const params = {
      ename: "send.targeted.message",
      receiver_conn_id: currentData.sender_conn_id,
      sender_conn_id: currentData.receiver_conn_id,
      event_type: "file.edit.single.snatch.save.progress",
      trans_id: createTransId(),
      message: ""
    }

    let temMessage: any = {}

    try {
      temMessage = JSON.parse(currentData.message)
    } catch (error) {
      console.log(error)
    }

    const message = { deviceId: temMessage?.deviceId, progressPercent, error }

    console.log("setProcess", params, currentData, temMessage, message)

    params.message = JSON.stringify(message)
    socket.request(params);
  }

  // 有人正在抢占编辑权限 不允许第三人抢占
  function waitEditorNotice(data: any) {
    const params = {
      ename: "send.targeted.message",
      receiver_conn_id: data.sender_conn_id,
      sender_conn_id: data.receiver_conn_id,
      event_type: data.event_type,
      trans_id: data.trans_id,
      message: ""
    }

    const message = { ...JSON.parse(data.message), "status": "busy" }
    params.message = JSON.stringify(message)
    console.log("waitEditorNotice", params)
    socket.request(params);
  }

  // 询问是否结束编辑状态
  function createSaveStep1Cb(data: any, status: string) {
    return () => {
      const params = {
        ename: "send.targeted.message",
        receiver_conn_id: data.sender_conn_id,
        sender_conn_id: data.receiver_conn_id,
        event_type: "file.edit.single.snatch.ask",
        trans_id: data.trans_id,
        message: ""
      }

      const message = { ...JSON.parse(data.message), "status": status }
      params.message = JSON.stringify(message)
      console.log("createSaveStep1Cb", params)
      socket.request(params);
    }
  }

  function createSaveStep2Cb(data: any, status: string) {
    return () => {
      const params = {
        ename: "send.targeted.message",
        receiver_conn_id: data.sender_conn_id,
        sender_conn_id: data.receiver_conn_id,
        event_type: "file.edit.single.force.snatch",
        trans_id: data.trans_id,
        message: ""
      }

      const message = { ...JSON.parse(data.message), "status": status }
      params.message = JSON.stringify(message)
      console.log("createSaveStep2Cb", params)
      socket.request(params);
    }
  }

  function createSaveStep3Cb(data: any) {
    return () => {
      const params = {
        ename: "send.targeted.message",
        receiver_conn_id: data.sender_conn_id,
        sender_conn_id: data.receiver_conn_id,
        event_type: "file.edit.single.snatch.finish",
        trans_id: data.trans_id,
        message: ""
      }

      const message = { ...JSON.parse(data.message), "status": "finish" }
      params.message = JSON.stringify(message)
      console.log("createSaveStep3Cb", params)
      socket.request(params);
    }
  }

  return true
}

function createTransId() {
  return Date.now() + '-' + Math.floor(Math.random() * 10000000)
}