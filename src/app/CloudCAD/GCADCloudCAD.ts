import pubsub from './pubsub.js'
const polyfill = () => {
  // polyfill WeakRef
  if (!window.WeakRef) {
    (window as any).WeakRef = function (a:any) {
      return {
        deref: function() {
          return a
        }
      }
    }
  }
}

interface IGCADCloudCADPARAMS {
  token: string; // 必须，用于编辑图纸的token
  element: string; //必须，指定要在哪儿显示图形
  param: {
    wrapId?: string; // 兼容WebCAD参数
    //均是可选部分，具体使用细节见后 param参数使用
    fileName?: string; // 可选，显示的文件名称。（该值会参与文件校验）当不指定时，开发商服务器必须采用传递方式1
    cadServer?: string; // 浩辰CAD服务器地址。 可选，当不指定是： https://cloud.gstarcad.com/v2/api
    fileId?: number; // 可选。仅用于后端 PUSH方式。此FILEID是开发商服务器向浩辰服务器PUSH文件时返回的文件ID
  };
}

/**
 * @description 快速集成云编辑页面 sdk
 */
export default async function GCADCloudCAD({
  token, // 必须，用于编辑图纸的token
  element, // 必须，指定要在哪儿显示图形
  param = {}, //均是可选部分，具体使用细节见后 param参数使用
}: IGCADCloudCADPARAMS) {
  polyfill()
  const defaultParam = {
    cadServer: 'https://cloudapi.gstarcad.com',
    // PUSH方式
    fileId: 0,
    fileName: '',
  };
  if (param.cadServer && param.cadServer.endsWith('/')) {
    param.cadServer = param.cadServer.slice(0, -1)
  }
  param = { ...defaultParam, ...param };

  if (!param.cadServer) param.cadServer = 'https://cloudapi.gstarcad.com';

  // 参数校验
  {
    // 必选参数校验
    if (!token) return alert(`参数异常：token=${token}`)
    if (!element) return alert(`参数异常：element=${element}`)
    // fileId 必须有
    if (!param.fileId) return alert(`参数异常：param.fileId=${param.fileId}`)
    // fileId强制转Number
    param.fileId = Number(param.fileId)
  }

  // 获取容器dom
  const _DOM_wrap = document.getElementById(element)
  if (!_DOM_wrap) return alert(`参数异常：id=${element} dom不存在！`)

  // 监听来自 iframe 的消息
  window.addEventListener('message', function(event) {
    // 验证消息的来源，确保安全
    if (event.origin !== param.cadServer) {
      return
    }
    // 打印收到的消息
    console.log('Received message from iframe:', event.data)
    if (event.data.action) {
      pubsub.publish(event.data.action, event.data.payload)
    }
  })
  const _DOM_iframe: HTMLIFrameElement = createIframe(_DOM_wrap)
  try {
    await initIframe(_DOM_iframe, param.cadServer as string)
  } catch(err) {
    return alert(`加载失败`)
  }

  pubsub.subscribe('close', (ename: string, {msg}: any) => {
    if (_DOM_iframe) {
      _DOM_iframe.contentWindow?.close()
      _DOM_wrap.removeChild(_DOM_iframe)
    }
  })

  // 向 iframe 发送消息
  function sendMessageToIframe(msg: any) {
    (_DOM_iframe.contentWindow as any).postMessage(msg, param.cadServer as string)
  }
  function deserializeError(data:any) {
    if (data && data.__isSerializedError) {
      const err = new Error(data.message);
      err.name = data.name;
      err.stack = data.stack;
      return err;
    }
    return data;
  }
  // 封装sendMessage为Promise
  function sendMessage(params: {
    action: string,
    payload: any
  }) {
    const eid = params.action + (+new Date())
    return new Promise((resolve, reject) => {
      pubsub.subscribeOnce(`_sendMessageResponse.${eid}`, (ename: string, data: {status: 'reject' | 'resolve', result: any}) => {
        if (data.status === 'resolve') {
          resolve(data.result)
        } else {
          reject(deserializeError(data.result))
        }
      })
      sendMessageToIframe({
        ...params,
        eid
      })
    })
  }

  const cloudCAD = await sendMessage({
    action: 'main',
    payload: {
      ...param,
      token
    }
  })

  return {
    start: () => {
      return sendMessage({
        action: 'start',
        payload: {}
      })
    },
    on: (eventName: string, cb: any) => {
      return pubsub.subscribe(eventName, cb)
    },
    off: (handle: string | number) => {
      pubsub.unsubscribe(handle)
    },
    downloadImage: () => {
      sendMessageToIframe({
        action: 'call',
        payload: {
          fn: 'pubsub.publish',
          params: 'webui.showSaveImgCanvas2'
        }
      })
    },
    downloadDrawing: () => {
      sendMessageToIframe({
        action: 'call',
        payload: {
          fn: 'pubsub.publish',
          params: 'webui.downloadFile'
        }
      })
    },
    call: (fn: string, params: any) => {
      return sendMessage({
        action: 'call',
        payload: {
          fn,
          params
        }
      })
    }
  }
}

function createIframe(_DOM_wrap: HTMLElement) {
  // 创建iframe
  const _DOM_iframe = document.createElement('iframe')
  _DOM_iframe.setAttribute('style', 'width:100%;height:100%;')
  _DOM_iframe.setAttribute('allowfullscreen', 'true')
  _DOM_iframe.setAttribute('frameborder', '0')
  _DOM_wrap.appendChild(_DOM_iframe)
  return _DOM_iframe
}

function initIframe(_DOM_iframe: HTMLIFrameElement, cadServer: string): Promise<boolean|null> {
  return new Promise((resolve, reject) => {
    _DOM_iframe.addEventListener('load', handleLoad)
    _DOM_iframe.src = `${cadServer}/cloudCAD.html?v=${__CLIENT_VERSION__}`
    

    function handleLoad() {
      console.log('iframe loaded successfully!');
      if (_DOM_iframe.contentWindow) {
        _DOM_iframe.removeEventListener('load', handleLoad)
        return resolve(true)
      } else {
        console.warn('iframe contentWindow is null');
        _DOM_iframe.removeEventListener('load', handleLoad)
        return reject(null)
      }
    }
    
  })

}