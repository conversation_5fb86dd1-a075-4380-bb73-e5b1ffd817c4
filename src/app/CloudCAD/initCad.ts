/* eslint-disable @typescript-eslint/no-empty-function */
import { createHttpRequest } from '../common/service'
import { ICADPARAMS, IGlobalConfig, IUserInfo, IFileInfo, FILE_TYPE_NAME, EExternalInterfaceError } from './types'
import { errorTips, promiseWrap, combineBreadcrumb, closePage, getConfig, showConfirmDialog } from '../common/utils'
import { dialog } from 'mdui/functions/dialog.js';
import 'mdui/mdui.css';
declare let window: any & typeof globalThis;

import DownloadManager from '@/utils/DownloadManager'
import t from '../common/i18n'

import pubsub from './pubsub.js'

interface TemDataItemType {
  data: Blob
  storageId?: number | string
  fileId?: number | string
}
// 初始化cad程序
const initCad = async ({
  wrapId = 'wcadWrap',
  fileName = '',
  did,
  uid,
  documentCloudPath = '',
  documentLocalPath = '', // indexdb中的路径
  iconUrl = '/icon',
  baseUrl = '/xxxx',
  fetch_listUrl = '/fetch_list.json',
  s1Url = '/s1_need.json',
  s2Url = '/s2_need.json',
  externalMount = {},
  enableHeader = false,
  logLevel = 5,
  breadcrumbList,
  showCreateVersionButton,
  showSaveStatus,
  wc = {}
}: ICADPARAMS, userInfo: IUserInfo) => {
  const cloudCAD = new (window as any).GStarCloudCAD()

  const cloudcanvas = await cloudCAD.init({
    wrapId,
    iconUrl,
    showCustomHeader: !enableHeader,
    showCreateVersionButton,
    showSaveStatus,
    fileName: fileName,
    hideLoading: true,
    breadcrumbList,
    avatar: {
      name: userInfo.user_name,
      url: userInfo.avatarUrl,
      menu: [],
      logoutCallBack: () => {
        // emitter.emit('logout')
      }
    }
  })

  // cloudCAD外挂方法
  cloudCAD.externalMount = { ...cloudCAD.externalMount, ...externalMount }

  // 进度通知回调
  invoke_callBack.setProcess = (code, msg) => {
    if (code === -1) {
      errorTips(t("开图失败，请刷新页面重试！"))
      wc?.wc_loading.setAttribute('tips', t('开图失败，请刷新页面重试！'))
      cloudCAD.pubsub.publish('cloudCAD.event.loadError', {
        did,
        date: +new Date(),
        code,
        msg
      })
      return
    }
    cloudCAD.wasmPostCallback({
      type: 'loadProcess',
      payload: JSON.stringify({ process: code })
    })

    if (code === 100) {
      wc?.wc_loading.setAttribute('isopen', 'false')
      cloudCAD.externalMount?.finish()
    }
  }

  // 修改boot配置
  cfg_param.cloudcanvas = cloudcanvas

  cfg_param.JSZip = window.JSZip
  cfg_param.CryptoJS = window.CryptoJS
  cfg_param['var'].DOWNLOADSVR = baseUrl
  function getJsonFileInfo(url: string) {
    return new Promise((resolve, reject) => {
      fetch(url)
        .then(response => {
          return response.arrayBuffer()
        })
        .then(res => {
          const dataView = new DataView(res);
          const decoder = new TextDecoder("utf-8");
          const jsonString = decoder.decode(dataView);
          try {
            const fileinfo = JSON.parse(jsonString);
            resolve(fileinfo);
          } catch (err) {
            resolve(jsonString)
          }
        })
        .catch(err => reject(err))
    })
  }
  cfg_param.fetches = () => getJsonFileInfo(fetch_listUrl + `?t=${+new Date()}`)
  cfg_param.files.s1_need = () => getJsonFileInfo(s1Url + `?t=${+new Date()}`)
  cfg_param.files.s2_need = () => getJsonFileInfo(s2Url + `?t=${+new Date()}`)


  cfg_param.document.logLevel = logLevel

  return {
    cloudCAD,
    cfg_param,
    invoke_callBack
  }
}

const cfg_param: any = {
  application: {
    'name': "GstarCAD Cloud",
    'sandbox': "cloud.gstarcad.com",
    'vesion': 2300,
    'vendor': '苏州浩辰软件股份有限公司',
    'user': '张三',
    'userid': 123456,
    'mode': 3
  },
  document: {
    'name': '浩辰大厦一层平面图',
    'size': 565656,
    'vesion': '1.1',
    'sha1': 'abcdedfabcdedfabcdedfabcdedf',
    'createtime': '2023-01-01 12:00:20',
    'lastmodifytime': '2023-01-01 12:00:20',
    'lastmodifyuserid': 123456,
    'logicpath': '/浩辰大厦/施工图/浩辰大厦一层平面图.dwg',
    'data': '',
    'localPath': '/data/sample.dwg',
    'fileid': 2342342
  },
  license: {},
  collaboration: {},
  services: {
    document: [
      'https://document.gstarcad.com'
    ],
    meetings: [ //多个用于负载均横
      'https://metting0.gstarcad.com',
      'https://metting1.gstarcad.com',
      'https://metting2.gstarcad.com'
    ],
    cdns: ''
  },
  'var': {
    "DOWNLOADSVR": '/',   //下载源目录 使用baseURL
    "APPROOT": "/data/"    //db存储目录 /opt/gstarcad/v2023/
  },
  files: {
    s1_need: async () => {
      let fileinfo: any = [];
      function getJsonFileInfo() {
        return new Promise((resolve, reject) => {
          fetch('s1_need.json')
            .then(response => {
              return response.arrayBuffer()
            })
            .then(res => {
              const dataView = new DataView(res);
              const decoder = new TextDecoder("utf-8");
              const jsonString = decoder.decode(dataView);
              fileinfo = JSON.parse(jsonString);
              resolve(res);
            })
            .catch(err => reject(err))
        })
      }
      await getJsonFileInfo()
      return fileinfo
    },
    s2_need: async () => {
      let fileinfo: any = [];
      function getJsonFileInfo() {
        return new Promise((resolve, reject) => {
          fetch('s2_need.json')
            .then(response => {
              return response.arrayBuffer()
            })
            .then(res => {
              const dataView = new DataView(res);
              const decoder = new TextDecoder("utf-8");
              const jsonString = decoder.decode(dataView);
              fileinfo = JSON.parse(jsonString);
              resolve(res);
            })
            .catch(err => reject(err))
        })
      }
      await getJsonFileInfo()
      return fileinfo
    }
  },
  fetches: async () => {
    console.log("fetches")
    let fileinfo = {};
    function getJsonFileInfo() {
      return new Promise((resolve, reject) => {
        fetch('fetch_list.json')
          .then(response => {
            return response.arrayBuffer()
          })
          .then(res => {
            const dataView = new DataView(res);
            const decoder = new TextDecoder("utf-8");
            fileinfo = decoder.decode(dataView);
            resolve(res);
          })
          .catch(err => reject(err))
      })
    }
    await getJsonFileInfo()
    return fileinfo
  },
  writeFile: (path: any, data: any) => {
    window.FS.writeFile(path, data);
  },
  big_fssync: async (load: any) => {
    return new Promise((resolve, reject) => {
      window.FS.syncfs(load, function (err: any) {
        if (err) {
          reject(err);
        } else {
          resolve(err);
        }
      });
    });
  },
  big_local_existFile: (path: string) => {//文件是否存在
    try {
      return window.FS.isFile(window.FS.stat(path.replace('//', '/')).mode);//
    } catch (e) {
      return false;
    }
  },
  fs_delfile: (path: string) => {
    return new Promise((resolve, reject) => {
      window.FS.unlink(path, function (err: Error) {
        if (err) {
          reject(err);
        } else {
          resolve(err);
        }
      });
      cfg_param.big_fssync(false);
    });
  }
};
const invoke_callBack = {
  eventBeginStage1: () => {
  },
  eventEndStage1: () => {

  },

  eventPrepareStage2Files: () => { },
  eventBeginStage2: () => { },
  eventEndStage2: () => { },

  showMsg: (code: any, msg: any) => {
    console.log(code, msg);
  },
  setProcess: (code: any, msg: any) => {
  },
  showMain: (code: any, msg: any) => {
  }
};

let temData: TemDataItemType[] = []

export const createCADExternalMount = (globalConfig: IGlobalConfig, userInfo: IUserInfo, fileInfo: IFileInfo) => {
  const { h_getLicense, h_uploadFile, h_uploadModifyRecord, h_getHistoryTagList, h_addHistoryTag, h_deleteHistoryTag, h_downloadFileByFileInfo, h_getFileListByDrop, h_getFileInfoByPath } = createHttpRequest({
    api_endpoint: globalConfig.api_endpoint,
    token: userInfo.token
  })

  // 通过文件路径获取文件信息
  const getFileInfoByPathHandle = async (path: string) => {

  }

  return {
    uploadModifyRecord: async (data: {
      cmd: string;
      curPt: {
        x: number;
        y: number;
        z: number;
      };
      file: File;
      baseId: number;
      isShot: boolean;
      lastsaveIncrVersion: string;
      cursaveIncrVersion: string;
      param?: string;
    }) => {
      let param = null
      if (data?.param) {
        param = JSON.parse(data.param)
      }
      if (data.isShot) {
        console.log('自动上传快照', data)
        return new Promise((resolve, reject) => {
          // 如果是快照
          h_uploadFile(
            {
              folderId: fileInfo.parentId,
              replace: true,
              file: data.file,
              tagType: 2,
            },
            (evt: any) => {
              // const progressPercent = ((evt.loaded / evt.total) * 100).toFixed(2);
              // if (parseInt(progressPercent) === 100) {
              //   resolve(true);
              // }
            }
          ).then(res => {
            if (res.code !== 0) {
              window.__GSTAR_CLOUD_CAD__.instance.pubsub.publish('external.interface.error', {
                type: EExternalInterfaceError.saveSnapshot,
                code: res.code,
                msg: res.msg
              })
              throw new Error(res.msg)
            } else {
              resolve(true);
            }
          }).catch((e: any) => {
            reject(false);
          });
        });
      } else {
        console.log('自动上传增量', data)
        const [err, result] = await promiseWrap(
          h_uploadModifyRecord({
            fileId: fileInfo.id,
            baseId: data.baseId || 0,
            srcStorageId: fileInfo.sid || 0,
            lastModify: data.lastsaveIncrVersion,
            currModify: data.cursaveIncrVersion,
            file: data.file,
          })
        );
        if (err) {
          console.log('上传增量失败', err);
          if (err?.code === 400681) {
            dialog({
              headline: t("提醒（您已无法编辑文件）"),
              description: t(`文件编辑权限已转由用户{editor}获取，如需编辑请前往文件列表页重新进入编辑。`, { editor: err?.data?.editor }),
              actions: [
                {
                  text: t("确定"),
                  onClick: () => {
                    closePage({
                      msg: t(`文件编辑权限已转由用户{editor}获取，如需编辑请前往文件列表页重新进入编辑。`, { editor: err?.data?.editor })
                    })
                  },
                }
              ]
            })
          }
          if (param && param?.type === 'addHistoryTag') {
            pubsub.publish('gcloud.upload.finish', {
              result: 'error',
              errorMsg: t('上传增量失败')
            })
          }
          throw new Error(t('上传增量失败'));
        } else {
          if (param && param?.type === 'addHistoryTag') {
            pubsub.publish('gcloud.upload.finish', {
              result: 'ok',
              recordId: result.data?.recordId,
            })
          }
        }

        return result.data?.recordId;
      }
    },
    getHistoryVersionList: async (status: 0 | 1 | 2) => {
      try {
        const { data } = await h_getHistoryTagList({
          fileId: fileInfo.id,
          status,
          sign: `${fileInfo.name}+${Date.now()}`
        })
        return data.list || []
      } catch (error) {
        console.log('获取历史版本列表失败', error)
      }
    },
    saveVersion: async (params: any) => {
      try {
        await h_addHistoryTag({
          baseStorageId: Number(fileInfo.sid),
          fileId: Number(fileInfo.id),
          lastRecordId: params.recordId,
          userTag: params.tag,
        })
      } catch (error: any) {
        errorTips(t('创建历史版本失败'))
        console.log('创建历史版本失败', error)
      }
    },
    deleteHistoryVersion: async (params: any) => {
      try {
        await h_deleteHistoryTag({
          fileId: Number(fileInfo.id),
          userTag: params.tag,
          verId: params.verId
        })
      } catch (error: any) {
        errorTips(t('删除历史版本失败'))
        console.log('删除历史版本失败', error)
      }
    },
    downloadFileSource: async (fileName: string, fileId: number, reqFrom = 'cloudcad') => {
      try {
        const buffer = await h_downloadFileByFileInfo({
          fileName,
          fileId,
          reqFrom
        })
        return {
          data: buffer
        }
      } catch (error) {
        console.log("下载文件失败: ", error)
        errorTips(t("下载文件失败!"))
        return {
          data: ''
        }
      }
    },
    getFileList: async (query: any) => {
      let fileType = '1';
      for (const [key, value] of Object.entries(FILE_TYPE_NAME)) {
        if (query.fileTypes) {
          const list = query.fileTypes.map((v: string) => v.toLowerCase())
          if (list.includes(value)) {
            fileType += ',' + key;
          }
        }
      }
      const params = {
        folderId: 2,
        limit: 5,
        searchName: '',
        searchBeginTime: '',
        searchEndTime: '',
        fileType,
        sortField: 0,
        ascOrDesc: 'desc',
        lastFileId: 0,
        lastFileSize: 0,
        lastFileModifyTime: '',
        lastFileName: '',
        ...query
      };
      try {
        const { data } = await h_getFileListByDrop(params);
        return {
          list: data?.fileList || [],
          firstPage: data?.firstPage,
          lastPage: data?.lastPage,
          crumbList: data?.crumbsList.map((v: any) => {
            v.title = v.folderName;
            return v;
          })
        }
      } catch (e) {
        console.log("获取下拉文件列表失败：", e)
        errorTips(t("获取下拉文件列表失败!"))
        return {
          list: [],
          firstPage: true,
          lastPage: false,
          crumbList: []
        }
      }
    },
    getFileInfoByPath: h_getFileInfoByPath,

    getThumbnailUrl: async (item: any, thumbnailType: 'small' | 'middle' | 'large') => {
      let size = '64x48'
      switch (thumbnailType) {
        case 'small':
          size = '64x48';
          break;
        case 'middle':
          size = '128x96'
          break;
        case 'large':
          size = '256x192'
          break;
      }
      let imgId = ''
      try {
        if (item.thumbnail) {
          const parsedThumbnail = JSON.parse(item.thumbnail)
          if (typeof parsedThumbnail === 'object') {
            imgId = parsedThumbnail[size] ?? ''
          } else if (typeof parsedThumbnail === 'number') {
            imgId = item.thumbnail
          }
        }
      } catch (error) {
        console.log(error)
        return null
      }

      if (isInArr(temData, imgId)) {
        // 从缓存中根据storageId 取数据
        const arr = temData.filter(v => v.storageId == imgId)
        if (arr.length) {
          const item = arr[0]
          console.log("缓存");
          return (window as any).URL.createObjectURL(item?.data)
        }
        return null;
      } else {
        // 走接口获取缩略图
        const downloadManage = await new DownloadManager({ storageId: imgId, token: userInfo.token, host: globalConfig?.api_endpoint })
        const data: any = await downloadManage.getPreviewData()
        temData.push(data)
        // 本地缓存超过100 删除掉前10条
        if (temData.length > 100) {
          temData = temData.slice(10)
        }
        return data?.data ? (window as any).URL.createObjectURL(data.data) : null
      }


      // 如果是cad相关图纸
      // return `${globalConfig.api_endpoint}/api/v2/_st/_storage/_download?Authorization=${userInfo.token}&storageId=${imgId}`;
    },
    openCloudCad: async (filePath: string) => {
      // 暂不实现
      // try {
      //   const { fileInfo, crumbsList } = await getFileInfoByPathHandle(filePath)
      //   const { data: licenseData = {} } = await h_getLicense()
      //   const disabledEdit = !licenseData.modules.some((i: any) => ['MakeDWGIncrMerge'].includes(i.type) && i.usable)
      //   editFile(disabledEdit, fileInfo, crumbsList)
      // } catch (e) {
      //   console.log("打开在线编辑失败: ", e)
      //   errorTips("打开在线编辑失败!")
      // }
    },
    getFontInfoList: async () => {
      return new Promise(function (resolve, reject) {
        const result = {
          "crumbsList": [
            {
              "folderId": 1,
              "folderName": "data"
            },
            {
              "folderId": 5,
              "folderName": "sys_resource"
            },
            {
              "folderId": 10,
              "folderName": "part"
            }
          ],
          "currPageNums": 3,
          "fontFileList": [
            {
              "lastModifyTime": "2024-02-07 11:38:27",
              "etag": "24fd1171dbcbc1c4373f4e8fda7f30686cb6083b",
              "userTag": "2024-02-07 11:38:26",
              "editStatus": 0
            },
            {
              "lastModifyTime": "2024-02-07 11:38:11",
              "etag": "213da55af2bde9df445d1a7a5bbae2ef25d7cdee",
              "userTag": "2024-02-07 11:38:10",
              "editStatus": 0
            },
            {
              "lastModifyTime": "2024-02-07 11:37:40",
              "etag": "d27fb47a750688e5e79adf0623647a61a88dd91d",
              "userTag": "2024-02-07 11:37:39",
              "editStatus": 0
            }
          ],
          "fontInfoList": [
            {
              "bShx": false,
              "localName": "SourceHanSansCN-Regular.ttf",
              "globalName": "Regular.ttf",
              "charSet": "???? CN",
              "pitchFamily": 1
            },
            {
              "bShx": true,
              "localName": "??_GB2312.shx",
              "globalName": "Regular.shx",
              "charSet": "??_GB2312",
              "pitchFamily": 1
            },
            {
              "bShx": true,
              "localName": "HZTXT.SHX",
              "globalName": "HZTXT.SHX",
              "charSet": "bigfont 1.0",
              "pitchFamily": 1
            },
            {
              "bShx": true,
              "localName": "HZFS1.SHX",
              "globalName": "HZFS1.SHX",
              "charSet": "unifont 1.0",
              "pitchFamily": 1
            },
            {
              "bShx": true,
              "localName": "HZFS2.SHX",
              "globalName": "HZFS2.SHX",
              "charSet": "shapes 1.0",
              "pitchFamily": 1
            },
            {
              "bShx": true,
              "localName": "HZFS3.SHX",
              "globalName": "HZFS3.SHX",
              "charSet": "shapes 1.1",
              "pitchFamily": 1
            },
            {
              "bShx": true,
              "localName": "HZFS.SHX",
              "globalName": "HZFS.SHX",
              "charSet": "bigfont 1.0",
              "pitchFamily": 1
            },
            {
              "bShx": true,
              "localName": "??_GB2312.shx",
              "globalName": "Regular.shx",
              "charSet": "??_GB2312",
              "pitchFamily": 1
            }
          ],
          "totalFontNums": 3
        };
        resolve(result);
      })
    },
    openHelp: (val: string) => {
      if (val) {
        if (val === 'MAINHELP') {
          const href = 'https://www.gstarcad.com/help/GstarCAD_2024_zh-CN.html'
          window.open(href, '_blank')
        } else {
          const firstChar = val.charAt(0).toUpperCase();
          const href = firstChar === "_" ? `https://www.gstarcad.com/help/CloudGstarCAD_zh-CN/detail/commandReferences/${val}.html` : `https://www.gstarcad.com/help/CloudGstarCAD_zh-CN/detail/commandReferences_${firstChar}/${val}.html`
          window.open(href, '_blank')
        }
      }
    },
    token: userInfo.token,
    api_endpoint: globalConfig.api_endpoint,
  }
}

export default initCad

export function getIndexedDBValue(key: string) {
  const DB_NAME = '/data'
  const DB_STORE_NAME = 'FILE_DATA'

  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME)
    request.onsuccess = (event: any) => {
      const db = event.target.result
      const transaction = db.transaction(DB_STORE_NAME, 'readonly')
      const objectStore = transaction.objectStore(DB_STORE_NAME)
      const getRequest = objectStore.get(key)
      getRequest.onsuccess = (e: any) => {
        resolve(e.target.result)
      }
      getRequest.onerror = (e: any) => {
        reject(event.target.error);
      }
    }
    request.onerror = (event: any) => {
      reject(event.target.error)
    }
  })
}


const getCannotEditCodeMap = () => ({
  '1': t('当前无法编辑，如需编辑请前往文件列表页重新进入编辑。'),
  '2': t('您已被管理员强制退出云原生编辑页面，如有问题请联系管理员。'),
  '3': t('身份信息失效，请关闭重试！')
})
// 不能编辑提示框
export function showCannotEditDialog(code: '1' | '2' | '3', _msg: string | undefined = undefined) {
  const cannotEditCodeMap = getCannotEditCodeMap()
  const msg = _msg || cannotEditCodeMap[code] || cannotEditCodeMap['1']
  // 弹框提示用户关闭当前编辑页
  showConfirmDialog({
    headerTitle: t('您已无法编辑文件！'),
    confirmText: t('关闭'),
    rejectText: '',
    msg,
    confirmCB: () => {
      closePage({ msg })
    }
  })
}

const isInArr = (arr: TemDataItemType[], storageId: number | string) => {
  return arr.some(v => (v as any)?.storageId == storageId)
}