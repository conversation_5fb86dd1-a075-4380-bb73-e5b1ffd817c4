import { sleep } from '@/utils/httpUtil';
import { errorTips, getGlobalObject, polyfill } from '../common/utils';
import { usePreview2d, showTokenInvalidDialog } from './usePreview2d';
import CloudCadUtils from "@/utils/CloudCadUtils"
import t from '../common/i18n'
import type { IGCADPARAMS } from '../type'

const hideFunction = {
  save: [
    // 保存
    'saveAs', // 另存为
    'savePDF', // 另存PDF
    'saveImg', // 截图
    'print', // 打印
    'extract', // 图纸抽取
  ],
  measures: [
    // 测量
    'measureLength', // 长度
    'measureArea', // 面积
    'measureCoordinate', // 坐标
    'measureArc', // 弧长
    'measureAngle', // 角度
    'measureSetProportion', // 设比例
    'measureAccuracy', // 精度
  ],
  notes: [
    // 批注
    'noteCustomizeLine', // 手绘线
    'noteArrow', // 箭头
    'noteWord', // 文字
    'noteCloudLine', // 云线
    'noteImage', // 图片
    'noteGuideLine', // 引线
    'noteStraightLine', // 直线
    'noteRectangle', // 矩形
    'noteOval', // 椭圆
    'noteCheckView', // 审图
    'noteStamp', // 图章
  ],
  viewport: [
    // 视口相关
    'zoomE', // 显示全图
    'custom', // 窗口缩放
    'fullScreen', // 全屏
    'switchBackground', //切换背景色
  ],
};

/**
 * @description 快速集成2D插件
 */
export default async function GCAD({
  vendorcode, //必须，开发者的编码.(由服务器指定生成，参见后端部分）
  element, //必须，指定要在哪儿显示图形
  param: originParam,
}: IGCADPARAMS) {
  polyfill()
  // 地址栏带有isInvalid 表示是已经被踢出 socket不进行重连且显示弹窗
  const url = new URL(window.location.href)
  const isInvalid = url.searchParams.get("isInvalid")
  if (isInvalid) {
    showTokenInvalidDialog()
    return
  }
  const defaultParam = {
    cadServer: 'https://cloudapi.gstarcad.com',
    // PUSH方式
    fileId: '',
    fileName: '',
    etag: '',
    size: '',
    version: 'V1',
    // pull方式
    fileDownLoadUrl: '',
    userToken: '',
    fileToken: '',
    hideFunction: false,
    isXRef: '',
  };
  if (originParam.cadServer?.endsWith('/')) {
    originParam.cadServer = originParam.cadServer.slice(0, -1);
  }
  const param = { ...defaultParam, ...originParam };

  if (!param.cadServer) param.cadServer = 'https://cloudapi.gstarcad.com';

  // 参数校验
  {
    // 必选参数校验
    if (!vendorcode) alert(t(`参数异常：vendorcode={vendorcode}`, { vendorcode }));
    if (!element) alert(t(`参数异常：element={element}`, { element }));
    // fileId 与 fileDownLoadUrl 二选一
    if (
      (param.fileId && param.fileDownLoadUrl) ||
      (!param.fileId && !param.fileDownLoadUrl)
    ) {
      alert(
        t(`参数异常：param.fileId={fileId} 或 param.fileDownLoadUrl={fileDownLoadUrl}`, {
          fileId: param.fileId,
          fileDownLoadUrl: param.fileDownLoadUrl
        })
      );
    }
  }
  // 记录默认布局名
  let defaultLayoutName = '';
  let notesData: any[] = [];
  let gstarSDK: any = null;
  const config = {
    waterFlag: false,
    waterText: '',
    map: false,
    stampList: [] as (string | null)[],
    stampWidth: 0,
    stampHeight: 0,
  };

  const {
    clearTimer,
    getOcf,
    getNotes,
    uploadNotes,
    getDwg,
    getPdf,
    getMarks,
    deleteMarks,
    markChange,
    h_downloadFileBySid,
    h_getOpenDWGConfig,
    clearSocket,
    h_getFileIdByFileUrl,
    h_downloadImage
  } = usePreview2d({ api_endpoint: param.cadServer, token: vendorcode });

  // 如果使用FileDownLoadUrl方式，则先获取补充 fileId
  if (param.fileDownLoadUrl) {
    try {
      param.fileId = await h_getFileIdByFileUrl(param);
    } catch (err: any) {
      const msg = err.message || JSON.stringify(err);
      return new Error('fileDownLoadUrl download error:' + msg);
    }
  }

  // 初始化插件
  const initSDK = () => {
    return new Promise((resolve, reject) => {
      getGlobalObject(`${import.meta.env.VITE_ENV === 'cdn' ? import.meta.env.VITE_PUBLIC_PATH : param.cadServer}/assets/GStarSDK.js`, 'GStarSDK')
        .then(async (GStarSDK: any) => {
          const _gstarSDK = new GStarSDK({
            wrapId: element,
            apiHost: param.cadServer,
            toolbarHideItems:
              param?.hideFunction === 'true' ? hideFunction : {}, // 隐藏hideFunction里面的功能
            isHideMarks: param?.hideFunction === 'true' ? true : false, //是否隐藏软件自带的书签功能
          });

          await _gstarSDK.initFunction({
            cadServer: param.cadServer,
          });

          return resolve(_gstarSDK);
        })
        .catch((err) => {
          // 打印版本信息
          try {
            const { error } = window.console
            error('init SDK error:', err)
          } catch (err) {
            // 忽略
          }
        });
    });
  };

  // 初始化水印
  const initWatermark = (text: string) => {
    getGlobalObject(`${import.meta.env.VITE_ENV === 'cdn' ? import.meta.env.VITE_PUBLIC_PATH : param.cadServer}/assets/watermark.js`, 'watermark').then(
      (watermark: any) => {
        watermark.load({
          watermark_txt: text, //水印的内容
          watermark_width: 200, //水印宽度
          watermark_height: 100, //水印长度
          watermark_x_space: 0, //水印起始位置x轴坐标
          watermark_y_space: 0, //水印起始位置Y轴坐标
          watermark_font: '微软雅黑', //水印字体
          watermark_color: '#FFFFFF', //水印字体颜色
          watermark_fontsize: '20px', //水印字体大小
          watermark_alpha: 0.2, //水印透明度
          watermark_angle: 30, //水印倾斜度数
          watermark_parent_node: 'wrap2d', //'webcad'改成自己代码中显示cad图纸div的id名称
        });
      }
    );
  };

  // 切换布局callback
  const switchLayout = (
    ename: string,
    layout: { globalName: string; nickName: string }
  ) => {
    if (layout.globalName) {
      getOcf({
        originFileId: param.fileId,
        etag: param.etag,
        layoutName:
          layout.globalName === defaultLayoutName ? '' : layout.globalName,
        name: param.fileName,
        version: param.version || '',
        isXRef: param?.isXRef,
      })
        .then(async (ocf) => {
          if (ocf && gstarSDK) {
            const obj = await gstarSDK.render('ocf', ocf, '1', true);
            console.log('notesData', notesData);
            if (notesData.length) {
              gstarSDK.notes.show(notesData);
            }

            return obj;
          } else {
            console.error('获取资源失败/插件不存在');
            console.error('ocf', ocf);
            console.error('插件实例', gstarSDK);
            throw new Error(t('获取资源失败!'));
          }
        })
        .then(() => {
          console.warn('切换布局完成');
        })
        .catch((err) => {
          errorTips(err.message || JSON.stringify(err));
        });
    } else {
      errorTips(t('切换布局信息错误！'));
    }
  };

  // 批注修改
  const noteChange = (ename: string, noteInfo: any) => {
    if (gstarSDK) {
      // 获取全量的当前批注，直接上传
      uploadNotes({
        notes: gstarSDK.notes.data,
        fileName: param.fileName,
        originFileId: param.fileId,
        // version: props.version || ''
      });
    } else {
      console.error('批注修改，但插件实例不存在', gstarSDK);
    }
  };

  // 删除标签
  const deleteMark = (ename: string, data: any) => {
    console.log(111, data);
    deleteMarks({
      etag: param.etag,
      fileId: parseInt(param.fileId),
      delId: data.id,
    });
  };

  // 另存dwg
  const saveDwg = async (params: any) => {
    console.log('saveDwg params', params);
    if (gstarSDK) {
      gstarSDK.Tips.showProgress(30, gstarSDK.locals.get('converting'));
      try {
        const dwgInfo = await getDwg({
          originFileId: param.fileId as unknown as number,
          etag: param.etag,
          cadType: params.cadtype,
          dwgver: params.dwgver,
          version: param.version || '',
        });
        gstarSDK.Tips.showProgress(60, gstarSDK.locals.get('downloading'));
        const fileBuffer = await h_downloadFileBySid(dwgInfo);
        // 下载
        const blob = new Blob([fileBuffer], {
          type: 'application/x-dwg;charset=UTF-8',
        });
        if ((navigator as any).msSaveOrOpenBlob) {
          (navigator as any).msSaveOrOpenBlob(
            blob,
            `${params.savename}.${params.cadtype}`
          );
        } else {
          const a = document.createElement('a');
          a.href = window.URL.createObjectURL(blob);
          a.setAttribute('download', `${params.savename}.${params.cadtype}`);
          document.body.appendChild(a);
          a.click();
          (a.parentNode as any).removeChild(a);
        }

        gstarSDK.Tips.showProgress(
          100,
          gstarSDK.locals.get('download_completed')
        );
      } catch (err) {
        console.error('另存dwg错误：', err);
        gstarSDK.Tips.showTips(
          gstarSDK.locals.get('save_as_error'),
          gstarSDK.Tips.defaultTime,
          true
        );
      }
      await sleep(500);
      gstarSDK.Tips.closeProgress();
    }
  };

  // 另存pdf
  const savePdf = async (params: any) => {
    console.log('savePdf params', params);
    // 接口传参key不一致，需要格式化
    const pdfParams = {
      layoutName: params.layoutname,
      paperWidth: params.dPaperWidth,
      paperHeight: params.dPaperHeight,
      colorType: params.iHaveColor,
      scale: params.dScale,
      scopeType: params.iScapeType,
      pt1X: params.dPt1X,
      pt1Y: params.dPt1Y,
      pt2X: params.dPt2X,
      pt2Y: params.dPt2Y,
    };
    console.log('format pdf params', pdfParams);
    if (gstarSDK) {
      gstarSDK.Tips.showProgress(30, gstarSDK.locals.get('converting'));
      try {
        const dwgInfo = await getPdf({
          originFileId: param.fileId,
          etag: param.etag,
          pdfParams,
          version: param.version || '',
        });
        gstarSDK.Tips.showProgress(60, gstarSDK.locals.get('downloading'));
        const fileBuffer = await h_downloadFileBySid(dwgInfo);
        // 下载
        const blob = new Blob([fileBuffer], { type: 'application/pdf' });
        if ((navigator as any).msSaveOrOpenBlob) {
          (navigator as any).msSaveOrOpenBlob(blob, `${params.savename}.pdf`);
        } else {
          const a = document.createElement('a');
          a.href = window.URL.createObjectURL(blob);
          a.setAttribute('download', `${params.savename}.pdf`);
          document.body.appendChild(a);
          a.click();
          (a.parentNode as any).removeChild(a);
        }

        gstarSDK.Tips.showProgress(
          100,
          gstarSDK.locals.get('download_completed')
        );
      } catch (err) {
        console.error('另存pdf错误：', err);
        gstarSDK.Tips.showTips(
          gstarSDK.locals.get('save_as_error'),
          gstarSDK.Tips.defaultTime,
          true
        );
      }
      await sleep(500);
      gstarSDK.Tips.closeProgress();
    }
  };

  // 打印
  const printPdf = async (params: any) => {
    console.log('printPdf params', params);
    // 接口传参key不一致，需要格式化
    const pdfParams = {
      paperWidth: params.dPaperWidth,
      paperHeight: params.dPaperHeight,
      colorType: params.iHaveColor,
      scale: params.dScale,
      scopeType: params.iScapeType,
      pt1X: params.dPt1X,
      pt1Y: params.dPt1Y,
      pt2X: params.dPt2X,
      pt2Y: params.dPt2Y,
    };
    console.log('format printPdf params', pdfParams);
    if (gstarSDK) {
      gstarSDK.Tips.showProgress(30, gstarSDK.locals.get('converting'));
      try {
        const dwgInfo = await getPdf({
          originFileId: param.fileId,
          etag: param.etag,
          pdfParams,
          version: param.version || '',
        });
        gstarSDK.Tips.showProgress(60, gstarSDK.locals.get('ready_print'));
        const fileBuffer = await h_downloadFileBySid(dwgInfo);
        // iframe打印
        const blob = new Blob([fileBuffer], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        gstarSDK.func.save.print._DOM_iframe.setAttribute('src', url);
        gstarSDK.func.save.print._DOM_iframe.onload = () => {
          gstarSDK.func.save.print._DOM_iframe.contentWindow.print();
        };
      } catch (err) {
        console.error('打印错误：', err);
        gstarSDK.Tips.showTips(
          gstarSDK.locals.get('print_error'),
          gstarSDK.Tips.defaultTime,
          true
        );
      }
      await sleep(500);
      gstarSDK.Tips.closeProgress();
    }
  };

  // 另存回调，修改弹框默认文件名
  const cbSaveDwg = () => {
    const fileNameDomID = 'GStarSDK-saveAs-form-fileName';
    (document.getElementById(fileNameDomID) as HTMLInputElement).value =
      param.fileName.replace(/\.([a-zA-Z0-9]+)$/g, '');
  };

  // 另存pdf回调，修改弹框默认文件名
  const cbSavePdf = () => {
    const fileNameDomID = 'GStarSDK-savePDF-form-fileName';
    (document.getElementById(fileNameDomID) as HTMLInputElement).value =
      param.fileName.replace(/\.([a-zA-Z0-9]+)$/g, '');
  };

  try {
    const res = await h_getOpenDWGConfig({ group: '2d' });
    if (res.code === 0) {
      if (res.data?.list.length) {
        for (const v of res.data.list) {
          if (v.key === 'waterFlag') {
            config.waterFlag = v.value === '0' ? false : true;
          }
          if (v.key === 'map') {
            console.log(v.value);
            config.map = v.value === '0' ? false : true;
            console.log(config);
          }

          if (v.key === 'stampList') {
            // config.stampList = JSON.parse(v.value);
            const tempStampList = JSON.parse(v.value);
            const data = await Promise.allSettled(tempStampList.map(h_downloadImage));
            const successfulDownloads = data
              .filter(result => result.status === 'fulfilled')
              .map(result => result.value);
            const imgList = successfulDownloads.map(arrayBuffer => {
              if (!arrayBuffer) return null; // 处理可能的空值
              const blob = new Blob([arrayBuffer], { type: 'image/jpeg' }); // 根据实际类型调整
              return URL.createObjectURL(blob);
            })
            config.stampList = imgList;
          }

          if (v.key === 'waterText') {
            config.waterText = v.value;
          }

          if (v.key === 'stampWidth') {
            config.stampWidth = v.value;
          }

          if (v.key === 'stampHeight') {
            config.stampHeight = v.value;
          }
        }
      }
    }
  } catch (error) {
    console.log(error);
  }

  const _gstarSDK = await initSDK();
  gstarSDK = _gstarSDK;
  gstarSDK.CloudCadUtils = CloudCadUtils
  window.gstarSDK = gstarSDK;
  gstarSDK.Tips.showProgress(0, gstarSDK.locals.get('loading'));
  // 开启缩放平移
  gstarSDK.enableZoom();
  gstarSDK.enablePan();
  console.log('config', config);
  if (config.map) {
    gstarSDK.enableMap();
  } else {
    gstarSDK.disableMap();
  }

  // 设置切换布局cb
  gstarSDK.on('changeLayout', switchLayout);
  // 监听批注改变
  gstarSDK.on('noteChange', noteChange);
  // 监听书签改变
  gstarSDK.on('markChange', markChange);
  // 监听书签删除确定事件
  gstarSDK.on('deleteMark', deleteMark);

  // 设置另存三个方法的上层实现
  if (gstarSDK.func?.save?.saveAs)
    gstarSDK.func.save.saveAs.customSaveAs = saveDwg;
  if (gstarSDK.func?.save?.savePDF)
    gstarSDK.func.save.savePDF.customSavePDF = savePdf;
  if (gstarSDK.func?.save?.saveAs)
    gstarSDK.func.save.print.customPrint = printPdf;

  // 设置另存pdf、另存dwg弹框文件名默认为源文件名
  gstarSDK.on('functionTrigger', (ename: string, funcName: string) => {
    switch (funcName) {
      case 'saveAs':
        cbSaveDwg();
        break;
      case 'savePDF':
        cbSavePdf();
        break;
    }
  });

  if (config.waterFlag) {
    initWatermark(config.waterText);
  }

  if (gstarSDK?.notes?.noteStamp) {
    gstarSDK.notes.noteStamp.params.width = config.stampWidth; // 图章宽度值
    gstarSDK.notes.noteStamp.params.height = config.stampHeight; // 图章高度值
    if (config.stampList.length) gstarSDK.notes.noteStamp.maps = { ...config.stampList };
  }

  gstarSDK._destroy = () => {
    // 取消异步轮询相关
    clearTimer();
    // 取消socket监听
    clearSocket();
    gstarSDK.destroy();
    gstarSDK = null;
  };

  const ocf = await getOcf({
    originFileId: param.fileId,
    etag: param.etag,
    name: param.fileName,
    version: param.version || '',
    isXRef: param?.isXRef,
  });

  // 渲染ocf
  if (ocf) {
    gstarSDK
      .render('ocf', ocf)
      .then(() => {
        // 初次render后记录默认布局
        if (gstarSDK?.layoutInfo?.globalName) {
          defaultLayoutName = gstarSDK?.layoutInfo?.globalName;
        }
        // 获取批注显示,异常不影响渲染
        notesData = [];
        getNotes({
          originFileId: param.fileId,
          fileName: param.fileName,
          // version: props.version || ''
        })
          .then((notes) => {
            console.log('批注数据：', notes);
            if (notes) {
              notesData = notes;
              gstarSDK.notes.show(notes);
            }
          })
          .catch((err) => {
            console.error('批注显示错误：', err.message);
            gstarSDK.Tips.showProgress(0, '批注显示错误：' + err.message)
          });

        getMarks({
          etag: param.etag,
          fileId: parseInt(param.fileId),
        })
          .then((res) => {
            if (gstarSDK.marks) {
              if (res) {
                res.forEach((v: any) => {
                  v.center = JSON.parse(v.center);
                });
                gstarSDK.marks.markList = res;
              }
            }
          })
          .catch((err) => {
            console.error('书签数据错误：', err.message);
            gstarSDK.Tips.showProgress(0, '书签数据错误：' + err.message)
          });
      })
      .catch((err: Error) => {
        errorTips(err.message || JSON.stringify(err));
        gstarSDK.Tips.showProgress(0, err.message || JSON.stringify(err))
      });
  } else {
    console.error('获取资源失败！');
    gstarSDK.Tips.showProgress(0, '获取资源失败！')
  }

  return gstarSDK;
}
