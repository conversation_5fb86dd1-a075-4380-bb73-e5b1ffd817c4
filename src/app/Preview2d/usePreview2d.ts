import socket from "@/utils/socket";
import { createHttpRequest } from "../common/service";
import { ETASKPRIORITY, ETASKSTATUS, ETASKTYPE, TFSMFunction } from "../type";
import { createDeviceid, errorTips, getFileSha1, getMFGType, isManufacturing, getConfig } from "../common/utils";
import '@vaadin/confirm-dialog'
import type { IMataVersion, IFSMResult } from '../type'
import t from '../common/i18n'

export function showTokenInvalidDialog() {
  // 弹框提示用户关闭当前编辑页
  let _DOM_confirm = document.createElement('vaadin-confirm-dialog') as any
  _DOM_confirm.setAttribute('header-title', t('您已无法编辑文件！'))
  _DOM_confirm.setAttribute('confirm-text', t('关闭'))
  _DOM_confirm.setAttribute('reject-text', '')
  _DOM_confirm.setAttribute('reject-button-visible', '')
  _DOM_confirm.setAttribute("style", "z-index: 999999 !important;");
  _DOM_confirm.innerText = t(`身份信息失效，请关闭重试！`)
  _DOM_confirm.noCloseOnEsc = true

  _DOM_confirm.addEventListener('confirm', () => {
    _DOM_confirm.remove()
    _DOM_confirm = null
    window.close()
  })
  document.body.appendChild(_DOM_confirm)

  _DOM_confirm.opened = true
}
// 上传资源到storage中
interface IUploadStorageParams {
  data: any;
  fileName: string;
  storageType?: string;
  fileId: string | number;
  fileType: number;
}
export function usePreview2d(param: any) {
  const SOCKETTASKEND = "task.end"; // socket任务事件结束
  const POLLINGTIME = 60000; // 轮询间隔时长

  // 各种定时器
  let intervalPollingOcf: NodeJS.Timer | null | undefined = null; // OCF轮询计时器
  let intervalPollingDwg: NodeJS.Timer | null | undefined = null; // DWG轮询计时器
  let intervalPollingPdf: NodeJS.Timer | null | undefined = null; // PDF轮询计时器
  // http接口
  const {
    h_getSelfUserInfo,
    h_getMeta,
    h_setMeta,
    h_downloadFileBySid,
    h_getTaskInfoById,
    h_isNeedReConvert,
    h_initTask,
    h_setTaskPriority,
    h_uploadFileByStorageId,
    h_createMark,
    h_getMarks,
    h_deleteMarks,
    h_getOpenDWGConfig,
    h_getFileIdByFileUrl,
    h_downloadImage
  } = createHttpRequest({
    api_endpoint: param.api_endpoint,
    token: param.token,
  });

  // socket连接
  h_getSelfUserInfo().then(async userInfo => {
    const globalConfig = await getConfig()
    socket.connect({
      conn_type: 0,
      token: param.token, // 认证token
      url: param.api_endpoint, // socket连接地址
      key: userInfo.key || globalConfig.key || '', //aes key
      uid: userInfo.id, // 用户id（唯一标识即可）
      uname: userInfo.user_name, // 用户名（理论可以任意填或uid即可）
      did: await createDeviceid() // 设备id （用浏览器指纹）
    })

    // 监听token失效
    socket.on("user.token.invalid", (ename, data) => {
      const url = new URL(window.location as any)
      url.searchParams.set("isInvalid", "true")
      history.replaceState(null, "", url)
      showTokenInvalidDialog()
    })
  })

  const clearTimer = () => {
    if (intervalPollingOcf) {
      clearInterval(intervalPollingOcf);
      intervalPollingOcf = null;
    }
    if (intervalPollingDwg) {
      clearInterval(intervalPollingDwg);
      intervalPollingDwg = null;
    }
    if (intervalPollingPdf) {
      clearInterval(intervalPollingPdf);
      intervalPollingPdf = null;
    }
  }
  // 获取ocf
  const getOcf = async (props: {
    originFileId: string | number;
    etag: string;
    layoutName?: string;
    name?: string;
    version?: string;
    isXRef?: string;
  }) => {
    const METAKEY = "gcad2dviewer";
    const TASK_TYPE = isManufacturing(props.name)
      ? getMFGType(props.name)
      : ETASKTYPE.CONVERT_OCF;

    const fileId = props.originFileId;
    const etag = props.etag;
    const layoutName = props.layoutName;
    const version = props?.version || "";

    console.log("props", props);

    console.warn("---------------获取ocf", fileId, layoutName, etag);
    // 第一次获取元信息
    const FSMGetMeta1: TFSMFunction = () => {
      console.log("FSMGetMeta1");
      return new Promise((resolve, reject) => {
        h_getMeta({
          fileId,
          metaKey: METAKEY,
        })
          .then(async ({ data }) => {
            console.log("data", data);
            if (data && data[METAKEY]) {
              // 元信息存在
              if (typeof data[METAKEY] === "string")
                data[METAKEY] = JSON.parse(data[METAKEY]);
              console.log("first meta data:", data[METAKEY]);
              // const metaVersionDataList: IMataVersion[] =
              //   data[METAKEY]["metaInfo"];
              // const metaData = metaVersionDataList[0]
              //   ? metaVersionDataList[0].data
              //   : undefined;

              const metaVersionDataList: IMataVersion[] =
                data[METAKEY]["metaInfo"];
              const metaData = metaVersionDataList[0]?.history[version] || undefined;

              // let metaData;
              // if (history) {
              //   metaData = metaVersionDataList[0]?.history[version] || undefined;
              // } else {
              //   metaData = metaVersionDataList[0]?.data || undefined;
              // }

              // 如果存在元数据
              if (metaData) {
                // 先检查etag是否一致
                if (metaData["info"]?.etag != etag) {
                  console.log("FSMGetMeta1 etag不一致");
                  return resolve({
                    done: false,
                    next: FSMInitTask,
                    nextParams: {},
                  });
                }
                console.log("etag一致， 检查元信息已有资源");
                // 如果直接有资源，下载返回
                if (!layoutName) {
                  // 默认布局检查
                  console.log("检查默认布局资源");
                  if (metaData.main && metaData.main.storageId) {
                    console.log("存在默认ocf资源");
                    const { data: convertData } = await h_isNeedReConvert(fileId, version, METAKEY);
                    if (convertData.reConvert) {
                      // 如果依赖文件发生了变更要触发重新转换
                      return resolve({
                        done: false,
                        next: FSMInitTask,
                        nextParams: {},
                      });
                    } else {
                      return resolve({
                        done: false,
                        next: FSMGetOcfByStorageId,
                        nextParams: {
                          storageId: metaData.main.storageId,
                        },
                      });
                    }
                  }
                } else {
                  // 具体布局检查
                  console.log(`检查${layoutName}布局资源`);
                  const layoutInfo = metaData.transitionList?.find(
                    (v: any) => v.layoutName === layoutName
                  );
                  if (layoutInfo && layoutInfo.storageId) {
                    console.log("存在其他ocf资源");
                    const { data: convertData } = await h_isNeedReConvert(fileId, version, METAKEY);
                    if (convertData.reConvert) {
                      // 如果依赖文件发生了变更要触发重新转换
                      return resolve({
                        done: false,
                        next: FSMInitTask,
                        nextParams: {},
                      });
                    } else {
                      return resolve({
                        done: false,
                        next: FSMGetOcfByStorageId,
                        nextParams: {
                          storageId: layoutInfo.storageId,
                        },
                      });
                    }
                  }
                }

                console.log("无资源，检查元信息任务");

                // 兼容默认布局任务与具体布局任务差异
                let taskInfo:
                  | { taskStatus: number; taskId: string | number }
                  | undefined;
                if (!layoutName) {
                  // 默认布局检查
                  console.log("检查默认布局任务");
                  taskInfo = metaData.info.transition;
                } else {
                  // 具体布局检查
                  console.log(`检查${layoutName}布局任务`);
                  taskInfo = metaData.transitionList?.find(
                    (v: any) => v.layoutName === layoutName
                  );
                }

                if (taskInfo && taskInfo.taskStatus) {
                  console.log("存在任务", taskInfo);
                  switch (taskInfo.taskStatus) {
                    case ETASKSTATUS.pending: // 排队
                      return resolve({
                        done: false,
                        next: FSMUpgradeTask,
                        nextParams: {
                          taskId: taskInfo.taskId,
                        },
                      });
                    case ETASKSTATUS.partialFinished: // 部分完成
                    case ETASKSTATUS.executing: // 执行中
                      return resolve({
                        done: false,
                        next: FSMWiatTask,
                        nextParams: {
                          taskId: taskInfo.taskId,
                        },
                      });
                    case ETASKSTATUS.finished: // 完成
                      // 如果没资源任务成功，重新获取一次元信息
                      return resolve({
                        done: false,
                        next: FSMGetMeta2,
                        nextParams: {},
                      });
                    case ETASKSTATUS.failed: // 失败
                    case ETASKSTATUS.canceled: // 取消
                    default:
                  }
                }
              }
            }

            console.log("元信息不存在、任务失败、任务取消、无任务，发起转换任务");
            // 元信息不存在、任务失败、任务取消、无任务，发起转换任务
            return resolve({
              done: false,
              next: FSMInitTask,
              nextParams: {},
            });
          })
          .catch((err) => {
            // 第一次获取元信息异常（元信息版本低等问题），重新发起转换任务
            console.error("第一次获取元信息逻辑错误，重新发起转换任务", err);
            return resolve({
              done: false,
              next: FSMInitTask,
              nextParams: {},
            });
          });
      });
    };

    // 发起转换任务
    const FSMInitTask: TFSMFunction = () => {
      console.log("FSMInitTask", TASK_TYPE);
      // 发起转换任务
      return new Promise((resolve, reject) => {
        h_initTask({
          fileId: fileId,
          type: TASK_TYPE,
          version,
          config: {
            layoutName,
          },
        })
          .then((res) => {
            // 返回正常
            if (res.code === 0 && res.data.id) {
              resolve({
                done: false,
                next: FSMWiatTask,
                nextParams: { taskId: res.data.id },
              });
            } else {
              // 返回异常
              resolve({
                done: true,
                result: {
                  state: false,
                  data: new Error(
                    "发起转换任务错误：" +
                    res.msg +
                    " res: " +
                    JSON.stringify(res)
                  ),
                },
              });
            }
          })
          .catch((err) => {
            resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          });
      });
    };

    // 获取OCF StorageId
    const FSMGetOcfByStorageId: TFSMFunction = ({
      storageId,
    }: {
      storageId: string;
    }) => {
      console.log("FSMGetOcfByStorageId", storageId);
      return new Promise((resolve, reject) => {
        h_downloadFileBySid(
          { storageId }
        )
          .then((arrayBuffer: ArrayBuffer) => {
            return resolve({
              done: true,
              result: {
                state: true,
                data: arrayBuffer,
              },
            });
          })
          .catch((err) => {
            console.error("FSMGetOcfByStorageId失败，重新发起转换任务", err);
            return resolve({
              done: false,
              next: FSMInitTask,
              nextParams: {},
            });
          });
      });
    };

    // 等待任务
    const FSMWiatTask: TFSMFunction = ({
      taskId,
    }: {
      taskId: string | number;
    }) => {
      console.log("FSMWiatTask taskId", taskId);

      return new Promise((resolve, reject) => {
        // socket和长轮询来等待任务状态
        socket.on(SOCKETTASKEND, (_ename, data) => {
          if (data.taskId == taskId) {
            // 取消轮询
            if (intervalPollingOcf) {
              clearInterval(intervalPollingOcf);
              intervalPollingOcf = null;
            }
            // 任务状态改变/完成，取消当前时间订阅，返回任务信息
            clearSocket()
            if (_ename === "task.end.ok") {
              if (data.notExistXRefs) {
                const msg =
                  `缺少外部参照物：${data.notExistXRefs.toString()}` +
                  "<br/>" +
                  "建议您以上传文件夹的方式上传图纸及其外部参照，或将外部参照上传至云图同目录下再重新开图。";
                errorTips(msg);
              }
              resolve({
                done: false,
                next: FSMGetMeta2,
                nextParams: {},
              });
            } else {
              resolve({
                done: true,
                result: {
                  state: false,
                  data: new Error(
                    `事件通知任务失败！事件名：${_ename}  数据：${JSON.stringify(
                      data
                    )}`
                  ),
                },
              });
            }
          }
        });

        // 加入轮询机制
        if (intervalPollingOcf) {
          clearInterval(intervalPollingOcf);
        }
        intervalPollingOcf = setInterval(async () => {
          try {
            const taskInfo = await h_getTaskInfoById(taskId);
            if (taskInfo.code === 0 && taskInfo.data) {
              const task = taskInfo.data;
              // 如果任务终结了
              if (
                task.status === ETASKSTATUS.finished ||
                task.status === ETASKSTATUS.canceled ||
                task.status === ETASKSTATUS.failed
              ) {
                console.log("轮询任务结束", task);
                // 取消轮询
                if (intervalPollingOcf) {
                  clearInterval(intervalPollingOcf);
                  intervalPollingOcf = null;
                }
                // 取消socket监听
                clearSocket()
                // 状态机next
                return resolve({
                  done: false,
                  next: FSMGetMeta2,
                  nextParams: {},
                });
              }
            } else {
              throw new Error("获取任务状态异常：" + JSON.stringify(taskInfo));
            }
          } catch (err) {
            // 取消轮询
            if (intervalPollingOcf) {
              clearInterval(intervalPollingOcf);
              intervalPollingOcf = null;
            }
            console.error("轮询任务异常", err);
            return resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          }
        }, POLLINGTIME);
      });
    };

    // 提升任务等级
    const FSMUpgradeTask: TFSMFunction = ({
      taskId,
    }: {
      taskId: string | number;
    }) => {
      console.log("FSMUpgradeTask");
      return new Promise((resolve, reject) => {
        // 提升任务优先级，不管成功与否
        h_setTaskPriority({
          id: parseInt(taskId as string),
          priority: ETASKPRIORITY.highest,
        });
        // 接口
        resolve({
          done: false,
          next: FSMWiatTask,
          nextParams: { taskId },
        });
      });
    };

    // 二次获取元信息
    const FSMGetMeta2: TFSMFunction = () => {
      console.log("FSMGetMeta2");
      return new Promise((resolve, reject) => {
        h_getMeta({
          fileId,
          metaKey: METAKEY,
        })
          .then(({ data }) => {
            // 元信息存在
            if (data && data[METAKEY]) {
              console.log("second meta data:", data[METAKEY]);
              if (typeof data[METAKEY] === "string")
                data[METAKEY] = JSON.parse(data[METAKEY]);
              // const metaVersionDataList: IMataVersion[] =
              //   data[METAKEY]["metaInfo"];
              // const metaData = metaVersionDataList[0]
              //   ? metaVersionDataList[0].data
              //   : undefined;

              const metaVersionDataList: IMataVersion[] =
                data[METAKEY]["metaInfo"];
              const metaData = metaVersionDataList[0]?.history[version] || undefined;

              // let metaData;
              // if (history) {
              //   metaData = metaVersionDataList[0]?.history[version] || undefined;
              // } else {
              //   metaData = metaVersionDataList[0]?.data || undefined;
              // }

              if (metaData) {
                if (metaData["info"]?.etag != etag) {
                  console.log("FSMGetMeta2 etag不一致");
                  return resolve({
                    done: false,
                    next: FSMInitTask,
                    nextParams: {},
                  });
                }
                console.log("etag一致， 检查元信息已有资源");
                // 如果直接有资源，下载返回
                if (!layoutName) {
                  // 默认布局检查
                  console.log("检查默认布局资源");
                  if (metaData.main && metaData.main.storageId) {
                    console.log("存在目标资源");
                    return resolve({
                      done: false,
                      next: FSMGetOcfByStorageId,
                      nextParams: {
                        storageId: metaData.main.storageId,
                      },
                    });
                  }
                } else {
                  // 具体布局检查
                  console.log(`检查${layoutName}布局资源`);
                  const layoutInfo = metaData.transitionList?.find(
                    (v: any) => v.layoutName === layoutName
                  );
                  if (layoutInfo && layoutInfo.storageId) {
                    console.log("存在目标资源");
                    return resolve({
                      done: false,
                      next: FSMGetOcfByStorageId,
                      nextParams: {
                        storageId: layoutInfo.storageId,
                      },
                    });
                  }
                }
              }
              // 第二次不在检查任务信息了
            }
            // 第二次元信息不存在或不存在资源，直接报错
            console.error("第二次获取元信息中无资源！", data[METAKEY]);
            resolve({
              done: true,
              result: {
                state: false,
                data: new Error("无资源信息:" + JSON.stringify(data)),
              },
            });
          })
          .catch((err) => {
            resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          });
      });
    };

    let fsm: IFSMResult = {
      done: false,
      next: props.isXRef === "1" ? FSMInitTask : FSMGetMeta1,
      nextParams: {},
    };
    while (!fsm.done) {
      if (fsm.next) {
        fsm = await fsm.next(fsm.nextParams);
      } else {
        throw new Error("fsm.next undefined: " + JSON.stringify(fsm));
      }
    }

    if (fsm.result?.state) {
      return fsm.result.data;
    } else {
      return undefined;
    }
  }
  // 获取批注
  const getNotes = async ({
    originFileId,
    fileName,
  }: {
    fileName: string;
    originFileId: string | number;
  }) => {
    const METAKEY = "note";
    try {
      const { data } = await h_getMeta({
        fileId: originFileId,
        metaKey: METAKEY,
      });
      console.log("批注元信息", data);
      if (data.note && JSON.parse(data.note).storageId) {
        const res = await h_downloadFileBySid({ storageId: JSON.parse(data.note).storageId }, "json");
        console.log("批注元信息", res);
        return res;
      } else {
        throw new Error("批注元信息无资源");
      }
    } catch (e) {
      console.log("获取批注错误", e);
    }
  }
  // 获取书签
  const getMarks = async ({
    etag,
    fileId
  }: {
    etag: string;
    fileId: number;
  }) => {
    try {
      const { data } = await h_getMarks({
        etag,
        fileId
      });
      if (data.bookMark) {
        return JSON.parse(data.bookMark);
      } else {
        throw new Error("书签息无资源");
      }
    } catch (e) {
      console.log("获取批注错误", e);
    }
  }
  // 删除书签
  const deleteMarks = async ({
    etag,
    fileId,
    delId
  }: {
    etag: string;
    fileId: number;
    delId: string
  }) => {
    try {
      const { data } = await h_deleteMarks({
        etag,
        fileId,
        delId
      });
      if (data.bookMark) {
        return JSON.parse(data.bookMark);
      } else {
        throw new Error("书签为空");
      }
    } catch (e) {
      console.log("书签错误", e);
    }
  }
  // 上传批注
  const uploadNotes = async ({
    notes,
    originFileId,
    fileName,
  }: {
    notes: any;
    fileName: string;
    originFileId: string | number;
  }) => {
    const METAKEY = "note";
    try {
      const data = await uploadFileToStorage({
        data: JSON.stringify(notes),
        fileName,
        storageType: "application/json",
        fileId: originFileId,
        fileType: 6,
      });
      const { data: metaData } = await h_getMeta({
        fileId: originFileId,
        metaKey: METAKEY,
      });
      await h_setMeta({
        fileId: parseInt(originFileId as string),
        metaKey: METAKEY,
        value: JSON.stringify(data),
        version: metaData.version,
      });
      console.warn("上传批注完成");
    } catch (e) {
      console.error("上传批注错误", e);
    }
  }
  // 获取dwg
  const getDwg = async (props: {
    originFileId: string | number;
    etag: string;
    dwgver: string | number;
    cadType: string;
    version: string;
  }) => {
    const fileId = props.originFileId;
    const version = props.version;

    // 发起转换任务
    const FSMInitTask: TFSMFunction = () => {
      console.log("FSMInitTask", "2dwg");
      // 发起转换任务
      return new Promise((resolve, reject) => {
        h_initTask({
          fileId: fileId,
          type: ETASKTYPE.SAVE_DWG,
          version,
          config: {
            dwgVersionType: props.dwgver,
            saveAsType: props.cadType,
          },
        })
          .then((res) => {
            // 返回正常
            if (res.code === 0 && res.data.id) {
              resolve({
                done: false,
                next: FSMWiatTask,
                nextParams: { taskId: res.data.id },
              });
            } else {
              // 返回异常
              resolve({
                done: true,
                result: {
                  state: false,
                  data: new Error(
                    "发起转换任务错误：" +
                    res.msg +
                    " res: " +
                    JSON.stringify(res)
                  ),
                },
              });
            }
          })
          .catch((err) => {
            resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          });
      });
    };

    // 等待任务
    const FSMWiatTask: TFSMFunction = ({
      taskId,
    }: {
      taskId: string | number;
    }) => {
      console.log("FSMWiatTask taskId", taskId);

      return new Promise((resolve, reject) => {
        // socket和长轮询来等待任务状态
        socket.on(SOCKETTASKEND, (_ename, data) => {
          if (data.taskId == taskId) {
            // 取消轮询
            if (intervalPollingDwg) {
              clearInterval(intervalPollingDwg);
              intervalPollingDwg = null;
            }
            // 任务状态改变/完成，取消当前时间订阅，返回任务信息
            clearSocket()
            if (_ename === "task.end.ok") {
              if (data.notExistXRefs) {
                const msg =
                  `缺少外部参照物：${data.notExistXRefs.toString()}` +
                  "<br/>" +
                  "建议您以上传文件夹的方式上传图纸及其外部参照，或将外部参照上传至云图同目录下再重新开图。";
                errorTips(msg);
              }
              // 如果没有结果id，也报错
              if (data.resultId) {
                resolve({
                  done: false,
                  next: FSMGetTaskResult,
                  nextParams: {
                    resultId: data.resultId,
                  },
                });
              } else {
                resolve({
                  done: true,
                  result: {
                    state: false,
                    data: new Error(`任务成功但未返回resultId`),
                  },
                });
              }
            } else {
              resolve({
                done: true,
                result: {
                  state: false,
                  data: new Error(
                    `事件通知任务失败！事件名：${_ename}  数据：${JSON.stringify(
                      data
                    )}`
                  ),
                },
              });
            }
          }
        });

        // 加入轮询机制
        if (intervalPollingDwg) {
          clearInterval(intervalPollingDwg);
        }
        intervalPollingDwg = setInterval(async () => {
          try {
            const taskInfo = await h_getTaskInfoById(taskId);
            if (taskInfo.code === 0 && taskInfo.data) {
              const task = taskInfo.data;
              // 如果任务终结了
              if (
                task.status === ETASKSTATUS.finished ||
                task.status === ETASKSTATUS.canceled ||
                task.status === ETASKSTATUS.failed
              ) {
                console.log("轮询任务结束", task);
                // 取消轮询
                if (intervalPollingDwg) {
                  clearInterval(intervalPollingDwg);
                  intervalPollingDwg = null;
                }
                // 取消socket监听
                clearSocket()

                // 需要对成功失败分别处理,因为失败了直接获取元信息有可能拿到上次的数据
                if (task.status === ETASKSTATUS.finished) {
                  // 完成
                  // 如果没有结果id，也报错
                  if (task.resultId) {
                    return resolve({
                      done: false,
                      next: FSMGetTaskResult,
                      nextParams: {
                        resultId: task.resultId,
                      },
                    });
                  } else {
                    resolve({
                      done: true,
                      result: {
                        state: false,
                        data: new Error(`任务成功但未返回resultId`),
                      },
                    });
                  }
                } else {
                  // 失败/取消
                  resolve({
                    done: true,
                    result: {
                      state: false,
                      data: new Error(`轮询任务失败 2dwg 事件：${task.id}`),
                    },
                  });
                }
              }
            } else {
              throw new Error("获取任务状态异常：" + JSON.stringify(taskInfo));
            }
          } catch (err) {
            // 取消轮询
            if (intervalPollingDwg) {
              clearInterval(intervalPollingDwg);
              intervalPollingDwg = null;
            }
            console.error("轮询任务异常", err);
            return resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          }
        }, POLLINGTIME);
      });
    };

    // 获取任务完成结果信息
    const FSMGetTaskResult: TFSMFunction = ({
      resultId,
    }: {
      resultId: string | number;
    }) => {
      console.log("FSMGetTaskResult", "2dwg");
      return new Promise((resolve, reject) => {
        h_downloadFileBySid(
          { storageId: resultId },
          "json"
        )
          .then(({ data }) => {
            if (data?.dwg && data.dwg.storageId) {
              resolve({
                done: true,
                result: {
                  state: true,
                  data: data.dwg,
                },
              });
            } else {
              throw new Error("任务结果中无资源信息:" + JSON.stringify(data));
            }
          })
          .catch((err) => {
            resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          });
      });
    };

    let fsm: IFSMResult = {
      done: false,
      next: FSMInitTask,
      nextParams: {},
    };
    while (!fsm.done) {
      if (fsm.next) {
        fsm = await fsm.next(fsm.nextParams);
      } else {
        throw new Error("fsm.next undefined: " + JSON.stringify(fsm));
      }
    }

    if (fsm.result?.state) {
      return fsm.result.data;
    } else {
      console.error("fsm saveDwg error:", fsm.result?.data);
      return undefined;
    }
  }
  // 获取pdf
  const getPdf = async (props: {
    originFileId: string | number;
    etag: string;
    pdfParams: any;
    version: string;
  }) => {
    const fileId = props.originFileId;
    const pdfParams = props.pdfParams;
    const version = props.version;

    // 发起转换任务
    const FSMInitTask: TFSMFunction = () => {
      console.log("FSMInitTask", "2pdf");
      // 发起转换任务
      return new Promise((resolve, reject) => {
        h_initTask({
          fileId: fileId,
          type: ETASKTYPE.SAVE_PDF,
          config: pdfParams,
          version,
        })
          .then((res) => {
            // 返回正常
            if (res.code === 0 && res.data.id) {
              resolve({
                done: false,
                next: FSMWiatTask,
                nextParams: { taskId: res.data.id },
              });
            } else {
              // 返回异常
              resolve({
                done: true,
                result: {
                  state: false,
                  data: new Error(
                    "发起转换任务错误：" +
                    res.msg +
                    " res: " +
                    JSON.stringify(res)
                  ),
                },
              });
            }
          })
          .catch((err) => {
            resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          });
      });
    };

    // 等待任务
    const FSMWiatTask: TFSMFunction = ({
      taskId,
    }: {
      taskId: string | number;
    }) => {
      console.log("FSMWiatTask taskId", taskId);

      return new Promise((resolve, reject) => {
        // socket和长轮询来等待任务状态
        socket.on(SOCKETTASKEND, (_ename, data) => {
          if (data.taskId == taskId) {
            // 取消轮询
            if (intervalPollingPdf) {
              clearInterval(intervalPollingPdf);
              intervalPollingPdf = null;
            }
            // 任务状态改变/完成，取消当前时间订阅，返回任务信息
            clearSocket()
            if (_ename === "task.end.ok") {
              if (data.notExistXRefs) {
                const msg =
                  `缺少外部参照物：${data.notExistXRefs.toString()}` +
                  "<br/>" +
                  "建议您以上传文件夹的方式上传图纸及其外部参照物，或将外部参照上传至云图同目录下再重新开图。";
                errorTips(msg);
              }
              // 如果没有结果id，也报错
              if (data.resultId) {
                resolve({
                  done: false,
                  next: FSMGetTaskResult,
                  nextParams: {
                    resultId: data.resultId,
                  },
                });
              } else {
                resolve({
                  done: true,
                  result: {
                    state: false,
                    data: new Error(`任务成功但未返回resultId`),
                  },
                });
              }
            } else {
              resolve({
                done: true,
                result: {
                  state: false,
                  data: new Error(
                    `事件通知任务失败！事件名：${_ename}  数据：${JSON.stringify(
                      data
                    )}`
                  ),
                },
              });
            }
          }
        });

        // 加入轮询机制
        if (intervalPollingPdf) {
          clearInterval(intervalPollingPdf);
        }
        intervalPollingPdf = setInterval(async () => {
          try {
            const taskInfo = await h_getTaskInfoById(taskId);
            if (taskInfo.code === 0 && taskInfo.data) {
              const task = taskInfo.data;
              // 如果任务终结了
              if (
                task.status === ETASKSTATUS.finished ||
                task.status === ETASKSTATUS.canceled ||
                task.status === ETASKSTATUS.failed
              ) {
                console.log("轮询任务结束", task);
                // 取消轮询
                if (intervalPollingPdf) {
                  clearInterval(intervalPollingPdf);
                  intervalPollingPdf = null;
                }
                // 取消socket监听
                clearSocket()

                // 需要对成功失败分别处理,因为失败了直接获取元信息有可能拿到上次的数据
                if (task.status === ETASKSTATUS.finished) {
                  // 完成
                  // 如果没有结果id，也报错
                  if (task.resultId) {
                    resolve({
                      done: false,
                      next: FSMGetTaskResult,
                      nextParams: {
                        resultId: task.resultId,
                      },
                    });
                  } else {
                    resolve({
                      done: true,
                      result: {
                        state: false,
                        data: new Error(`任务成功但未返回resultId`),
                      },
                    });
                  }
                } else {
                  // 失败/取消
                  resolve({
                    done: true,
                    result: {
                      state: false,
                      data: new Error(`轮询任务失败 2pdf 事件：${task.id}`),
                    },
                  });
                }
              }
            } else {
              throw new Error("获取任务状态异常：" + JSON.stringify(taskInfo));
            }
          } catch (err) {
            // 取消轮询
            if (intervalPollingPdf) {
              clearInterval(intervalPollingPdf);
              intervalPollingPdf = null;
            }
            console.error("轮询任务异常", err);
            return resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          }
        }, POLLINGTIME);
      });
    };

    // 获取任务完成结果信息
    const FSMGetTaskResult: TFSMFunction = ({
      resultId,
    }: {
      resultId: string | number;
    }) => {
      console.log("FSMGetTaskResult", "2pdf");
      return new Promise((resolve, reject) => {
        h_downloadFileBySid(
          { storageId: resultId },
          "json"
        )
          .then(({ data }) => {
            console.log(data);
            if (data?.pdf && data.pdf.storageId) {
              resolve({
                done: true,
                result: {
                  state: true,
                  data: data.pdf,
                },
              });
            } else {
              throw new Error("任务结果中无资源信息:" + JSON.stringify(data));
            }
          })
          .catch((err) => {
            console.log("err", err);

            resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          });
      });
    };

    let fsm: IFSMResult = {
      done: false,
      next: FSMInitTask,
      nextParams: {},
    };
    while (!fsm.done) {
      if (fsm.next) {
        fsm = await fsm.next(fsm.nextParams);
      } else {
        throw new Error("fsm.next undefined: " + JSON.stringify(fsm));
      }
    }

    if (fsm.result?.state) {
      return fsm.result.data;
    } else {
      console.error("fsm savePdf error:", fsm.result?.data);
      return undefined;
    }
  }

  // 书签变化
  const markChange = (props: {
    gstarSDK: any,
    etag: string,
    fileId: string,
    ename: string,
    data: any
  }) => {
    const { gstarSDK, etag, fileId, ename, data } = props;
    if (gstarSDK) {
      if (data.action === 'add' || data.action === 'modify') {
        h_createMark({
          center: JSON.stringify(data.data.center),
          etag: etag,
          fileId: parseInt(fileId),
          name: data.data.name,
          scale: data.data.scale,
          id: data.data.id
        })
      }
    } else {
      console.error('书签修改，但插件实例不存在', gstarSDK)
    }
  }

  async function uploadFileToStorage({
    data,
    fileName,
    storageType = "application/json",
    fileId,
    fileType,
  }: IUploadStorageParams) {
    const blob = new Blob([data], {
      type: storageType,
    });
    const file = new File([blob], fileName, {
      type: storageType,
    });
    const sha1 = await getFileSha1(file);
    const { data: res } = await h_uploadFileByStorageId({
      attribute: JSON.stringify({
        replace: true,
        attach_to: parseInt(fileId as string),
        digest: sha1,
        file_type: fileType,
      }),
      file,
    });
    return res;
  }

  // 取消socket监听
  const clearSocket = () => {
    socket.off(SOCKETTASKEND);
  }
  return {
    clearTimer,
    getOcf,
    getNotes,
    getMarks,
    deleteMarks,
    uploadNotes,
    getDwg,
    getPdf,
    markChange,
    h_downloadFileBySid,
    h_getOpenDWGConfig,
    clearSocket,
    h_getFileIdByFileUrl,
    h_downloadImage
  }
}