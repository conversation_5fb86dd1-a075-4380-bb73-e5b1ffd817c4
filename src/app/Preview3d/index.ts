/**
 * @description 快速集成3D插件
 */

import socket from '@/utils/socket';
import { createHttpRequest } from '../common/service';
import {
  errorTips,
  getGlobalObject,
  getConfig,
  connectSocket,
  isManufacturing,
  getUserInfo,
  getMFGType,
  polyfill
} from '../common/utils';
import { ETASKPRIORITY, ETASKSTATUS, ETASKTYPE } from '../type';
import { loadNote } from './notes';
import type { IGCADPARAMS, TFSMFunction, IMataVersion, IFSMResult, IMetaData } from '../type'
import t from '../common/i18n'

// hoops可能支持brep的文件后缀, 根据https://docs.techsoft3d.com/exchange/latest/start/supported-formats.html
const brepFormatList = ['sat', 'sab', 'dwg', 'dxf', 'ipt', 'iam', 'model', 'session', 'dlv', 'exp', 'catdrawing', 'catpart', 'catproduct', 'catshape', 'cgr', '3dxml', 'asm', 'neu', 'prt', 'xas', 'xpr', 'dgn',
  'mf1', 'arc', 'unv', 'pkg',
  'ifc', 'ifczip',
  'igs', 'iges',
  'jt',
  'prt',
  'x_b', 'x_t', 'xmt', 'xmt_txt',
  'pdf',
  'prc',
  '3dm',
  'asm', 'par', 'pwd', 'psm',
  'sldasm', 'sldprt',
  'stp', 'step', 'stpz',
  'stpx', 'stpxz',
  'vda'
]
// asm he prt单独处理
const asmRegex = /^\w+\.(prt|asm|PRT|ASM)\.\d+$/;

export default async function GCAD3D({
  vendorcode, //必须，开发者的编码.(由服务器指定生成，参见后端部分）
  element, //必须，指定要在哪儿显示图形
  param: originParam, //均是可选部分，具体使用细节见后 param参数使用
}: IGCADPARAMS) {
  polyfill()
  const defaultParam = {
    cadServer: 'https://cloudapi.gstarcad.com',
    // PUSH方式
    fileId: '',
    fileName: '',
    etag: '',
    size: '',
    version: 'V1',
    // pull方式
    fileDownLoadUrl: '',
    userToken: '',
    fileToken: '',
  };
  const param = { ...defaultParam, ...originParam };

  if (!param.cadServer) param.cadServer = 'https://cloudapi.gstarcad.com';

  param.wrapId = element;
  // 图纸版本
  let version = 'V1';

  // 参数校验
  {
    // 必选参数校验
    if (!vendorcode) alert(t(`参数异常：vendorcode={vendorcode}`, { vendorcode }));
    if (!element) alert(t(`参数异常：element={element}`, { element }));
    // fileId 与 fileDownLoadUrl 二选一
    if (
      (param.fileId && param.fileDownLoadUrl) ||
      (!param.fileId && !param.fileDownLoadUrl)
    ) {
      alert(
        t(`参数异常：param.fileId={fileId} 或 param.fileDownLoadUrl={fileDownLoadUrl}`, {
          fileId: param.fileId,
          fileDownLoadUrl: param.fileDownLoadUrl
        })
      );
    }
  }

  let wcad: any = null;
  const METAKEY = 'gcad3dviewer';
  const POLLINGTIME = 60000; // 轮询间隔时长
  let pollingTimer: NodeJS.Timer | null | undefined = null; // 轮询计时器

  const SOCKETTASKWAIT = 'task.end'; // socket任务事件结束
  const IGNOREERROR = 'ignoreError';

  const globalConfig = await getConfig(param.cadServer);
  const userInfo = getUserInfo();
  // http接口
  const {
    h_getFileIdByFileUrl,
    h_getFileInfo,
    h_getMeta,
    h_downloadFileBySid,
    h_getTaskInfoById,
    h_isNeedReConvert,
    h_initTask,
    h_setTaskPriority,
    h_getSDKAuth,
  } = createHttpRequest({
    api_endpoint: globalConfig.api_endpoint,
    token: vendorcode,
  });

  // 连接socketio
  await connectSocket(globalConfig.socketIo_url, globalConfig.key, vendorcode);

  // 初始化插件
  const initWebCAD = (cadServer: string) => {
    return new Promise((resolve, reject) => {
      console.log('initWebCAD');
      const WebCADPromise: Promise<any> = getGlobalObject(
        `${import.meta.env.VITE_ENV === 'cdn' ? import.meta.env.VITE_PUBLIC_PATH : param.cadServer}/assets/WebCAD.js`,
        'WebCAD'
      );
      console.log('initWebCAD', WebCADPromise);
      WebCADPromise.then(async (WebCAD) => {
        console.log('initWebCAD', WebCAD, param.wrapId, param, vendorcode);
        const wcad = new WebCAD({
          wrapId: param.wrapId,
        });

        // 判断过滤不支持测量的文件类型
        let isDisableMeasure = true
        const fileExtension = param.fileName.split('.').pop()
        if ((fileExtension && brepFormatList.includes(fileExtension.toLowerCase())) || asmRegex.test(param.fileName)) {
          isDisableMeasure = false
        }
        // 禁用测量
        if (isDisableMeasure) {
          wcad.disabledMenus.push('btn-Measure')
          wcad.disabledMenus.push('btn-MeasureSetting')
        }
        const auth3D = await h_getSDKAuth(globalConfig.key);
        await wcad.init({
          funcPermissions: auth3D['3DJSSDKFormat'] || [],
          languagePath: param.languagePath || '',
          treeLanguagePath: param.treeLanguagePath || '',
        });
        return resolve(wcad);
      }).catch((error) => reject(error));
    });
  };

  // 第一次获取元信息
  const FSMGetMeta1: TFSMFunction = ({
    fileId,
    etag,
  }: {
    fileId: string | number;
    etag: string;
  }) => {
    console.log('FSMGetMeta1', etag);
    return new Promise((resolve, reject) => {
      h_getMeta({
        fileId,
        metaKey: METAKEY,
      })
        .then(async ({ data }) => {
          if (data && data[METAKEY]) {
            // 元信息存在
            if (typeof data[METAKEY] === 'string')
              data[METAKEY] = JSON.parse(data[METAKEY]);
            console.log('data[METAKEY]', data[METAKEY]);

            const metaVersionDataList: IMataVersion[] =
              data[METAKEY]['metaInfo'];
            const metaData =
              metaVersionDataList[0]?.history[param?.version] || undefined;

            // let metaData
            // if(props?.history) {
            //   metaData = metaVersionDataList[0]?.history[props?.version] || undefined
            // } else {
            //   metaData = metaVersionDataList[0]?.data || undefined
            // }

            // 如果有元数据
            if (metaData) {
              // 先检查etag是否一致
              if (etag && metaData['info']?.etag != etag) {
                console.log('etag不一致');
                return resolve({
                  done: false,
                  next: FSMInitTask,
                  nextParams: {fileId},
                });
              }

              if (metaData['main']) {
                console.log('准备执行render');
                const { data: convertData } = await h_isNeedReConvert(
                  fileId,
                  param.version,
                  METAKEY
                );
                if (convertData.reConvert) {
                  return resolve({
                    done: false,
                    next: FSMInitTask,
                    nextParams: {fileId},
                  });
                } else {
                  return resolve({
                    done: false,
                    next: FSMRender,
                    nextParams: {
                      metaData,
                      fileId
                    },
                  });
                }
              }
              // 如果有任务
              if (
                metaData.info?.transition &&
                metaData.info.transition.taskStatus
              ) {
                // 如果任务状态
                switch (metaData.info.transition.taskStatus) {
                  case ETASKSTATUS.pending:
                    return resolve({
                      done: false,
                      next: FSMUpgradeTask,
                      nextParams: metaData.info.transition.taskId,
                    });
                  case ETASKSTATUS.partialFinished:
                  case ETASKSTATUS.executing:
                    return resolve({
                      done: false,
                      next: FSMWiatTask,
                      nextParams: metaData.info.transition.taskId,
                    });
                  case ETASKSTATUS.finished:
                    // 如果没资源任务成功，重新获取一次元信息，任务成功还是没资源属于异常
                    return resolve({
                      done: false,
                      next: FSMGetMeta2,
                      nextParams: {
                        fileId: param.fileId,
                        etag: param.etag,
                      },
                    });
                  case ETASKSTATUS.failed:
                  case ETASKSTATUS.canceled:
                  default:
                }
              }
            }
          }
          // 元信息不存在、任务失败、任务取消、无任务，发起转换任务
          return resolve({
            done: false,
            next: FSMInitTask,
            nextParams: {fileId},
          });
        })
        .catch((err) => {
          // resolve({
          //   done: true,
          //   result: {
          //     state: false,
          //     data: err
          //   }
          // })
          // 第一次获取元信息异常（元信息版本低等问题），重新发起转换任务
          return resolve({
            done: false,
            next: FSMInitTask,
            nextParams: {fileId},
          });
        });
    });
  };

  // 渲染，初始化插件，通过有资源的元信息渲染
  const FSMRender: TFSMFunction = ({metaData, fileId}: {metaData:IMetaData, fileId: number | string}) => {
    console.log('FSMRender', metaData);
    return new Promise((resolve, reject) => {
      wcad._loadFile = (resourceName: string | number) => {
        console.log('+++++++++++++++++++_loadFile', resourceName);
        // 主资源名固定为main.dat, 其他资源slice.dat中找
        let storageId: string | number | null | undefined = null;
        //
        if (metaData) {
          if (resourceName === 'main.dat') {
            // 主文件
            storageId = metaData['main']?.storageId;
          } else if (typeof resourceName === 'string') {
            // 分片文件
            if (metaData['slice']) {
              // 使用index匹配分片文件
              const _index = parseInt(resourceName) - 1;
              for (const index in metaData['slice']) {
                if (_index == parseInt(index)) {
                  storageId = metaData['slice'][index].storageId;
                  break;
                }
              }
            }
          } else {
            // number 是brep文件
            storageId = resourceName; // brep storageId
          }
        }
        return new Promise((reso, reje) => {
          if (storageId) {
            // 模拟分片加载失败
            // if (storageId == 6290) storageId = 7777
            let time = +new Date();
            h_downloadFileBySid({storageId})
              .then((res) => {
                time = +new Date() - time;
                reso({
                  arrayBuffer: res,
                  time
                })
              })
              .catch((err) => reje(err));
          } else {
            const errMsg = t(`无storageId:{storageId} {type}: {resourceName}`, {
              storageId,
              type: typeof resourceName === 'string' ? t('分片index') : t('brep storageId'),
              resourceName
            });
            reje(errMsg);
            resolve({
              done: true,
              result: {
                state: false,
                data: new Error(errMsg),
              },
            });
          }
        });
      };

      // 解析传递过来的fileName，作为三维结构树的定节点显示
      function parseFileNameAndExtension(filename: string) {
        // 找到最后一个点的位置，分割文件名和后缀名
        const dotIndex = filename.lastIndexOf('.');
        if (dotIndex !== -1) {
          const name = filename.substring(0, dotIndex);
          const extension = filename.substring(dotIndex + 1);
          return { name, extension };
        } else {
          return { name: filename, extension: '' };
        }
      }
      const { name: originName, extension: originFormat } =
        parseFileNameAndExtension(param.fileName);
      wcad
        .render({
          originName: param.fileName,
          originFormat,
          subsection: {
            filePath: '',
            suffix: '',
          },

          isSubsection: metaData.info?.transition.type == 1 ? true : false,
          fileSource: 'main.dat',
          measureSource: metaData?.brepStorageIds || [],

          // 测试批注
          // fileSource:
          //   'https://ocf-pre-cn.51ake.com/temp/ocf3/2024/01/10/0546414358@2-A.ocf3',
          // getSubsection: (index: string) => {
          //   return `https://ocf-pre-cn.51ake.com/temp/ocf3/2024/01/10/0546414358@2-${index}.ocf3`;
          // },
          // measureSource:
          //   'https://ocf-pre-cn.51ake.com/temp/brep/2024/01/10/0546414358-1.brep',
          // isSubsection: true,
        })
        .then(() => {
          resolve({
            done: true,
            result: {
              state: true,
            },
          });
        })
        .catch((err: any) => {
          if (err === 'parse file error!') {
            resolve({
              done: false,
              next: FSMInitTask,
              nextParams: {fileId},
            });
          } else {
            resolve({
              done: true,
              result: {
                state: false,
                data: err,
              },
            });
          }
        });
    });
  };

  // 等待任务
  const FSMWiatTask: TFSMFunction = (taskId: string | number) => {
    console.log('FSMWiatTask', taskId);

    return new Promise((resolve, reject) => {
      // socket和长轮询来等待任务状态
      socket.on(SOCKETTASKWAIT, (_ename, data) => {
        console.log('111111111', data, taskId);

        if (data.taskId == taskId) {
          // 取消轮询
          if (pollingTimer) {
            clearInterval(pollingTimer);
            pollingTimer = null;
          }
          // 任务状态改变/完成，取消当前时间订阅，返回任务信息
          socket.off(SOCKETTASKWAIT);
          if (_ename === 'task.end.ok') {
            resolve({
              done: false,
              next: FSMGetMeta2,
              nextParams: {
                fileId: param.fileId,
                etag: param.etag,
              },
            });
          } else {
            // 判断新增的任务失败后需要redo
            if (data.redo_type) {
              resolve({
                done: false,
                next: FSMInitTask,
                nextParams: {
                  fileId: param.fileId,
                  taskType: data.redo_type,
                },
              })
            } else {
              resolve({
                done: true,
                result: {
                  state: false,
                  data: t('转换任务失败！'), // 在全局监听中提示报错信息，避免重复提示
                },
              })
            }
          }
        }
      });

      // 加入轮询机制
      if (pollingTimer) {
        clearInterval(pollingTimer);
      }
      pollingTimer = setInterval(async () => {
        try {
          const taskInfo = await h_getTaskInfoById(taskId);
          if (taskInfo.code === 0 && taskInfo.data) {
            const task = taskInfo.data;
            // 如果任务终结了
            if (
              task.status === ETASKSTATUS.finished ||
              task.status === ETASKSTATUS.canceled ||
              task.status === ETASKSTATUS.failed
            ) {
              // 取消轮询
              if (pollingTimer) {
                clearInterval(pollingTimer);
                pollingTimer = null;
              }
              // 取消socket监听
              socket.off(SOCKETTASKWAIT);
              // 判断新增的任务失败后需要redo
              if (task.status === ETASKSTATUS.failed && task.redo_type) {
                return resolve({
                  done: false,
                  next: FSMInitTask,
                  nextParams: {
                    fileId: param.fileId,
                    taskType: task.redo_type,
                  },
                })
              }
              // 状态机next
              return resolve({
                done: false,
                next: FSMGetMeta2,
                nextParams: {
                  fileId: param.fileId,
                  etag: param.etag,
                },
              });
            }
          } else {
            throw new Error(t('获取任务状态异常：{taskInfo}', { taskInfo: JSON.stringify(taskInfo) }));
          }
        } catch (err) {
          // 取消轮询
          if (pollingTimer) {
            clearInterval(pollingTimer);
            pollingTimer = null;
          }
          console.error('轮询任务异常', err);
          return resolve({
            done: true,
            result: {
              state: false,
              data: err,
            },
          });
        }
      }, POLLINGTIME);
    });
  };

  // 第二次获取元信息
  const FSMGetMeta2: TFSMFunction = ({
    fileId,
    etag,
  }: {
    fileId: string | number;
    etag: string;
  }) => {
    console.log('FSMGetMeta2');
    return new Promise((resolve, reject) => {
      h_getMeta({
        fileId,
        metaKey: METAKEY,
      })
        .then(({ data }) => {
          // 元信息存在
          if (data && data[METAKEY]) {
            console.log('data[METAKEY]', data[METAKEY]);
            if (typeof data[METAKEY] === 'string')
              data[METAKEY] = JSON.parse(data[METAKEY]);
            // const metaVersionDataList: IMataVersion[] = data[METAKEY]['metaInfo']
            // const metaData = metaVersionDataList[0] ? metaVersionDataList[0].data : undefined

            const metaVersionDataList: IMataVersion[] =
              data[METAKEY]['metaInfo'];
            const metaData =
              metaVersionDataList[0]?.history[param?.version] || undefined;

            // let metaData
            // if(props?.history) {
            //   metaData = metaVersionDataList[0]?.history[props?.version] || undefined
            // } else {
            //   metaData = metaVersionDataList[0]?.data || undefined
            // }

            if (metaData) {
              // 先检查etag是否一致
              if (etag && metaData['info']?.etag != etag) {
                console.log('第二次获取信息', metaData['info']?.etag, etag);
                return resolve({
                  done: false,
                  next: FSMInitTask,
                  nextParams: {fileId},
                });
              }
              // 如果有资源
              if (metaData['main']) {
                return resolve({
                  done: false,
                  next: FSMRender,
                  nextParams: {
                    metaData,
                    fileId
                  },
                });
              }
            }
            // 第二次不在检查任务信息了
          }
          // 第二次元信息不存在或不存在资源，直接报错
          resolve({
            done: true,
            result: {
              state: false,
              data: new Error(t('无资源信息:{data}', { data: JSON.stringify(data) })),
            },
          });
        })
        .catch((err) => {
          resolve({
            done: true,
            result: {
              state: false,
              data: err,
            },
          });
        });
    });
  };

  // 发起任务
  const FSMInitTask: TFSMFunction = ({fileId, taskType}: { fileId: string | number, taskType?: string}) => {
    console.log('FSMInitTask', fileId, taskType, param.fileName);
    // 发起转换任务
    return new Promise((resolve, reject) => {
      // 优先使用传递的任务类型发起任务
      const TASK_TYPE = taskType || (isManufacturing(param.fileName)
      ? getMFGType(param.fileName)
      : ETASKTYPE.CONVERT_VSF)

      console.log('发起任务', TASK_TYPE)
      h_initTask({
        fileId: fileId,
        version: param.version,
        type: TASK_TYPE,
      })
        .then((res) => {
          console.log('res', res);
          // 返回正常
          if (res.code === 0 && res.data.id) {
            resolve({
              done: false,
              next: FSMWiatTask,
              nextParams: res.data.id,
            });
          } else {
            // 返回异常
            resolve({
              done: true,
              result: {
                state: false,
                data: new Error(
                  t('发起转换任务错误：{msg} res: {res}', {
                    msg: res.msg,
                    res: JSON.stringify(res)
                  })
                ),
              },
            });
          }
        })
        .catch((err) => {
          resolve({
            done: true,
            result: {
              state: false,
              data: err,
            },
          });
        });
    });
  };

  // 提升任务等级
  const FSMUpgradeTask: TFSMFunction = (taskId: string | number) => {
    console.log('FSMUpgradeTask');
    return new Promise((resolve, reject) => {
      // 提升任务优先级，不管成功与否
      h_setTaskPriority({
        id: parseInt(taskId as string),
        priority: ETASKPRIORITY.highest,
      });
      // 接口
      resolve({
        done: false,
        next: FSMWiatTask,
        nextParams: taskId,
      });
    });
  };

  // 开启状态机--获取中间格式数据
  const startFSM = async () => {
    let fsm: IFSMResult = {
      done: false,
      next: FSMGetMeta1,
      nextParams: {
        fileId: param.fileId,
        etag: param.etag,
      },
    };
    while (!fsm.done) {
      if (fsm.next) {
        fsm = await fsm.next(fsm.nextParams);
      } else {
        throw new Error('fsm.next undefined: ' + JSON.stringify(fsm));
      }
    }
    return fsm.result;
  };

  const destroy = () => {
    // 取消异步轮询相关
    if (pollingTimer) {
      clearInterval(pollingTimer);
      pollingTimer = null;
    }
    // 取消socket监听
    socket.off(SOCKETTASKWAIT);
    // 销毁sdk
    try {
      if (wcad) wcad.destroy()
      wcad = null
    } catch (err) {
      console.error(err)
    }
  };

  try {
    if (param.cadServer) {
      param.cadServer = param.cadServer.replace(/\/+$/, '');
    }
    wcad = await initWebCAD(param.cadServer);
  } catch (err: any) {
    const msg = err.message || JSON.stringify(err);
    throw new Error('initWebCAD Error!');
  }

  // 如果使用FileDownLoadUrl方式，则先获取补充 fileId
  if (param.fileDownLoadUrl) {
    try {
      param.fileId = await h_getFileIdByFileUrl(param);
    } catch (err: any) {
      const msg = err.message || JSON.stringify(err);
      wcad.setMask(true, 'fileDownLoadUrl download error:' + msg)
      return new Error('fileDownLoadUrl download error:' + msg);
    }
  }

  // 根据fileId再获取file详细信息补充参数
  try {
    const {
      data: { FileInfo },
    } = await h_getFileInfo(param.fileId as string);
    version = FileInfo.version;
    // 用户传了使用用户传的文件名，没有使用获取到的文件名
    if (!param.fileName) param.fileName = FileInfo.name;
  } catch (err: any) {
    const msg = err.message || JSON.stringify(err);
    wcad.setMask(true, 'getFileInfo error:' + msg)
    return new Error('getFileInfo error:' + msg);
  }

  try {
    const stateRes = await startFSM();
    if (stateRes && stateRes.state) {
      // 加载批注
      wcad.token = vendorcode;
      wcad.apiHost = globalConfig.api_endpoint;
      wcad.fileId = param.fileId;
      wcad.userId = userInfo?.id;
      loadNote(wcad);
    } else {
      wcad.setMask(true, stateRes?.data || t('其他错误'))
      throw new Error(stateRes?.data || t('其他错误'));
    }
  } catch (err: any) {
    const errMsg = err.message || JSON.stringify(err);
    if (wcad.setMask) {
      wcad.setMask(true, errMsg)
    } else {
      errorTips(errMsg)
    }
  }
  wcad._destroy = destroy;
  return wcad;
}
