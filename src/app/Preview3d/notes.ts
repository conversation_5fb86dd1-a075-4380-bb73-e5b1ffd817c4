import '@vaadin/dialog';
import '@vaadin/button';
import '@vaadin/notification';
import './index.scss'

import {errorTips} from '../common/utils'
import t from '../common/i18n'
interface IGetNotesParams {
  apiHost: string
  token: string
  fileId: string | number
}

enum ENoteState {
  default = 0,
  add,
  edit,
  del
}

export const getNotes = async ({
  apiHost,
  token,
  fileId
}: IGetNotesParams) => {
  const apiUrl = `${apiHost}/api/v2/_st/_file/_get3dNote`
  const queryParams = `?fileId=${fileId}`
  const response = await fetch(`${apiUrl}${queryParams}`, {
    method: 'GET',
    mode: "cors",
    headers: {
      "Authorization": token
    }
  })
  const res = await response.json()

  return (res?.data?.['3D_Note']) || '{"noteList":[]}'
}

interface ISetNotesParams {
  apiHost: string
  token: string
  data: any
}
export const setNotes = async ({
  apiHost,
  token,
  data
}: ISetNotesParams) => {
  const apiUrl = `${apiHost}/api/v2/_st/_file/_set3dNote`
  const queryParams = ``
  const response = await fetch(`${apiUrl}${queryParams}`, {
    method: 'POST',
    mode: "cors",
    headers: {
      "Content-Type": "application/json",
      "Authorization": token
    },
    body: JSON.stringify(data)
  })

  const res = await response.json()

  return res
}

interface IGetUserInfoParams {
  apiHost: string
  token: string
  userId: string | number
}
const getUserInfo = async ({
  apiHost,
  token,
  userId
}: IGetUserInfoParams) => {
  try {
    const userIdSplit = (userId as string).split(':')
    // 如果是sdk调用
    if (userIdSplit[0] === 'vendor') {
      return {
        id: userId,
        user_name: userIdSplit[1]
      }
    }
  // eslint-disable-next-line no-empty
  } catch (err) {}

  const apiUrl = `${apiHost}/api/v2/_user/${userId}`
  const queryParams = ``
  const response = await fetch(`${apiUrl}${queryParams}`, {
    method: 'GET',
    mode: "cors",
    headers: {
      "Content-Type": "application/json",
      "Authorization": token
    }
  })
  const res = await response.json()
  return res.data
}

const userInfoMap:any = {}
let delGraphList:any = []

export const loadNote =  async (wcad:any) => {
  wcad.note.on('change', async (result:any) => {
    console.log('change-------------------', result)
    const { action, data, cb } = result
    let noteState = ENoteState.add // 默认批注状态为添加
    switch (action) {
      case 'edit':
        noteState = ENoteState.edit
        break
      case 'add': {
        noteState = ENoteState.add
        break
      }
      case 'delete':
        noteState = ENoteState.del
        break
      case 'undoGraph':
      case 'clearGraph':
        delGraphList = delGraphList.concat(data?.childNotes || [])
        return    
    }
    try {
      const originData = toOrigin(data, wcad.userId, delGraphList)
      originData.state = noteState
      changeNotes(wcad, originData, data, cb, action)
      delGraphList = []
    } catch (err: any) {
      errorTips(t(`操作失败: {error}`, { error: err.message || err }))
    }
  })
  
  const notsRes = await getNotes({
    apiHost: wcad.apiHost,
    token: wcad.token,
    fileId: wcad.fileId
  })
  let { noteList } = JSON.parse(notsRes)
  console.log('获取批注结果', noteList);
  if (!noteList) noteList = []

  try {
    // 获取批注相关的  缺失的 用户信息，补全userInfoMap
    const getBatchUser = [
      ...new Set<string>(
        noteList.map((originNote: IOriginData) => originNote.userId).filter((v: string) => v && !userInfoMap[v])
        .concat([wcad.userId])
      )
    ]
    .map((userId) => getUserInfo({
      apiHost: wcad.apiHost,
      token: wcad.token,
      userId
    }))
    const userList = await Promise.all(getBatchUser)
    userList.forEach((user:any) => {
      userInfoMap[user.id] = user
    })
  } catch(err) {
    console.error(err)
    console.log('获取批注相关用户信息失败')
  }

  // 根据缓存的用户信息补全批注用户参数
  noteList = noteList.map((originNote: IOriginData) => {
    return {
      ...originNote,
      creatorName: userInfoMap[originNote.userId]?.user_name || ''
    }
  })

  const loaclNotes = noteList.map((originNote: IOriginData) => toLocal(originNote, wcad.userId)).reverse()
  console.log('-----------------loaclNotes', loaclNotes)

  wcad.note.create(loaclNotes)
}

const changeNotes = async (wcad: any, originData: IOriginData, data: any, cb: any, action: any) => {
  try {
    const res = await setNotes({
      apiHost: wcad.apiHost,
      token: wcad.token,
      data: {
        fileId: Number(wcad.fileId),
        noteList: [
          originData
        ]
      }
    })
    console.log('note change data', data, res)
    // const res = {code: 0}
    if (res.code === 0) {
      data.creatorName = userInfoMap[wcad.userId]?.user_name || ''
      cb.bind(cb)(data)
    } else {
      throw new Error(t('服务端返回失败'))
    }
  } catch (err: any) {
    if (!navigator.onLine || err.name === 'TypeError') {
      errorTips(t('网络连接异常，请稍后重试'))
    } else {
      errorTips(t(`{action}批注 操作失败: {error}`, { action, error: err.message || err }))
    }
  }
}

interface IOriginData {
  noteId: string
  userId: string | number
  value: string
  graphList: {
    id: string
    state?: number
    value: string
  }[]
  state?: number
}
interface ILocalData {
  nid: string
  userId: string | number
  childNotes: {
    nid: string
    state?: number
    [key: string]: any
  }[]
  [key: string]: any
}
function toOrigin(localData: ILocalData, curUserId: string | number, delGraphList: any[]) :IOriginData {
  const { childNotes, userId, nid, state, ...data } = localData
  const _graphList = delGraphList.map(_graph => {
    const { nid, ...graph } = _graph
    return {
      "id": nid,
      "state": ENoteState.del,
      "value": JSON.stringify(graph)
    }
  })
  return {
    noteId: nid,
    state,
    graphList: childNotes.map((_graph) => {
      const { id, state, nid, ...graph } = _graph
      return {
        "id": nid,
        "state": state,
        "value": JSON.stringify(graph)
      }
    }).concat(_graphList),
    userId: userId || curUserId, // 不存在userId则为当前user
    value: JSON.stringify(data)
  }
}

function toLocal(originData: IOriginData, curUserId: string | number) {
  const { graphList, noteId, userId, value, ...other } = originData
  const data = JSON.parse(value)
  const res = {
    childNotes: graphList?.map?.((_graph: any) => {
      const { id, state, value } = _graph
      return {
        nid: id,
        state,
        ...JSON.parse(value)
      }
    }) || [],
    nid: noteId,
    userId,
    ...data,
    ...other,
  }

  if (userId !== curUserId) {
    res.disableEdit = true
    res.disableDelete = true
  }
  return res
}