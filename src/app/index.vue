<template>
  <div class="app-container">
    <component :is="appComponent" :appProps="appProps" />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent, onUnmounted } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { emitter } from "@/utils/mitt"
import { $t } from '@/locales'

const $route = useRoute()
const $router = useRouter()

// 前端应用在根组件下动态加载
const apps = import.meta.glob('./*.vue')
const appComponent = ref<any>(null)
const appProps = ref({})

onMounted(() => {
  // 参数校验
  if (!$route.params.aname) {
    return alert($t('参数缺失 app'))
  }

  // 关闭app 事件
  emitter.on('app.close', ({ aname }) => {
    closeApp()
  })

  openApp($route.params.aname as string, $route.query)
})

onBeforeRouteUpdate(async (to, from) => {
  //仅当 从在线编辑页跳转到预览页触发
  if (to.name === 'app') {
    const link = apps[`./${to.params.aname}.vue`]
    const com = defineAsyncComponent(link as any)
    appComponent.value = com
    appProps.value = to.query
  }
})

onUnmounted(() => {
  emitter.off('app.close')
})

const openApp = (aName: string, data: any) => {
  if (appComponent.value) closeApp()

  const link = apps[`./${aName}.vue`]
  const com = defineAsyncComponent(link as any)
  appComponent.value = com
  appProps.value = data
}

const closeApp = () => {
  appComponent.value = null
  appProps.value = {}
  // 如果有之前记录则back，没有是新页签没记录则关闭页签
  if ($router.options.history.state.back) {
    $router.back()
  } else {
    window.close()
  }
  // if ($router.options.history.state.back) {
  //   const targetUrl = $router.options.history.state.back as string
  //   $router.back()
  // } else {
  //   $router.back()
  // }
}

</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
}
</style>