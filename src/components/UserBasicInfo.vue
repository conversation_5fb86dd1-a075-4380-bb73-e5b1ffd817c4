<template>
  <div v-event:showUserBasic="showUserBasic">
    <el-drawer v-model="isShow" size="30%" direction="rtl" :title="$t('用户基本信息')" @close="resetForm(ruleFormRef)" :close-on-click-modal="false">
      <template #header>
        <div class="drawer-title">{{ $t('用户基本信息') }}</div>
      </template>
      <template #default>
        <div v-if="isShow">
          <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="left" label-width="120px"
            style="max-width: 555px" size="default" status-icon>
            <el-form-item :label="$t('用户头像')">
              <el-upload class="avatar-uploader" :show-file-list="false" :accept="'.jpg,.jpeg,.png,.bmp'"
                :on-change="(file: UploadFile, uploadFiles: UploadFiles) => uploadFile(file, uploadFiles)"
                :auto-upload="false">
                <el-avatar :size="50" :src="avatarUrl" v-if="avatarUrl" />
                <div v-else class="avatar-uploader-icon">
                  <el-icon class="action-icon">
                    <component :is="$ElIcon['Avatar']" />
                  </el-icon>
                </div>
                <!-- <el-icon v-else class="avatar-uploader-icon">
                  <Plus />
                </el-icon> -->
              </el-upload>
            </el-form-item>
            <el-form-item :label="$t('用户名')">
              <span>{{ ruleForm.user_name }}</span>
            </el-form-item>
            <el-form-item :label="$t('姓名')">
              <span>{{ ruleForm.fullname }}</span>
            </el-form-item>
            <el-form-item :label="$t('手机号')" prop="phone">
              <el-input v-model="ruleForm.phone" clearable />
            </el-form-item>
            <el-form-item :label="$t('邮箱')" prop="email">
              <el-input v-model="ruleForm.email" clearable maxlength="128" minlength="1" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('所属权限组')">
              <span>{{ getAuthGroup(ruleForm.groups) }}</span>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="submitForm(ruleFormRef)">{{ $t('确认') }}</el-button>
          <el-button type="primary" @click="resetForm(ruleFormRef)">{{ $t('重置') }}</el-button>
          <el-button @click="isShow = false">{{ $t('取消') }}</el-button>
        </div>
      </template>
    </el-drawer>
    <el-dialog :title="$t('头像设置')" v-model="showSetAvatarDialog" width="400px">
      <div class="cropperBox">
        <vue-cropper ref="cropper" :canMoveBox="true" :img="avatarBase64" :fixedBox="true" :autoCrop="true"
          autoCropWidth="200" autoCropHeight="200" outputType="png"></vue-cropper>
      </div>
      <template #footer>
        <div class="optionBtn">
          <el-button @click="rotateLeft"><i class="fa fa-rotate-left"></i>{{ $t('左旋转') }}</el-button>
          <el-button @click="rotateRight"><i class="fa fa-rotate-right"></i>{{ $t('右旋转') }}</el-button>
          <el-button @click="getPickAvatar" type="primary"><i class="fa fa-save"></i>{{ $t('保存') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import type { FormInstance, FormRules, UploadFile, UploadFiles } from 'element-plus'
import { VueCropper } from "vue-cropper";
import { validateEmail, validatePhone } from '@/utils/validate'
import { useUserStore } from "@/stores/user";
import { avatar } from '@/utils/const'
import { uploadImage } from '@/services/file';
import { $t } from '@/locales';
const userStores = useUserStore()
const ruleFormRef = ref<FormInstance>()
const cropper = ref()

interface DetailProps {
  item?: any
}
const isShow = ref(false)
const showSetAvatarDialog = ref(false)
const avatarUrl = ref(userStores.avatarUrl)
const avatarBase64 = ref()

watch(
  () => userStores.avatarUrl,
  async (v) => {
    if (v) {
      avatarUrl.value = v
    }
  },
  { deep: true }
);


const ruleForm = reactive({
  user_name: userStores.user_name,
  fullname: userStores.fullname,
  phone: userStores.phone,
  email: userStores.email,
  groups: userStores.groups,
  avatar: userStores.avatar || avatar
})

const uploadFile = async (file: UploadFile, uploadFiles: UploadFiles) => {
  try {
    if (file.raw) {
      const data = file.raw;
      const reader = new FileReader();
      reader.readAsDataURL(data);
      reader.onload = (e: any) => {
        const imgCode = e.target.result;
        avatarBase64.value = imgCode
        showSetAvatarDialog.value = true;
      }
    }
  } catch (e) {
    console.log(e);
  }
}

const getPickAvatar = () => {
  cropper.value.getCropBlob(async (data: any) => {
    if (data.size > 2097152) {
      ElMessage.error($t('图片大于2M，请进行裁剪或重新选择'))
    }
    const blob = window.URL.createObjectURL(data)
    avatarUrl.value = blob
    const file = new File([data], 'test', {
      type: "image/jpeg",
    })
    const res = await uploadImage({
      image: file || ''
    })
    ruleForm.avatar = res.data.key
  })
  showSetAvatarDialog.value = false;
}

const rotateLeft = () => {
  cropper.value.rotateLeft();
}
const rotateRight = () => {
  cropper.value.rotateRight();
}

const formPhone = (rule: any, value: any, callback: any) => {
  if (!validatePhone(value)) {
    callback(new Error($t("手机号格式不正确")))
  } else {
    callback()
  }
}
const formEmail = (rule: any, value: any, callback: any) => {
  if (!validateEmail(value)) {
    callback(new Error($t("邮箱格式不正确")))
  } else {
    callback()
  }
}


const rules = reactive<FormRules>({
  phone: [{ validator: formPhone, trigger: 'blur' }],
  email: [{ validator: formEmail, trigger: 'blur' }],
})

const getAuthGroup = (groups: Recordable[]) => {
  return groups.length > 0 ? groups.map(v => v.fullname).join(",") : '-'
}

const showUserBasic = async () => {
  try {
    await userStores.getInfo()
    Object.assign(ruleForm, {...userStores})
    isShow.value = true
  } catch (e) {
    console.log(e);
  }
}

const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid, fields) => {
    if (valid) {
      // TODO
      await userStores.sefInfo({
        avatar: ruleForm.avatar === avatar ? '' : ruleForm.avatar,
        phone: ruleForm.phone,
        email: ruleForm.email
      })
      ElMessage({
            type: 'success',
            message: $t('修改用户信息成功'),
        })
      isShow.value = false
    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  avatarUrl.value = userStores.avatarUrl
  ruleForm.avatar = userStores.avatar || avatar
  formEl.resetFields()
}

defineExpose({
  isShow,
})
const props = withDefaults(defineProps<DetailProps>(), {
  item: {},
  isShow: false,
})

</script>
<style scoped>
.create-type {
  width: 120px;
}

.cropperBox {
  width: 100%;
  height: 300px;
}
</style>
