<template>
  <el-dialog v-model="editPasswordDialogVisible" :title="$t('修改用户密码')" width="400px" @close="resetForm(ruleFormRef)">
    <el-form :model="userForm" label-position="left" ref="ruleFormRef" :rules="rules">
      <el-form-item :label="$t('旧密码')" :label-width="formLabelWidth" prop="password" show-password maxlength="32" minlength="6"
        required>
        <el-input v-model="userForm.password" autocomplete="off" show-password maxlength="32" minlength="6" required />
      </el-form-item>
      <el-form-item :label="$t('新密码')" :label-width="formLabelWidth" prop="newPassword">
        <el-input v-model="userForm.newPassword" autocomplete="off" show-password maxlength="32" minlength="6" required />
      </el-form-item>
      <el-form-item :label="$t('确认密码')" :label-width="formLabelWidth" prop="checkPassword">
        <el-input v-model="userForm.checkPassword" autocomplete="off" show-password maxlength="32" minlength="6"
          required />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="editPasswordDialogVisible = false">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)">
          {{ $t('确定') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { editUserPwd, getUserById } from '@/services/user';
import { encryptByMd5 } from '@/utils';
import { validatePassWord } from '@/utils/validate';
import type { FormInstance, FormRules } from 'element-plus';
import { useUserStore } from "@/stores/user"
import { reactive, ref, computed, onBeforeMount } from 'vue'
import { storeToRefs } from 'pinia';
import { emitter } from "@/utils/mitt";
import { $t } from '@/locales';

const formLabelWidth = '140px'
const userStores = useUserStore()
const ruleFormRef = ref<FormInstance>()
const editPasswordDialogVisible = ref(false);
const oldPassword = ref(""); // 目前前端不校验旧密码
const userForm = reactive({
  password: '',
  newPassword: '',
  checkPassword: '',
})

const { id } = storeToRefs(userStores);

const user_id = computed(() => id.value)

onBeforeMount(() => {
    // 显示弹窗
    emitter.on("changePWDDialogShow", () => {
      editPassword()
    });

});

const formUserPassword = (rule: any, value: string, callback: any) => {
  if (!validatePassWord(value)) {
    callback(new Error($t('密码格式不正确，长度在6到32位之间')))
  } else {
    callback()
  }
}

const formUserNewPassword = (rule: any, value: any, callback: any) => {
  if (!validatePassWord(value)) {
    callback(new Error($t('密码格式不正确，长度在6到32位之间')))
  } else {
    callback()
  }
}

const formUserCheckPassword = (rule: any, value: any, callback: any) => {
  if (userForm.newPassword !== value) {
    callback(new Error($t('新密码俩次输入的不一致，请重新输入')))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  password: [{ validator: formUserPassword, trigger: 'change' }, { validator: formUserPassword, trigger: 'blur' }],
  newPassword: [{ validator: formUserNewPassword, trigger: 'change' }, { validator: formUserNewPassword, trigger: 'blur' }],
  checkPassword: [{ validator: formUserCheckPassword, trigger: 'change' }, { validator: formUserCheckPassword, trigger: 'blur' }],
})

// 重置
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const editPassword = async () => {
  try {
    const id = userStores.id
    const { data } = await getUserById(id);
    oldPassword.value = data.password;
    editPasswordDialogVisible.value = true;
  } catch (e) {
    console.log(e)
  }
}

// 确定
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid, fields) => {
    if (valid) {
      // TODO
      try {
        await editUserPwd({ id: user_id.value, old_password: encryptByMd5(userForm.password), new_password: encryptByMd5(userForm.checkPassword) })
        ElMessage({
          type: 'success',
          message: $t('修改密码成功'),
        })
        editPasswordDialogVisible.value = false;
      } catch (e) {
        console.log(e);
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

</script>