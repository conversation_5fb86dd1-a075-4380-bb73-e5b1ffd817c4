<template>
    <el-tooltip :hide-after="0" effect="dark" :content="props.tooltipContent ? props.tooltipContent : String(props.content)" placement="top"
        :disabled="isShow">
        <template #content>
            <!-- 此处的默认值先看tooltipContent有没有，没有就给默认content -->
            <slot name="tooltipContent">{{ props.tooltipContent ? props.tooltipContent : props.content }}</slot>
        </template>
        <div :class="{ 'content': !isShow }" :style="{ width: props.width, maxWidth: props.maxWidth }"
            @mouseover="isShowTooltip">
            <span ref="contentRef" class="whitespace-nowrap">
                <!-- 给一个没有写插槽的默认值，兼容纯文本的情况 -->
                <slot name="content">{{ props.content }}</slot>
            </span>
        </div>
    </el-tooltip>
</template>
<script setup lang="ts">
import { ref, onMounted, onUpdated } from 'vue'
// 定义props的类型
interface tipProps {
    content: any,
    width?: string,
    maxWidth?: string,
    tooltipContent?: string
    showTip?: boolean
}
// 使用withDefaults来给props赋默认值
const props = withDefaults(defineProps<tipProps>(), {
    content: '',
    width: '',
    maxWidth: '',
    tooltipContent: '',
    showTip: true,
})
// 使用isShow来控制tooltip是否显示
const isShow = ref<boolean>(props.showTip)
// 在span标签上定义一个ref
const contentRef = ref()
onMounted(() => {
    isShowTooltip()
})
onUpdated(() => {
    isShowTooltip()
})
const isShowTooltip = function (): void {
    // 计算span标签的offsetWidth与盒子元素的offsetWidth，给isShow赋值
    setTimeout(() => {
        if (contentRef.value.parentNode.offsetWidth >= contentRef.value.offsetWidth) {
            isShow.value = true
        } else {
            isShow.value = false
        }
    }, 0);
}
</script>
<style>
.content {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}
</style>