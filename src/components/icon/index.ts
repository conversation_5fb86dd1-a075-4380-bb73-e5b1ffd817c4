import { markRaw } from "vue";
// 引入全部图标
// import * as Icons from "@element-plus/icons"

// 按需引入图标
import {
    Delete,
    Folder,
    EditPen,
    DeleteFilled,
    Operation,
    Platform,
    SetUp,
    DocumentCopy,
    Document,
    Tickets,
    User,
    UserFilled,
    Guide,
    Avatar,
    Postcard,
    Tools,
    TrendCharts,
    FolderOpened,
    CopyDocument,
    ArrowLeft,
    Expand,
    Fold,
    CaretLeft,
    CaretRight,
    HomeFilled,
    Unlock,
    Lock,
    Edit,
    Download,
    RefreshLeft,
    Refresh,
    Check,
    Close,
    WarningFilled,
    Files,
    Promotion,
    Rank,
    Search,
    MoreFilled,
    Upload,
    Plus,
    ZoomIn,
    CircleCheckFilled,
    CircleCloseFilled,
    Position,
    Camera,
    Clock,
    Memo,
    Reading,
    HelpFilled,
    Management,
    Calendar,
    Coin,
    WarnTriangleFilled,
    View,
    ScaleToOriginal,
    Wallet,
    DataAnalysis
} from "@element-plus/icons-vue";

const dictIcon = markRaw({
    Delete: Delete,
    Folder: Folder,
    EditPen: EditPen,
    DeleteFilled: DeleteFilled,
    Operation: Operation,
    Platform: Platform,
    SetUp: SetUp,
    DocumentCopy: DocumentCopy,
    Document: Document,
    Tickets: Tickets,
    User: User,
    UserFilled: UserFilled,
    Guide: Guide,
    Avatar: Avatar,
    Postcard: Postcard,
    Tools: Tools,
    TrendCharts: TrendCharts,
    FolderOpened: FolderOpened,
    CopyDocument: CopyDocument,
    ArrowLeft: ArrowLeft,
    Expand: Expand,
    Fold: Fold,
    CaretLeft: CaretLeft,
    CaretRight: CaretRight,
    HomeFilled: HomeFilled,
    Unlock: Unlock,
    Lock: Lock,
    Edit: Edit,
    Download: Download,
    RefreshLeft: RefreshLeft,
    Refresh: Refresh,
    Check: Check,
    Close: Close,
    WarningFilled: WarningFilled,
    Files: Files,
    Promotion: Promotion,
    Rank: Rank,
    Search: Search,
    MoreFilled: MoreFilled,
    Upload: Upload,
    Plus: Plus,
    ZoomIn: ZoomIn,
    CircleCheckFilled: CircleCheckFilled,
    CircleCloseFilled: CircleCloseFilled,
    Position: Position,
    Camera: Camera,
    Clock: Clock,
    Memo: Memo,
    Reading: Reading,
    HelpFilled: HelpFilled,
    Management: Management,
    Calendar: Calendar,
    Coin,
    WarnTriangleFilled,
    View,
    ScaleToOriginal,
    Wallet,
    DataAnalysis
});


const installIcon = (app: any) => {
    // 便于模板获取
    app.config.globalProperties.$ElIcon = dictIcon;
    // 使用全部图标
    // app.config.globalProperties.$icon = Icons
};

export default installIcon;
