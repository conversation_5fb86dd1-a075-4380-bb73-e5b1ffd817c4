<template>
  <div v-if="!taskList.length" class="flex items-center justify-center el-table__empty-text w-full">{{ $t('暂无数据') }}</div>
  <template v-else>
    <div v-for="(task) in taskList" :key="task.id" class="task">
      <SvgIcon :name="'loading'" :spin="true" :size="16"
        v-if="task.status !== ETASKSTATUS.finished && task.status !== ETASKSTATUS.failed && task.status !== ETASKSTATUS.partialFinished">
      </SvgIcon>
      <el-icon :color="'green'" :size="16" class="block"
        v-if="task.status === ETASKSTATUS.finished || task.status === ETASKSTATUS.partialFinished">
        <component :is="$ElIcon['CircleCheckFilled']" />
      </el-icon>
      <el-icon :color="'red'" :size="16" class="block" v-if="task.status === ETASKSTATUS.failed">
        <component :is="$ElIcon['CircleCloseFilled']" />
      </el-icon>
      <div class="task-name">{{ task.name }}</div>
      <span>{{ task.process }}</span>
    </div>
  </template>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ETASKSTATUS } from '@/model/task'
import socket from '@/utils/socket';
import { emitter } from '@/utils/mitt'
import { $t } from '@/locales'

interface Task {
  id: number, // 任务id
  status: number, // 任务状态
  name: string, // 任务名称
  process: string, // 任务进度百分比
}

const taskList = ref<Task[]>([])

onMounted(() => {
  socket.on('task', taskChange)
  emitter.on('addTaskToAsync', addTaskToAsync)
})

onUnmounted(() => {
  socket.off('task', taskChange)
  emitter.off('addTaskToAsync')
})

// 添加任务
const addTaskToAsync = (task: Task) => {
  taskList.value = taskList.value.concat([task])
}

const taskChange = (ename: string, data: any) => {
  switch (ename) {
    case 'task.progress.change':
      if (taskList.value && data) {
        taskList.value = taskList.value.map((v: Task) => {
          if (v.id === data.taskId) {
            v.process = `${data.newProgress}%`
            if (data.newProgress === 100) {
              v.status = ETASKSTATUS.finished
            }
          }
          return v
        })
      }
      break
    case 'task.end.error':
    case 'task.cancel.ok':
      if (taskList.value && data) {
        taskList.value = taskList.value.map((v: Task) => {
          if (v.id === data.taskId) {
            v.status = ETASKSTATUS.failed
          }
          return v
        })
      }
      break
  }
}

const close = () => {
  taskList.value = []
}

defineExpose({
  close,
  taskList
});
</script>

<style lang="scss" scoped>
.task-list {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 9999999;
  box-shadow: 0 0 8px #d8d8d8d6;
  width: 350px;
  display: flex;
  flex-direction: column;
  height: max-content;
}

.head {
  height: 32px;
  line-height: 32px;
  background-color: #0069ff;
  border-color: #0069ff;
  padding-left: 16px;
  padding-right: 16px;
  display: flex;
  align-items: center;
  color: #fff;
  cursor: move;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    vertical-align: middle;
  }

  .mark {
    cursor: pointer;
    transition: transform .3s linear;
  }

  .folded {
    transform: rotate(180deg);
  }
}

.body.folded {
  max-height: 0px;
}

.body {
  transition: max-height .3s linear;
  max-height: 300px;
  background-color: #fff;
  overflow: auto;
  padding-right: 16px;
  padding-left: 16px;
}

.task {
  padding: 3px 8px 3px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  border: .5px solid #d8d8d8;
  border-radius: 4px;
  margin-bottom: 8px;
}

.task-name {
  padding-top: 4px;
  padding-bottom: 4px;
  margin-left: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}
</style>