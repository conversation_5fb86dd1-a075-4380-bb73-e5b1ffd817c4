<template>
    <div v-loading="loading" class="layout-table" v-event:clearAll="clearAll">
        <div class="layout-table-filter" v-if="showFilter">
            <!-- 搜索区 -->
            <el-form :inline="true" :model="formState" class="filter-option" ref="ruleFormRef" label-position="left"
                :label-width="120">
                <template v-for="(item) in searchList" :key="item.key">
                    <el-form-item :name="item.key" :prop="item.key">
                        <template #label>
                            <span :title="item.name" class="form-label-text">{{ item.name }}</span>
                        </template>
                        <el-input v-model="formState[item.key]" :placeholder="item.placeholder || $t('请输入')"
                            v-if="item.type === 'input'" :style="{ width: item.width || form_width }" clearable>
                            <template #prepend v-if="item.hasPrepend">
                                <el-select :placeholder="$t('请选择类型')" v-model="formState[item.preType]"
                                    :style="{ width: item.preWidth || '120px' }" @change="formState[item.key] = ''">
                                    <el-option v-for="option in item.PreOptions" :key="option.value" :label="option.label"
                                        :value="option.value" />
                                </el-select>
                            </template>
                        </el-input>
                        <el-select v-model="formState[item.key]" clearable :placeholder="item.placeholder || $t('请选择')"
                            v-if="item.type === 'select'" :style="{ width: item.width || form_width }">
                            <el-option v-for="option in item.options" :key="option.value" :label="option.label"
                                :value="option.value" />
                        </el-select>
                        <el-date-picker v-if="item.type === 'dataPicker'" v-model="formState[item.key]" type="daterange"
                            range-separator="-" :start-placeholder="$t('开始时间')" :end-placeholder="$t('结束时间')" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </template>
            </el-form>
            <div style="flex: 1">
            </div>
            <div class="button-group">
                <el-button v-noBlur type="primary" @click="submitForm(ruleFormRef)" :title="$t('查询')">
                    <span class="btn-text">{{ $t('查询') }}</span>
                </el-button>
                <el-button v-noBlur type="primary" @click="resetForm(ruleFormRef)" :title="$t('重置')">
                    <span class="btn-text">{{ $t('重置') }}</span>
                </el-button>
            </div>
        </div>
        <div class="layout-table-content">
            <div class="layout-table-header">
                <slot name="tableHeader"></slot>
                <div style="flex: 1">
                    <MenuList style="marginLeft: 0px" :menuList="menuList" :items="selectRows"
                        v-if="menuList && menuList.length > 0 && selectRows && selectRows.length > 0"></MenuList>
                </div>
                <el-button v-noBlur type="primary" v-if="enableImport" @click="importFn" :title="$t('导入')">
                    <span class="btn-text">{{ $t('导入') }}</span>
                </el-button>
                <el-button v-noBlur type="primary" v-if="enableExport" @click="exportFn" :disabled="totalCount === 0" :title="$t('导出')">
                    <span class="btn-text">{{ $t('导出') }}</span>
                </el-button>
                <div class="flex items-center" v-if="enableBlockView">
                    <SvgIcon name="block" :size="18" class="cursor-pointer" v-if="viewType === 'list'"
                        @click="() => viewType = 'block'"></SvgIcon>
                    <el-checkbox @change="handleCheckAllChange" :indeterminate="isIndeterminate" @click.stop :label="isSelectAll ? $t('取消全选') : $t('全选')" class="ml-2" v-if="viewType === 'block'"
                        v-model="isSelectAll">{{ (isSelectAll || isIndeterminate) ?
              $t('{selected} / {total}', { selected: selectRows.length, total: tableData.length }) : $t('全选') }}</el-checkbox>
                    <SvgIcon name="list" :size="18" class="ml-2 cursor-pointer" v-if="viewType === 'block'"
                        @click="() => viewType = 'list'"></SvgIcon>
                </div>
            </div>
            <el-table @click.stop v-show="viewType === 'list' && !loading" :data="tableData" :max-height="table_max_height"
                ref="tableRef" @selection-change="selectedRowChange" :header-cell-style="{ 'background': '#F3F5FC' }" stripe
                border @sort-change="sortChange">
                <el-table-column v-for="column in columns" :key="column?.prop" :label="column?.label"
                    :prop="column?.property" :width="column?.width" :type="column?.type"
                    :show-overflow-tooltip="column?.showOverflowTooltip" :fixed="column?.fixed" :formatter="column?.format"
                    :align="column?.align" :sortable="column?.sortable">
                    <template #default="scope" v-if="column?.slotName">
                        <slot :name="column?.slotName" :row="scope.row" :index="scope.$index" :count="tableData.length">
                        </slot>
                    </template>
                    <template #default="scope" v-else-if="column?.property === 'name' && showRowItemIcon">
                        <div class="flex items-center" @click="gotoFile(scope.row)">
                            <FileIcon :width="40" :height="30" :item="scope.row"></FileIcon>
                            <div class="ml-2 truncate" v-ellipsis="scope.row.name">{{ scope.row.name }}</div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <FileItemBlock v-if="enableBlockView" v-show="viewType === 'block' && !loading && enableBlockView"
                :items="tableData" :menuList="quickMenuList" @selectRowChange="selectedRowChange"
                :isSelectAll="isSelectAll" :style="{height: table_max_height}" @goToItem="gotoFile" :isIndeterminate="isIndeterminate">
                <template #menuExtra="{ item }">
                    <slot name="menuExtra" :item="item"></slot>
                </template>
            </FileItemBlock>
            <div class="layout-table-pagination" v-if="showFlipPage">
                <slot name="pageChange"></slot>
            </div>
            <el-pagination class="layout-table-pagination" v-if="showPagination && totalCount" :total="totalCount"
                :current-page="currentPage" :page-sizes="pagination?.pageSizes" :page-size="pageSize"
                :layout="pagination?.layout" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch, nextTick, onBeforeMount, onUnmounted } from 'vue';
import { useForm } from "@/hooks/useForm"
import type { TableOption, TableColumn, TablePagination, TableDataParams } from '@/model/table'
import type { ElTable, FormInstance } from 'element-plus'
import { $t } from '@/locales'

// 定义props
interface TableProps {
    columns: typeof TableColumn[]; // 每列的配置项
    options?: TableOption; // 表格配置项
    pagination?: TablePagination; // 表格分页配置项
    getList: (params: TableDataParams) => Promise<{ list: Array<object>, total: number }>; // 获取table data 方法，需要返回promise
    selectRow?: []; // 选择列数组
    showPagination?: boolean; // 是否显示分页
    refresh?: any // 自定义更新字段 可以通过改变这个值来触发页面刷新,重新获取数据
    loading?: boolean // loading
    showFilter?: boolean // 是否显示搜索栏
    menuList?: any[] // 按钮列表
    quickMenuList?: any[] // 快捷按钮列表 (目前俩个按钮没有统一,后续统一)
    searchList?: Recordable[] // 搜索栏项
    enableExport?: boolean; // 是否支持导出
    enableImport?: boolean; // 是否支持导入
    sortChange?: (column: any, prop: any, order: any) => void // 排序方法
    enableBlockView?: boolean // 是否支持卡片模式
    showRowItemIcon?: boolean // 是否显示名称前缀ICON
    showFlipPage?: boolean // 是否显示翻页 上一页， 下一页
}
// 接受父组件参数，配置默认值
const props = defineProps<TableProps>();
const tableData = ref<any>([]);
const totalCount = ref(0);
const currentPage = ref(props?.pagination?.currentPage || 1);
const pageSize = ref(props?.pagination?.pageSizes?.[0] || 10);
const selectRows = ref([] as object[])
const table_content_height = ref('86px');
const table_max_height = ref("400px")
const form_width = ref("220px");
const formState = reactive<{ [key: string]: any }>({});
const ruleFormRef = ref<FormInstance>()
const tableRef = ref<InstanceType<typeof ElTable>>()
const viewType = ref('list') // 列表显示模式 支持list block
const isSelectAll = ref(false);
const isIndeterminate = ref(true)

/** 为了让formState的值在dom之前，防止重置的时候清空 */
onBeforeMount(() => {
    formatSearch(props.searchList)
})

onMounted(() => {
    getListData();
    getTableContentHeight();
    window.addEventListener("resize", onResize);
})


onUnmounted(() => {
    window.removeEventListener("resize", onResize);
});

// 监听父组件refresh值,变化了刷新页面
watch(
    () => props.refresh,
    () => {
        getListData();
    },
    { deep: true }
);
// 监听searchList变化
watch(
    () => props.searchList,
    (v) => {
        if (window.location.search) {
            nextTick(() => {
                formatSearch(v)
                getListData();
            })
        }
        formatSearch(v)
    },
    { deep: true }
);

watch(
    () => selectRows.value,
    (v) => {
        const len = v.length
        isSelectAll.value = len === tableData.value.length
        isIndeterminate.value = len > 0 && len < tableData.value.length
    },
    { deep: true }
);

watch(
    () => viewType.value,
    () => {
        getTableContentHeight();
    },
);

const emit = defineEmits<{
    (e: "export", data: any): void,
    (e: "goto", data: any): void,
    (e: "handleSelectionChange", data: any): void,
    (e: "import"): void
}>()

const handleCheckAllChange = (val: boolean) => {
  isIndeterminate.value = false
}

const formatSearch = (data?: Recordable[]) => {
    if (!data) return;
    data.forEach(v => {
        if (v.hasPrepend) {
            formState[v.preType] = v.PreOptions[0]?.value || '';
        }
        if (v.type === 'select') {
            formState[v.key] = v.defaultValue || v.options[0]?.value
        }
        if (v.defaultValue) {
            formState[v.key] = v.defaultValue;
        }
        if (new URLSearchParams(window.location.search).get(v.key)) {
            formState[v.key] = new URLSearchParams(window.location.search).get(v.key);
        }
    })
}

const onResize = () => {
    getTableContentHeight()
}

/** 暂定解决 */
const getTableContentHeight = () => {
    setTimeout(() => {
        const filterDom = document.querySelector('.layout-table-filter');
        table_content_height.value = `calc(100% - ${props.showFilter ? (filterDom?.clientHeight || 0) + 4 : 0}px)`
    }, 100);
    nextTick(() => {
        setTimeout(() => {
            const f = document.querySelector('.layout-table-content');
            if (f) {
                table_max_height.value = `${f?.clientHeight - 56 - 40 - 52 - 4}px`
            }
        }, 100);
    })
}

// 获取表格数据
async function getListData() {
    const params = props.showPagination ? { page: currentPage.value, limit: pageSize.value, search: props.showFilter ? formState : '' } : {};
    if (props.getList) {
        const { list = [], total } = await props.getList(params);
        tableData.value = JSON.parse(JSON.stringify(list));
        totalCount.value = total;
    } else {
        tableData.value = [];
        totalCount.value = 0;
    }
}

// 选择数据方法
const selectedRowChange = (val: Array<object>) => {
    selectRows.value = val
    emit('handleSelectionChange', selectRows.value)
}

// 分页切换每页数量
const handleSizeChange = (val: number) => {
    console.log(`${val} items per page`)
    pageSize.value = val;
    getListData()
}

// 分页切换页数
const handleCurrentChange = (val: number) => {
    console.log(`current page: ${val}`)
    currentPage.value = val;
    getListData();
}

const { resetForm, submitForm } = useForm({
    submitCallBack: () => { getListData() },
    resetFormCallBack: () => {
        currentPage.value = props?.pagination?.currentPage || 1;
        pageSize.value = props?.pagination?.pageSizes?.[0] || 10;
        getListData()
    },
    val: null,
    name: ''
})

// 导入
const importFn = () => {
    emit('import')
}

const exportFn = () => {
    emit("export", { search: props.showFilter ? formState : '' });
}

const clearAll = () => {
    tableRef.value?.clearSelection()
    selectRows.value = []
    isSelectAll.value = false
}

const gotoFile = (val: any) => {
    emit("goto", val);
}

defineExpose({
    tableData
})


</script>
<style lang="scss" scoped>
.layout-table {
    height: 100%;

    &>div {
        padding: var(--app-padding-mid);
        background: #fff;
        margin-bottom: var(--app-margin-min);
    }
}

.layout-table-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: var(--app-padding-mid);
    margin-bottom: var(--app-margin-min);
}

.filter-option {
    // width: 100%;
    margin-bottom: -16px;
}

.layout-table-search-label {
    width: 64px;
    display: flex;
    align-items: center;
}

.layout-table-search {
    width: 256px;
    display: flex;
}

.layout-table-content {
    // min-height: v-bind(table_content_height);
    height: v-bind(table_content_height);
}

.layout-table-header {
    display: flex;
    justify-content: space-between;
    // align-items: center;
    // height: 64px;
    margin-bottom: 24px;
}

.layout-table-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>
