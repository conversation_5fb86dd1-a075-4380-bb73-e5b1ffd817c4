/** 公共按钮组件 后续考虑内置一些通用按钮暴露出去 兼容页面添加更多按钮 */

<template>
    <div>
        <div v-if="isQuick">
            <el-tooltip :show-after="100" :hide-after="0" :enterable="false" :content="menu.label" placement="top" v-for="menu in menus" :key="menu.key" >
                <span>
                    <template v-if="isPermission(menu, items[0])">
                        <el-icon class="action-icon" v-if="!menu.svgIcon" @click.stop="menu.click(items[0])">
                            <component :is="$ElIcon[menu.icon]" />
                        </el-icon>
                        <SvgIcon v-else :name="menu.svgIcon" :width="14" :height="14" class="menu-svg" @click.stop="menu.click(items[0])"></SvgIcon>
                    </template>
                </span>
            </el-tooltip>
        </div>
        <div v-else>
            <el-button v-for="menu in menuList" :key="menu.key" @click.stop="menu.click(items)" :title="menu.label">
                <span class="btn-text">{{ menu.label }}</span>
            </el-button>
        </div>
    </div>
</template>


<script lang="ts" setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router';
import { REFRESH_LABEL_NAME } from '@/model/file'

interface MenuProps {
    menuList: any[],
    items: any[],
    isQuick?: boolean // 是否是快捷操作
}

const props = defineProps<MenuProps>()
const route = useRoute()

const menus = computed(() => {
    const list = props.menuList.filter((menu) => {
        if (typeof menu.hide === 'function') {
            return !menu.hide(props.items[0])
        } else{
            return !menu.hide
        }
   })
    const index = list.findIndex((item) => item.key === 'refresh')
    if (index !== -1) {
        list[index].label = REFRESH_LABEL_NAME[props.items[0].fileType]
    }
    return list
})

// const menus = {
//     // TODO 通用按钮逻辑处理
// }

interface FileRow {
    id: number
    specialFunctions: string[]
}

interface Menu {
    label: string
    icon: string
    key: string
}

const isPermission = (menu: Menu, row: FileRow) => {
  return row.specialFunctions?.length && row.specialFunctions.includes(menu.key) || false
}

</script>

<style scoped>
.action-icon {
    cursor: pointer;
    margin-right: 10px;
}
.menu-svg {
    margin-right: 10px;
    position: relative;
    bottom: 2px;
    cursor: pointer;
}
</style>
<style>
.cls-2 {
    /* stroke: rgb(96, 98, 102); */
    stroke: black;
}
</style>
