import * as Icons from '@element-plus/icons';
<template>
  <div class='tip-box'>
    <el-icon class="action-icon">
      <component :is="$ElIcon['WarningFilled']" />
    </el-icon>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang='ts'>

</script>

<style lang='scss' scoped>
.tip-box {
  width: 100%;
  height: auto;
  overflow: hidden;
  border: 1px solid #0052CC;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #EBF6FF;
}

.action-icon {
  font-size: 20px;
  color: #4A9CF2;
  margin-right: 20px;
}

.content {
  width: 100%;
  height: auto;
  overflow: hidden;
}
</style>