/** 批量结果页,用户的批量操作比如批量删除会弹出该弹框,然后一条一条显示结果信息,后续还考虑可以导出 */
<template>
    <el-dialog v-model="dialogVisible" :width="detailWidth" destroy-on-close>
        <template #header>
            <div class="drawer-title">{{ detailTitle }}</div>
        </template>
        <template #default>
            <el-card class="box-card">
                <div class="result-row" v-for="(item, index) in items" :key="index">
                    <TipText width="257px" :content="item.user_name || items.name">
                        <template v-slot:content>
                            <div>{{ item.user_name || items.name }}</div>
                        </template>
                    </TipText>
                    <div>{{ $t('等待中') }}</div> <!-- 显示icon?  -->
                    <div>{{ $t('成功') }}</div> <!-- 成功显示对号,失败显示原因  -->
                </div>
            </el-card>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onBeforeMount } from 'vue'
import { emitter } from "@/utils/mitt";
import { $t } from '@/locales';
import type { ResponseData } from '@/model/public';

export interface detailType {
    dialogVisible: boolean
    show: (v: object) => void
}
interface BatchResultProps {
    items?: any,
    width?: string,
    title?: string,
    batchApi?: (id: string) => Promise<ResponseData | any>
}

const props = withDefaults(defineProps<BatchResultProps>(), {
    items: [],
    width: "50%",
    title: "",
    isShow: false,
})

const dialogVisible = ref(false)
const items = ref()
const detailTitle = ref(props.title);
const detailWidth = ref(props.width);

const show = (data: object) => {
    items.value = data
    dialogVisible.value = true
}

defineExpose({
    dialogVisible,
    show
})

onBeforeMount(() => {
    // 显示弹窗
    emitter.on("batchResultDialogShow", (params: { data: any, title?: string, width?: string }) => {
        items.value = params.data;
        if (params.title) detailTitle.value = params.title;
        if (params.width) detailWidth.value = params.width;
        dialogVisible.value = true;
    });

});

const batch = async () => {
    // TODO 执行批量操作,每一条更新一下items里面的数据
}

</script>

<style scoped>
.box-card {
    max-height: 60vh;
    overflow: auto;
}

.result-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    border: 1px solid #ebeef5;
    padding-left: 12px;
    padding-right: 12px;
}
</style>
