
<template>
  <div class=''>
    <span class="page-total" v-if="props.showTotal"> {{ $t('共 {total} 条', { total: props.total }) }}</span>
    <el-select v-model="size" class="page-size" @change="changePageSize">
      <el-option v-for="option in pageSizes" :key="option.value" :label="option.label"
                                :value="option.value" />
    </el-select>
    <el-button type="primary" @click="previousPage" class="first-page" :disabled="props.previousFlag">
      <span class="pagination-btn-text">{{ $t('上一页') }}</span>
    </el-button>
    <template v-if="props.pageArr">
      <span :class="[ activeNumber === item ? 'page-item active' : 'page-item']" v-for="(item, key) in pageArr" :key="key + 'o'" @click="changePage(item)">{{ item }}</span>
    </template>
    <el-button type="primary" @click="nextPage" class="next-page" :disabled="props.nextFlag">
      <span class="pagination-btn-text">{{ $t('下一页') }}</span>
    </el-button>
  </div>
</template>


<script setup lang='ts'>
import { ref, defineEmits, watchEffect } from 'vue';
import { $t } from '@/locales';

const emit = defineEmits(['previousPage', 'nextPage', 'changePage', 'sizeChange'])
// 定义props的
interface Props {
  nextFlag: boolean
  previousFlag: boolean
  pageArr?: string[]
  currPage: number
  pageSize: number
  total?: number
  showTotal?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  nextFlag: true,
  previousFlag: true,
  pageArr: [] as never,
  currPage: 0,
  total: 0,
  pageSize: 10,
  showTotal: false
})

const activeNumber = ref("0")
const size = ref(props.pageSize)

const pageSizes = [{
  label: $t('{size}条/页', { size: 10 }),
  value: 10
}, {
  label: $t('{size}条/页', { size: 20 }),
  value: 20
}, {
  label: $t('{size}条/页', { size: 30 }),
  value: 30
}, {
  label: $t('{size}条/页', { size: 40 }),
  value: 40
}]

watchEffect(() => activeNumber.value = props.currPage.toString() )

function nextPage(){
  emit('nextPage')
}

function previousPage(){
  emit('previousPage')
}

function changePage(key:string){
  if(key === activeNumber.value) {
    return  
  }
  activeNumber.value = key
  emit('changePage', key)
}

// 改变页数
function changePageSize(val: number) {
  emit('sizeChange', val)
}

</script>

<style scoped>
.page-item {
  width: 32px;
  height: 32px;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  line-height: 32px;
  font-size: 14px;
  cursor: pointer;
  margin: 0 5px;
  border-radius: 3px;
}

.page-item:hover {
  background-color: #0052CC;
  color: #fff;
}

.page-size {
  width: 100px;
  margin-right: 12px;
}
.page-total {
  margin-right: 16px;
  color: var(--el-text-color-regular);
}
.active {
  background-color: #0052CC;
  color: #fff;
}

.pagination-btn-text {
  display: inline-block;
  max-width: 80px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
