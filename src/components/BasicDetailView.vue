/** 详情公共组件,目前功能比较少,只展示具体key, value值, 由于没有上国际化翻译问题还没有处理, 后续慢慢重构 */
<template>
    <el-drawer v-model="dialogVisible" :width="detailWidth" destroy-on-close>
        <template #header>
            <div class="drawer-title">{{ detailTitle }}</div>
        </template>
        <template #default>
            <el-card class="box-card">
                <el-row :gutter="20" justify="center" v-for="(item) in items" :key="item.key">
                    <el-col :span="12">
                        <div class="grid grid-label">{{ t(`model.${item.key }`) }}</div>
                    </el-col>
                    <el-col :span="12" class="grid">
                        <TipText width="257px" :content="item.value" :showTip="items.isHtml">
                            <template v-slot:content>
                                <span v-if="item.isHtml" v-html="item.value"></span>
                                <span v-else class="grid-value">{{ item.value || "-" }}</span>
                            </template>
                        </TipText>
                    </el-col>
                </el-row>
            </el-card>
        </template>
    </el-drawer>
</template>

<script lang="ts" setup>
import { ref, onBeforeMount, inject } from 'vue'
import { emitter } from "@/utils/mitt";

export interface detailType {
    dialogVisible: boolean
    show: (v: object) => void
}
interface DetailProps {
    items?: any,
    width?: string,
    title?: string,
}

const props = withDefaults(defineProps<DetailProps>(), {
    items: [],
    width: "50%",
    title: "",
    isShow: false,
})

const dialogVisible = ref(false)
const items = ref()
const detailTitle = ref(props.title);
const detailWidth = ref(props.width);
const t = inject<any>('t');

const show = (data: object) => {
    items.value = data
    dialogVisible.value = true
}

defineExpose({
    dialogVisible,
    show
})

onBeforeMount(() => {
    // 显示弹窗
    emitter.on("detailViewShow", (params: { data: any, title?: string, width?: string }) => {
        items.value = params.data;
        if (params.title) detailTitle.value = params.title;
        if (params.width) detailWidth.value = params.width;
        dialogVisible.value = true;
    });


});

</script>

<style scoped>
.box-card {
    max-height: 60vh;
    overflow: auto;
}

.grid {
    height: 32px;
    display: flex !important;
    justify-content: center;
    align-items: center;
    font-size: 14px;
}

.grid-label {
    color: #909399;
}

.grid-value {
    color: #606266;
}
</style>
