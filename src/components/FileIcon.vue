<template>
    <div class="flex relative" :style="{width: width, height: height}" :key="_key">
        <img v-if="showThumbnail && userStores.token" :src="imgSrc"
            :style="{ maxHeight: props.item.thumbnail ? '100%' : 'none', maxWidth: props.item.thumbnail ? '100%' : 'none', width: props.item.thumbnail ? 'auto' : width, height: props.item.thumbnail ? 'auto' : height, objectFit: props.item.thumbnail ? 'contain' : 'cover' }"
            :crossorigin="'anonymous'" :onerror="imgOnError" class="thumbnail-img">
        <SvgIcon :name="name" :size="size" :width="props.width" :height="props.height" v-else></SvgIcon>
        <SvgIcon :name="type" :size="14" class="mark" v-if="type"></SvgIcon>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { FILETYPE, type FileItem } from "@/model/file";
import { getFileIconName, isType } from '@/utils'
import { useUserStore } from "@/stores/user";
import { unknown_icon, THUMB_SIZE } from '@/utils/const';
import { getPreviewUrl } from '@/utils/file';

interface Props {
    item: FileItem,
    size?: number | string,
    width?: number | string,
    height?: number | string,
    type?: string // 角标
    isBlock?: boolean // 是否是block模式
}

const props = defineProps<Props>();
const name = computed(() => {
    const iconName = getFileIconName(props.item);
    return `${iconName}${props.isBlock && props.item.fileType !== FILETYPE.FOLDER ? '-block' : ''}`;
})

const _key = computed(() => {
    return `${props.item.id}-${Math.random()}`
})

const showThumbnail = computed(() => {
    // 目前只有图片和2D,3D文件展示缩略图
    return isType(props.item, "image") || (props.item.thumbnail && props.item.thumbnail !== '0')
})

const userStores = useUserStore()

const imgSrc = computed(() => {
    const thumbSize = props.isBlock ? THUMB_SIZE.LARGE : THUMB_SIZE.SMALL
    const previewUrl = getPreviewUrl(props.item, (props.item.thumbnail && props.item.thumbnail !== '0') ? 'cad' : '', thumbSize);
    return previewUrl || ''
})

const width = computed(() => {
    return props.width ? props.width + 'px' : props.size ? props.size + 'px' : '100%'
})

const height = computed(() => {
    return props.height ? props.height + 'px' : props.size ? props.size + 'px' : '100%'
})

const imgOnError = (e: any) => {
    const img = e.srcElement;
    img.src = unknown_icon;
    img.onerror = null;
}

</script>

<style lang="scss" scoped>
.mark {
    position: absolute;
    margin-top: 16px;
}

.thumbnail-img {
    // object-fit: cover;
}
</style>