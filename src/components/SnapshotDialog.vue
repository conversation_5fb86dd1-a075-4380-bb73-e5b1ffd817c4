/** 快照列表页 */
<template>
  <el-dialog v-model="dialogVisible" :width="detailWidth" destroy-on-close @close="reset">
    <template #header>
      <div class="drawer-title">{{ detailTitle }}</div>
    </template>
    <template #default>
      <div class="flex justify-between">
        <!-- 面包屑 -->
        <Breadcrumb :breadcrumbList="crumbsList" @changeBreadcrumb="(item: CrumbsListItem) => openDir(item)">
        </Breadcrumb>
        <div class="flex items-center">
          <SvgIcon name="block" :size="18" class="cursor-pointer" v-if="viewType === 'list'"
            @click="() => viewType = 'block'"></SvgIcon>
          <SvgIcon name="list" :size="18" class="ml-2 cursor-pointer" v-if="viewType === 'block'"
            @click="() => viewType = 'list'"></SvgIcon>
        </div>
      </div>

      <el-table :data="items" :height="table_max_height" v-show="viewType === 'list'">
        <el-table-column :label="$t('文件名')" :show-overflow-tooltip="true" prop="name" width="350" fixed="left">
          <template #default="scope">
            <div class="dir-name" @click="($event) => handleClickFileName(scope.row, $event)">
              <FileIcon :width="40" :height="30" :item="scope.row"></FileIcon>
              <div class="ml-2 truncate">{{ scope.row.name }}</div>
              <div v-if="scope.row.noteHaveAlter || scope.row.fileHaveAlter" class="file-note-new">
                <img v-if="scope.row.noteHaveAlter" src="/assets/imgs/note-new.png">
                <img v-if="scope.row.fileHaveAlter" src="/assets/imgs/file-new.png">
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="ownerName" :label="$t('上传者')" min-width="140" />
        <el-table-column property="version" :label="$t('当前版本')" min-width="120" />
        <el-table-column property="lastModifyTime" sortable min-width="160" :label="$t('修改时间')" />
        <el-table-column property="size" sortable :label="$t('文件大小')" min-width="120">
          <template #default="scope">
            {{ scope.row.fileType !== 1 ? formatSize(scope.row.size) : '' }}
          </template>
        </el-table-column>
        <el-table-column property="createTime" min-width="160" :label="$t('创建时间')" />
      </el-table>

      <FileItemBlock :style="{ height: table_max_height }" v-show="viewType === 'block'" :items="items"
        @goToItem="handleClickFileName" :hideSelected="true" :handleClickFileName="handleClickFileName">
      </FileItemBlock>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onBeforeMount, reactive } from 'vue'
import { emitter } from "@/utils/mitt";
import { $t } from '@/locales';
import { formatSize } from '@/utils'
import type { CrumbsListItem, FileItem } from '@/model/file';
import { gotoItem } from '@/utils/file';

export interface detailType {
  dialogVisible: boolean
  show: (v: object) => void
}
interface snapshotProps {
  items?: any,
  width?: string,
  title?: string,
}

const props = withDefaults(defineProps<snapshotProps>(), {
  items: [],
  width: "50%",
  title: "",
  isShow: false,
})

const dialogVisible = ref(false)
const table_max_height = ref('45vh')
const items = ref()
const viewType = ref('list')
const detailTitle = ref(props.title);
const detailWidth = ref(props.width);
const crumbsList = reactive<CrumbsListItem[]>([])

const show = (data: object) => {
  items.value = data
  dialogVisible.value = true
}

defineExpose({
  dialogVisible,
  show
})

onBeforeMount(() => {
  // 显示弹窗
  emitter.on("snapshotDialogShow", (params: { item?: Recordable, width?: string }) => {
    if (params.item) {
      detailTitle.value = $t('{name}快照', { name: params.item.name });
      openDir(params.item)
    }
    if (params.width) detailWidth.value = params.width;
    dialogVisible.value = true;
  });

});

const changeBreadcrumb = (item: Recordable) => {
  const index = crumbsList.findIndex(v => v.folderId === (item.id || item.folderId))
  if (index !== -1) {
    crumbsList.splice(index + 1, crumbsList.length - index);
  } else {
    crumbsList.push({
      folderId: item.id,
      folderName: item.name,
      children: item.children
    })
  }
}

const handleClickFileName = (item: FileItem, event: MouseEvent) => {
  gotoItem(item, () => openDir(item))
}

const openDir = (item: Recordable) => {
  changeBreadcrumb(item)
  items.value = item.children
}

const reset = () => {
  crumbsList.splice(0, crumbsList.length)
  viewType.value = 'list'
}

</script>

<style scoped>
.dir-name {
  cursor: pointer;
  display: flex;
  align-items: center;
}
.file-note-new {
  position: relative;
  top: -12px;
  width: 26px;
  height: 12px;
  cursor: text;
}
</style>
