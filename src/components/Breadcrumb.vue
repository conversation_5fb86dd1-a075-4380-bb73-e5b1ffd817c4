
/** 公共面包屑组件,后续兼容添加路由,添加复制路径,省略号显示面包屑等功能 */
<template>
  <el-breadcrumb class="breadcrumb-box" separator="/">
    <el-breadcrumb-item v-for="item in breadcrumbList" @click="handleLink(item)" :key="item.folderId">
      <TipText width="100%" max-width="160px" :content="item.folderName">
        <template v-slot:content>
          {{ item.folderName }}
        </template>
      </TipText>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>
<script setup lang="ts">
import type { CrumbsListItem } from '@/model/file';


interface Props {
  breadcrumbList: CrumbsListItem[]
  limit?: number // 同一行最多显示多个面包屑,多余的用...下拉的方式展示
}
const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "changeBreadcrumb", data: any): void,
}>()

const handleLink = (item: CrumbsListItem) => {
  emit('changeBreadcrumb', item)
}


</script>
<style lang="scss" scoped>
.breadcrumb-box {
  margin: 16px 0;
  line-height: 16px;
  :deep(.el-breadcrumb__inner) {
    color: #0069ff !important;
    cursor: pointer !important;
  }
}
</style>
