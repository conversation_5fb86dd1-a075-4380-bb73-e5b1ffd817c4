<template>
  <el-dialog :title="$t('编辑')" v-model="dialogVisible" @close="dialogClose" width="500px" destroy-on-close custom-class="edit-permission">
    <!-- 编辑 -->
    <div v-if="editStatus === 'edit'">
      <div class="title">{{ $t('该文件{editor}正在编辑中，如需进入编辑，请点击继续编辑。', { editor: fileData.editor }) }}</div>
      <div class="comment">注：当前文件仅支持一人编辑，点击“继续编辑”{{ fileData.editor }}将终止编辑。</div>
    </div>
    <!-- 归并 -->
    <div v-if="editStatus === 'merge'">
      <div class="title">{{ $t('当前文件正在归并中，请耐心等待...') }}</div>
      <el-progress :percentage="mergeProgress" :strokeWidth="16" />
    </div>
    <!-- 停止合并 -->
    <div v-if="editStatus === 'stop'">
      <div class="title text-[#909399]">{{ $t('停止归并后{editor}编辑的部分数据将无法找回，确认停止归并并进入文件编辑页面？', { editor: fileData.editor }) }}</div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="continueEdit" v-if="editStatus === 'edit'">{{ $t('继续编辑') }}</el-button>
        <el-button type="primary" @click="stopMerge" v-if="editStatus === 'merge'">{{ $t('停止并编辑') }}</el-button>
        <el-button type="primary" @click="affirmStop" v-if="editStatus === 'stop'">{{ $t('确定') }}</el-button>
        <el-button type="default" @click="dialogClose">{{ $t('取消') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang='ts'>
import { ref, computed, onMounted, onBeforeMount } from 'vue';
import { useRouter } from 'vue-router';
import { snatchEdit, getMergeData, giveUpMerge } from '@/services/file'
import { emitter } from "@/utils/mitt";
import { $t } from '@/locales';
import { openUrlFn } from "@/utils";

type ModeType = 'edit' | 'merge' | 'stop'

const dialogVisible = ref(false)
const editStatus = ref<ModeType>('edit')
const fileData:any = ref({ editor: '', queryData: { id: 0 } })
const allMerge = ref(0)
const currentMerge = ref(0)
const $router = useRouter();

let timer: any = null

const mergeProgress = computed(() => {
  if (allMerge.value === 0) {
    return 0
  }
  return Math.round(((allMerge.value - currentMerge.value) / allMerge.value) * 100)
})

onMounted(() => {
  // 显示弹窗
  emitter.on("showEditPermissionDialog", (params: { data: any }) => {
    openDialog(params.data)
  });
});

onBeforeMount(() => {
  emitter.off("showEditPermissionDialog")
});

const continueEdit = async () => {
  try {
    const res = await snatchEdit({
      fileId: fileData.value.queryData.id,
      devId: fileData.value.queryData.did ?? ''
    })
    if (res.code === 0) {
      editStatus.value = 'merge'
      getMergeProgress(true)
      timer = setInterval(() => {
        getMergeProgress(false)
      }, 1000)
    }
  } catch (error) {
    console.log(error)
  }
}

const getMergeProgress = async (isFirst: boolean) => {
  try {
    const res = await getMergeData(fileData.value.queryData.id)
    const waitMergeNums = res?.data?.waitMergeNums ?? 0
    if (isFirst) {
      allMerge.value = waitMergeNums
    }
    if (waitMergeNums > allMerge.value) {
      const diff = waitMergeNums - currentMerge.value
      allMerge.value += diff
    }
    currentMerge.value = waitMergeNums
    if (res.data.waitMergeNums === 0) {
      dialogClose()
      toEditFile()
    }
  } catch(error) {
    console.log(error)
  }
}

const stopMerge = () => {
  clearInterval(timer)
  editStatus.value = 'stop'
}

const affirmStop = async () => {
  try {
    const res = await giveUpMerge({
      fileId: fileData.value.queryData.id
    })
    if (res.code === 0) {
      dialogClose()
      toEditFile()
    }
  } catch (error) {
    console.log(error)
  }
}

const dialogClose = () => {
  dialogVisible.value = false
  clearInterval(timer)
}

const openDialog = (data: any) => {
  console.log("11111111", data)
  clearInterval(timer)
  editStatus.value = 'edit'
  fileData.value = data
  dialogVisible.value = true
}

const toEditFile = () => {
  // $router.push({
  //   path: `/app/CloudCAD`,
  //   query: fileData.value.queryData,
  // })
  openUrlFn(`/cloudCAD.html`, {...fileData.value.queryData, enableHeader: true})
}

defineExpose({
  openDialog
})
</script>
<style lang='scss'>
.edit-permission {
  .el-dialog__body {
    height: auto !important;
    .title {
      font-size: 14px;
      margin-bottom: 4px;
    }
  
    .comment {
      font-size: 12px;
      color: #A8ABB2;
    }
  }
}
</style>