<template>
    <el-card class="transfer-layout border-0" v-show="isShow">
        <div class="head">
            <span>{{ $t('任务列表') }}</span>
            <SvgIcon :name="'shouqi'" :size="16" class="mark ml-2" :class="{ 'folded': isCollapse }"
                @click="isCollapse = !isCollapse"></SvgIcon>
            <el-icon :color="'#fff'" :size="14" class="block ml-2 cursor-pointer" @click="close">
                <component :is="$ElIcon['Close']" />
            </el-icon>
        </div>
        <div class="body" :class="{ 'folded': isCollapse }">
            <el-tabs v-model="activeName" type="border-card" class="border-0 h-full">
                <el-tab-pane name="uploadTab" :label="uploadTitle">
                    <UploadProgress />
                </el-tab-pane>
                <el-tab-pane name="taskTab" :label="taskTitle" class="task-pane">
                    <AsyncTaskList ref="asyncTaskRef" />
                </el-tab-pane>
            </el-tabs>
        </div>
    </el-card>
</template>
  
<script setup lang="ts">
import { computed, ref, onMounted, watch, onUnmounted } from 'vue';
import { ETASKSTATUS } from '@/model/task'
import { emitter } from '@/utils/mitt'
import UploadProgress from "@/components/upload/UploadProgress.vue";
import AsyncTaskList from "@/components/AsyncTaskList.vue";
import { uploadState } from '@/stores/upload'
import { $t } from '@/locales';
import { storeToRefs } from 'pinia';

const asyncTaskRef = ref<InstanceType<typeof AsyncTaskList> | null>(null);

const uploadStore = uploadState()
const { show } = storeToRefs(uploadStore);
const uploadReqArr = computed(() => uploadStore.uploadReqArr)
const finishUpload = computed(() => uploadStore.uploadFinishNumber)

const taskShow = ref(false)
const isCollapse = ref(false)
const isShow = computed(() => show.value || taskShow.value)

const activeName = ref('uploadTab')

const uploadTitle = computed(() => {
    if (finishUpload.value === uploadReqArr.value.length) return $t('上传任务')
    return $t('上传中（{finished}/{total}）', { finished: finishUpload.value, total: uploadReqArr.value.length })
})

const taskTitle = computed(() => {
    const taskList = asyncTaskRef.value?.taskList || []
    if (!taskList.length) return $t('转换任务')
    const successCount = taskList.filter(task => task.status === ETASKSTATUS.finished).length
    const failedCount = taskList.filter(task => task.status === ETASKSTATUS.failed).length
    return $t('转换任务（成功: {success} 失败: {failed} 进行中: {running}）', {
        success: successCount,
        failed: failedCount,
        running: taskList.length - successCount - failedCount
    })
})

watch(uploadReqArr, (val) => {
    if (val.length) {
        activeName.value = 'uploadTab'
        isCollapse.value = false
    }
});

onMounted(() => {
    emitter.on('addTaskToAsync', addTaskToAsync)
})

onUnmounted(() => {
    emitter.off('addTaskToAsync')
})

const addTaskToAsync = () => {
    activeName.value = 'taskTab'
    taskShow.value = true
    isCollapse.value = false
}

const close = () => {
    uploadStore.reset()
    asyncTaskRef.value?.close()
    taskShow.value = false
}

</script>
  
<style lang="scss" scoped>
.transfer-layout {
    position: absolute;
    right: 12px;
    bottom: 0;
    z-index: 9999999;
    box-shadow: 0 0 8px #d8d8d8d6;
    width: 600px;
    display: flex;
    flex-direction: column;
    height: max-content;

    :deep(.el-card) {
        border: none;
    }

    :deep(.el-tabs__content) {
        padding: 0;
    }

    :deep(.el-card__body) {
        padding: 0;
    }

    :deep(.el-tabs__header) {
        background-color: #fafafa
    }

    .task-pane {
        padding: 16px;
        height: 260px;
        overflow-y: auto;
    }
}

.head {
    height: 50px;
    line-height: 50px;
    background-color: #0069ff;
    border-color: #0069ff;
    padding-left: 21px;
    padding-right: 21px;
    display: flex;
    align-items: center;
    color: #fff;

    span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        vertical-align: middle;
    }

    .mark {
        cursor: pointer;
        transition: transform .3s linear;
    }

    .folded {
        transform: rotate(180deg);
    }
}

.body.folded {
    height: 0px;
}

.body {
    transition: .3s cubic-bezier(.4, 0, 1, 1) !important;
    height: 300px;
    background-color: #fff;


}
</style>