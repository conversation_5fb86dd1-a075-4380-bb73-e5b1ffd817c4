<template>
    <div class="dragger" @dragenter.stop="dragenter" draggable="false" @drop.prevent.stop="drop"
        @dragend.prevent.stop="dropEnd" @dragover.prevent.stop="dropOver">
        <div :class="dragging ? 'dragging forbidden-child-point-event' : 'normal'" @dragleave="dragleave" draggable="true">
            <div class="hint">
                <el-icon>
                    <component :is="$ElIcon['Rank']" />
                </el-icon>
                <span class="ml-2 align-middle">拖拽上传</span>
            </div>
        </div>
        <slot></slot>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue"

interface Props {
    enableDrop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    enableDrop: true,
})

const dragging = ref(false);

const emit = defineEmits<{
    (e: "importFile", data: any[]): void
}>()

const dragenter = (e: DragEvent) => {
    e.preventDefault();
    // 如果拖拽的不是文件则直接return
    if (e.dataTransfer && !e.dataTransfer.types.includes('Files')) {
        return;
    }
    dragging.value = props.enableDrop;
}

const drop = async (e: DragEvent) => {
    e.preventDefault();
    if (!dragging.value) return;
    dragging.value = false;
    if (e.dataTransfer) {
        const { items, files } = e.dataTransfer
        const item = items[0]?.webkitGetAsEntry()// 获取当前文件夹的Entry（webkit内核特有），然后去递归Entry
        if (item) {
            // 说明是文件夹
            if (item.isDirectory) {
                const filesList: any[] = []
                await scanFiles(item, filesList);
                setTimeout(() => {
                    emit('importFile', filesList)
                }, 150);
            } else {
                if (files) emit('importFile', Array.from(files))
            }
        }
    }
}

const scanFiles = (entry: any, filesList: any[]) => {

    return new Promise((resolve, reject) => {
        if (entry.isDirectory) {
            const directoryReader = entry.createReader()
            const fnReadEntries = async (entries: any[]) => {
                try {
                    for (let i = 0; i < entries.length; i++) {
                        await scanFiles(entries[i], filesList)
                    }
                    if (entries.length > 0) {
                        directoryReader.readEntries(fnReadEntries);
                    }
                    resolve(1)
                } catch (e: any) {
                    console.log(2)
                    reject(e)
                }
            }
            directoryReader.readEntries(fnReadEntries)
        } else {
            entry.file(
                async (file: any) => {
                    const path = entry.fullPath.substring(1)
                    /**修改webkitRelativePath 是核心操作，原因是拖拽会的事件体中webkitRelativePath是空的，而且webkitRelativePath 是只读属性，普通赋值是不行的。所以目前只能使用这种方法将entry.fullPath 赋值给webkitRelativePath**/
                    const newFile = Object.defineProperty(file, 'webkitRelativePath', {
                        value: path,
                    })
                    filesList.push(newFile)
                    resolve(1)
                },
                (e: any) => {
                    reject(e)
                }
            )
        }
    })
}



const dropEnd = (e: DragEvent) => {
    e.preventDefault();
}


const dropOver = (e: DragEvent) => {
    e.preventDefault();
}



const dragleave = (e: DragEvent) => {
    e.preventDefault();
    dragging.value = false;
}

</script>

<style lang="scss" scoped>
.dragger {
    position: relative;
    width: 100%;
}

.normal {
    display: none;
}

.dragging {
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
    // border: 2px dashed #1a80f5;
    border: 2px dashed var(--el-color-primary);
    // background-color: rgba(26, 128, 245, 0.4);
    // background-color: var(--el-color-primary-light-9);
    background-color: #1a80f50a;
    box-sizing: border-box;
    display: block;
    z-index: 5;
}

.forbidden-child-point-event {
    &>div {
        pointer-events: none !important;
    }
}

.hint {
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    // height: 40%;
    line-height: 48px;
    background: #1a80f5;
    box-shadow: 0 2px 4px 0 rgb(4 4 4 / 15%);
    border-radius: 4px;
    background-color: #1a80f5;
    color: #fff;
    padding-right: 16px;
    padding-left: 16px;
    position: absolute;
    font-size: 14px;
    text-align: center;
    display: flex;
    align-items: center;
}
</style>