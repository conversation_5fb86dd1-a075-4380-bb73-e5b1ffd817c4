<template>
  <div>
    <el-upload ref="uploadRef" :show-file-list="false" :auto-upload="false" id="upload-dom" :multiple="true" :drag="drag"
      :on-change="changeChores">
      <el-button :type="buttonType" :title="buttonTitle">
        <span class="btn-text">{{ buttonTitle }}</span>
      </el-button>
    </el-upload>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, defineExpose, computed } from 'vue';
import type { UploadUserFile, UploadInstance, UploadFile, UploadFiles, UploadProps } from 'element-plus'
import { uploadState } from '@/stores/upload'
import type { UploadItem } from '@/stores/upload'
import { guid, getExt, getShortName, formatSize, getFileSha1 } from '@/utils'
import { uploadFile } from '@/services/file'
// import {debounce} from 'ts-debounce-throttle'
import type { UploadQuery, NewUploadFile } from '@/model/file'
import { UPLOAD_STATUS } from '@/utils/const'
import { emitter } from "@/utils/mitt";
import { $t } from '@/locales';

// 定义props的
interface Props {
  buttonTitle?: string
  buttonType?: string
  webkitdirectory?: boolean
  drag?: boolean
  folderId: number
  toFolderId?: number
  accept?: string
}
const props = withDefaults(defineProps<Props>(), {
  buttonTitle: $t('上传文件'),
  buttonType: 'default',
  webkitdirectory: false,
  drag: false,
  folderId: 1,
  toFolderId: 1,
  accept: ''
})

const uploadRef = ref<UploadInstance>();
const maxLength = ref(0);

const emit = defineEmits<{
  (e: "changeFiles", data: any): void,
}>()

const initElUpload = () => {
  const uploadEl = uploadRef.value?.$el as HTMLElement
  const inputEl = uploadEl.querySelector<HTMLInputElement>('input')!
  inputEl.webkitdirectory = props.webkitdirectory
}

onMounted(() => {
  initElUpload()
});

const fileList = ref<UploadUserFile[]>()

// 监听input选择文件变化
const changeChores: UploadProps['onChange'] = (file: UploadFile, uploadFiles: UploadFiles) => {
  const length = uploadFiles.length
  maxLength.value = Math.max(length, maxLength.value)
  setTimeout(async () => {
    if (length !== maxLength.value) {
      return
    } else {
      emit("changeFiles", uploadFiles)
      maxLength.value = 0;
      uploadRef.value && uploadRef.value.clearFiles()
    }
  })
}

defineExpose({
  fileList
})
</script>
<style lang="scss" scoped>
::v-deep .el-upload {
  --el-upload-dragger-padding-horizontal: 0px;
  --el-upload-dragger-padding-vertical: 0px;
  display: inline-flex;
}

::v-deep .el-upload-list {
  height: 360px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
