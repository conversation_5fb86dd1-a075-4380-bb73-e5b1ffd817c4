<template>
  <el-table class="upload-table" :data="uploadReqArr" border>
    <el-table-column prop="name" :label="$t('文件名')" width="338">
      <template v-slot="scope">
        <span :title="scope.row.name" class="file-name">{{ scope.row.shortName }}</span><span>.{{
          scope.row.nameExt }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="formatSize" :label="$t('大小')" width="118">
    </el-table-column>
    <el-table-column prop="status" :label="$t('状态')">
      <template v-slot="scope">
        <img class="status-icon" v-if="scope.row.status === UPLOAD_STATUS.BEFORE" src="/assets/imgs/upload/sleep.png"
          alt="">
        <!-- <img class="status-icon rotate" v-else-if="scope.row.status === UPLOAD_STATUS.IN"
              src="/assets/imgs/upload/loading.png" alt=""> -->
        <img class="status-icon" v-else-if="scope.row.status === UPLOAD_STATUS.SUCCESS"
          src="/assets/imgs/upload/success.png" alt="">
        <img class="status-icon" v-else-if="scope.row.status === UPLOAD_STATUS.FAIL" src="/assets/imgs/upload/error.png"
          alt="">
        <span class="status-text" v-if="scope.row.status === UPLOAD_STATUS.BEFORE">{{ $t('待上传') }}</span>
        <span class="status-text" v-else-if="scope.row.status === UPLOAD_STATUS.IN">
          <el-progress :percentage="scope.row.progress" style="width: 120px" />
        </span>
        <span class="status-text" v-else-if="scope.row.status === UPLOAD_STATUS.SUCCESS">{{ $t('上传成功') }}</span>
        <span class="status-text error-text" :title="scope.row.errorMsg || $t('上传失败')"
          v-else-if="scope.row.status === UPLOAD_STATUS.FAIL">{{ scope.row.errorMsg || $t('上传失败') }}</span>
        <img class="status-icon delete-item" v-if="scope.row.status === UPLOAD_STATUS.BEFORE"
          src="/assets/imgs/upload/delete.png" @click="deleteUpload(scope.row.uid)" alt="">
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup lang='ts'>
import { computed } from 'vue'
import { uploadState } from '@/stores/upload'
import { UPLOAD_STATUS } from '@/utils/const'
import { $t } from '@/locales'

const uploadStore = uploadState()
const uploadReqArr = computed(() => uploadStore.uploadReqArr)

function deleteUpload(uid: string) {
  uploadStore.deleteUploadReq(uid)
}
</script>
<style lang='scss' scoped>
.upload-progress {
  position: fixed;
  width: 600px;
  bottom: 66px;
  right: 19px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 3px;
  z-index: 6;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.14);
}

.upload-content {
  width: 100%;
  height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
}


.upload-header {
  height: 50px;
  width: 100%;
  background: #409eff;
  border-radius: 3px 3px 0 0;


  &-title {
    font-size: 16px;
    font-weight: 500;
    text-align: left;
    color: #ffffff;
    line-height: 50px;
    margin-left: 32px;
  }
}


.open-window {
  width: 200px;
  height: 30px;
  text-align: left;
}

.slide-up,
.slide-down {
  width: 16px;
  display: block;
  height: 16px;
  cursor: pointer;
  margin-right: 6px;
  z-index: 3;
  margin-top: 19px;
  float: right;
}

.close-window {
  width: 16px;
  display: block;
  height: 16px;
  cursor: pointer;
  margin-right: 17px;
  z-index: 3;
  margin-top: 18px;
  float: right;
}

.window-title {
  display: inline-block;
  margin-left: 5px;
  font-size: 12px;
  line-height: 26px;
}


:deep(.el-table) {
  color: rgba(0, 0, 0, 0.90);
}

:deep(.el-table thead) {
  color: rgba(0, 0, 0, 0.40);
}

:deep(.el-table .cell, .el-table th div, .el-table--border td:first-child .cell, .el-table--border th:first-child .cell) {
  padding-left: 17px;
  font-weight: 400 !important;
}

.file-name {
  height: 24px;
  display: inline-block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
  max-width: 253px;
}

.status-text,
.status-icon {
  vertical-align: middle;
  display: inline-block;
  margin-right: 8px;
}

.error-text {
  max-width: calc(100% - 30px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.rotate {
  animation: loading 1s linear infinite;
}

@keyframes loading {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.delete-item {
  cursor: pointer;
}

.hide {
  display: none;
}

.upload-table {
  height: 260px;
}
</style>