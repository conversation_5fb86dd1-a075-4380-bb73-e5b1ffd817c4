/**
 * 公共详情页
 * TODO 完成模板自定义渲染和公共props
 */
import { defineComponent, ref } from "vue";
import {
    ElDrawer,
    ElScrollbar,
    ElDescriptions,
    ElDescriptionsItem,
} from "element-plus";
import TipText from "@/components/TipText.vue";
import { emitter } from "@/utils/mitt";
import { useI18n } from "vue-i18n";

export default defineComponent({
    name: "Descriptions",
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
        width: {
            type: String,
            default: "30%",
        },
        drawerFooter: {
            type: Element,
            default: null,
        },
        DescriptionProps: {
            type: Object,
            default: () => {
                return { column: 1, border: true };
            },
        },
    },
    setup(props, { slots, attrs }) {
        const detailTitle = ref(props.title);
        const detailWidth = ref(props.width);
        const items = ref<Recordable>([]);
        const isShow = ref(props.dialogVisible);
        const drawerFooter = ref(props.drawerFooter);
        const drawerSlots = ref();
        const {t:$t} = useI18n()
        emitter.on(
            "detailViewShow2",
            async (params: {
                data?: any;
                title?: string;
                width?: string;
                drawerFooter?: any;
            }) => {
                items.value = params.data || [];
                if (params.title) detailTitle.value = params.title;
                if (params.width) detailWidth.value = params.width;
                drawerFooter.value = params.drawerFooter || null;
                drawerSlots.value = getDrawSlots();
                isShow.value = true;
            }
        );

        /** 获取插槽数据 */
        const titleSlot = {
            title: () => slots?.title && slots.title({ props, attrs }),
        };
        /** 获取样式数据 */
        const extraSlot = {
            extra: () => slots?.extra && slots.extra({ props, attrs }),
        };

        const descriptionsSlot =
            slots?.title && !slots?.extra
                ? titleSlot
                : slots?.extra && !slots?.title
                ? extraSlot
                : slots?.title && slots?.extra
                ? Object.assign(titleSlot, extraSlot)
                : null;
        const getDrawSlots = () => {
            let drawerSlots = {
                header: () => (
                    <div class={"drawer-title"}>{detailTitle.value}</div>
                ),
                default: () => (
                    <ElScrollbar height={"80vh"}>
                        <ElDescriptions
                            {...props.DescriptionProps}
                            {...attrs}
                            v-slots={descriptionsSlot}
                        >
                            {items.value.map(
                                (item: Recordable, index: number) => {
                                    return (
                                        <ElDescriptionsItem
                                            label={$t(
                                                `formKey.${item.key}`
                                            )}
                                            key={index}
                                            align={"left"}
                                        >
                                            {item.isHtml ? (
                                                <div class={"w-full"}>
                                                    {item.value}
                                                </div>
                                            ) : (
                                                <TipText
                                                    width="20vw"
                                                    content={item.value}
                                                    showTip={item.isHtml}
                                                >
                                                    v-slots=
                                                    {{
                                                        content: () => {
                                                            <>
                                                                <span class="grid-value">
                                                                    {item.value}
                                                                </span>
                                                            </>;
                                                        },
                                                    }}
                                                </TipText>
                                            )}
                                        </ElDescriptionsItem>
                                    );
                                }
                            )}
                        </ElDescriptions>
                    </ElScrollbar>
                ),
            };
            const footer = {
                footer: drawerFooter.value,
            };
            if (drawerFooter.value) {
                drawerSlots = { ...drawerSlots, ...footer };
            }
            return drawerSlots;
        };
        return () => (
            <div>
                <ElDrawer
                    v-model={isShow.value}
                    width={detailWidth.value}
                    destroy-on-close
                    v-slots={{ ...drawerSlots.value }}
                ></ElDrawer>
            </div>
        );
    },
});
