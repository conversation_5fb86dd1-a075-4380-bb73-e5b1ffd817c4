<template>
    <svg :class="[$attrs.class, spin && 'svg-icon-spin']" :style="getStyle" aria-hidden="true" class="svg-icon">
        <use :xlink:href="symbolId" />
    </svg>
</template>
<script lang="ts">
import type { CSSProperties } from 'vue';
import { defineComponent, computed } from 'vue';

export default defineComponent({
    name: 'SvgIcon',
    props: {
        prefix: {
            type: String,
            default: 'icon',
        },
        name: {
            type: String,
            required: true,
        },
        size: {
            type: [Number, String],
            default: 16,
        },
        spin: {
            type: Boolean,
            default: false,
        },
        width: {
            type: [Number, String],
            required: false
        },
        height: {
            type: [Number, String],
            required: false
        },
    },
    setup(props) {
        const symbolId = computed(() => `#gstar-${props.prefix}-${props.name}`);

        const getStyle = computed((): CSSProperties => {
            const { size } = props;
            let s = `${size}`;
            s = `${s.replace('px', '')}px`;
            return {
                width: props.width ? props.width + 'px' : s,
                height: props.height ? props.height + 'px' : s,
            };
        });
        return { symbolId, getStyle };
    },
});
</script>
<style lang="scss" scoped>
.svg-icon {
    display: inline-block;
    overflow: hidden;
    vertical-align: -0.15em;
    fill: currentColor;
}

.svg-icon-spin {
    animation: loadingCircle 1s infinite linear;
}

@keyframes loadingCircle {
    0% {
        transform: rotate(0);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>