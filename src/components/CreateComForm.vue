/** 公共创建编辑表单弹窗 */
<template>
    <el-drawer v-model="isShow" :size="size" direction="rtl" @close="resetForm(ruleFormRef)" :close-on-click-modal="false">
        <template #header>
            <div class="drawer-title">{{ formTitle }}</div>
        </template>
        <template #default>
            <div v-if="isShow">
                <el-form ref="ruleFormRef" class="comForm" :model="ruleForm" :rules="rules" :label-width="labelWidth" label-position="left"
                    style="max-width: 555px" size="default" status-icon>
                    <slot :scope="ruleForm" :isUpdate="isUpdate"> </slot>
                </el-form>
            </div>
        </template>
        <template #footer>
            <div class="button-group" style="flex: auto">
                <el-button type="primary" @click="submitForm(ruleFormRef)" :title="$t('确认')">
                    <span class="btn-text">{{ $t('确认') }}</span>
                </el-button>
                <el-button v-if="!(isUpdate && !updateShowReset)" type="primary" @click="resetForm(ruleFormRef)" :title="$t('重置')">
                    <span class="btn-text">{{ $t('重置') }}</span>
                </el-button>
                <el-button @click="isShow = false" :title="$t('取消')">
                    <span class="btn-text">{{ $t('取消') }}</span>
                </el-button>
            </div>
        </template>
    </el-drawer>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import { useForm } from "@/hooks/useForm"
import type { FormInstance, FormRules, FormItemRule } from 'element-plus'
import { $t } from '@/locales'

const ruleFormRef = ref<FormInstance>()

const emit = defineEmits<{
    (e: "onCreateOrUpdateSubmit", data: any, isUpdate: boolean): void
}>()

type Arrayable<T> = T | T[];

interface formProps {
    item?: any
    title: string,
    default_form: { [a: string]: any },
    rules?: { [x: string]: Arrayable<FormItemRule> | undefined },
    size?: string,
    labelWidth?: string,
    updateShowReset?: boolean, // 编辑状态时是否显示重置按钮
}

const props = withDefaults(defineProps<formProps>(), {
    item: {},
    isShow: false,
    rules: undefined,
    size: "30%",
    labelWidth: "120px",
    updateShowReset: true
})

const isShow = ref(false)
const item = ref()
const isUpdate = ref(false);

let ruleForm = reactive({ ...props.default_form })

watch(() => props.default_form, (value) => {
    ruleForm = reactive({ ...value })
})

const formTitle = computed(() => { return $t('{action}{title}', { action: isUpdate.value ? $t('修改') : $t('添加'), title: props.title }) })

const rules = reactive<FormRules>(props.rules ? {
    ...props.rules
} : {})

const { resetForm, submitForm } = useForm({
    submitCallBack: () => { emit("onCreateOrUpdateSubmit", ruleForm, isUpdate.value) },
    resetFormCallBack: () => {
        setDefault()
    },
    val: null,
    name: ''
})

const show = (data?: { [a: string]: any }) => {
    item.value = data
    if (data) {
        item.value = data
        for (const i in ruleForm) {
            ruleForm[i] = data[i];
        }
        isUpdate.value = true;
    } else {
        setDefault();
        isUpdate.value = false;
    }
    isShow.value = true
}

const setDefault = () => {
    for (const i in ruleForm) {
        ruleForm[i] = props.default_form[i]
    }
}

defineExpose({
    isShow,
    show
})

</script>
<style scoped>
.create-type {
    width: 120px;
}
.comForm :deep(.el-form-item__label) {
  display: block;
}
</style>
