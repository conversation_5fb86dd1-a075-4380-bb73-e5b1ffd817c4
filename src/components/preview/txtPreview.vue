<template>
  <div class="preview-txt">{{ textContext }}</div>
</template>

<script setup lang="ts">
import type { FileItem } from '@/model/file';
import axios from 'axios';
import { onMounted, ref } from 'vue';

interface Props {
  item: FileItem,
}

const props = defineProps<Props>();

const textContext = ref('');

onMounted(() => {
  if (props.item.url) {
    axios.get(props.item.url, {
      responseType: 'text'
    })
      .then((response: any) => {
        console.log("response---", response)
        if (response.data) {
          textContext.value = response.data
        }
      })
  }
})

</script>

<style lang="scss" scoped>
.preview-txt {
  width: 100%;
  font-size: 24px;
  margin-left: 16px;
  margin-top: 16px;
}
</style>