
/**
 * 公共预览模板, 需要支持图片,各类文档视频等,以及最后兼容开图 
 * 后续添加公共菜单栏
 *
 */
<template>
  <div class="preview" v-if="isShow">
    <div class="header">
      <div class="header-left">{{ previewItem.name }}</div>
      <div class="header-right">
        <el-icon class="btn" @click="close">
          <component :is="$ElIcon['Close']" />
        </el-icon>
      </div>
    </div>
    <el-container class="draw">
      <imagePreview v-if="preview.isImg" :item="previewItem"></imagePreview>
      <pdfPreview v-if="preview.isPDF" :item="previewItem"></pdfPreview>
      <!-- <officePreview v-if="preview.isOffice" :item="previewItem"></officePreview> -->
      <txtPreview v-if="preview.isTxt" :item="previewItem"></txtPreview>
      <mdPreview v-if="preview.isMd" :item="previewItem"></mdPreview>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import type { FileItem } from '@/model/file';
import { emitter } from '@/utils/mitt';
import { ref, onUnmounted, onMounted, computed } from 'vue'
import imagePreview from './imagePreview.vue';
import pdfPreview from './pdfPreview.vue';
import officePreview from './officePreview.vue'
import mdPreview from './mdPreview.vue'
import txtPreview from './txtPreview.vue';
import { useFile } from '@/hooks/useFile';
import { useRoute } from 'vue-router'

const emit = defineEmits<{
  (e: "close"): void
}>()

const route = useRoute()
const previewItem = ref()
const isShow = ref(false);
const scrollY = ref(0);

const preview = computed(() => {
  const { isPDF, isImg, isTxt, isMarkdown } = useFile(previewItem.value)
  return {
    isPDF: isPDF.value,
    isImg: isImg.value,
    isTxt: isTxt.value,
    isMd: isMarkdown.value
  }
})

onMounted(() => {
  if (window.$globalConfig?.openDrawTab === 'new' && route.name === 'preview') {
    const params = new URLSearchParams(window.location.search);
    const paramsObject: any = {};

    for (const [key, value] of params) {
      paramsObject[key] = value;
    }
    init({...paramsObject})
  }


  // 显示弹窗
  emitter.on("previewShowDialog", (item: FileItem) => {
    init(item)
  });
  window.document.addEventListener("keydown", enterESC)
})

const init = (item: any) => {
  isShow.value = true;
  previewItem.value = item;
  scrollY.value = window.scrollY
  document.body.style.cssText = `position:fixed;top:-${scrollY.value}px;height: 100%;width:100%;overflow: hidden;`
  window.history.pushState(null, '', window.location.href);
  window.addEventListener('popstate', handleBack, false);
}

onUnmounted(() => {
  emitter.off("previewShowDialog")
  window.document.removeEventListener("keydown", enterESC);
  window.removeEventListener('popstate', handleBack, false)
})

const handleBack = () => {
  window.removeEventListener('popstate', handleBack, false)
  window.history.pushState(null, '', window.location.href);
  close()
}

const enterESC = (e: KeyboardEvent) => {
  // 监听键盘点击esc事件
  if (e.keyCode === 27) {
    // close()
  }
}

// 组件销毁
const close = () => {
  if (window.$globalConfig?.openDrawTab === 'new') window.close()
  document.body.style.cssText = ''
  window.scrollTo(0, scrollY.value)
  isShow.value = false;
}

defineExpose({
  isShow,
  close
})

</script>

<style lang="scss" scoped>
.preview {
  z-index: 100;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  padding: 0px !important;
  // background-image: linear-gradient(180deg, rgba(0, 0, 0, .85) 0%, rgba(0, 0, 0, .65) 100%);
  background-image: linear-gradient(180deg, rgba(0, 0, 0) 0%, rgba(0, 0, 0) 100%)
}

.header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 16px;
  padding-right: 16px;
}

.header-left {
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: PingFangSC-Regular;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 16px;
  color: #fff;
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
}

.btn {
  color: #fff;
  font-size: 24px;
  cursor: pointer;
}

.draw {
  height: calc(100vh - 50px);
  background-color: #fff;
}
</style>