<template>
  <iframe class="preview-pdf" :src="previewUrl"></iframe>
</template>

<script setup lang="ts">
import type { FileItem } from '@/model/file';
import { computed } from 'vue';

interface Props {
  item: FileItem,
}

const props = defineProps<Props>();

const previewUrl = computed(() => {
  return `${import.meta.env.VITE_PUBLIC_PATH}/assets/pdf/web/viewer.html?file=${encodeURIComponent(props.item.url as string)}`
})

</script>

<style lang="scss" scoped>
.preview-pdf {
  width: 100%;
  height: 100%;
}
</style>