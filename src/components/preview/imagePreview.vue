<template>
  <div class="preview-img" v-if="item.url">
    <img :src="item.url" alt="Preview Image" class="image" @wheel="handleMouseWheel"
      :style="imgStyle" @mousedown="handleMouseDown" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, type CSSProperties } from 'vue';
import type { FileItem } from '@/model/file';
import { throttle } from 'ts-debounce-throttle'

interface Props {
  item: FileItem,
}

defineProps<Props>();

const transform = ref({
  scale: 1,
  deg: 0,
  offsetX: 0,
  offsetY: 0,
  enableTransition: false,
})

const imgStyle = computed(() => {
  const { scale, deg, offsetX, offsetY, enableTransition } = transform.value
  let translateX = offsetX / scale
  let translateY = offsetY / scale

  switch (deg % 360) {
    case 90:
    case -270:
      [translateX, translateY] = [translateY, -translateX]
      break
    case 180:
    case -180:
      [translateX, translateY] = [-translateX, -translateY]
      break
    case 270:
    case -90:
      [translateX, translateY] = [-translateY, translateX]
      break
  }

  const style: CSSProperties = {
    transform: `scale(${scale}) rotate(${deg}deg) translate(${translateX}px, ${translateY}px)`,
    transition: enableTransition ? 'transform .3s' : '',
  }
  return style
})


const handleMouseWheel = (e: WheelEvent) => {
  const delta = e.deltaY;
  if (delta > 0) {
    // 向下滚动，缩小图片
    if (transform.value.scale <= 0.5) return;
    transform.value.scale = Math.max(transform.value.scale - 0.1, 0.1);
  } else {
    // 向上滚动，放大图片
    transform.value.scale = Math.min(transform.value.scale + 0.1, 2);
  }
}

const handleMouseDown = (e: MouseEvent) => {
  transform.value.enableTransition = false

  const { offsetX, offsetY } = transform.value
  const startX = e.pageX
  const startY = e.pageY

  const dragHandler = throttle((ev: MouseEvent) => {
    transform.value = {
      ...transform.value,
      offsetX: offsetX + ev.pageX - startX,
      offsetY: offsetY + ev.pageY - startY,
    }
  })
  document.addEventListener('mousemove', dragHandler)
  document.addEventListener('mouseup', () => {
    document.removeEventListener('mousemove', dragHandler)
    transform.value.enableTransition = true
  })
  e.preventDefault()
}

</script>

<style lang="scss" scoped>
.preview-img {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image {
  max-height: 80%;
}
</style>