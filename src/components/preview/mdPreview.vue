<template>
  <MdPreview :editorId="id" :modelValue="content" />
  <MdCatalog :editorId="id" :scrollElement="scrollElement" />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MdPreview, MdCatalog } from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';
import type { FileItem } from '@/model/file';

interface Props {
  item: FileItem,
}

const props = defineProps<Props>();

const id = 'preview-only';
const content = ref();
const scrollElement = document.documentElement;
onMounted(() => {
  if (props.item.url) {
    fetch(props.item.url)
      .then((response: any) => {
        return response.blob()
      })
      .then((blob) => {
        const reader = new FileReader();
        reader.onload = function() {
          const text = reader.result;
          content.value = text
        };
        reader.readAsText(blob);
      });
  }
})
</script>