<template>
  <DocumentEditor id="docEditor" documentServerUrl="http://localhost:80" :config="config"
    :events_onDocumentReady="onDocumentReady" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { DocumentEditor } from "@onlyoffice/document-editor-vue";
import type { FileItem } from '@/model/file';
import { getExt } from '@/utils';

interface Props {
  item: FileItem,
}
const props = defineProps<Props>();
console.log("url", props.item.url)


const config = computed(() => {
  const fileType = getExt(props.item.name);
  return {
    document: {
      fileType,
      key: String(props.item.id),
      title: props.item.name,
      url: props.item.url || "",
      permissions: {
        edit: false, //是否可以编辑: 只能查看，传false
        print: false,
        download: false,
        // "fillForms": true,//是否可以填写表格，如果将mode参数设置为edit，则填写表单仅对文档编辑器可用。 默认值与edit或review参数的值一致。
        // "review": true //跟踪变化
      },
    },
    documentType: getFileType(fileType),
    editorConfig: {
      lang: "zh-cn",
      //定制
      customization: {
        autosave: false,
        chat: false,
        comments: false,
        help: false,
        //是否显示插件
        plugins: false,
      },
      mode: "view",
      callbackUrl: "http://localhost:2022"
    },
  }
})

const getFileType = (fileType: string) => {
  let docType = "";
  const fileTypesDoc = [
    "doc",
    "docm",
    "docx",
    "dot",
    "dotm",
    "dotx",
    "epub",
    "fodt",
    "htm",
    "html",
    "mht",
    "odt",
    "ott",
    "pdf",
    "rtf",
    "txt",
    "djvu",
    "xps",
  ];
  const fileTypesCsv = [
    "csv",
    "fods",
    "ods",
    "ots",
    "xls",
    "xlsm",
    "xlsx",
    "xlt",
    "xltm",
    "xltx",
  ];
  const fileTypesPPt = [
    "fodp",
    "odp",
    "otp",
    "pot",
    "potm",
    "potx",
    "pps",
    "ppsm",
    "ppsx",
    "ppt",
    "pptm",
    "pptx",
  ];
  if (fileTypesDoc.includes(fileType)) {
    docType = "text";
  }
  if (fileTypesCsv.includes(fileType)) {
    docType = "spreadsheet";
  }
  if (fileTypesPPt.includes(fileType)) {
    docType = "presentation";
  }
  return docType;
}
const onDocumentReady = () => {
  console.log("DocumentEditor is ready!");
}
</script>