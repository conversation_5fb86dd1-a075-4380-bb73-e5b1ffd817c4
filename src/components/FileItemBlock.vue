<template>
    <div class="block-item-container" v-event:clearAll="clearAll">
        <template v-if="items.length > 0">
            <div class="block-item-wrap" v-for="item in items" :key="item.id">
                <div class="block-item" @click.prevent.stop="clickItem(item)" @dblclick.stop="goToFile(item)"
                    :class="{ 'selected': item.isSelected, 'hasSelected': !hideSelected && menuList }">
                    <div class="block-top">
                        <FileIcon :size="isShowThumbnail(item) ? null : 66" :item="item"
                            :class="isShowThumbnail(item) ? 'block-file-thumbnail' : 'block-file-icon'" :isBlock="true">
                        </FileIcon>
                        <el-checkbox v-if="!hideSelected" v-model="item.isSelected" class="block-icon-check" />
                        <div v-if="item?.noteHaveAlter || item?.fileHaveAlter" class="file-note-new">
                            <img v-if="item?.noteHaveAlter" src="/assets/imgs/note-new.png">
                            <img v-if="item?.fileHaveAlter" src="/assets/imgs/file-new.png">
                        </div>
                    </div>
                    <div class="block-name">
                        <div v-if="editInfo?.editId === item.id" class="flex items-center w-full">
                            <slot name="inputName" :item="item"></slot>
                        </div>
                        <div class="file-name" :title="item.name" v-else>
                            <div class="file-name-left file-block-name">{{ getFileName(item) }}</div>
                            <div class="file-name-right" v-if="item.fileType !== 1">{{ `.${getExt(item.name)}` }}</div>
                        </div>
                        <div class="expireTime">
                            <div class="file-size">
                                {{ item.fileType !== 1 ? formatSize(item.size) || '-' : '-' }}
                            </div>
                            <!-- <el-divider direction="vertical" /> -->
                            <div v-if="isRecycledPage" :class="getClass(item)">
                                <el-divider direction="vertical" /> {{ item.remainTime }}
                            </div>
                        </div>
                        <div class="block-time file-block-info mt-2">
                            <small>{{ `${item.lastModifyTime || item.deleteTime || ''}` }} {{ item.ownerName ? `|
                                ${item.ownerName}`
                                : '' }}</small>
                        </div>
                        <div class="action mt-1" v-if="item.id && menuList">
                            <MenuList :menuList="menuList" :items="[item]" :isQuick="true"></MenuList>
                            <slot name="menuExtra" :item="item"></slot>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <div v-else class="empty-text">
            {{ $t('暂无数据') }}
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, watch, ref } from 'vue';
import { getExt, getShortName, isType } from "@/utils";
import { FILETYPE, type FileItem } from '@/model/file';
import { formatSize } from '@/utils'
import { useRoute } from 'vue-router'
import { $t } from '@/locales'

interface Props {
    items: Item[],
    isSelectAll: boolean,
    hideSelected?: boolean,
    menuList?: Recordable[],
    editInfo?: Recordable
    limitName?: () => void
    confirmEditName?: (item?: any) => void
    cancelEditName?: () => void
    isIndeterminate?: boolean
}
interface Item extends FileItem {
    isSelected: boolean
    deleteTime: string
    remainTime: string
    noteHaveAlter?: boolean
    fileHaveAlter?: boolean
}
const route = useRoute()
const props = defineProps<Props>();
const timer = ref<NodeJS.Timeout | null>(null)
const rename = ref(props.editInfo?.editName)
const selectRows = computed(() => {
    return props.items.filter(item => item.isSelected)
})

const item_wrap_height = computed(() => {
    return '220px'
})

// 目前使用hardCode,后续取消这种方式
const isRecycledPage = computed(() => {
    return route.name === 'filesRecycled'
})

const emit = defineEmits<{
    (e: "selectRowChange", data: Item[]): void,
    (e: "goToItem", data: Item): void
}>()

watch(
    () => selectRows.value,
    (v) => {
        emit('selectRowChange', v)

    },
    { deep: true }
);

watch(
    () => props.editInfo,
    (v: Recordable) => {
        rename.value = v.editName
    },
    { deep: true }
);

watch(
    () => props.isSelectAll,
    (v) => {
        if (!v && props.isIndeterminate) return
        props.items.map(item => {
            item.isSelected = v;
            return item
        })
    },
);

const isShowThumbnail = (item: Item) => {
    return isType(item, "image") || (item.thumbnail && item.thumbnail !== '0')
}

const confirmEditName = (item: Item) => {
    if (props.confirmEditName) props.confirmEditName({
        fileType: item.fileType,
        rename: rename.value,
        name: item.name
    })
}

const getFileName = (item: Item) => {
    if (item.fileType === FILETYPE.FOLDER) {
        return item.name
    } else return getShortName(item.name)
}
const clickItem = (item: Item) => {
    if (!props.menuList) {
        goToFile(item)
        return;
    }
    item.isSelected = !item.isSelected
}

const getClass = (item: Item) => {
    if (item.deleteTime) {
        const interval = new Date(item.deleteTime).getTime() + 10 * 24 * 60 * 60 * 1000 - new Date().getTime()
        if (interval <= (7 * 24 * 60 * 60 * 1000)) return 'expire'
    }
    return ''
}

const goToFile = (item: Item) => {
    if (timer.value) {
        clearTimeout(timer.value)
    }
    emit('goToItem', item)
}

const clearAll = () => {
    props.items.map(item => {
        item.isSelected = false;
        return item
    })
}

</script>

<style lang="scss" scoped>
.block-item-container {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    padding: 0 6px;
    overflow: auto;
    align-content: flex-start;
    margin-left: -12px;
    margin-right: -12px;
}

@media screen and (min-width: 600px) {
    .block-item-wrap {
        width: 33.33333333%;
    }

}

@media screen and (min-width: 960px) {
    .block-item-wrap {
        width: 33.33333333%;
    }
}

@media screen and (min-width: 1024px) {
    .block-item-wrap {
        width: 25%;
    }
}

@media screen and (min-width: 1280px) {
    .block-item-wrap {
        width: 20%;
    }

}

@media screen and (min-width: 1440px) {
    .block-item-wrap {
        width: 16.66666667%;
    }
}

@media screen and (min-width: 1680px) {
    .block-item-wrap {
        width: 14.28571429%;
    }
}

@media screen and (min-width: 1920px) {
    .block-item-wrap {
        width: 12.5%;
    }
}

@media screen and (min-width: 2560px) {
    .block-item-wrap {
        width: 10%;
    }
}

.block-item-wrap {
    // width: 220px;
    display: inline-block;
    padding: 6px 6px;
    word-wrap: normal;
    min-width: 180px;
}

.block-item {
    width: 100%;
    height: v-bind(item_wrap_height);
    margin: auto;
    position: relative;
    // border: 1px solid #e5ebf2;
    border-radius: 4px;
    box-sizing: border-box;
    cursor: pointer;
    line-height: 1;
    // border-radius: 5px;
    border: 1px solid #ebeef5;
    border-radius: 11px 9px 9px 9px;
    overflow: hidden;
    background-color: #fff;
    color: #000000de;

    &:hover {
        border: 0.5px solid #0069ff;

        .block-top {
            background-color: #f5f9ff;
        }
    }
}

.hasSelected:hover {
    .action {
        display: flex;
    }

    .block-time {
        display: none;
    }
}

.selected {
    border: 0.5px solid #0069ff !important;

    .block-top {
        background-color: #f5f9ff !important;
    }
}

.block-top {
    background-color: #f7f7fb;
    border-color: #f5f9ff;
    height: 127px;
    display: flex;
    justify-content: center;
    position: relative;
}

.block-file-thumbnail {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background: #000;
}

.block-file-icon {
    position: relative;
    // height: 66px;
    min-width: 48px;
    // width: 48px;
    margin-top: 35px;
}

.block-icon-check {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 1;
    width: 16px;
    height: 16px;
}

.block-name {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-left: 16px;
    // padding-right: 16px;
    padding-top: 16px;
}

.file-size {
    // margin-left: 4px;
}

.file-name {
    display: inline-flex;
    width: 84%;
    line-height: 20px;
    color: #585858;
}

.file-name-left {
    text-overflow: ellipsis;
    overflow: hidden;
    min-width: 10px;
    max-width: 70%;
}

.file-name-right {
    margin-right: 4px;
    display: inline-block;
    text-overflow: ellipsis;
    max-width: 30%;
    min-width: 45px;
    overflow: hidden;
}

.block-time {
    color: #989898;
}

.action {
    display: none;
    height: calc(100% - 127px);
    align-items: center;
    white-space: normal;
}

.expireTime {
    display: flex;
    font-size: 12px;
    margin-top: 8px;
    color: #989898;
}

.action-icon {
    cursor: pointer;
    margin-right: 6px;
}

.edit-name {
    width: 75%;
}

.file-note-new {
    position: absolute;
    bottom: 0;
    right: 0;
    margin-right: 15px;
}

.expire {
    color: red;
}
.empty-text {
    display:flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    height: 100%;
    width: 100%;
}
</style>