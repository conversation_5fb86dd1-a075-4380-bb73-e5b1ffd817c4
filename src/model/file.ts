// 文件管理
import type { UploadFile } from "element-plus";

/**
 * 文件列表每条数据
 */
export interface FileItem {
  lastModifyTime: string; // 更新时间
  fileType?: number; // 文件格式 1: 目录 2:dwg 3:rvt 4:stl 5:zip
  size: number; // 大小
  commentSize: number; // 批注大小
  id: number;
  name: string; //  文件名
  createTime: string; // 创建时间
  parentId: number; // 父ID
  ownerId: string;
  ownerName: string; //  拥有者
  version: string; //  版本号
  thumbnail?: string; // 缩略图id
  permType: number;
  etag?: string;
  routeUrl?: string;
  children?: FileItem[];
  fileId?: number | string;
  url?: string;
  progress?: number;
  progressError?: boolean;
  isHistory?: boolean;
  sid?: number;
  rename?: string;
  noteHaveAlter?: boolean; // 是否有批注
  fileHaveAlter?: boolean; // 是否有修改
  specialFunctions?: string[]; // 文件权限
  hasXRef?: number; // 是否是外部参照 0 不是 1是
  hideFunction?: boolean;
}

export interface FileItemShortcut {
  id: number;
  name: string;
  ownerId: string;
  fileId: number;
  fileType: number;
  version: string;
  thumbnail: number;
  etag: string;
}

/**
 * 下载文件
 */
export interface DownloadQuery {
  fileName: string; // 文件名
  fileId: number; // 文件夹ID
  reqFrom?: string; // 下载来源 （cloudcad）
}

/**
 * 获取文件列表参数
 */
export interface GetFileListQuery {
  folderId: number; // 文件夹id
  direction: number; // 第几页 默认第一页
  limit: string | number; // 每页数量
  searchName: string; // 搜索条件
  searchBeginTime: string;
  searchEndTime: string;
  fileType: number; // 文件类型
  sortField: number; // 排序字段
  ascOrDesc: string; // 顺序/倒序
  lastFileId: number; // 上一页最后一个文件id
  lastFileSize: number; // 上一页最后一个文件大小
  lastFileModifyTime: string; // 上一页最后一个文件创建时间
  lastFileName: string; // 上一页最后一个文件名称
}

/**
 * 定义element表格排序变化返回参数类型
 */
export interface SortChange {
  column: Promise<any>;
  order: string;
  prop: string;
}

/**
 * 排序值类型
 */
export interface SortKey {
  descending: string;
  ascending: string;
  default: string;
}

/**
 * 文件列表排序字段类型
 */
export interface SortFieIdKey {
  lastModifyTime: number;
  size: number;
  name: string;
}

/**
 * 回收站列表排序字段类型
 */
export interface RecycleSortFieIdKey {
  deleteTime: number;
  size: number;
}

/**
 * 文件重命名请求参数类型
 */
export interface RenameFileQuery {
  folderId: number;
  newName: string;
  oldName: string;
}

/**
 * 创建文件夹请求参数类型
 */
export interface CreateFolderQuery {
  parentId: number;
  folderName: string;
}

/**
 * 面包屑数据的每一条
 */
export interface CrumbsListItem {
  folderId: number;
  folderName: string;
  children?: FileItem[];
}

/**
 * 上传文件请求参数
 */
export interface UploadQuery {
  folderId?: number;
  replace?: boolean;
  etag: string;
  file: File | string;
  path?: string;
  fileName?: string;
}

export interface NewUploadFile extends UploadFile {
  state?: number;
}

export interface DeleteFileQuery {
  fileNameArr: string[];
  fileIdArr: number[];
  folderId: number;
}

/**
 * 获取回收站列表请求参数
 */
export interface GetRecycledListQuery {
  direction: number; // 前进还是后退
  limit: number; // 每页数量
  sortField: number; // 排序字段
  ascOrDesc: string; // 顺序/倒序
  lastFileId: number; // 上一页最后一个文件id
  lastFileSize: number; // 上一页最后一个文件大小
  lastFileDeleteTime: string;
  reqPage: number;
  reqPageIdArr: string;
}

// 定义字体列表表格数据类型
export interface RecycledTableItem {
  id: number;
  name: string;
  deleteTime: string;
  saveTime: number;
  size: number;
  remainDays: number;
}

export interface fileInfoItem {
  fileId: number;
  fileName: string;
  replace?: boolean;
}

// 定义回收站批量还原
export interface RecycledBatchQuery {
  fileInfoArr: fileInfoItem[];
}

interface deleteIdArr {
  fileId: number;
  fileName: string;
  replace?: boolean;
}

// 定义回收站批量删除
export interface RecycledDeleteBatchQuery {
  fileIdArr: number[];
}

// 获取目录树的请求体
export interface GetTreeQuery {
  folderId: number;
}

// 移动文件请求
export interface MoveFileQuery {
  fileNameArr: string[];
  fromFolderId: number;
  toFolderId: number;
}

// 模糊查询用户和用户组
export interface SearchGroupQuery {
  keyword: string;
}

// 文件设置权限
export interface SetFileRightItem {
  authority: number;
  identityId: string;
  identityType: number;
}
export interface SetFileRightQuery {
  fileId: number;
  filePermType: number;
  authorityList: SetFileRightItem[];
}

// 获取字体列表
export interface GetFontListQuery {
  folderId: number;
  page?: number;
  limit?: number;
  searchName?: string;
  fileType?: number;
  searchBeginTime?: string;
  searchEndTime?: string;
  sortField?: number;
  ascOrDesc?: string;
}

export interface UploadFontQuery {
  folderId: number;
  etag: string;
  replace: boolean;
  file: File | string;
}

// 获取文件授权列表
export interface FileRightListQuery {
  fileId: number | string;
  filePermType: number;
}

export interface GetFileByStorageIdQuery {
  storageId: number | string;
  fileName?: string;
  Authorization?: string;
  type?: number;
}
export interface getFilePermListQuery {
  searchFileName: number | string;
  accountType: number | string;
  searchAccountName: number | string;
}
export interface DeleteFilePermQuery {
  accountType: number | string;
  fileId: number | string;
  grantType: number | string;
  identityId: number | string;
}

// 任务类型
export enum EFILEMATEKEY {
  DWG_META = "gcad2dviewer", // DWG文件元信息
  SAVE_PDF_META = "gcad2dsavepdf", // savepdf文件元信息
  SAVE_DWG_META = "gcad2dsavedwg", // savedwg文件元信息
  CONVERT_PDF = "gcadpdf2dwg", // 转换pdf元信息
  CONVERT_DWF = "gcaddwf2dwg", // 转换dwf元信息
}

// 文件类型
export const FILETYPE = {
  FOLDER: 1, // 文件夹
  DWG: 2,
  RVT: 3,
  TAR_GZ: 4,
  ZIP: 5,
  PDF: 6,
  DWT: 7,
  DWS: 8,
  PNG: 11,
  RAR: 12,
  TAR: 13,
  BMP: 23,
  JPEG: 24,
  SVG: 25,
  STL: 79,
  TTF: 200,
  TTC: 201,
  OTF: 202,
  SHX: 203,
  DWF: 54,
  SLDDRW: 56,
};

export const FILE_TYPE_NAME = {
  [FILETYPE.FOLDER]: 'folder',
  [FILETYPE.DWG]: 'dwg',
  [FILETYPE.RVT]: 'rvt',
  [FILETYPE.TAR_GZ]: 'tar.gz',
  [FILETYPE.ZIP]: 'zip',
  [FILETYPE.PDF]: 'pdf',
  [FILETYPE.DWT]: 'dwt',
  [FILETYPE.DWS]: 'dws',
  [FILETYPE.PNG]: 'png',
  [FILETYPE.RAR]: 'rar',
  [FILETYPE.TAR]: 'tar',
  [FILETYPE.BMP]: 'bmp',
  [FILETYPE.JPEG]: 'jpeg',
  [FILETYPE.SVG]: 'svg',
  [FILETYPE.STL]: 'stl',
  [FILETYPE.TTF]: 'ttf',
  [FILETYPE.TTC]: 'ttc',
  [FILETYPE.OTF]: 'otf',
  [FILETYPE.SHX]: 'shx',
  [FILETYPE.DWF]: 'dwf',
  [FILETYPE.SLDDRW]: 'SLDDRW'
}

export const ALL_TYPE = [
  FILE_TYPE_NAME[FILETYPE.DWG],
  FILE_TYPE_NAME[FILETYPE.RVT],
  FILE_TYPE_NAME[FILETYPE.TAR_GZ],
  FILE_TYPE_NAME[FILETYPE.ZIP],
  FILE_TYPE_NAME[FILETYPE.PDF],
  FILE_TYPE_NAME[FILETYPE.DWT],
  FILE_TYPE_NAME[FILETYPE.DWS],
  FILE_TYPE_NAME[FILETYPE.PNG],
  FILE_TYPE_NAME[FILETYPE.RAR],
  FILE_TYPE_NAME[FILETYPE.TAR],
  FILE_TYPE_NAME[FILETYPE.BMP],
  FILE_TYPE_NAME[FILETYPE.JPEG],
  FILE_TYPE_NAME[FILETYPE.SVG],
  FILE_TYPE_NAME[FILETYPE.STL],
  FILE_TYPE_NAME[FILETYPE.TTF],
  FILE_TYPE_NAME[FILETYPE.TTC],
  FILE_TYPE_NAME[FILETYPE.OTF],
  FILE_TYPE_NAME[FILETYPE.SHX],
  FILE_TYPE_NAME[FILETYPE.DWF],
  FILE_TYPE_NAME[FILETYPE.SLDDRW]
]

export interface recoverQuestData {
  fileId: number;
  toVerId: number;
}

export const REFRESH_LABEL_NAME = {
  [FILETYPE.PDF]: 'pdf转换dwg',
  [FILETYPE.DWF]: 'dwf转换dwg',
  [FILETYPE.SLDDRW]: 'SLDDRW转换dwg',
}

export const TRANSLATE_TASK_TYPE = {
  [FILETYPE.PDF]: 'ConvertPDF',
  [FILETYPE.DWF]: 'ConvertDWF',
  [FILETYPE.SLDDRW]: 'ConvertSlddrw2DWG',
}