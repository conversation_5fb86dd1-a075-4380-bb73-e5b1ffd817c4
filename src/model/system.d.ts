/**
 * 修改系统基本信息
 */
export interface PutSystemInfoQuery {
    site_domain: string;
    site_copyright: string;
    site_beian: string;
    site_name: string;
}

/**
 * 修改存储配置信息
 */
export interface PutSaveInfoQuery {
    root: string;
}

/**
 * 修改任务配置信息
 */
export interface PutTaskInfoQuery {
    allowautoclientlink: boolean;
    dbstatementcachecount: number;
    load_last_node_count: number;
    load_last_task_time: number;
    synccommanddeadline: number;
    taskdispatch_backpressure: number;
}

/**
 * 修改备份配置信息
 */
export interface PutBackupInfoQuery {
    prefix: string;
    enable: boolean;
    checked_cycle: string;
    checked_content: string[];
    backup_time: string;
    backup_path: string;
}

/**
 * 系统安装参数
 */
export interface InstallSystemParams {
    backupFile: string; //备份恢复所需要的的数据文件的路径
    database: string; //数据库名称
    db_password: string; // 数据库登录密码
    db_type: "mysal" | "sqlite3" | "postgresql";
    db_username: string; // 数据库登录用户
    hostname: string; // 数据库地址：默认127.0.0.1
    hostport: string; // 数据库端口:默认3306
    httpPort: string; // 站点端口-默认8000 (必填)
    httpProtocol: "http" | "https"; // http协议-默认http
    httpsCertFile: string; // https私钥
    httpsKeyFile: string; // https公钥
    password: string; // 管理员密码 -- 传md5值 (必填)
    prefix: string; // 数据库表前缀
    rootpath: string; // 系统图纸存储根路径 (必填)
    siteFavicon: string; // favicon图标
    siteLogo: string; // 站点logo
    siteName: string; // 站点名称
    site_domain: string; // 站点域名- 默认127.0.0.1
    sqlFilePath: string; // 数据库类型： 数据库文件地址（sqlite3时，才有）
    username: string; // 管理员账号 (必填)
}

/**
 * 修改云原生设置
 */
export interface PutCloudConfigQuery {
    edit_type: string
    save_classify?: number
    single?: {
        save_cycle?: number
        save_type?: number
    }
}