import type { AxiosRequestConfig } from "axios";

declare module "axios" {
    export interface AxiosRequestConfig {
        Authorization?: string;
    }
}

export interface ResponseData {
    code: number;
    msg: string;
    data: any;
}
declare global {
    interface File {
        mozSlice?: any;
        webkitSlice?: any;
    }

    type Recordable<T = any> = Record<string, T>;
    interface GlobalConfigs {
        api_endpoint?: string;
        enable2DOpenDraw?: boolean;
        enable3DOpenDraw?: boolean;
        socketIo_url?: string;
        server_is_install?: boolean;
    }
    type TMethod = "GET" | "POST" | "PUT" | "DELETE" | "HEAD";
    type resType =
        | "arraybuffer"
        | "json"
        | "text"
        | "blob"
        | "document"
        | "stream";
    interface HttpRequestConfig extends AxiosRequestConfig {
        ignore?: boolean;
    }
}

export type LocaleType = "zh_CN" | "en";
export interface LocaleSetting {
    locale: LocaleType;
}
