export interface CreateGroupParams {
    description?: string; // 描述
    name?: string; // 名称
    parentCode?: string; // 父编号
    code?: string; // 编号
}

export interface Group {
    code?: string;
    description?: string;
    name: string;
    fullname?: string; // 全称
    parentCode?: string;
    status?: number; // 0正常 1禁用
    auth?: number;
    ctime?: string; // 创建时间
    dtime?: string; // 删除时间
    id: string;
    utime?: string; // 更新时间
    userNum?: number; // 成员数
}

export interface ExportParams {
    parentCode?: string; // 父级组编码-默认空
    code?: string; // 搜索条件-编码
    name?: string; // 搜索条件-名称
}
