export interface Task {
    id: number;
    type: string; // 类型
    priority: number; // 优先级 最低1，低2，略低3，正常4，略高5，高6，最高7
    status: number; // 状态 已就绪1，执行中2，已完成3，部分完成4，已失败5，已取消6，已终止7
    nodeName: string; // 运行节点
    userName: string; // 所属用户
    createTime: string; // 创建时间
    startTime: string; // 开始时间
    endTime: string; // 结束时间
    progress: number; // 进度
    retryTimes: number; // 重试次数
    tags: string; // 标签
}

// 转换任务状态
export enum ETASKSTATUS {
    pending = 1, // 已就绪
    executing, // 执行中
    finished, // 已完成
    partialFinished, // 部分完成
    failed, // 已失败
    canceled, // 已取消
}
// 任务优先级
export enum ETASKPRIORITY {
    lowest = 1, // 最低
    low, // 低
    lower, // 略低
    normal, // 正常
    higher, // 略高
    high, // 高
    highest, // 最高
}

// 任务类型
export enum ETASKTYPE {
    CONVERT_OCF = "DWGVisualization", // 转换ocf文件
    SAVE_DWG = "SaveAsDWG", // 保存dwg
    SAVE_PDF = "SaveAsPDF", // 保存pdf
    THUMBNAIL_2D = "MakeDWGThumbnail", // 生成2D缩略图
    THUMBNAIL_BIG_2D = "MakeDWGBigThumbnail", // 生成2D大缩略图
    CONVERT_VSF = "BuildingVisualization", // 转换vsf文件
    THUMBNAIL = "MakeBuildingThumbnail", // 生成缩略图
    CONVERT_PDF = "ConvertPDF", // 转换pdf文件
    SELF_CHECK = "SelfCheck", // 自我检测
    MANUFACTURING = "ManufacturingVisualization",
    MANUFACTURING_THUMBNAIL = "MakeManufacturingThumbnail",
    MANUFACTURING_SKP = "MFGSkpVisualization",
    CONVERT_3D_OCF = "dwg2ocf3d", // 转换3d ocf文件
    CONVERT_RVT = "rvt2ocf3", // 转换rvt文件
    BACKUP = "backuptask", // 备份
    DATA_ARRANGE = "dataclean", // 数据整理
    RESTART = "restart", // 重启服务
    CONVERT_DWF = "ConvertDWF", // 转换dwf文件
    DATA_CLEAR_TIMEOUT_DATA = "DataClearTimeOutData"
}
