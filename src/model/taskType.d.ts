// 创建任务类型
export interface CreateTaskTypeParams {
    application?: string;
    category?: number; //  1是前端任务 4是普通后台任务 5系统后台任务
    comment: string;
    function?: string;
    name?: string;
    type?: string;
}

export interface TaskType {
    id: number;
    name?: string; // 名称
    appSandbox?: string; // 沙盒
    category?: number; // 类别
    comment?: string; // 备注
    createTime?: string; // 创建时间
    function?: string; // 功能
    type?: string; // 类型
    version?: string; // 版本
    application?: string; // 应用 由沙盒和版本拼接
    execStatus?: number; // 任务执行状态 execStatus 0:不执行 1:执行
    execPolicy?: string; //  (月份（1-12） 日（1-31） 星期（1-7）小时（0-23）分钟（0-59）秒(0-59) 用下划线_分割)（月份 日 星期） 传0则不指定
}
