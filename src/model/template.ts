export interface TemplateItem {
  createTime: string;
  enable: number;
  etag: string;
  fileHaveAlter: boolean;
  fileType: number;
  hasXRef: number;
  id: number;
  lastEditor: string;
  lastModifiedTime: string;
  metaId: number;
  name: string;
  noteHaveAlter: boolean;
  ownerId: string;
  ownerName: string;
  parentId: number;
  permType: number;
  sid: number;
  size: number;
  specialFunctions: string[];
  thumbnail:number;
  version: string;
}