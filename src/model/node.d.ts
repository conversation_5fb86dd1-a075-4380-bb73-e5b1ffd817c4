// 创建节点
export interface CreateNodeParams {
    comment?: string;
    concurrent: number;
    id: string;
    name?: string;
    tags?: string;
    status?: number;
}

export interface Node {
    newId?: string;
    id: string;
    status?: number; // status 1-已连接，2-未连接，3-未绑定
    concurrent?: number; // 最大并发数， 默认值为4 范围为1~50
    createTime?: string; // 创建时间
    enable?: number; // 1代表启用， 2代表禁用
    name?: number; // 名称
    lastLinkTime?: string; // 连接时间
    executions?: boolean; // 任务数
    totalTaskCount?: number; // 历史任务数
    faliedTaskPercent?: number // 机器失败比例
    os?: string // 机器操作系统
    platform?: string // 机器平台
}
