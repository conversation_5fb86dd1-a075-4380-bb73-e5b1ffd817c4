export interface PlanTaskType {
  id?: number
  name?: string
  type?: string
  execPolicy?: string //  (月份（1-12） 日（1-31） 星期（1-7）小时（0-23）分钟（0-59）秒(0-59) 用下划线_分割)（月份 日 星期） 传0则不指定
  execStatus?: number // 任务执行状态 execStatus 0:不执行 1:执行
  taskParam?: string
  userComment?: string
  averageRunTime?: number // 平均执行时间
  lastRunTime?: string // 上次执行时间
  nextRunTime?: string // 下次执行时间
  [key: string]: unknown
}

export interface TaskType {
  id: number
  name: string
  type: string
  [key: string]: unknown
}

export interface SelectOptionsType {
  label: string
  value: string
}

export interface CreateTaskTypeParams {
  id: number | undefined
  name: string
  type: string
  prefix?: string
  backup_path?: string
  checked_content?: string[]
  expireTime?: string
  userComment: string
  [name: string]: unknown
}

export interface backupContentType {
  enable: boolean
  name: string
  type: string
}

export interface backupInitType {
  backupFileName: string
  backupFileTime: string
  backContentList: backupContentType[]
}
