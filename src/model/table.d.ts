import type { ElTableColumn } from "element-plus";
import type { ElTable } from "element-plus";

interface TableOption {
    data: typeof ElTable.data;
}

interface TableDataParams {
    page?: number;
    limit?: number;
    search?: any;
    [a?: string]: any;
}

const PAGE_LAYOUT = [
    "pre", //
    "pager",
    "next",
    "jumper",
    "total",
] as const;

type Field = typeof PAGE_LAYOUT[number];

type Separated<S extends string> = `${S}${"" | `, ${S}`}`;
type Layout = Separated<Separated<Field>>;

interface TablePagination {
    currentPage: number;
    pageSizes: Array<number>;
    limit: number;
    layout: Layout;
}
interface TableSearchType {
    key?: string;
    type?: "input" | "select" | "dataPicker";
    name?: string;
    width?: string;
    placeholder?: string;
    options?: [];
    defaultValue?: any;
}
interface Explose {
    isShow: boolean;
    show: (v?: any, g?: any) => void;
    close: () => void;
}

interface SortKey {
    descending: string;
    ascending: string;
    default: string;
}

interface SortChange {
    column: Promise<any>;
    order: string;
    prop: string;
}

interface SortFieIdKey {
    lastModifyTime: number;
    size: number;
}

export type {
    TableOption,
    ElTableColumn as TableColumn,
    TableDataParams,
    TablePagination,
    TableSearchType,
    Explose,
    SortKey,
    SortChange,
    SortFieIdKey,
};
