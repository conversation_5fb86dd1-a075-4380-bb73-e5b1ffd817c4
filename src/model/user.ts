// 创建用户
export interface CreateUserParams {
    email?: string;
    first_name: string;
    last_name: string;
    menu_ids?: number[];
    password?: string;
    phone?: string;
    status?: number;
    user_name?: string;
    permissionGroup?: string;
}
export interface User {
    auth?: number;
    ctime?: string;
    dtime?: string;
    email?: string;
    first_name?: string;
    id: string;
    last_name?: string;
    login_ip?: string;
    login_time?: string;
    // menu_auth?: string,
    phone?: string;
    root?: number;
    status?: number | string;
    user_name?: string;
    utime?: string;
    disable?: boolean; // true 禁用 false 启用
    fullname?: string; // 姓名
    groups?: any[];
}
