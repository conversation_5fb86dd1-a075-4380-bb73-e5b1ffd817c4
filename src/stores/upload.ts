import { defineStore } from "pinia";
import { ref, computed } from "vue";
import type { Ref } from "vue";
import { UPLOAD_STATUS } from "@/utils/const";
import type { UploadFile } from "element-plus";
import type { NewUploadFile } from "@/model/file";
export interface PromiseFn {
    (file: UploadFile, uid: string): Promise<void>;
}
// 定义表格数据类型
export interface UploadItem {
    name: string;
    size: number;
    formatSize: string;
    status: number;
    shortName?: string;
    nameExt?: string;
    state?: number;
    fn?: PromiseFn;
    uid: string;
    file: NewUploadFile;
    errorMsg?: string;
    progress?: number;
}

// export const uploadState = defineStore('uploadState', () => {
//   const uploadReqArr = ref<UploadItem[]>([])
//   const uploadReqIn = ref<UploadItem[]>([])

//   const updateUploadReqArr = function (value: Ref<UploadItem[]>) {
//     uploadReqArr.value = uploadReqArr.value.concat(value.value)
//   }

//   const updateUploadReqIn = function () {
//     uploadReqIn.value = uploadReqIn.value.filter(v => v.state === UPLOAD_STATUS.SUCCESS || UPLOAD_STATUS.FAIL)
//     if (uploadReqIn.value.length >= 3) {
//       return
//     } else {
//       uploadReqArr.value.forEach(v => {
//         if (uploadReqIn.value.length < 3) {
//           if (v.status === UPLOAD_STATUS.BEFORE) {
//             if (v.fn) {
//               v.fn(v.file)
//             }
//             uploadReqIn.value.push(v)
//           }
//         }
//       })
//     }
//   }

//   // 更新上传请求总列表状态
//   const updateUploadReqState = function(value: UploadItem){
//     uploadReqArr.value.forEach(v => {
//       if(v.uid === value.uid) {
//           v.state = value.state
//       }
//     })
//   }

//   // 更新上传中列表的状态
//   const updateUploadInState = function (value: UploadItem) {
//     uploadReqIn.value.forEach(v => {
//       if(v.uid === value.uid) {
//           v.state = value.state
//       }
//     })
//   }

//   return {
//     uploadReqArr,
//     updateUploadReqArr,
//     updateUploadReqIn,
//     updateUploadReqState,
//     updateUploadInState
//   }
// })

export interface UploadState {
    uploadReqArr: UploadItem[];
    uploadReqIn: UploadItem[];
    uploadFinishNumber: number;
    show: boolean;
}

export const uploadState = defineStore("uploadState", {
    state: () =>
        <UploadState>{
            uploadReqArr: [],
            uploadReqIn: [],
            uploadFinishNumber: 0,
            show: false,
        },

    actions: {
        updateUploadReqArr(value: UploadItem[]) {
            this.uploadReqArr = value.concat(this.uploadReqArr);
            console.log("this.uploadReqArr", this.uploadReqArr)
            if (this.uploadReqArr.length) {
                this.show = true;
            } else {
                this.show = false;
            }
        },

        // 更新上传中的数组
        updateUploadReqIn() {
            this.uploadReqIn = this.uploadReqIn.filter(
                (v) => v?.status === UPLOAD_STATUS.IN
            );
            if (this.uploadReqIn.length >= 3) {
                return;
            } else {
                this.uploadReqArr.forEach((v) => {
                    if (this.uploadReqIn.length < 3) {
                        if (v.status === UPLOAD_STATUS.BEFORE) {
                            v.status = UPLOAD_STATUS.IN;
                            this.uploadReqIn.push(v);
                            if (v.fn) {
                                v.fn(v.file, v.uid);
                            }
                        }
                    }
                });
            }
        },

        // 更新上传请求总列表状态
        updateUploadReqState(uid: string, status: number) {
            this.uploadReqArr.forEach((v) => {
                // console.log(v.uid, uid);
                if (v.uid === uid) {
                    v.status = status;
                }
            });
        },

        // 更新上传进度
        updateUploadReqProgress(uid: string, progress: number) {
            // console.log("updateUploadReqProgress", uid, progress)
            this.uploadReqArr.forEach((v) => {
                // console.log(v.uid, uid);
                if (v.uid === uid) {
                    v.progress = progress;
                }
            });
        },

        // 计算完成数量
        computedFinishUpload() {
            let nub = 0;
            this.uploadReqArr.forEach((v) => {
                if (
                    v.status === UPLOAD_STATUS.SUCCESS ||
                    v.status === UPLOAD_STATUS.FAIL
                ) {
                    nub++;
                }
                // console.log(v);
            });

            this.uploadFinishNumber = nub;
        },

        // 更新上传中列表的状态
        updateUploadInState(uid: string, status: number, errorMsg?: string) {
            this.uploadReqIn.forEach((v) => {
                if (v.uid === uid) {
                    v.status = status;
                    v.errorMsg = errorMsg || ''
                }
            });
        },

        // 删除待上传的
        deleteUploadReq(uid: string) {
            const arr = this.uploadReqArr.filter((v) => v.uid !== uid);

            this.uploadReqArr = arr;
        },

        // 关闭清空
        reset() {
            this.show = false
            this.uploadReqArr = []
            this.uploadReqIn = []
            this.uploadFinishNumber = 0
        }
    },
});
