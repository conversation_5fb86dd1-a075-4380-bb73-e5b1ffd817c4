import { defineStore } from "pinia";
import * as authApi from "@/services/auth";
import {
  changeUserSelfInfo,
  getUserById,
  getUserSelfInfo,
} from "@/services/user";
import router from "@/router/index";
import { encryptByMd5 } from "@/utils/index";
import type { RouteLocationNormalizedLoaded } from "vue-router";
import { useStoreRouter } from "./router";
import { downloadImage } from "@/services/file";
import { avatar } from "@/utils/const";

// 用户实例 待补充
interface User {
  version: number;
  id: string;
  token: string;
  user_name: string;
  fullname: string;
  groups: Recordable[];
  email: string;
  phone: string;
  avatar: string;
  avatarUrl?: string;
  key?: string;
  auth: {
    auths: Recordable[];
    id: number;
    menu: string;
  }[];
  menu: Recordable[];
}

const VERSION = 1;

const upgradeVersionMap: {
  [key: number | string]: (state: any) => any;
} = {
  "1": (state: any) => {
    // 比如1升级到2，增加了一个kk字段
    state.version = 2;
    state.kk = "default kk";
    return state;
  },
  "2": (state: any) => {
    // 比如2升级到3，增加了一个oo字段
    state.version = 3;
    state.oo = "default oo";
    return state;
  },
};

export const useUserStore = defineStore("user", {
  state: (): User => ({
    token: "",
    id: "",
    user_name: "",
    fullname: "",
    groups: [],
    email: "",
    phone: "",
    avatar: "",
    avatarUrl: "",
    key: "",
    auth: [],
    menu: [],
    version: VERSION,
  }),
  persistedState: {
    persist: true,
    migrate: (state) => {
      // console.log('版本检查', state)
      // while(state.version != VERSION) {
      //   state = upgradeVersionMap[state.version as number](state)
      // }
      // console.log('升级后', state)
      return state;
    },
  },
  actions: {
    async login(loginData: authApi.ILoginData) {
      try {
        const { data, msg } = await authApi.login({
          account: loginData.user_name,
          password: encryptByMd5(loginData.password),
          // type: loginData.type,
        });
        Object.assign(this, {
          ...data,
        });
        this.getAvatar(this.avatar)
        return true;
      } catch (e: any) {
        if (e.code && e.code === 500000) {
          ElMessage.error(e.msg);
        }
        throw e;
      }
    },
    async logout() {
      await authApi.logout();
    },
    async gotoInstall() {
      router.replace("/install");
    },
    async getInfo() {
      // TODO 实时获取用户数据(用于更新头像名称权限之类的)
      try {
        const { data } = await getUserSelfInfo();
        // Object.assign(this, {
        //   ...data,
        // });
        this.phone = data.phone;
        this.email = data.email;
        this.fullname = data.fullname;
        this.user_name = data.user_name;
        this.groups = data.groups;
        this.avatar = data.avatar;
      } catch (e) {
        console.log(e);
      } finally {
        this.getAvatar(this.avatar)
      }
    },
    async sefInfo(params: { avatar: string; email: string; phone: string }) {
      await changeUserSelfInfo(params);
      this.email = params.email;
      this.phone = params.phone;
      this.avatar = params.avatar;
      this.getAvatar(this.avatar)
    },
    async getAvatar(key: string) {
      if (!key) {
        this.avatar = avatar;
        this.avatarUrl = avatar;
        return;
      }
      try {
        const res = await downloadImage(key);
        const url = URL.createObjectURL(new Blob([res]));
        this.avatarUrl = url;
      } catch (e) {
        console.log(e);
      }
    },
    reset() {
      Object.assign(this, {
        token: "",
        id: "",
        user_name: "",
        email: "",
        phone: "",
        key: "",
        auth: [],
        menu: [],
        version: VERSION,
        avatar: "",
        avatarUrl: avatar
      });
    },
  },
});
