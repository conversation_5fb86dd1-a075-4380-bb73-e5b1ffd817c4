import { defineStore } from "pinia";
import type { RouteRecordRaw } from "vue-router";
import { asyncRoutes } from "@/router/route";
import { filterAsyncRoutes } from "@/utils/router";
import router, { resetRouter } from "@/router";
import { redirectRoot, QUICK_MENU_MAX_COUNT } from "@/utils/const";
import { FILETYPE, type FileItem, type FileItemShortcut } from "@/model/file";
import {
  createShortcutApi,
  delShortcutApi,
  getShortcutList,
} from "@/services/shortcut";
import { delVersionBaseLineApi, getVersionBaseLineList } from "@/services/version";
/**
 * 路由列表
 * @methods setRoutesList 设置路由数据
 * @methods setColumnsMenuHover 设置分栏布局菜单鼠标移入 boolean
 * @methods setColumnsNavHover 设置分栏布局最左侧导航鼠标移入 boolean
 */

interface RoutesListState {
  menuList: RouteRecordRaw[];
  redirectRoot: string;
  activeNavPath: string;
  activeMenuPath: string;
  activeMenuTitle: string;
  navList: Array<any>;
  breadcrumbList: Array<any>;
  keepAliveNames: string[];
  cachedViews: string[];
  isAddFinish: boolean;
  folderMenuList: FileItem[];
  fileMenuList: FileItem[];
  versionBaseLineList: FileItem[]
}

export const useStoreRouter = defineStore("storeRouter", {
  state: (): RoutesListState => ({
    menuList: [],
    redirectRoot: redirectRoot,
    activeNavPath: "",
    activeMenuPath: "",
    activeMenuTitle: "",
    navList: [],
    breadcrumbList: [],
    keepAliveNames: [],
    cachedViews: ['systemBasic', 'systemSdk'],
    isAddFinish: false,
    folderMenuList: [],
    fileMenuList: [],
    versionBaseLineList: [],
  }),
  actions: {
    async getVersionBaseLineList() {
      try {
        const { data } = await getVersionBaseLineList();
        this.versionBaseLineList = data
      } catch (e) {
        console.log(e);
      }
    },
    async delVersionBaseLine(item: FileItem) {
      try {
        await delVersionBaseLineApi(item.id);
        const index = this.versionBaseLineList.findIndex(
          (v) => v.id === item.id
        );
        if (index !== -1) {
          this.versionBaseLineList.splice(index, 1);
        }
      } catch (e) {
        console.log(e);
      }
    },
    async getQuickMenuList() {
        try {
            const { data } = await getShortcutList();
            this.folderMenuList = data.filter(
            (v: any) => v.fileType === FILETYPE.FOLDER
            );
            this.fileMenuList = data.filter(
            (v: any) => v.fileType !== FILETYPE.FOLDER
            );
        } catch (e) {
            console.log(e);
        }
    },
    // 入栈
    async addQuickMenuList(item: FileItem) {
      if (
        this.folderMenuList.some(
          (v) => v.id === item.id || v.name === item.name
        ) ||
        this.fileMenuList.some((v) => v.id === item.id || v.name === item.name)
      )
        return;
      try {
        await createShortcutApi({
          fileId: item.id,
          fileThumbnail: item.thumbnail,
          fileType: item.fileType,
          fileVersion: item.version,
          fileEtag: item.etag,
          name: item.name,
        });
        this.getQuickMenuList()
      } catch (e) {
        console.log(e);
      }
    },
    // 出栈
    async delQuickMenu(item: FileItem) {
      try {
        await delShortcutApi(item.id);
        const folderIndex = this.folderMenuList.findIndex(
          (v) => v.id === item.id
        );
        const fileIndex = this.fileMenuList.findIndex((v) => v.id === item.id);
        if (folderIndex !== -1) {
          this.folderMenuList.splice(folderIndex, 1);
        }
        if (fileIndex !== -1) {
          this.fileMenuList.splice(fileIndex, 1);
        }
      } catch (e) {
        console.log(e);
      }
    },
    async setActivePath(path = "/") {
      const arr: Array<string> = path.split("/");
      // 设置当前左侧导航激活路径
      this.activeNavPath = path;
      // 设置当前顶部导航激活路径
      this.activeMenuPath = "/" + arr[1];
      this.setNavList();
    },
    async setNavList() {
      const filterList: any = this.menuList.filter(
        (item: any) => this.activeMenuPath === item.path
      );
      if (filterList.length) {
        this.navList = filterList[0].children;
        this.activeMenuTitle = filterList[0].meta.title;
      }
    },
    async setBreadcrumbList(arr: Array<any>) {
      this.breadcrumbList = arr;
    },
    async setKeepAliveNames(arr: Array<string>) {
      this.keepAliveNames = arr;
    },
    async setRoutes(menu: Recordable[]) {
      // 如果是超级管理员或者管理员应该默认有所有路由权限
      // if (判断是否是超级管理员和管理员) accessedRoutes = asyncRoutes;
      const accessedRoutes =
        filterAsyncRoutes(asyncRoutes, menu);
      this.redirectRoot =
        (accessedRoutes[0]?.redirect as string) || accessedRoutes[0]?.path;
      this.menuList = accessedRoutes;
      this.menuList.forEach((route) => {
        router.addRoute("root", route);
      });
      this.isAddFinish = true;
      console.log("add router over", router.getRoutes());
    },
    reset() {
      console.log("router reset");
      Object.assign(this, {
        menuList: [],
        redirectRoot: "",
        activeNavPath: "",
        activeMenuPath: "",
        activeMenuTitle: "",
        navList: [],
        breadcrumbList: [],
        keepAliveNames: [],
        cachedViews: [],
        folderMenuList: [],
        fileMenuList: [],
        isAddFinish: false,
      });
      resetRouter();
    },
  },
});
