import { getSystemBasicInfo } from "@/services/system";
import { defineStore } from "pinia";
import * as Storage from "@/utils/storage";
import { changeFavicon } from "@/utils";

const default_config = {
    isNavExpand: true,
    site_beian: "苏ICP备12077906号",
    site_copyright:
        "Copyright © 1992-2023 苏州浩辰软件股份有限公司 版权所有",
    site_name: "浩辰云CAD 企业版",
    site_version: "",
    site_domain: "",
    site_favicon: "",
    site_logo: "",
    site_host: "",
}

interface ConfigState {
    isNavExpand: boolean;
    site_beian: string;
    site_copyright: string;
    site_name: string;
    site_version: string;
    site_domain: string;
    site_favicon: string;
    site_logo: string;
    site_host: string;
}
export const useStoreConfig = defineStore("storeConfig", {
    state: (): ConfigState | any =>
        Storage.get("storeConfig", "localStorage")
            ? JSON.parse(Storage.get("storeConfig", "localStorage") as string)
            : default_config,
    actions: {
        toggleNavExpand() {
            this.isNavExpand = !this.isNavExpand;
        },
        async getConfig() {
            try {
                const { data } = await getSystemBasicInfo();
                Object.assign(this, {
                    ...data,
                });
                changeFavicon()
            } catch (e) {
                console.log(e);
                Object.assign(this, {
                    ...default_config,
                });
            }
        },
    },
});
