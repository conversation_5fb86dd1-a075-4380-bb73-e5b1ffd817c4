import type { LocaleSetting, LocaleType } from "@/model/public";
import { StorageType, localeSetting } from "@/utils/const";
import { defineStore } from "pinia";
import store from "@/stores";
import * as Storage from "@/utils/storage";

const LOCALE_KEY = "locale";

const lsLocaleSetting = (Storage.get(LOCALE_KEY, StorageType.LOCAL) ||
    localeSetting) as LocaleSetting;

interface LocaleState {
    localInfo: LocaleSetting;
}

export const useLocaleStore = defineStore({
    id: "locale",
    state: (): LocaleState => ({
        localInfo: lsLocaleSetting,
    }),
    getters: {
        getLocale(): LocaleType {
            return this.localInfo?.locale || "zh_CN";
        },
    },
    actions: {
        setLocaleInfo(info: Partial<LocaleSetting>) {
            this.localInfo = { ...this.localInfo, ...info };
            Storage.set(LOCALE_KEY, this.localInfo, StorageType.LOCAL);
        }
    },
});

export function useLocaleStoreWithOut() {
    return useLocaleStore(store);
}
