export default {
  // 表单
  formKey: {
    id: "ID",
    groups: "所属用户组",
    root: "超级管理员",
    auth: "权限",
    application: "应用",
    menu_auth: "按钮权限",
    disable: "是否禁用",
    code: "编号",
    parentCode: "父级编号",
    type: "类型",
    name: "名称",
    description: "描述",
    children: "子类",
    category: "类别",
    function: "功能",
    comment: "备注",
    appSandbox: "沙盒",
    version: "版本",
    priority: "优先级",
    createTime: "创建时间",
    startTime: "开始时间",
    endTime: "结束时间",
    progress: "进度",
    status: "状态",
    userName: "用户名",
    tags: "标签",
    retryTimes: "重试次数",
    nodeName: "节点名称",
    developer: "开发者",
    downloadAddress: "下载地址",
    installTime: "安装时间",
    concurrent: "并发数",
    enable: "启用",
    lastLinkTime: "最后连接时间",
    firstLinkTime: "首次连接时间",
    cpu: "CPU",
    mem: "内存",
    os: "操作系统",
    platform: "平台",
    ip: "IP地址",
    adapter: "网卡",
    executions: "任务数",
    totalTaskCount: "历史任务数",
    user_name: "用户名",
    first_name: "名",
    last_name: "姓",
    fullname: "姓名",
    email: "邮箱",
    phone: "电话",
    login_time: "最后登录时间",
    login_ip: "登录IP",
    ctime: "创建时间",
    utime: "更新时间",
    dtime: "删除时间",
    prefix: "备份文件前缀",
    backup_path: "备份路径",
    checked_cycle: "备份策略",
    backup_time: "备份开始时间",
    checked_content: "备份内容",
    execPolicy: "执行时间",
    execStatus: '执行状态',
    expireTime: '数据过期时间（天）'
  },
  resCode: {
    400400: "错误请求",
    400401: "签名错误",
    400402: "签名失效，请重新登录",
    400403: "禁止访问",
    400404: "未找到",
    400405: "不允许使用该方法",
    400408: "请求超时",
    400415: "参数错误",
    400416: "操作失败", // 一般后端增删改查操作失败
    400417: "记录不存在",
    400418: "记录已存在",
    400419: "禁止重复提交",
    400420: "接收文件失败!",
    500000: "服务器尚未完成引导安装",
    500500: "服务器内部错误",
    500501: "服务器不支持该请求",
    500502: "网关错误",
    500503: "服务不可用",
    500504: "网关超时",
    500513: "操作不支持",
    500514: "内部服务异常",
    400421: "用户不存在",
    400422: "用户被禁用",
    400423: "用户名或密码错误",
    400424: "重置密码失败",
    400425: "用户名、邮箱、手机号不能同时为空",
    400426: "用户名已存在",
    400427: "邮箱已存在",
    400428: "手机号已存在",
    400429: "读取导入文件失败",
    400430: "导入文件格式错误",
    400431: "超过最大限制数，一次最多导入1000条",
    400432: "用户旧密码错误",
    400433: "查询用户信息失败",
    400434: "超级管理员禁止操作",
    400441: "用户组不存在",
    400442: "用户组嵌套深度溢出",
    400443: "读取导入文件失败",
    400444: "导入文件格式错误",
    400445: "超过最大限制数，一次最多导入1000条",
    400446: "部门编码不能重复",
    400447: "导入数据格式错误",
    400448: "用户组编码已存在",
    400449: "导出数据未空",
    400450: "父用户组不存在",
    400451: "用户名已存在",
    400461: "节点已存在",
    400462: "节点不存在",
    400463: "节点名称已存在",
    400464: "不存在未绑定节点",
    400481: "任务不存在",
    400482: "任务param参数无效",
    400483: "任务暂无标准输出",
    400484: "任务暂无错误输出",
    400501: "任务类型不存在",
    400502: "任务类型类型已存在",
    400503: "任务类型名称已存在",
    400504: "任务config参数无效",
    400521: "应用不存在",
    400522: "应用名称已存在",
    400600: "没有读权限",
    400601: "没有写权限",
    400602: "还原文件冲突",
    400603: "参数错误",
    400604: "下载失败",
    400605: "文件已存在",
    400701: "名称已存在",
    400801: "批注content参数无效",
    400900: "事件名称不存在",
    400901: "生成房间号错误",
  },
  notification: {
    file_add_success: "{fullName}在目录{folder_name}中上传了新文件{fileName}",
    file_delete_success: "{fullName}在目录{folder_name}中删除了文件{fileName}",
    file_node_change: "{fullName}为文件{fileName}添加了新批注",
    task_create_ok: "创建{taskObj}的{taskName}任务, 成功了!",
    task_create_error: "创建{taskObj}的{taskName}任务, 失败了!",
    task_start_ok: "{taskObj}的{taskName}任务, 开始执行了!",
    task_cancel_ok: "{taskObj}的{taskName}任务, 取消执行了!",
    task_end_ok: "{taskObj}的{taskName}任务, 完成了!",
    task_end_error: "{taskObj}的{taskName}任务, {errReason}",
  },
  // 页面
  // login
  "账号登录": "账号登录",
  "用户名/邮箱/手机号": "用户名/邮箱/手机号",
  "请输入用户名": "请输入用户名",
  "请输入密码": "请输入密码",
  "密码格式不正确，长度在6到32位之间": "密码格式不正确，长度在6到32位之间",
  "登录": "登录",
  "开发文档": "开发文档",
  // files
  "文件（夹）名": "文件（夹）名",
  "文件或者文件夹名": "文件或者文件夹名",
  "文件格式": "文件格式",
  "修改时间": "修改时间",
  "开始时间": "开始时间",
  "结束时间": "结束时间",
  "查询": "查询",
  "重置": "重置",
  "上传文件": "上传文件",
  "上传文件夹": "上传文件夹",
  "新建文件": "新建文件",
  "新建文件夹": "新建文件夹",
  "删除": "删除",
  "移动": "移动",
  "全选": "全选",
  "请输入文件夹名称": "请输入文件夹名称",
  "文件名": "文件名",
  "特色功能": "特色功能",
  "创建者": "创建者",
  "文件大小": "文件大小",
  "创建时间": "创建时间",
  "确认": "确认",
  "取消": "取消",
  "转换失败": "转换失败",
  "同一路径不能复制": "同一路径不能复制",
  "目标文件/文件夹": "目标文件/文件夹",
  "是否更新": "是否更新",
  "提示！": "提示！",
  "拷贝成功": "拷贝成功",
  "同一路径不能移动": "同一路径不能移动",
  "不能移动到子集文件夹中": "不能移动到子集文件夹中",
  "移动成功": "移动成功",
  "该任务类型未获得授权": "该任务类型未获得授权",
  "转换默认dwg存储路径为当前目录下，如果存在同名文件，则会增加版本": "转换默认dwg存储路径为当前目录下，如果存在同名文件，则会增加版本",
  "如果有密码则输入密码": "如果有密码则输入密码",
  "转换": "转换",
  "该文件正处于转换中或已转换完成,请点击": "该文件正处于转换中或已转换完成,请点击",
  "按钮进行跳转查看": "按钮进行跳转查看",
  "你确定要生成的快照吗": "你确定要生成的快照吗",
  "快照": "快照",
  "请输入快照名称,如果不填写,则默认是文件名": "请输入快照名称,如果不填写,则默认是文件名",
  "生成快照": "生成快照",
  "名称格式为0到128位不包含<>:*?？: 特殊字符": "名称格式为0到128位不包含<>:*?？: 特殊字符",
  "删除文件": "删除文件",
  "确定要批量删除选择的文件吗": "确定要批量删除选择的文件吗",
  "确定要删除吗": "确定要删除吗",
  "删除成功": "删除成功",
  "重名校验失败": "重名校验失败",
  "目标文件/文件夹已存在，是否更新": "目标文件/文件夹已存在，是否更新",
  "无文件上传！": "无文件上传！",
  "名称不能为空": "名称不能为空",
  "名称不能包含下列任何字符：\\ /:?*\"<>|": "名称不能包含下列任何字符：\\ /:?*\"<>|",
  "文件名已存在": "文件名已存在",
  "修改成功": "修改成功",
  "创建成功": "创建成功",
  "全部文件": "全部文件",
  "工程图文件需转换为DWG格式后才可查看。是否立即转换？转换成功后会在该文件同目录下生成名称相同，后缀为DWG的文件。": "工程图文件需转换为DWG格式后才可查看。是否立即转换？转换成功后会在该文件同目录下生成名称相同，后缀为DWG的文件。",
  "提示": "提示",
  "重命名": "重命名",
  "拷贝": "拷贝",
  "下载": "下载",
  "文件授权": "文件授权",
  "文件快照": "文件快照",
  "文件历史版本": "文件历史版本",
  "编辑": "编辑",
  "转换完成": "转换完成",
  "转换中,进度": "转换中,进度",
  "文件库": "文件库",
  "操作": "操作",
  "全部": "全部",

  // src/views/layout/header.vue
  "基本信息": "基本信息",
  "修改密码": "修改密码",
  "登出": "登出",

  // src/views/layout/nav.vue
  "删除快捷文件": "删除快捷文件",
  "删除快照文件": "删除快照文件",
  "你确定要删除{name}快捷文件吗": "你确定要删除{name}快捷文件吗",
  "你确定要删除{name}快照文件吗": "你确定要删除{name}快照文件吗",

  // src/views/files/part/index.vue
  "上传零件": "上传零件",
  "零件文件名": "零件文件名",
  "零件类型": "零件类型",
  "零件名称": "零件名称",
  "请输入": "请输入",
  "目标文件：{files}已存在": "目标文件：{files}已存在",
  "你确定要删除{text}吗": "你确定要删除{text}吗",
  "这{count}个零件": "这{count}个零件",
  "{name}零件": "{name}零件",

  // src/views/files/recycled/index.vue
  "回收站为你保存 {days} 天内删除的文件，支持原路径还原文件。": "回收站为你保存 {days} 天内删除的文件，支持原路径还原文件。",
  "永久删除": "永久删除",
  "还原": "还原",
  "名称": "名称",
  "删除时间": "删除时间",
  "保留天数": "保留天数",
  "重名文件": "重名文件",
  "重名文件文件名": "重名文件文件名",
  "注：已选择的文件还原目标目录存在同名文件，点击确定后将以当前文件创建最新版本": "注：已选择的文件还原目标目录存在同名文件，点击确定后将以当前文件创建最新版本",
  "该文件已被删除，无法浏览,请还原后查看": "该文件已被删除，无法浏览,请还原后查看",
  "删除失败": "删除失败",
  "确定要批量还原选择的文件吗": "确定要批量还原选择的文件吗",
  "还原文件": "还原文件",
  "确定要删除 {name} 吗": "确定要删除 {name} 吗",
  "确定要还原 {name} 吗": "确定要还原 {name} 吗",
  "还原成功": "还原成功",

  // src/views/files/template/index.vue
  "上传模板": "上传模板",
  "模板名称": "模板名称",
  "启用": "启用",
  "查看": "查看",
  "请选择": "请选择",
  "启用状态": "启用状态",
  "未启用": "未启用",
  "禁用": "禁用",
  "你确定要{action}{name}模板吗": "你确定要{action}{name}模板吗",
  "这个": "这个",
  "{action}成功": "{action}成功",
  "模板文件格式只包括({types})": "模板文件格式只包括({types})",
  "上传成功": "上传成功",
  "这{count}个模板": "这{count}个模板",
  "{name}模板": "{name}模板",

  // src/views/files/material/index.vue
  "上传材质": "上传材质",
  "材质文件名": "材质文件名",
  "材质类型": "材质类型",
  "材质名称": "材质名称",
  "请输入材质名称": "请输入材质名称",
  "这{count}个材质": "这{count}个材质",
  "{name}材质": "{name}材质",

  // src/views/customer/user/index.vue
  "创建用户": "创建用户",
  "添加用户到用户组": "添加用户到用户组",
  "重置密码": "重置密码",
  "用户名": "用户名",
  "姓名": "姓名",
  "手机号": "手机号",
  "邮箱": "邮箱",
  "状态": "状态",
  "最后登录时间": "最后登录时间",
  "批量删除": "批量删除",
  "批量删除用户": "批量删除用户",
  "用户": "用户",
  "是": "是",
  "否": "否",
  "用户详情": "用户详情",
  "你确定要{action}{name}用户吗": "你确定要{action}{name}用户吗",
  "{action}用户成功": "{action}用户成功",
  "修改用户": "修改用户",
  "{action}用户组成功": "{action}用户组成功",
  "修改用户组": "修改用户组",
  "添加成功": "添加成功",
  "请输入新密码": "请输入新密码",
  "密码格式为6到32位任意字符": "密码格式为6到32位任意字符",
  "修改密码成功": "修改密码成功",
  "重置密码失败": "重置密码失败",
  "导入成功": "导入成功",
  "导出成功": "导出成功",
  "所有": "所有",
  "用户组用户列表": "用户组用户列表",

  // src/views/customer/group/index.vue
  "添加用户组": "添加用户组",
  "添加子组": "添加子组",
  "成员管理": "成员管理",
  "编号": "编号",
  "描述": "描述",
  "成员数": "成员数",
  "用户组编号": "用户组编号",
  "请输入编号": "请输入编号",
  "用户组名称": "用户组名称",
  "请输入名称": "请输入名称",
  "创建用户组": "创建用户组",
  "用户组": "用户组",

  // src/views/customer/components/importDialog.vue
  "第一步，下载模板文件(UTF-8)": "第一步，下载模板文件(UTF-8)",
  "xls/xlsx文件": "xls/xlsx文件",
  "第二步，编辑模板文件": "第二步，编辑模板文件",
  "第三部步，上传编辑好的文件": "第三部步，上传编辑好的文件",
  "点击上传 或 拖拽到这里": "点击上传 或 拖拽到这里",
  "保存": "保存",
  "批量导入": "批量导入",
  "只支持上传一个文件": "只支持上传一个文件",
  "只支持上传 .xlsx, .xls, 后缀的文件": "只支持上传 .xlsx, .xls, 后缀的文件",
  "校验失败": "校验失败",
  "文件内容格式不正确，请参考模板": "文件内容格式不正确，请参考模板",
  "上一级用户组名称": "上一级用户组名称",
  "模板": "模板",

  // src/views/system/sdk/index.vue
  "2D样例": "2D样例",
  "3D样例": "3D样例",
  "编辑样例": "编辑样例",

  // src/views/system/basic/index.vue
  "系统名称": "系统名称",
  "请输入系统名称": "请输入系统名称",
  "站点LOGO": "站点LOGO",
  "站点favicon": "站点favicon",
  "系统访问地址": "系统访问地址",
  "请输入系统访问地址": "请输入系统访问地址",
  "备案信息": "备案信息",
  "请输入备案信息": "请输入备案信息",
  "版权信息": "版权信息",
  "请输入版权信息": "请输入版权信息",
  "系统版本号": "系统版本号",

  "请填写备案信息": "请填写备案信息",
  "请填写版权信息": "请填写版权信息",
  "请填写系统访问地址": "请填写系统访问地址",
  "请填写系统名称": "请填写系统名称",
  "图片大小不能超过 {size}!": "图片大小不能超过 {size}!",

  // src/views/system/task/index.vue
  "客户端自动连接": "客户端自动连接",
  "允许": "允许",
  "不允许": "不允许",
  "SQL执行队列大小": "SQL执行队列大小",
  "节点预加载的数量": "节点预加载的数量",
  "任务预加载的时间间隔(秒)": "任务预加载的时间间隔(秒)",
  "异步任务超时时间(秒)": "异步任务超时时间(秒)",
  "任务调度缓冲区大小": "任务调度缓冲区大小",
  "最大转换图纸超时时间(秒)": "最大转换图纸超时时间(秒)",

  // src/views/system/log/index.vue
  "清空": "清空",
  "操作类型": "操作类型",
  "调用接口": "调用接口",
  "操作名": "操作名",
  "操作时间": "操作时间",
  "结果": "结果",
  "请输入接口": "请输入接口",
  "时间": "时间",
  "你确定要清空所有日志吗": "你确定要清空所有日志吗",
  "清空成功": "清空成功",

  // src/views/system/license/index.vue
  "版本&许可授权": "版本&许可授权",
  "授权码": "授权码",
  "可用": "可用",
  "不可用": "不可用",
  "支持用户数": "支持用户数",
  "支持节点数": "支持节点数",
  "允许登录设备数": "允许登录设备数",
  "三方接入": "三方接入",
  "允许编辑设备数": "允许编辑设备数",
  "许可期限": "许可期限",
  "授权模块": "授权模块",
  "备注：": "备注：",
  "2D支持的文件类型": "2D支持的文件类型",
  "3D支持的文件类型": "3D支持的文件类型",
  "2D标准支持的功能": "2D标准支持的功能",
  "2D高级支持的功能": "2D高级支持的功能",
  "2D行业支持的功能": "2D行业支持的功能",
  "3D标准支持的功能": "3D标准支持的功能",
  "3D高级支持的功能": "3D高级支持的功能",
  "3D行业支持的功能": "3D行业支持的功能",
  "授权功能列表": "授权功能列表",
  "序号": "序号",
  "授权功能": "授权功能",
  "未授权": "未授权",

  // src/views/system/storage/index.vue
  "存储根路径": "存储根路径",

  // src/views/system/thirdParty/index.vue
  "添加三方": "添加三方",
  "重置令牌": "重置令牌",
  "设置过期时间": "设置过期时间",
  "设置权限": "设置权限",
  "三方令牌": "三方令牌",
  "三方名称": "三方名称",
  "三方标识": "三方标识",
  "过期时间": "过期时间",
  "三方状态": "三方状态",
  "设置令牌过期时间": "设置令牌过期时间",
  "三方token": "三方token",
  "文件校验地址": "文件校验地址",
  "向浩辰推送文件接口": "向浩辰推送文件接口",
  "获取外部参照文件地址": "获取外部参照文件地址",
  "安全级别": "安全级别",
  "无认证": "无认证",
  "弱认证": "弱认证",
  "强认证": "强认证",
  "开": "开",
  "关": "关",
  "请输入三方标识": "请输入三方标识",
  "三方标识: 2~128个字符，只能使用字母、数字、下划线，需要以字母开头": "三方标识: 2~128个字符，只能使用字母、数字、下划线，需要以字母开头",
  "请输入过期时间": "请输入过期时间",
  "请输入三方名称": "请输入三方名称",
  "标识": "标识",
  "令牌": "令牌",
  "令牌过期时间": "令牌过期时间",
  "你确定要{action}{name}令牌吗": "你确定要{action}{name}令牌吗",
  "三方调用未获得授权": "三方调用未获得授权",
  "{action}三方成功": "{action}三方成功",
  "修改三方": "修改三方",
  "创建三方": "创建三方",
  "你确定要重置{name}令牌吗": "你确定要重置{name}令牌吗",
  "重置成功": "重置成功",
  "设置成功": "设置成功",
  "三方账户": "三方账户",

  // src/components/ChangePasswordDialog.vue
  "修改用户密码": "修改用户密码",
  "旧密码": "旧密码",
  "确认密码": "确认密码",
  "新密码俩次输入的不一致，请重新输入": "新密码俩次输入的不一致，请重新输入",

  // src/components/BatchResultDialog.vue
  "等待中": "等待中",
  "成功": "成功",

  // src/components/SnapshotDialog.vue
  "上传者": "上传者",
  "当前版本": "当前版本",
  "{name}快照": "{name}快照",

  // src/components/upload/UploadFile.vue

  // src/components/upload/UploadProgress.vue
  "大小": "大小",
  "待上传": "待上传",
  "上传失败": "上传失败",

  // src/components/CreateComForm.vue
  "添加": "添加",
  "{action}{title}": "{action}{title}",

  // src/components/UserBasicInfo.vue
  "用户基本信息": "用户基本信息",
  "用户头像": "用户头像",
  "所属权限组": "所属权限组",
  "头像设置": "头像设置",
  "左旋转": "左旋转",
  "右旋转": "右旋转",
  "图片大于2M，请进行裁剪或重新选择": "图片大于2M，请进行裁剪或重新选择",
  "手机号格式不正确": "手机号格式不正确",
  "邮箱格式不正确": "邮箱格式不正确",
  "修改用户信息成功": "修改用户信息成功",

  // src/components/EditPermission.vue
  "该文件{editor}正在编辑中，如需进入编辑，请点击继续编辑。": "该文件{editor}正在编辑中，如需进入编辑，请点击继续编辑。",
  "注：当前文件仅支持一人编辑，点击继续编辑{editor}将终止编辑。": "注：当前文件仅支持一人编辑，点击继续编辑{editor}将终止编辑。",
  "当前文件正在归并中，请耐心等待...": "当前文件正在归并中，请耐心等待...",
  "停止归并后{editor}编辑的部分数据将无法找回，确认停止归并并进入文件编辑页面？": "停止归并后{editor}编辑的部分数据将无法找回，确认停止归并并进入文件编辑页面？",
  "继续编辑": "继续编辑",
  "停止并编辑": "停止并编辑",
  "确定": "确定",

  // src/views/sample/index.ts
  "一、图纸显示": "一、图纸显示",
  "图纸显示": "图纸显示",
  "打开本地ocf": "打开本地ocf",
  "打开小地图": "打开小地图",
  "隐藏菜单": "隐藏菜单",
  "自定义菜单": "自定义菜单",
  "水印": "水印",
  "快速集成": "快速集成",
  "二、功能接口": "二、功能接口",
  "按钮点击事件": "按钮点击事件",
  "图层布局": "图层布局",
  "另存、转PDF": "另存、转PDF",
  "三、批注功能": "三、批注功能",
  "审图、图章": "审图、图章",
  "批注操作": "批注操作",
  "视图定位": "视图定位",
  "四、扩展功能": "四、扩展功能",
  "叠图功能": "叠图功能",
  "图纸对比": "图纸对比",
  "3d样例": "3d样例",

  // src/hooks/useForm.ts
  "你确定要删除{name}{type}吗": "你确定要删除{name}{type}吗",
  "你确定要{action}{name}{type}吗": "你确定要{action}{name}{type}吗",

  // src/components/Pagination.vue
  "共 {total} 条": "共 {total} 条",
  "上一页": "上一页",
  "下一页": "下一页",
  "{size}条/页": "{size}条/页",

  // src/components/AsyncTaskList.vue
  "暂无数据": "暂无数据",

  // src/components/TransferLayout.vue
  "任务列表": "任务列表",
  "上传任务": "上传任务",
  "上传中（{finished}/{total}）": "上传中（{finished}/{total}）",
  "转换任务": "转换任务",
  "转换任务（成功: {success} 失败: {failed} 进行中: {running}）": "转换任务（成功: {success} 失败: {failed} 进行中: {running}）",

  // src/components/GList.vue
  "请选择类型": "请选择类型",

  // src/utils/index.ts
  "已连接": "已连接",
  "未连接": "未连接",
  "未绑定": "未绑定",
  "每周{week}": "每周{week}",
  "每年第{month}月的": "每年第{month}月的",
  "每月": "每月",
  "{day}号": "{day}号",
  "每天": "每天",
  "执行": "执行",

  // src/views/error-page/
  "您当前无权限访问...": "您当前无权限访问...",
  "该页面不存在...": "该页面不存在...",
  "出错啦!": "出错啦!",
  "请检查您输入的URL是否正确，或单击下面的按钮返回主页": "请检查您输入的URL是否正确，或单击下面的按钮返回主页",
  "返回首页": "返回首页",

  // src/views/system/install/index.vue
  "浩辰CAD企业版初始化面板": "浩辰CAD企业版初始化面板",
  "系统新装": "系统新装",
  "服务端HTTP协议": "服务端HTTP协议",
  "https公钥": "https公钥",
  "https私钥": "https私钥",
  "服务端监听端口": "服务端监听端口",
  "数据库地址": "数据库地址",
  "数据库端口": "数据库端口",
  "数据库文件": "数据库文件",
  "数据表前缀": "数据表前缀",
  "数据库账号": "数据库账号",
  "文件路径为安装主机上的路径，不是操作主机路径": "文件路径为安装主机上的路径，不是操作主机路径",
  "图片大小不能超过{size}": "图片大小不能超过{size}",
  "系统安装中...": "系统安装中...",
  "安装成功,点击重启系统后生效,预计5秒左右": "安装成功,点击重启系统后生效,预计5秒左右",

  // src/views/system/database/index.vue

  // src/utils/const.ts
  "浩辰图纸管理系统": "浩辰图纸管理系统",
  "前端任务": "前端任务",
  "普通后台任务": "普通后台任务",
  "系统后台任务": "系统后台任务",
  "系统后台定时任务": "系统后台定时任务",
  "已就绪": "已就绪",
  "执行中": "执行中",
  "已完成": "已完成",
  "部分完成": "部分完成",
  "已失败": "已失败",
  "已取消": "已取消",
  "最低": "最低",
  "低": "低",
  "略低": "略低",
  "略高": "略高",
  "高": "高",
  "最高": "最高",
  "简体中文": "简体中文",
  "视口相关": "视口相关",
  "图层提取": "图层提取",
  "爆炸": "爆炸",
  "剖切": "剖切",
  "结构树": "结构树",

  // src/App.vue
  "服务器尚未完成引导安装": "服务器尚未完成引导安装",

  // src/app/index.vue
  "参数缺失 app": "参数缺失 app",

  // src/router/route.ts
  "样例SDK": "样例SDK",

  // src/views/customer/auth/index.vue
  "用户权限设置": "用户权限设置",
  "用户组权限设置": "用户组权限设置",

  // src/views/customer/components/permissionView.vue
  "编辑权限": "编辑权限",
  "请输入搜索内容": "请输入搜索内容",
  "{title}已存在": "{title}已存在",

  // src/views/customer/file/auth.vue
  "限制编辑": "限制编辑",
  "限制编辑、限制查看": "限制编辑、限制查看",
  "查看、编辑": "查看、编辑",
  "账户类型": "账户类型",
  "账户名称": "账户名称",
  "授权类型": "授权类型",
  "权限": "权限",
  "授权时间": "授权时间",
  "文件权限": "文件权限",

  // src/views/services/application/index.vue
  "正在安装中,请耐心等待...": "正在安装中,请耐心等待...",
  "安装应用": "安装应用",
  "沙盒": "沙盒",
  "开发者": "开发者",
  "批量卸载": "批量卸载",
  "应用名称": "应用名称",
  "应用类型": "应用类型",
  "应用沙盒": "应用沙盒",
  "应用详情": "应用详情",
  "文件检验中...": "文件检验中...",
  "计算sha1 {name} (size:{size}) {random}": "计算sha1 {name} (size:{size}) {random}",
  "应用已安装": "应用已安装",
  "上传应用中...": "上传应用中...",
  "文件检验进度：{progress}%": "文件检验进度：{progress}%",
  "应用上传完成，正在安装中，请耐心等待": "应用上传完成，正在安装中，请耐心等待",
  "上传进度：{progress}%": "上传进度：{progress}%",
  "你确定要卸载{name}应用吗?": "你确定要卸载{name}应用吗?",

  // src/views/services/instance/index.vue
  "添加节点": "添加节点",
  "节点": "节点",
  "节点ID": "节点ID",
  "最大并发数": "最大并发数",
  "标签(tags)": "标签(tags)",
  "请输入节点ID": "请输入节点ID",
  "节点ID为2~128个字符，只能使用字母、数字、下划线，需要以字母开头": "节点ID为2~128个字符，只能使用字母、数字、下划线，需要以字母开头",
  "机器状态": "机器状态",
  "并发数": "并发数",
  "任务数": "任务数",
  "历史任务数": "历史任务数",
  "首次连接时间": "首次连接时间",
  "最后连接时间": "最后连接时间",
  "节点tag标签": "节点tag标签",
  "{action}节点成功": "{action}节点成功",
  "客户端秘钥文件下载": "客户端秘钥文件下载",
  "节点客户端下载": "节点客户端下载",
  "检测": "检测",
  "绑定": "绑定",

  // src/views/services/task/index.vue
  "重新执行": "重新执行",
  "运行节点": "运行节点",
  "所属用户": "所属用户",
  "你确定要清空所有任务吗?": "你确定要清空所有任务吗?",
  "你确定要取消{id}任务吗?": "你确定要取消{id}任务吗?",
  "重新执行成功": "重新执行成功",

  // src/views/services/taskScheduler/index.vue
  "新增定时任务": "新增定时任务",
  "重复周期": "重复周期",
  "最后一次执行": "最后一次执行",
  "下次执行": "下次执行",
  "平均执行时间（秒）": "平均执行时间（秒）",
  "请选择任务类型": "请选择任务类型",
  "你确定要{title}该定时任务吗?": "你确定要{title}该定时任务吗?",
  "每月{date}号": "每月{date}号",
  "一": "一",
  "二": "二",
  "三": "三",
  "四": "四",
  "五": "五",
  "六": "六",
  "日": "日",

  // src/views/services/taskType/index.vue
  "添加任务类型": "添加任务类型",
  "请选择任务类别": "请选择任务类别",
  "请选择沙盒": "请选择沙盒",
  "不设置": "不设置",
  "按天": "按天",
  "按星期": "按星期",
  "按月": "按月",
  "每星期{week}执行": "每星期{week}执行",
  "每{month}月{day}号执行": "每{month}月{day}号执行",
  "更新{name}定时任务成功": "更新{name}定时任务成功",
  "不执行": "不执行",
  "任务类型详情": "任务类型详情",

  // src/views/system/fbackup/index.vue
  "是否启用": "是否启用",
  "备份文件名(范例)": "备份文件名(范例)",
  "备份文件前缀": "备份文件前缀",
  "备份文件日期样式": "备份文件日期样式",
  "备份路径": "备份路径",
  "备份策略": "备份策略",
  "备份开始时间": "备份开始时间",
  "备份内容": "备份内容",
  "系统备份恢复": "系统备份恢复",
  "恢复": "恢复",
  "请输入备份文件路径": "请输入备份文件路径",
  "系统备份恢复中...": "系统备份恢复中...",
  "恢复完成": "恢复完成",

  // src/views/system/cloudCADConf/index.vue
  "编辑类型": "编辑类型",
  "单人编辑": "单人编辑",
  "多人编辑": "多人编辑",
  "编辑类型的修改，需要重启服务后生效！": "编辑类型的修改，需要重启服务后生效！",
  "保存类型": "保存类型",
  "保存方式": "保存方式",
  "增量个数": "增量个数",
  "最大值3600": "最大值3600",
  "(单位：s)": "(单位：s)",
  "(单位：个)": "(单位：个)",
  "编辑页清除数据倒计时": "编辑页清除数据倒计时",
  "请填选择编辑类型": "请填选择编辑类型",
  "请填写保存周期": "请填写保存周期",
  "请选择保存类型": "请选择保存类型",
  "Tips:": "Tips:",

  // src/views/files/file/index.vue
  "支持上传文件、上传文件夹；": "支持上传文件、上传文件夹；",
  "上传文件类型包括：（注意这里的类型要对应文件格式的类型）等2D、3D格式": "上传文件类型包括：（注意这里的类型要对应文件格式的类型）等2D、3D格式",

  // src/views/files/font/index.vue
  "支持上传字体文件": "支持上传字体文件",
  "上传字体格式包括：ttf,ttc,otf,shx": "上传字体格式包括：ttf,ttc,otf,shx",
  "上传字体": "上传字体",
  "字体文件名": "字体文件名",
  "字体类型": "字体类型",
  "字体名称": "字体名称",
  "字体文件格式只包括(ttf,ttc,otf,shx)": "字体文件格式只包括(ttf,ttc,otf,shx)",
  "这{count}个字体": "这{count}个字体",
  "{name}字体": "{name}字体",
  "你确定要删除{text}吗?": "你确定要删除{text}吗?",

  // src/views/system/sdk/ShowCode.vue
  "刷新": "刷新",
  "运行": "运行",

  // src/views/system/openDWGConf/index.vue
  "水印工具": "水印工具",
  "水印文本": "水印文本",
  "请输入水印文本": "请输入水印文本",
  "显示图纸小地图": "显示图纸小地图",
  "图章": "图章",
  "图章宽度(px)": "图章宽度(px)",
  "请输入图章宽度": "请输入图章宽度",
  "图章高度(px)": "图章高度(px)",
  "请输入图章高度": "请输入图章高度",
  "请上传图章": "请上传图章",
  "最多只能上传10个图章": "最多只能上传10个图章",

  // src/views/system/authorizationRecords/index.vue
  "允许编辑设备数量：{allowCount}，当前已编辑设备数量：{currentCount}": "允许编辑设备数量：{allowCount}，当前已编辑设备数量：{currentCount}",
  "关键字": "关键字",
  "开始编辑时间": "开始编辑时间",
  "历史数据": "历史数据",
  "设备号": "设备号",
  "创建未使用": "创建未使用",
  "退出编辑时间": "退出编辑时间",
  "浏览器信息": "浏览器信息",
  "踢出": "踢出",
  "历史登录记录": "历史登录记录",
  "关 闭": "关 闭",
  "您当前正在对设备号：{browserId} 进行踢出操作，踢出后该设备将立即关闭并退出云原生编辑页面。确认继续进行该操作？": "您当前正在对设备号：{browserId} 进行踢出操作，踢出后该设备将立即关闭并退出云原生编辑页面。确认继续进行该操作？",
  "当前设备已结束编辑": "当前设备已结束编辑",

  // src/views/services/task/detail.vue
  "任务详情": "任务详情",
  "日志标准输出": "日志标准输出",
  "日志错误输出": "日志错误输出",
  "任务参数": "任务参数",
  "导出": "导出",
  "任务暂无输出": "任务暂无输出",
  "获取失败": "获取失败",
  "日志获取/解析错误：{error}": "日志获取/解析错误：{error}",
  "{id}任务{type}日志": "{id}任务{type}日志",

  // src/views/customer/components/createUser.vue
  "{action}用户": "{action}用户",
  "用户名2~128个字符，只能使用字母、数字、下划线，需要以字母开头": "用户名2~128个字符，只能使用字母、数字、下划线，需要以字母开头",
  "密码格式为6~32任意字符": "密码格式为6~32任意字符",

  // src/app/CloudCAD/index.ts
  "获取文件信息失败": "获取文件信息失败",
  "获取文件信息失败, 请返回文件列表重试！": "获取文件信息失败, 请返回文件列表重试！",
  "连接断开，重连中...": "连接断开，重连中...",
  "加锁失败{error}, 请返回文件列表重试！": "加锁失败{error}, 请返回文件列表重试！",
  "连接失败{msg}, 请返回文件列表重试！": "连接失败{msg}, 请返回文件列表重试！",
  "服务器已断开联接，修改的数据已保存成草稿，可以重新进入编辑继续之前工作。": "服务器已断开联接，修改的数据已保存成草稿，可以重新进入编辑继续之前工作。",
  "退出中...": "退出中...",
  "数据保存中，请勿关闭程序": "数据保存中，请勿关闭程序",
  "服务器已断开联接，自动保存数据失败，请重试保存或退出编辑": "服务器已断开联接，自动保存数据失败，请重试保存或退出编辑",
  "连接失败, 请返回文件列表重试！ {msg}": "连接失败, 请返回文件列表重试！ {msg}",
  "获取云CAD配置失败": "获取云CAD配置失败",
  "cloudCAD init error!": "cloudCAD init error!",
  "命令执行中！": "命令执行中！",
  "保存版本成功": "保存版本成功",
  "保存版本失败！": "保存版本失败！",
  "保存增量异常！": "保存增量异常！",
  "创建历史版本失败 {error}": "创建历史版本失败 {error}",
  "保存失败！{errorMsg}": "保存失败！{errorMsg}",
  "协同连接失败！": "协同连接失败！",
  "单人编辑初始化失败！": "单人编辑初始化失败！",
  "退出编辑": "退出编辑",
  "当前页面长时间没有操作，暂停页面编辑。": "当前页面长时间没有操作，暂停页面编辑。",
  "请稍候...": "请稍候...",
  "编辑加锁成功": "编辑加锁成功",
  "socket连接/加锁失败": "socket连接/加锁失败",
  "用户信息": "用户信息",
  "点击了编辑": "点击了编辑",
  "单人编辑cloudPath": "单人编辑cloudPath",
  "cloudPath 从url parse失败": "cloudPath 从url parse失败",
  "编辑-所有参数": "编辑-所有参数",
  "初始化失败": "初始化失败",
  "拼接请求": "拼接请求",

  // src/app/common/utils.ts
  "此页面无法被自动关闭，请手动关闭浏览器标签页。": "此页面无法被自动关闭，请手动关闭浏览器标签页。",

  // src/app/CloudCAD/initCad.ts
  "开图失败，请刷新页面重试！": "开图失败，请刷新页面重试！",
  "提醒（您已无法编辑文件）": "提醒（您已无法编辑文件）",
  "文件编辑权限已转由用户{editor}获取，如需编辑请前往文件列表页重新进入编辑。": "文件编辑权限已转由用户{editor}获取，如需编辑请前往文件列表页重新进入编辑。",
  "上传增量失败": "上传增量失败",
  "获取历史版本列表失败": "获取历史版本列表失败",
  "创建历史版本失败": "创建历史版本失败",
  "删除历史版本失败": "删除历史版本失败",
  "下载文件失败: ": "下载文件失败: ",
  "下载文件失败!": "下载文件失败!",
  "获取下拉文件列表失败：": "获取下拉文件列表失败：",
  "获取下拉文件列表失败!": "获取下拉文件列表失败!",
  "当前无法编辑，如需编辑请前往文件列表页重新进入编辑。": "当前无法编辑，如需编辑请前往文件列表页重新进入编辑。",
  "您已被管理员强制退出云原生编辑页面，如有问题请联系管理员。": "您已被管理员强制退出云原生编辑页面，如有问题请联系管理员。",
  "身份信息失效，请关闭重试！": "身份信息失效，请关闭重试！",
  "您已无法编辑文件！": "您已无法编辑文件！",
  "关闭": "关闭",

  // src/app/CloudCAD/cooperate.ts
  "初始化失败，请稍后再试！": "初始化失败，请稍后再试！",

  "在{position}": "在{position}",
  "执行了 {command} 命令": "执行了 {command} 命令",
  "增量合并失败": "增量合并失败",

  "增量上传成功，但没有recordId": "增量上传成功，但没有recordId",

  // src/app/CloudCAD/single.ts
  "{senderName} 要结束你的编辑，是否退出编辑。": "{senderName} 要结束你的编辑，是否退出编辑。",
  "否({time})": "否({time})",
  "{senderName} 已剥夺您的编辑权限，您被迫退出编辑页面": "{senderName} 已剥夺您的编辑权限，您被迫退出编辑页面",

  "无法编辑文件！": "无法编辑文件！",
  "正在保存您的编辑，请稍候...": "正在保存您的编辑，请稍候...",

  // src/app/Preview2d/index.ts
  "参数异常：vendorcode={vendorcode}": "参数异常：vendorcode={vendorcode}",
  "参数异常：element={element}": "参数异常：element={element}",
  "参数异常：param.fileId={fileId} 或 param.fileDownLoadUrl={fileDownLoadUrl}": "参数异常：param.fileId={fileId} 或 param.fileDownLoadUrl={fileDownLoadUrl}",
  "获取资源失败/插件不存在": "获取资源失败/插件不存在",

  "获取资源失败!": "获取资源失败!",

  "切换布局信息错误！": "切换布局信息错误！",

  // src/app/Preview3d/index.ts
  "无storageId:{storageId} {type}: {resourceName}": "无storageId:{storageId} {type}: {resourceName}",
  "分片index": "分片index",
  "brep storageId": "brep storageId",
  "转换任务失败！": "转换任务失败！",
  "获取任务状态异常：{taskInfo}": "获取任务状态异常：{taskInfo}",
  "无资源信息:{data}": "无资源信息:{data}",
  "发起转换任务错误：{msg} res: {res}": "发起转换任务错误：{msg} res: {res}",
  "其他错误": "其他错误",

  // src/app/Preview3d/notes.ts
  "操作失败: {error}": "操作失败: {error}",
  "服务端返回失败": "服务端返回失败",
  "网络连接异常，请稍后重试": "网络连接异常，请稍后重试",
  "{action}批注 操作失败: {error}": "{action}批注 操作失败: {error}",

  // src/app/Preview2d/usePreview2d.ts
  "缺少外部参照物：{refs}": "缺少外部参照物：{refs}",
  "建议您以上传文件夹的方式上传图纸及其外部参照，或将外部参照上传至云图同目录下再重新开图。": "建议您以上传文件夹的方式上传图纸及其外部参照，或将外部参照上传至云图同目录下再重新开图。",
  "事件通知任务失败！事件名：{ename}  数据：{data}": "事件通知任务失败！事件名：{ename}  数据：{data}",
  "批注元信息无资源": "批注元信息无资源",
  "书签息无资源": "书签息无资源",
  "书签为空": "书签为空",

  // ElMessageBox 和 ElMessage 弹框国际化
  "token已失效，请重新登录": "token已失效，请重新登录",
  "你确定要清空所有日志吗?": "你确定要清空所有日志吗?",
  "您确定将图纸版本恢复到此版本吗？": "您确定将图纸版本恢复到此版本吗？",
  "文件在线编辑功能未获得授权": "文件在线编辑功能未获得授权",
  "已达到当前时段最大用户链接数": "已达到当前时段最大用户链接数",

  // src/utils/CloudCadUtils 国际化
  "当前可以进入编辑页，请点击 确定 按钮继续": "当前可以进入编辑页，请点击 确定 按钮继续",
  "该图有正在执行的命令，是否要强制中断操作并保存？请注意，中断后会丢弃部分修改": "该图有正在执行的命令，是否要强制中断操作并保存？请注意，中断后会丢弃部分修改",
  "强制中断": "强制中断",
  "该图正在被抢占编辑权限，是否强制抢占编辑权限？注意强制抢占权限存在编辑者保存内容丢失的风险！": "该图正在被抢占编辑权限，是否强制抢占编辑权限？注意强制抢占权限存在编辑者保存内容丢失的风险！",
  "强制抢占": "强制抢占",
  "{editName} 正在编辑，是否要抢占编辑权限？": "{editName} 正在编辑，是否要抢占编辑权限？",
  "结束编辑": "结束编辑",
  "对方长时间未响应，是否强制抢占编辑权限？": "对方长时间未响应，是否强制抢占编辑权限？",
  "{editName} 用户命令正在执行中，无法被抢占": "{editName} 用户命令正在执行中，无法被抢占",
  "当前图纸正在被其他用户编辑中，如需进入编辑，请点击继续编辑。此操作将导致其他用户退出编辑状态。": "当前图纸正在被其他用户编辑中，如需进入编辑，请点击继续编辑。此操作将导致其他用户退出编辑状态。",
  "{editName} 拒绝了您的抢占请求，是否强制抢占编辑权限？注意强制抢占权限存在编辑者保存内容丢失的风险！": "{editName} 拒绝了您的抢占请求，是否强制抢占编辑权限？注意强制抢占权限存在编辑者保存内容丢失的风险！",
  "正在等待 {editName} 响应，剩余 ": "正在等待 {editName} 响应，剩余 ",
  "强制编辑": "强制编辑",
  "继续等待": "继续等待",
  "对方长时间未响应，是否强制进入编辑状态或继续等待？": "对方长时间未响应，是否强制进入编辑状态或继续等待？",
  "{editName} 正在对他的修改进行保存，请等待 ": "{editName} 正在对他的修改进行保存，请等待 ",
  "进入编辑中，请稍等......": "进入编辑中，请稍等......",

  // src/views/system/webSdkCertificate/index.vue
  "文件名：": "文件名：",
  "点击上传": "点击上传",
  "证书信息": "证书信息",
  "允许域名": "允许域名",
  "证书导入时间": "证书导入时间",

  // src/views/system/thirdPartyLog/index.vue
  "调用名称": "调用名称",


  // src/views/services/taskType/index.vue
  "执行时间": "执行时间",

  // src/views/services/taskScheduler/taskDrawer.vue
  "定时任务": "定时任务",
  "号执行": "号执行",
  "数据过期时间（天）": "数据过期时间（天）",
  "修改定时任务": "修改定时任务",
  "创建定时任务": "创建定时任务",

  // src/views/services/taskScheduler/recordDialog.vue
  "计划任务执行记录": "计划任务执行记录",
  "耗时（秒）": "耗时（秒）",
  "请选择状态": "请选择状态",

  // src/views/services/instance/checkNodeDialog.vue
  "节点检测": "节点检测",
  "开始检测": "开始检测",
  "提交任务": "提交任务",
  "任务已提交，进入任务队列": "任务已提交，进入任务队列",
  "任务执行": "任务执行",
  "任务执行中": "任务执行中",
  "完成": "完成",
  "成功或失败": "成功或失败",
  "错误": "错误",

  // src/views/services/instance/bindNodeDialog.vue
  "节点绑定": "节点绑定",
  "指纹": "指纹",
  "绑定成功": "绑定成功",

  // src/views/files/file/SetRightDialog.vue
  "需要限制谁能查看这个？": "需要限制谁能查看这个？",
  "有当前文件（夹）查看和编辑权限的所有人可以编辑本文件（夹）。使用上面的下拉框来选择限制给某些人查看或者编辑，注意查看权限会继承给本文件夹的子页面，编辑权限不继承。": "有当前文件（夹）查看和编辑权限的所有人可以编辑本文件（夹）。使用上面的下拉框来选择限制给某些人查看或者编辑，注意查看权限会继承给本文件夹的子页面，编辑权限不继承。",
  "输入一个用户名或者组名": "输入一个用户名或者组名",
  "所有人可以查看和编辑该页面": "所有人可以查看和编辑该页面",
  "所有人可以查看,特定的人可以编辑": "所有人可以查看,特定的人可以编辑",
  "只有特定的人可以查看或者编辑页面": "只有特定的人可以查看或者编辑页面",
  "{name}添加重复": "{name}添加重复",
  "权限设置成功": "权限设置成功",
  "请添加权限人员": "请添加权限人员",

  // src/views/files/file/HistoryDialog.vue
  "{name}文件历史版本": "{name}文件历史版本",
  "缩略图": "缩略图",

  // src/views/files/file/CreateFileDialog.vue
  "请输入模板名称": "请输入模板名称",
  "预览模板": "预览模板",
  "请选择模板": "请选择模板",
  "创建文件成功": "创建文件成功",

  // src/views/customer/components/groupTree.vue
  "请输入关键字查找": "请输入关键字查找",
  "用户组详情": "用户组详情",

  // src/views/customer/components/createGroup.vue
  "父级用户组": "父级用户组",
  "编号1~32个字符, 只能使用字母、数字、下划线，需要以字母开头": "编号1~32个字符, 只能使用字母、数字、下划线，需要以字母开头",

  // src/views/customer/components/AddUserToGroupDialog.vue
  "所有用户": "所有用户",
  "选中用户": "选中用户",

  "{selected} / {total}": "{selected} / {total}",

  // src/utils/http-message.ts
  "网络超时": "网络超时",
  "网络连接错误": "网络连接错误",
  "未知错误": "未知错误",

  // src/utils/index.ts
  "输入": "输入",

  "文件编辑权限已转由用户{err?.data?.editor}获取，如需编辑请前往文件列表页重新进入编辑。": "文件编辑权限已转由用户{err?.data?.editor}获取，如需编辑请前往文件列表页重新进入编辑。",

  // menu
  "文件管理": "文件管理",
  "模板管理": "模板管理",
  "字体管理": "字体管理",
  "材质管理": "材质管理",
  "零件管理": "零件管理",
  "回收站": "回收站",

  "应用管理": "应用管理",
  "实例(节点)管理": "实例(节点)管理",
  "任务类型管理": "任务类型管理",
  "任务管理": "任务管理",
  "计划任务": "计划任务",

  "用户管理": "用户管理",
  "用户组管理": "用户组管理",
  "权限策略": "权限策略",
  "文件权限管理": "文件权限管理",

  "系统设置": "系统设置",
  "任务设置": "任务设置",
  "数据库设置": "数据库设置",
  "存储设置": "存储设置",
  "备份与恢复": "备份与恢复",
  "编辑授权使用记录": "编辑授权使用记录",
  "WEB SDK证书导入": "WEB SDK证书导入",
  "系统操作日志": "系统操作日志",
  "三方访问日志": "三方访问日志",
  "系统安装": "系统安装",
  "2D 览图设置": "2D 视图设置",
  "云原生设置": "云原生设置",
  "授权许可证": "授权许可证",
  "节点名称": "节点名称",
  "标签": "标签",
  "类型": "类型",
  "详情": "详情",
  "卸载": "卸载",
  "节点详情": "节点详情",
  "返回": "返回",
  "备注": "备注",
  "修改": "修改",
  "创建": "创建",
  "任务类型": "任务类型",
  "类别": "类别",
  "功能": "功能",
  "用户名称": "用户名称",
  "优先级": "优先级",
};
