import type { App } from "vue";
import { type I18n, type Composer,createI18n } from "vue-i18n";
import { useLocaleStoreWithOut } from "@/stores/locale";
// element-plus国际化
import elementEnLocale from "element-plus/dist/locale/en.mjs";
import elementZhLocale from "element-plus/dist/locale/zh-cn.mjs";

import zhLocale from "./zh";
import enLocale from "./en";

const localeStore = useLocaleStoreWithOut();

const messages = {
    en: {
        ...enLocale,
        ...elementEnLocale,
    },
    zh: {
        ...zhLocale,
        ...elementZhLocale,
    },
};

export const getLocale = () => {
    return localeStore.getLocale === "zh_CN" ? "zh" : "en";
    // return 'zh'
};

export const i18n: I18n = createI18n({
    legacy: false,
    locale: getLocale(),
    fallbackLocale: "en",
    messages: messages,
});

export const $t = (i18n.global as Composer).t

export function setupI18n(app: App) {
    app.use(i18n);
}
