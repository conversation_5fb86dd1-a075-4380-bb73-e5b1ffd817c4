export default {
  // 表单
  formKey: {
    id: "ID",
    groups: "fhdaskj<PERSON>h   dafdasf dasfj<PERSON><PERSON><PERSON>j dasfdsalfjdsf",
    root: "fhdaskj<PERSON>h   dafdasf dasfjds<PERSON>fj dasfdsalfjdsf",
    auth: "fhdaskj<PERSON>h   dafdasf dasfjds<PERSON>fj dasfdsalfjdsf",
    application: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    menu_auth: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    disable: "fhdaskj<PERSON>h   dafdasf dasfjdslafj dasfdsalfjdsf",
    code: "fhdaskj<PERSON>h   dafdasf dasfjdslafj dasfdsalfjdsf",
    parentCode: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    type: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    name: "fhdaskj<PERSON>h   dafdasf dasfj<PERSON><PERSON><PERSON>j dasfdsalfjdsf",
    description: "fhdaskj<PERSON>h   dafdasf dasfjdsla<PERSON>j dasfdsalfjdsf",
    children: "fhdaskj<PERSON>h   dafdasf dasfjdslafj dasfdsalfjdsf",
    category: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    function: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    comment: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    appSandbox: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    version: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    priority: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    createTime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    startTime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    endTime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    progress: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    status: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    userName: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    tags: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    retryTimes: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    nodeName: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    developer: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    downloadAddress: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    installTime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    concurrent: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    enable: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    lastLinkTime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    firstLinkTime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    cpu: "CPU",
    mem: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    os: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    platform: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    ip: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    adapter: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    executions: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    totalTaskCount: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    user_name: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    first_name: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    last_name: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    fullname: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    email: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    phone: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    login_time: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    login_ip: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    ctime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    utime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    dtime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    prefix: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    backup_path: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    checked_cycle: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    backup_time: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    checked_content: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    execPolicy: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    execStatus: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    expireTime: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf"
  },
  resCode: {
    400400: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400401: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400402: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400403: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400404: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400405: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400408: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400415: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400416: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf", // 一般后端增删改查操作失败
    400417: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400418: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400419: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400420: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    500000: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    500500: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    500501: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    500502: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    500503: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    500504: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    500513: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    500514: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400421: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400422: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400423: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400424: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400425: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400426: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400427: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400428: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400429: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400430: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400431: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400432: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400433: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400434: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400441: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400442: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400443: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400444: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400445: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400446: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400447: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400448: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400449: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400450: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400451: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400461: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400462: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400463: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400464: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400481: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400482: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400483: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400484: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400501: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400502: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400503: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400504: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400521: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400522: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400600: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400601: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400602: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400603: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400604: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400605: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400701: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400801: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400900: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    400901: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  },
  notification: {
    file_add_success: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    file_delete_success: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    file_node_change: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    task_create_ok: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    task_create_error: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    task_start_ok: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    task_cancel_ok: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    task_end_ok: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
    task_end_error: "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  },
  // 页面
  // login
  "账号登录": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户名/邮箱/手机号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入用户名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入密码": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "密码格式不正确，长度在6到32位之间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "登录": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "开发文档": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  // files
  "文件（夹）名": "File/Folder Name with very long text for testing",
  "文件或者文件夹名": "File or Folder Name with very long text for testing",
  "文件格式": "File Format with very long text for testing",
  "修改时间": "Modification Time with very long text for testing",
  "开始时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "结束时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "查询": "Search with very long text for testing internationalization",
  "重置": "Reset with very long text for testing internationalization",
  "上传文件": "Upload File with very long text for testing internationalization",
  "上传文件夹": "Upload Folder with very long text for testing internationalization",
  "新建文件": "Create New File with very long text for testing internationalization",
  "新建文件夹": "Create New Folder with very long text for testing internationalization",
  "删除": "Delete with very long text for testing internationalization",
  "移动": "Move with very long text for testing internationalization",
  "全选": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入文件夹名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "特色功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建者": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件大小": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "确认": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "取消": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "转换失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "同一路径不能复制": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "目标文件/文件夹": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "是否更新": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "提示！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "拷贝成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "同一路径不能移动": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "不能移动到子集文件夹中": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "移动成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "该任务类型未获得授权": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "转换默认dwg存储路径为当前目录下，如果存在同名文件，则会增加版本": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "如果有密码则输入密码": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "转换": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "该文件正处于转换中或已转换完成,请点击": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "按钮进行跳转查看": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要生成的快照吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "快照": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入快照名称,如果不填写,则默认是文件名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "生成快照": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "名称格式为0到128位不包含<>:*?？: 特殊字符": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "删除文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "确定要批量删除选择的文件吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "确定要删除吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "删除成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重名校验失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "目标文件/文件夹已存在，是否更新": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "无文件上传！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "名称不能为空": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "名称不能包含下列任何字符：\\ /:?*\"<>|": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件名已存在": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "修改成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "全部文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "工程图文件需转换为DWG格式后才可查看。是否立即转换？转换成功后会在该文件同目录下生成名称相同，后缀为DWG的文件。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "提示": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重命名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "拷贝": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "下载": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件授权": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件快照": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件历史版本": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "转换完成": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "转换中,进度": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件库": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "操作": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "全部": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/layout/header.vue
  "基本信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "修改密码": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "登出": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/layout/nav.vue
  "删除快捷文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "删除快照文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要删除{name}快捷文件吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要删除{name}快照文件吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/files/part/index.vue
  "上传零件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "零件文件名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "零件类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "零件名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "目标文件：{files}已存在": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要删除{text}吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "这{count}个零件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{name}零件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/files/recycled/index.vue
  "回收站为你保存 {days} 天内删除的文件，支持原路径还原文件。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "永久删除": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "还原": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "删除时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "保留天数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重名文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重名文件文件名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "注：已选择的文件还原目标目录存在同名文件，点击确定后将以当前文件创建最新版本": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "该文件已被删除，无法浏览,请还原后查看": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "删除失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "确定要批量还原选择的文件吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "还原文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "确定要删除 {name} 吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "确定要还原 {name} 吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "还原成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/files/template/index.vue
  "上传模板": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "模板名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "启用": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "查看": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请选择": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "启用状态": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "未启用": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "禁用": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要{action}{name}模板吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "这个": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{action}成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "模板文件格式只包括({types})": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "这{count}个模板": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{name}模板": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/files/material/index.vue
  "上传材质": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "材质文件名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "材质类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "材质名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入材质名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "这{count}个材质": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{name}材质": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/user/index.vue
  "创建用户": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "添加用户到用户组": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重置密码": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "姓名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "手机号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "邮箱": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "状态": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "最后登录时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "批量删除": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "批量删除用户": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "是": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "否": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户详情": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要{action}{name}用户吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{action}用户成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "修改用户": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{action}用户组成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "修改用户组": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "添加成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入新密码": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "密码格式为6到32位任意字符": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "修改密码成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重置密码失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "导入成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "导出成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "所有": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户组用户列表": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/group/index.vue
  "添加用户组": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "添加子组": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "成员管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "编号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "描述": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "成员数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户组编号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入编号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户组名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建用户组": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户组": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/components/importDialog.vue
  "第一步，下载模板文件(UTF-8)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "xls/xlsx文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "第二步，编辑模板文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "第三部步，上传编辑好的文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "点击上传 或 拖拽到这里": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "保存": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "批量导入": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "只支持上传一个文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "只支持上传 .xlsx, .xls, 后缀的文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "校验失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件内容格式不正确，请参考模板": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上一级用户组名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "模板": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/sdk/index.vue
  "2D样例": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "3D样例": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "编辑样例": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/basic/index.vue
  "系统名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入系统名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "站点LOGO": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "站点favicon": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统访问地址": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入系统访问地址": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备案信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入备案信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "版权信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入版权信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统版本号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "请填写备案信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请填写版权信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请填写系统访问地址": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请填写系统名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图片大小不能超过 {size}!": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/task/index.vue
  "客户端自动连接": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "允许": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "不允许": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "SQL执行队列大小": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "节点预加载的数量": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务预加载的时间间隔(秒)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "异步任务超时时间(秒)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务调度缓冲区大小": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "最大转换图纸超时时间(秒)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/log/index.vue
  "清空": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "操作类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "调用接口": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "操作名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "操作时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "结果": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入接口": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要清空所有日志吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "清空成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/license/index.vue
  "版本&许可授权": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "授权码": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "可用": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "不可用": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "支持用户数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "支持节点数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "允许登录设备数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方接入": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "允许编辑设备数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "许可期限": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "授权模块": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备注：": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "2D支持的文件类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "3D支持的文件类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "2D标准支持的功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "2D高级支持的功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "2D行业支持的功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "3D标准支持的功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "3D高级支持的功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "3D行业支持的功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "授权功能列表": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "序号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "授权功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "未授权": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/storage/index.vue
  "存储根路径": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/thirdParty/index.vue
  "添加三方": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重置令牌": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "设置过期时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "设置权限": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方令牌": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方标识": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "过期时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方状态": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "设置令牌过期时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方token": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件校验地址": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "向浩辰推送文件接口": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "获取外部参照文件地址": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "安全级别": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "无认证": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "弱认证": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "强认证": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "开": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "关": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入三方标识": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方标识: 2~128个字符，只能使用字母、数字、下划线，需要以字母开头": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入过期时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入三方名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "标识": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "令牌": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "令牌过期时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要{action}{name}令牌吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方调用未获得授权": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{action}三方成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "修改三方": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建三方": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要重置{name}令牌吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重置成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "设置成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方账户": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/ChangePasswordDialog.vue
  "修改用户密码": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "旧密码": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "确认密码": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "新密码俩次输入的不一致，请重新输入": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/BatchResultDialog.vue
  "等待中": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/SnapshotDialog.vue
  "上传者": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "当前版本": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{name}快照": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/upload/UploadFile.vue

  // src/components/upload/UploadProgress.vue
  "大小": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "待上传": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/CreateComForm.vue
  "添加": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{action}{title}": "{action}{title}",

  // src/components/UserBasicInfo.vue
  "用户基本信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户头像": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "所属权限组": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "头像设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "左旋转": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "右旋转": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图片大于2M，请进行裁剪或重新选择": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "手机号格式不正确": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "邮箱格式不正确": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "修改用户信息成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/EditPermission.vue
  "该文件{editor}正在编辑中，如需进入编辑，请点击继续编辑。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "注：当前文件仅支持一人编辑，点击继续编辑{editor}将终止编辑。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "当前文件正在归并中，请耐心等待...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "停止归并后{editor}编辑的部分数据将无法找回，确认停止归并并进入文件编辑页面？": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "继续编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "停止并编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "确定": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/sample/index.ts
  "一、图纸显示": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图纸显示": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "打开本地ocf": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "打开小地图": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "隐藏菜单": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "自定义菜单": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "水印": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "快速集成": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "二、功能接口": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "按钮点击事件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图层布局": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "另存、转PDF": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三、批注功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "审图、图章": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "批注操作": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "视图定位": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "四、扩展功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "叠图功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图纸对比": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "3d样例": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/hooks/useForm.ts
  "你确定要删除{name}{type}吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要{action}{name}{type}吗": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/Pagination.vue
  "共 {total} 条": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上一页": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "下一页": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{size}条/页": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/AsyncTaskList.vue
  "暂无数据": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/TransferLayout.vue
  "任务列表": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传中（{finished}/{total}）": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "转换任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "转换任务（成功: {success} 失败: {failed} 进行中: {running}）": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/components/GList.vue
  "请选择类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/utils/index.ts
  "已连接": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "未连接": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "未绑定": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "每周{week}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "每年第{month}月的": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "每月": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{day}号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "每天": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "执行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/error-page/
  "您当前无权限访问...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "该页面不存在...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "出错啦!": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请检查您输入的URL是否正确，或单击下面的按钮返回主页": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "返回首页": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/install/index.vue
  "浩辰CAD企业版初始化面板": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统新装": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "服务端HTTP协议": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "https公钥": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "https私钥": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "服务端监听端口": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "数据库地址": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "数据库端口": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "数据库文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "数据表前缀": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "数据库账号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件路径为安装主机上的路径，不是操作主机路径": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图片大小不能超过{size}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统安装中...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "安装成功,点击重启系统后生效,预计5秒左右": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/database/index.vue

  // src/utils/const.ts
  "浩辰图纸管理系统": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "前端任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "普通后台任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统后台任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统后台定时任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "已就绪": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "执行中": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "已完成": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "部分完成": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "已失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "已取消": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "最低": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "低": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "略低": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "略高": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "高": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "最高": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "简体中文": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "视口相关": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图层提取": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "爆炸": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "剖切": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "结构树": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/App.vue
  "服务器尚未完成引导安装": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/index.vue
  "参数缺失 app": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/router/route.ts
  "样例SDK": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/auth/index.vue
  "用户权限设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户组权限设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/components/permissionView.vue
  "编辑权限": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入搜索内容": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{title}已存在": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/file/auth.vue
  "限制编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "限制编辑、限制查看": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "查看、编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "账户类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "账户名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "授权类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "权限": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "授权时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件权限": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/application/index.vue
  "正在安装中,请耐心等待...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "安装应用": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "沙盒": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "开发者": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "批量卸载": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "应用名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "应用类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "应用沙盒": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "应用详情": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件检验中...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "计算sha1 {name} (size:{size}) {random}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "应用已安装": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传应用中...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件检验进度：{progress}%": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "应用上传完成，正在安装中，请耐心等待": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传进度：{progress}%": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要卸载{name}应用吗?": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/instance/index.vue
  "添加节点": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "节点": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "节点ID": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "最大并发数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "标签(tags)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入节点ID": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "节点ID为2~128个字符，只能使用字母、数字、下划线，需要以字母开头": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "机器状态": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "并发数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "历史任务数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "首次连接时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "最后连接时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "节点tag标签": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{action}节点成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "客户端秘钥文件下载": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "节点客户端下载": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "检测": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "绑定": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/task/index.vue
  "重新执行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "运行节点": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "所属用户": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要清空所有任务吗?": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要取消{id}任务吗?": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重新执行成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/taskScheduler/index.vue
  "新增定时任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "重复周期": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "最后一次执行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "下次执行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "平均执行时间（秒）": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请选择任务类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要{title}该定时任务吗?": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "每月{date}号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "一": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "二": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "四": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "五": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "六": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "日": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/taskType/index.vue
  "添加任务类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请选择任务类别": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请选择沙盒": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "不设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "按天": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "按星期": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "按月": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "每星期{week}执行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "每{month}月{day}号执行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "更新{name}定时任务成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "不执行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务类型详情": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/fbackup/index.vue
  "是否启用": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备份文件名(范例)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备份文件前缀": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备份文件日期样式": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备份路径": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备份策略": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备份开始时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备份内容": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统备份恢复": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "恢复": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入备份文件路径": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统备份恢复中...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "恢复完成": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/cloudCADConf/index.vue
  "编辑类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "单人编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "多人编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "编辑类型的修改，需要重启服务后生效！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "保存类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "保存方式": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "增量个数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "最大值3600": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "(单位：s)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "(单位：个)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "编辑页清除数据倒计时": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请填选择编辑类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请填写保存周期": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请选择保存类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "Tips:": "Tips:",

  // src/views/files/file/index.vue
  "支持上传文件、上传文件夹；": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传文件类型包括：（注意这里的类型要对应文件格式的类型）等2D、3D格式": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/files/font/index.vue
  "支持上传字体文件": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传字体格式包括：ttf,ttc,otf,shx": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传字体": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "字体文件名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "字体类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "字体名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "字体文件格式只包括(ttf,ttc,otf,shx)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "这{count}个字体": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{name}字体": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要删除{text}吗?": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/sdk/ShowCode.vue
  "刷新": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "运行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/openDWGConf/index.vue
  "水印工具": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "水印文本": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入水印文本": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "显示图纸小地图": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图章": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图章宽度(px)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入图章宽度": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "图章高度(px)": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请输入图章高度": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请上传图章": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "最多只能上传10个图章": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/authorizationRecords/index.vue
  "允许编辑设备数量：{allowCount}，当前已编辑设备数量：{currentCount}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "关键字": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "开始编辑时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "历史数据": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "设备号": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建未使用": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "退出编辑时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "浏览器信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "踢出": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "历史登录记录": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "关 闭": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "您当前正在对设备号：{browserId} 进行踢出操作，踢出后该设备将立即关闭并退出云原生编辑页面。确认继续进行该操作？": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "当前设备已结束编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/task/detail.vue
  "任务详情": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "日志标准输出": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "日志错误输出": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务参数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "导出": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务暂无输出": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "获取失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "日志获取/解析错误：{error}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{id}任务{type}日志": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/components/createUser.vue
  "{action}用户": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户名2~128个字符，只能使用字母、数字、下划线，需要以字母开头": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "密码格式为6~32任意字符": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/CloudCAD/index.ts
  "获取文件信息失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "获取文件信息失败, 请返回文件列表重试！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "连接断开，重连中...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "加锁失败{error}, 请返回文件列表重试！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "连接失败{msg}, 请返回文件列表重试！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "服务器已断开联接，修改的数据已保存成草稿，可以重新进入编辑继续之前工作。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "退出中...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "数据保存中，请勿关闭程序": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "服务器已断开联接，自动保存数据失败，请重试保存或退出编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "连接失败, 请返回文件列表重试！ {msg}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "获取云CAD配置失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "cloudCAD init error!": "cloudCAD init error!",
  "命令执行中！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "保存版本成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "保存版本失败！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "保存增量异常！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建历史版本失败 {error}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "保存失败！{errorMsg}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "协同连接失败！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "单人编辑初始化失败！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "退出编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "当前页面长时间没有操作，暂停页面编辑。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请稍候...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "编辑加锁成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "socket连接/加锁失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "点击了编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "单人编辑cloudPath": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "cloudPath 从url parse失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "编辑-所有参数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "初始化失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "拼接请求": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/common/utils.ts
  "此页面无法被自动关闭，请手动关闭浏览器标签页。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/CloudCAD/initCad.ts
  "开图失败，请刷新页面重试！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "提醒（您已无法编辑文件）": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件编辑权限已转由用户{editor}获取，如需编辑请前往文件列表页重新进入编辑。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "上传增量失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "获取历史版本列表失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建历史版本失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "删除历史版本失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "下载文件失败: ": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "下载文件失败!": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "获取下拉文件列表失败：": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "获取下拉文件列表失败!": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "当前无法编辑，如需编辑请前往文件列表页重新进入编辑。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "您已被管理员强制退出云原生编辑页面，如有问题请联系管理员。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "身份信息失效，请关闭重试！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "您已无法编辑文件！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "关闭": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/CloudCAD/cooperate.ts
  "初始化失败，请稍后再试！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "在{position}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "执行了 {command} 命令": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "增量合并失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "增量上传成功，但没有recordId": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/CloudCAD/single.ts
  "{senderName} 要结束你的编辑，是否退出编辑。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "否({time})": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{senderName} 已剥夺您的编辑权限，您被迫退出编辑页面": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "无法编辑文件！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "正在保存您的编辑，请稍候...": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/Preview2d/index.ts
  "参数异常：vendorcode={vendorcode}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "参数异常：element={element}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "参数异常：param.fileId={fileId} 或 param.fileDownLoadUrl={fileDownLoadUrl}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "获取资源失败/插件不存在": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "获取资源失败!": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "切换布局信息错误！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/Preview3d/index.ts
  "无storageId:{storageId} {type}: {resourceName}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "分片index": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "brep storageId": "brep storageId",
  "转换任务失败！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "获取任务状态异常：{taskInfo}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "无资源信息:{data}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "发起转换任务错误：{msg} res: {res}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "其他错误": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/Preview3d/notes.ts
  "操作失败: {error}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "服务端返回失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "网络连接异常，请稍后重试": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{action}批注 操作失败: {error}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/app/Preview2d/usePreview2d.ts
  "缺少外部参照物：{refs}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "建议您以上传文件夹的方式上传图纸及其外部参照，或将外部参照上传至云图同目录下再重新开图。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "事件通知任务失败！事件名：{ename}  数据：{data}": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "批注元信息无资源": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "书签息无资源": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "书签为空": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // ElMessageBox 和 ElMessage 弹框国际化
  "token已失效，请重新登录": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "你确定要清空所有日志吗?": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "您确定将图纸版本恢复到此版本吗？": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件在线编辑功能未获得授权": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "已达到当前时段最大用户链接数": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/utils/CloudCadUtils 国际化
  "当前可以进入编辑页，请点击 确定 按钮继续": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "该图有正在执行的命令，是否要强制中断操作并保存？请注意，中断后会丢弃部分修改": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "强制中断": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "该图正在被抢占编辑权限，是否强制抢占编辑权限？注意强制抢占权限存在编辑者保存内容丢失的风险！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "强制抢占": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{editName} 正在编辑，是否要抢占编辑权限？": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "结束编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "对方长时间未响应，是否强制抢占编辑权限？": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{editName} 用户命令正在执行中，无法被抢占": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "当前图纸正在被其他用户编辑中，如需进入编辑，请点击继续编辑。此操作将导致其他用户退出编辑状态。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{editName} 拒绝了您的抢占请求，是否强制抢占编辑权限？注意强制抢占权限存在编辑者保存内容丢失的风险！": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "正在等待 {editName} 响应，剩余 ": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "强制编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "继续等待": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "对方长时间未响应，是否强制进入编辑状态或继续等待？": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{editName} 正在对他的修改进行保存，请等待 ": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "进入编辑中，请稍等......": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/webSdkCertificate/index.vue
  "文件名：": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "点击上传": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "证书信息": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "允许域名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "证书导入时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/system/thirdPartyLog/index.vue
  "调用名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",


  // src/views/services/taskType/index.vue
  "执行时间": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/taskScheduler/taskDrawer.vue
  "定时任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "号执行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "数据过期时间（天）": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "修改定时任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建定时任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/taskScheduler/recordDialog.vue
  "计划任务执行记录": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "耗时（秒）": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请选择状态": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/instance/checkNodeDialog.vue
  "节点检测": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "开始检测": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "提交任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务已提交，进入任务队列": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务执行": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务执行中": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "完成": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "成功或失败": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "错误": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/services/instance/bindNodeDialog.vue
  "节点绑定": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "指纹": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "绑定成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/files/file/SetRightDialog.vue
  "需要限制谁能查看这个？": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "有当前文件（夹）查看和编辑权限的所有人可以编辑本文件（夹）。使用上面的下拉框来选择限制给某些人查看或者编辑，注意查看权限会继承给本文件夹的子页面，编辑权限不继承。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "输入一个用户名或者组名": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "所有人可以查看和编辑该页面": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "所有人可以查看,特定的人可以编辑": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "只有特定的人可以查看或者编辑页面": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "{name}添加重复": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "权限设置成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请添加权限人员": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/files/file/HistoryDialog.vue
  "{name}文件历史版本": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "缩略图": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/files/file/CreateFileDialog.vue
  "请输入模板名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "预览模板": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "请选择模板": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建文件成功": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/components/groupTree.vue
  "请输入关键字查找": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户组详情": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/components/createGroup.vue
  "父级用户组": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "编号1~32个字符, 只能使用字母、数字、下划线，需要以字母开头": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/views/customer/components/AddUserToGroupDialog.vue
  "所有用户": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "选中用户": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "{selected} / {total}": "{selected} / {total}",

  // src/utils/http-message.ts
  "网络超时": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "网络连接错误": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "未知错误": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // src/utils/index.ts
  "输入": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "文件编辑权限已转由用户{err?.data?.editor}获取，如需编辑请前往文件列表页重新进入编辑。": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  // menu
  "文件管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "模板管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "字体管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "材质管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "零件管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "回收站": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "应用管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "实例(节点)管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务类型管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "计划任务": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "用户管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户组管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "权限策略": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "文件权限管理": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",

  "系统设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "数据库设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "存储设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备份与恢复": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "编辑授权使用记录": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "WEB SDK证书导入": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统操作日志": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "三方访问日志": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "系统安装": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "2D 览图设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "云原生设置": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "授权许可证": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "节点名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "标签": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "详情": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "卸载": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "节点详情": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "返回": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "备注": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "修改": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "创建": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "任务类型": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "类别": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "功能": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "用户名称": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
  "优先级": "fhdaskjfh   dafdasf dasfjdslafj dasfdsalfjdsf",
};
