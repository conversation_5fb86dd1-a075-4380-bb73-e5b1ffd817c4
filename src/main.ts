// 注册 icon sprite
import "virtual:svg-icons-register";

import { createApp } from "vue";

import App from "./App.vue";
import router, { resetRouter } from "./router";
import { loadDirectives } from "@/directives";
import { setupI18n } from "@/locales";

// 引入全局样式
import "element-plus/dist/index.css";
import "vue-cropper/dist/index.css";
import 'vue3-toastify/dist/index.css';

import "./styles/index.scss";
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import "./styles/tailwind.css";
import installIcon from "./components/icon";
import pinia from "./stores";

import "element-plus/theme-chalk/el-message.css";
import "element-plus/theme-chalk/el-message-box.css";

import { getGlobalConfig } from "@/config";
import Vue3Toastify, { type ToastContainerOptions } from 'vue3-toastify';

// 输出webclient版本
declare const __CLIENT_VERSION__: string;
try {
  const { log } = console
  log('CLIENT_VERSION:', __CLIENT_VERSION__)
} catch(e) { /* empty */ }

// polyfill WeakRef
if (!window.WeakRef) {
  (window as any).WeakRef = function (a:any) {
    return {
      deref: function() {
        return a
      }
    }
  }
}

const app = createApp(App);

getGlobalConfig().then((config) => {
  // 重置路由的 publicPath
  console.log('publicPath', config?.publicPath)
  if (config?.publicPath) resetRouter(config?.publicPath)
  app.use(installIcon);
  app.use(pinia);
  app.use(router);
  app.use(Vue3Toastify, {
    autoClose: 30000000,
    pauseOnFocusLoss: true,
  } as ToastContainerOptions);

  /** 加载自定义指令 */
  loadDirectives(app);
  /** 加载国际化 */
  setupI18n(app);

  app.mount("#app");
});