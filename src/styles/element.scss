.el-menu--horizontal > .el-menu-item.is-active {
    border-bottom: 0px !important;
    background-color: var(--app-color-menu-li-active) !important;
}
.el-menu--horizontal > .el-sub-menu.is-active .el-sub-menu__title {
    border-bottom: 0px !important;
}
.el-menu--horizontal > .el-menu-item {
    border-bottom: 0px !important;
}
.el-menu--horizontal .el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
    color: #fff !important;
    background-color: var(--app-color-menu-li-active) !important;
}
.el-sub-menu__icon-more {
    color: #fff !important;
}
.el-menu--horizontal > .el-sub-menu .el-sub-menu__title:hover {
    background-color: inherit !important;
}

.el-table__header th {
    background-color: #f3f5fc !important;
}

.el-descriptions__body .el-descriptions__table .el-descriptions__cell.is-left {
    white-space: nowrap !important;
    // max-width: 150px;
    // overflow: hidden;
    // text-overflow: ellipsis;
}

.el-message-box {
    position: relative;
    top: -25%;
}
.el-drawer__body {
    border-top: 1px solid;
    border-color: #e3e4e6;
}
.el-drawer__footer {
    border-top: 1px solid;
    border-color: #e3e4e6;
    padding-top: 20px !important;
}

.el-button[aria-disabled="false"]:hover {
    color: var(--el-button-hover-text-color);
    border-color: var(--el-button-hover-border-color);
    background-color: var(--el-button-hover-bg-color) !important;
}

.el-button.el-button--danger:focus {
    background-color: var(--el-color-danger);
}
.el-button.el-button--primary:focus {
    background-color: #0052cc;
}
.el-button.el-button--primary.el-button--default:focus {
    background-color: #0052cc;
}
.el-button.el-button--default:focus {
    background-color: #fff;
}
.el-button:focus {
    background-color: transparent;
}
.el-popper.is-dark {
    max-width: 500px;
}
.el-transfer-panel {
    width: 300px !important;
}
.el-transfer-panel__body {
    // height: 400px !important;
    // height: calc(100% - 40px) !important;
}
.el-transfer-panel {
    // height: 100%;
}
.el-transfer-panel__filter {
    width: 250px !important;
}

.el-dialog__body {
    // max-height: 65vh;
    // height: 460px !important;
}

.upload-table{
    &.el-table .cell{
        padding: 0 12px 0 20px;
      }
  }