// 导入变量

@import "./var.scss";
@import "./element.scss";
@import "./tailwind.css";
@import "./transition.scss";

// 全局样式

article,
aside,
blockquote,
body,
button,
dd,
details,
div,
dl,
dt,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
hr,
input,
legend,
li,
menu,
nav,
ol,
p,
section,
td,
textarea,
th,
ul {
    margin: 0;
    padding: 0;
}

*,
:after,
:before {
    box-sizing: border-box;
}


#GStarSDK-pc-toolsbar-float-notes {
    box-sizing: content-box!important;
  }

ul,
li {
    list-style: none;
}

html,
body,
#app {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
        Microsoft YaHei, SimSun, sans-serif;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -webkit-tap-highlight-color: transparent;
    background-color: var(--app-color-body-bg);
    font-size: var(--el-font-size-base);
    color: var(--el-text-color-primary);
}

.app-wrapper,
.common-view {
    width: 100%;
    height: 100%;
}

// 布局 - 页面中带有搜索栏时
.layout-vertical,
.layout-block {
    min-height: calc(
        100vh - var(--app-height-header) - var(--app-padding-min) -
            var(--app-margin-min) * 2 - 36px - 28px
    );
}
.layout-vertical {
    display: flex;
    flex-flow: column;
    & > div {
        padding: var(--app-padding-mid);
        background: #fff;
        margin-bottom: var(--app-margin-min);
    }
    .layout-vertical-filter {
        display: flex;
        .filter-btns {
            display: flex;
            width: 160px;
        }
        .filter-options {
            flex: 1;
            margin-bottom: -18px;
        }
        .el-form-item__label {
            width: 100px;
        }
        // .el-form-item__content {
        //   width: 210px;
        // }

        @media screen and (max-width: 1470px) {
            .el-form-item__label {
                // width: 120px;
            }
            .el-form--inline .el-form-item {
                margin-right: 12px;
            }
            // .el-form-item__content {
            //   width: 180px;
            // }
        }
        @media screen and (max-width: 1390px) {
            .el-form-item__label {
                width: 90px;
            }
            .el-form--inline .el-form-item {
                // margin-right: 0;
            }
            // .el-form-item__content {
            //   width: 160px;
            // }
        }
    }
    .layout-vertical-content {
        flex: 1;
    }
}
.layout-block {
    padding: var(--app-padding-mid);
    background: #fff;
}

.layout-action {
    display: flex;
    align-items: center;
    span {
        color: #409eff;
        font-weight: 600;
        cursor: pointer;
        &:not(:first-child) {
            margin-left: 12px;
        }
    }
}

// 状态显示 需要统一规范还是各种设置
.status {
    padding-left: 12px;
    padding-right: 12px;
    display: inline-block;
    padding-top: 2px;
    padding-bottom: 2px;
    border-radius: 4px;
}
.status-0 {
    background-color: #f0f9ec;
    color: #67c23a;
}
.status-1 {
    background-color: #fef1f1;
    color: #f56c6c;
}
.status-2 {
    background-color: #f4f4f5;
    color: #606266;
}
.status-3 {
    background-color: #f3ffff;
    color: #00c5d6;
}
.status-4 {
    background-color: #fffaf4;
    color: #f59a23;
}
.status-5 {
    background-color: #ebedf0;
    color: #606266;
}

// 文本样式工具类 - 国际化文本处理
.text-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.text-ellipsis-multiline {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}

// 按钮文本样式
.btn-text {
    @extend .text-ellipsis;
    max-width: 120px;
    display: inline-block;
}

// 表单标签文本样式
.form-label-text {
    @extend .text-ellipsis;
    max-width: 180px; // 增加最大宽度以适应国际化长标签
    display: inline-block;
}

// 表格单元格文本样式
.table-cell-text {
    @extend .text-ellipsis;
    width: 100%;
}

.table-cell-filename {
    @extend .text-ellipsis;
    max-width: 300px;
}

// 文件块视图文本样式
.file-block-name {
    @extend .text-ellipsis;
    max-width: 84%;
}

.file-block-info {
    @extend .text-ellipsis;
    max-width: 100%;
}

// 菜单项文本样式
.menu-item-text {
    @extend .text-ellipsis;
    max-width: 150px;
    display: inline-block;
}

// 带工具提示的文本容器
.text-with-tooltip {
    cursor: help;

    &:hover {
        color: var(--app-color-primary);
    }
}

// 统一的布局间距系统
:root {
    --layout-gap-row: 12px;    // 统一的行间距
    --layout-gap-col: 12px;    // 统一的列间距
    --layout-gap-small: 8px;   // 小间距
    --layout-gap-large: 16px;  // 大间距
}

// 统一的flex布局间距
.layout-flex {
    display: flex;
    gap: var(--layout-gap-row) var(--layout-gap-col);

    &.flex-wrap {
        flex-wrap: wrap;
    }

    &.items-center {
        align-items: center;
    }

    &.justify-between {
        justify-content: space-between;
    }

    &.justify-center {
        justify-content: center;
    }

    &.gap-small {
        gap: var(--layout-gap-small);
    }

    &.gap-large {
        gap: var(--layout-gap-large);
    }
}

// 兼容现有的flex类，统一间距
.flex {
    &.flex-wrap {
        gap: var(--layout-gap-row) var(--layout-gap-col);
    }
}

.items-center {
    &.flex-wrap {
        gap: var(--layout-gap-row) var(--layout-gap-col);
    }
}

.justify-between {
    &.flex-wrap {
        gap: var(--layout-gap-row) var(--layout-gap-col);
    }
}

// 表单布局间距统一
.el-form {
    &.el-form--inline {
        .el-form-item {
            margin-right: var(--layout-gap-col);
            margin-bottom: var(--layout-gap-row);
        }
    }

    // 表单标签样式优化 - 支持国际化长标签
    .el-form-item__label {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        // 为不同场景提供不同的标签宽度
        &.label-small {
            width: 80px !important;
        }

        &.label-medium {
            width: 120px !important;
        }

        &.label-large {
            width: 160px !important;
        }

        &.label-xlarge {
            width: 200px !important;
        }
    }
}

// 按钮组间距统一
.button-group {
    display: flex;
    gap: var(--layout-gap-small);
    flex-wrap: wrap;

    .el-button {
        margin: 0; // 重置Element Plus默认margin
    }
}

// 上传按钮组间距 - 现在由父容器的button-group统一管理
.upload-button {
    // 移除margin，由父容器的gap统一管理间距
    margin: 0;
}

// 操作按钮文本样式
.action-text {
    display: inline-block;
    max-width: 80px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;

    &:hover {
        color: var(--app-color-primary);
    }
}
.status-6 {
    background-color: #409eff;
    color: #fff;
}

.drawer-title {
    color: #333;
    font-size: 16px;
    font-weight: 600;
}
