<template>
  <div class="app-wrapper" @click="clearAll">
    <el-config-provider :locale="currentLocale">
      <router-view></router-view>
    </el-config-provider>
    <Description></Description>
    <UserBasicInfo></UserBasicInfo>
    <Preview></Preview>
    <TransferLayout></TransferLayout>
    <ChangePasswordDialogVue></ChangePasswordDialogVue>
    <!-- 编辑权限 -->
    <EditPermission />
    <BatchResultDialog></BatchResultDialog>
  </div>
</template>
<script setup lang="ts">
import { onBeforeMount, onUnmounted, onMounted } from 'vue'
import TransferLayout from "@/components/TransferLayout.vue";
import Description from "@/components/Description";
import UserBasicInfo from "@/components/UserBasicInfo.vue";
import Preview from "@/components/preview/index.vue";
import ChangePasswordDialogVue from '@/components/ChangePasswordDialog.vue'
import EditPermission from "@/components/EditPermission.vue";
import { RouterView, useRouter } from 'vue-router'
import { computed } from 'vue'
import { useLocaleStoreWithOut } from "@/stores/locale";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import { useUserStore } from "@/stores/user";
import globalEventSocket from './globalEvent/server';
import globalEventLocal from './globalEvent/local';
import systemEventSocket from './globalEvent/server/system';
import { emitter } from './utils/mitt';
import { changeFavicon, checkIsInstall } from './utils';
import { $t } from '@/locales';
import Fingerprint2 from 'fingerprintjs2';

const $router = useRouter();
const key = computed(() => $router.currentRoute.value.path)
const localeStore = useLocaleStoreWithOut();
const currentLocale = computed(() => { return localeStore.getLocale === "zh_CN" ? zhCn : zhCn })

// 订阅全局socket事件
globalEventSocket.sub()
// 订阅全局local事件
globalEventLocal.sub()
// 订阅system事件
systemEventSocket.sub()
// 新进页面事件
emitter.emit('enterPage')

onBeforeMount(async () => {
  // 校验是否安装
  const isInstall = await checkIsInstall()
  await $router.isReady()
  if (!isInstall) {
    if ($router.currentRoute.value.name !== 'install') ElMessage.error($t("服务器尚未完成引导安装"));
    const userStores = useUserStore()
    userStores.reset()
    $router.push('/install');
  }
  // 动态修改favicon
  changeFavicon()
})

onMounted(() => {
  // 您不应在页面加载时或加载后直接运行指纹。 而是使用setTimeout或requestIdleCallback将其延迟几毫秒，以确保指纹一致。
  if ((window as any).requestIdleCallback) {
      requestIdleCallback(() => {
        createFingerprint();
      });
    } else {
      setTimeout(() => {
        createFingerprint();
      }, 500);
    }
})

onUnmounted(() => {
  // 取消全局socket事件
  globalEventSocket.unsub()
  // 取消订阅全局local事件
  globalEventLocal.unsub()

  systemEventSocket.unsub()
})

// 取消所有选中和收起下拉列表
const clearAll = () => {
  emitter.emit('clearAll', "default")
}

 // 创建浏览器指纹
const createFingerprint = () => {
  // 浏览器指纹
  Fingerprint2.get((components: any[]) => { // 参数只有回调函数时，默认浏览器指纹依据所有配置信息进行生成
    const values = components.map(component => component.value); // 配置的值的数组
    const murmur = Fingerprint2.x64hash128(values.join(''), 31); // 生成浏览器指纹
    localStorage.setItem('browserId', murmur); // 存储浏览器指纹，在项目中用于校验用户身份和埋点
  })
}
</script>
<style scoped></style>
