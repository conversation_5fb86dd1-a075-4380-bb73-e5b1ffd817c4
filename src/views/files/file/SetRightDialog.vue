
<template>
  <el-dialog v-model="rightFlag" :title="$t('权限设置')" width="600px" :modal="false" :close-on-click-modal="false"
    :before-close="handleClose">
    <div v-loading="dialogLoading" class="dialog-body">
      <div>
        <el-select v-model="editRightVal" @change="getSelectRightVal" placeholder="Select">
          <el-option class="right-select" v-for="item in rightList" :key="item.value" :label="item.label"
            :value="item.value">
            {{ item.label }}
            <p>{{ item.tip }}</p>
          </el-option>
        </el-select>

        <span class="now-right-tip">{{ nowRightTip }}</span>
      </div>

      <div class="right-content" v-if="editRightVal == 1">
        <div class="right-content-text">
          <p>{{ $t('需要限制谁能查看这个？') }}</p>
          <p>{{ $t('有当前文件（夹）查看和编辑权限的所有人可以编辑本文件（夹）。使用上面的下拉框来选择限制给某些人查看或者编辑，注意查看权限会继承给本文件夹的子页面，编辑权限不继承。') }}</p>
        </div>
      </div>

      <div class="edit-right" v-if="editRightVal == 2">
        <el-select v-model="rightRange" multiple filterable remote reserve-keyword :placeholder="$t('输入一个用户名或者组名')"
          :remote-method="remoteMethod" :loading="loading">
          <el-option v-for="item in rightRangeList" :key="item.value" :label="item.identityName" :value="item">
            <div class="flex items-center">{{ item.identityName }} <SvgIcon
                :name="item.identityType === 1 ? 'user' : 'group'" :size="18" class="ml-2"></SvgIcon>
            </div>
          </el-option>
        </el-select>

        <span class="right-margin">{{ $t('可以编辑和查看') }}</span>

        <el-button class="right-margin" @click="() => addSetRightTable(3)">{{ $t('添加') }}</el-button>

        <el-table border :data="setRightTable" :show-header="false" stripe style="width: 100%; margin-top: 15px;"
          v-loading="getFileRightUserLoading">
          <el-table-column prop="identityName">
            <template #default="scope">
              <div class="flex items-center">{{ scope.row.identityName }} <SvgIcon
                  :name="scope.row.identityType === 1 ? 'user' : 'group'" :size="18" class="ml-2"></SvgIcon>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="authority">
            <template #default="scope">
              <span>{{
                mapPeopleRightName(scope.row.authority) }}</span>
            </template>
          </el-table-column>
          <el-table-column width="60">
            <template #default="scope">
              <span class="action-button"
                v-if="scope.row.identityId && !(scope.row.identityId === userId && scope.row.identityType === 1)"
                @click="deleteRight(scope.row.identityId)">{{ $t('删除') }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="edit-right" v-if="editRightVal == 3">
        <el-select v-model="rightRange" multiple filterable remote reserve-keyword :placeholder="$t('输入一个用户名或者组名')"
          :remote-method="remoteMethod" :loading="loading">
          <el-option v-for="item in rightRangeList" :key="item.value" :label="item.identityName" :value="item">
            <div class="flex items-center">{{ item.identityName }} <SvgIcon
                :name="item.identityType === 1 ? 'user' : 'group'" :size="18" class="ml-2"></SvgIcon>
            </div>
          </el-option>
        </el-select>

        <el-select class="right-margin" v-model="currentRight" :placeholder="$t('请选择')">
          <el-option v-for="item in peopleRightList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-button class="right-margin" @click="() => addSetRightTable(currentRight)">{{ $t('添加') }}</el-button>

        <el-table :data="setRightTable" :show-header="false" border stripe style="width: 100%; margin-top: 15px;"
          v-loading="getFileRightUserLoading">
          <el-table-column prop="identityName">
            <template #default="scope">
              <div class="flex items-center">{{ scope.row.identityName }} <SvgIcon
                  :name="scope.row.identityType === 1 ? 'user' : 'group'" :size="18" class="ml-2"></SvgIcon>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="authority">
            <template #default="scope">
              <span v-if="scope.row.identityId === userId && scope.row.identityType === 1">{{
                mapPeopleRightName(scope.row.authority) }}</span>
              <el-select class="right-margin" v-model="scope.row.authority" v-else-if="scope.row.identityId"
                :placeholder="$t('请选择')">
                <el-option v-for="item in peopleRightList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <!-- <span v-else-if="scope.row.identityId">{{ mapPeopleRightName(scope.row.authority) }}</span> -->
              <span v-else>{{ $t('无权限') }}</span>
            </template>
          </el-table-column>
          <el-table-column width="60">
            <template #default="scope">
              <span class="action-button"
                v-if="scope.row.identityId && !(scope.row.identityId === userId && scope.row.identityType === 1)"
                @click="deleteRight(scope.row.identityId)">{{ $t('删除') }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button :disabled="dialogLoading" @click="handleClose">{{ $t('取消') }}</el-button>
        <el-button type="primary" :disabled="dialogLoading" @click="handelConfirm">
          {{ $t('应用') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, withDefaults, watchEffect, computed, defineEmits } from 'vue'
import {
  searchGroupPeople,
  getFileRightList,
  postFileRight
} from '@/services/file'
import type { SetFileRightItem } from '@/model/file'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia';
import { $t } from '@/locales'

const userStores = useUserStore()

const { user_name, id, email, phone } = storeToRefs(userStores);

const userId = computed(() => id.value)

const userName = computed(() => {
  return user_name.value || phone.value || email.value || ''
})
// 定义props的
interface Props {
  setRightId: number
  rightFlag: boolean
  defaultRightValue: number
}

const props = withDefaults(defineProps<Props>(), {
  setRightId: 0,
  rightFlag: false,
  defaultRightValue: 1
})

// 权限确定和取消事件抛出
const emit = defineEmits(['setTightConfirm', 'setTightClose'])

// 定义选择种类之后加载当前权限列表的loading
const loading = ref(false)

// 定义整个弹窗的loading
const dialogLoading = ref(false)

// 定义权限弹窗开关
const rightFlag = computed(() => props.rightFlag)

const defaultRightValue = computed(() => props.defaultRightValue)

const setRightId = computed(() => props.setRightId)
// 更改权限值
const editRightVal = ref(1)

// 远程搜索选中的权限人或者群组
const rightRange = ref([])

// 权限类型列表
const rightList = [
  {
    label: $t('无限制'),
    value: 1,
    tip: $t('所有人可以查看和编辑该页面')
  },
  {
    label: $t('限制编辑'),
    value: 2,
    tip: $t('所有人可以查看,特定的人可以编辑')
  },
  {
    label: $t('限制编辑和查看'),
    value: 3,
    tip: $t('只有特定的人可以查看或者编辑页面')
  },
]

// 当前权限对应的提示文本
const nowRightTip = ref(rightList[0].tip)

interface RangeItemType {
  identityName: string
  identityId: string
  identityType: number
  value: string
  authority?: number
}
// 人员列表
const rightRangeList = ref<RangeItemType[]>([])

// 文件的权限类型
const peopleRightList = [
  {
    label: $t('可以查看'),
    value: 1
  },
  // {
  //   label: '可以编辑',
  //   value: 2,
  //   hide: true
  // },
  {
    label: $t('可以编辑和查看'),
    value: 3
  }
]

const mapPeopleRightName = (value: number) => {
  return peopleRightList.filter(v => v.value === value)[0]?.label
}

// 获取权限loading
const getFileRightUserLoading = ref(false)

// 表格中的权限数据 
const setRightTable = ref<RangeItemType[]>([])

// 权限设置-点击添加 把选中的人或者组添加到下方列表
const addSetRightTable = (currentRight?: number) => {
  if (rightRange.value.length) {
    let flag = false
    rightRange.value.forEach((v: RangeItemType) => {
      v.authority = currentRight || editRightVal.value

      flag = setRightTable.value.some((m: RangeItemType) => m && m.identityId === v.identityId)
      if (flag) {
        ElMessage({
          type: 'error',
          message: $t('{name}添加重复', { name: v.identityName }),
        })
      } else {
        setRightTable.value = setRightTable.value.concat([v])
      }
    })
    rightRange.value = []
  }

}

// 选择分类后 默认人员的权限
const currentRight = ref(3)

// 远程搜索组件触发查询
const remoteMethod = (val: string) => {
  searchUserGroup(val)
}

// 搜索用户组或者用户
const searchUserGroup = async (keyword: string) => {
  try {
    const res = await searchGroupPeople({ keyword })
    if (res.code === 0) {
      rightRangeList.value = res.data.searchList
    }

  } catch (error) {
    console.log(error)
  }
}

// 删除权限列表
const deleteRight = (identityId: string) => {
  const arr = setRightTable.value.filter((v: RangeItemType) => v.identityId !== identityId)

  setRightTable.value = arr
}

// 选择顶部类别获取最新文件权限列表
const getSelectRightVal = async (val: string) => {
  setRightTable.value = []
  nowRightTip.value = rightList.filter(v => v.value === parseInt(val))[0]?.tip
  try {
    getFileRightUserLoading.value = true
    const res = await getFileRightList({ fileId: setRightId.value, filePermType: editRightVal.value })
    getFileRightUserLoading.value = false
    if (res.code === 0) {
      setRightTable.value = setRightTable.value.concat(res.data.authorityList)
      if (setRightTable.value.some(m => m?.identityId === userId.value)) {
        const everyBody = {
          identityName: $t('每个人'),
          identityId: '',
          authority: 1,
          identityType: 2,
          fileId: setRightId.value,
          value: ''
        }
        setRightTable.value.unshift(everyBody)
      } else {
        const nowUser = {
          identityName: userName.value,
          identityId: userId.value,
          authority: 3,
          identityType: 1,
          fileId: setRightId.value,
          value: ''
        }
        setRightTable.value.unshift(nowUser)
        const everyBody = {
          identityName: $t('每个人'),
          identityId: '',
          authority: 1,
          identityType: 2,
          fileId: setRightId.value,
          value: ''
        }
        setRightTable.value.unshift(everyBody)
      }
    }
  } catch (error) {
    getFileRightUserLoading.value = false
    console.log(error)
  }
}

watchEffect(() => {
  editRightVal.value = defaultRightValue.value
})

// 初次打开弹窗 要加载默认类型的权限列表
getSelectRightVal(editRightVal.value.toString())

const handleClose = () => {
  emit('setTightClose')
}

// 点击应用后提交文件权限
const handelConfirm = async () => {
  const data: SetFileRightItem[] = []

  // 当最上面的选择的值不为1时
  if (editRightVal.value !== 1) {
    setRightTable.value.forEach((v: RangeItemType) => {
      //过滤掉权限列表中默认加的 每个人和admin等
      if (v.identityId && v.identityId !== '1') {
        const obj = {
          authority: v.authority || 0,
          identityId: v.identityId,
          identityType: v.identityType
        }

        data.push(obj)
      }

    })
  }

  const query = {
    fileId: setRightId.value,
    filePermType: editRightVal.value,
    authorityList: data
  }

  if (data.length || editRightVal.value === 1) {
    try {
      dialogLoading.value = true
      const res = await postFileRight(query)
      // dialogLoading.value = false
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: $t('权限设置成功'),
        })
        emit('setTightConfirm')
      } else {
        ElMessage({
          type: 'error',
          message: res.msg,
        })
      }

    } catch (error) {
      console.log(error)
      dialogLoading.value = false
    }
  } else {
    ElMessage({
      type: 'error',
      message: $t('请添加权限人员'),
    })
  }

}

</script>


<style lang='scss' scoped>
.dialog-body {
  height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.right-select {
  height: auto !important;
  line-height: 20px;
  margin-top: 10px;

  p {
    font-size: 12px;
    line-height: 12px;
    margin-bottom: 10px;
    color: #c0c4cc;
  }
}

.right-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.right-icon {
  width: 60.84px;
  height: 100px;
  display: block;
  margin-right: 20px;
}

.now-right-tip {
  margin-left: 15px;
}

.edit-right {
  margin-top: 40px;
}

.right-margin {
  margin-left: 15px;
}

.action-button {
  cursor: pointer;
}
</style>