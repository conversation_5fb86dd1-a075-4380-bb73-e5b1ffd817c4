<template>
  <div class="user-manage layout-vertical h-full" v-event:clearAll="clearAll">
    <!-- <div>
      <TipsModel>
        <template v-slot>
          <p>{{ $t('支持上传文件、上传文件夹；') }}</p>
          <p>{{ $t('上传文件类型包括：（注意这里的类型要对应文件格式的类型）等2D、3D格式') }}</p>
        </template>
      </TipsModel>
    </div> -->
    <div class="layout-vertical-filter justify-between layout-select">
      <!-- {{ $t('搜索区') }} -->
      <el-form :inline="true" :label-width="100" label-position="left" class="el-form--inline">
        <el-form-item class="folder">
          <template #label>
            <span class="form-label-text">{{ $t('文件（夹）名') }}</span>
          </template>
          <el-input v-model="listQueryData.searchName" clearable :placeholder="$t('文件或者文件夹名')" />
        </el-form-item>
        <el-form-item>
          <template #label>
            <span class="form-label-text">{{ $t('文件格式') }}</span>
          </template>
          <el-select v-model="listQueryData.fileType" @change="getSelectVal" clearable filterable>
            <el-option v-for="item in fileTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <template #label>
            <span class="form-label-text">{{ $t('修改时间') }}</span>
          </template>
          <div class="demo-date-picker">
            <div class="block">
              <el-date-picker v-model="updateTime" type="daterange" range-separator="-" :start-placeholder="$t('开始时间')"
                :end-placeholder="$t('结束时间')" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="getTime" />
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="button-group">
        <el-button type="primary" @click="onSubmit" :title="$t('查询')">
          <span class="btn-text">{{$t('查询')}}</span>
        </el-button>
        <el-button type="primary" @click="onReset" :title="$t('重置')">
          <span class="btn-text">{{$t('重置')}}</span>
        </el-button>
      </div>
    </div>

    <div class="layout-vertical-content flex flex-col list" v-loading="loading">
      <!-- {{ $t('操作区') }} -->
      <div class="layout-flex items-center flex-wrap">
        <div v-if="enableUpload" class="button-group">
          <UploadFile class="upload-button" :key="reloadUploadFile" :toFolderId="toFolderId" :buttonType="'primary'"
            :folderId="parentId" :accept="limitTypes" :buttonTitle="$t('上传文件')" @changeFiles="changeFiles">
          </UploadFile>
          <UploadFile class="upload-button" :key="reloadUploadFile" :toFolderId="toFolderId" :buttonType="'primary'"
            :folderId="parentId" :webkitdirectory="true" :accept="limitTypes" :buttonTitle="$t('上传文件夹')"
            @changeFiles="changeFiles">
          </UploadFile>
          <el-button @click.stop="createFile" v-permission:name="{ name: '添加', routeName: 'filesFile' }" :title="$t('新建文件')">
            <span class="btn-text">{{$t('新建文件')}}</span>
          </el-button>
          <el-button @click.stop="createDir" v-permission:name="{ name: '添加', routeName: 'filesFile' }" :title="$t('新建文件夹')">
            <span class="btn-text">{{$t('新建文件夹')}}</span>
          </el-button>
        </div>
        <div class="button-group">
          <el-button :disabled="disableDelete" @click.stop="batchDelete"
            v-permission:name="{ name: '删除', routeName: 'filesFile' }" :title="$t('删除')">
            <span class="btn-text">{{$t('删除')}}</span>
          </el-button>
          <!-- <el-button v-if="multipleSelection.length">下载</el-button> -->
          <el-button :disabled="disableMove" @click.stop="batchMove"
            v-permission:name="{ name: '移动', routeName: 'filesFile' }" :title="$t('移动')">
            <span class="btn-text">{{$t('移动')}}</span>
          </el-button>
        </div>
      </div>

      <div class="layout-flex justify-between flex-wrap">
        <!-- {{ $t('面包屑') }} -->
        <Breadcrumb :breadcrumbList="crumbsList" @changeBreadcrumb="handleBreadcrumbChange">
        </Breadcrumb>
        <div class="layout-flex items-center gap-small">
          <SvgIcon name="block" :size="18" class="cursor-pointer" v-if="viewType === 'list'"
            @click="() => viewType = 'block'"></SvgIcon>
          <el-checkbox @change="handleCheckAllChange" :indeterminate="isIndeterminate" @click.stop
            v-if="viewType === 'block'" v-model="isSelectAll">{{ (isSelectAll || isIndeterminate) ?
              `${selectedRows.length} / ${tableData.length}` : $t('全选') }}</el-checkbox>
          <SvgIcon name="list" :size="18" class="cursor-pointer" v-if="viewType === 'block'"
            @click="() => viewType = 'list'"></SvgIcon>
        </div>
      </div>

      <DropZone @importFile="uploadFileHandle" class="h-full flex-1 fileDrop" :enableDrop="enableUpload">
        <!-- {{ $t('表格') }} -->
        <el-table @click.stop v-show="viewType === 'list'" ref="multipleTableRef" :data="tableData" style="width: 100%"
          stripe @sort-change="sortChange" border @selection-change="handleSelectionChange"
          :max-height="table_max_height">
          <el-table-column type="selection" width="55" align="center" fixed="left" :selectable="isRowSelectable" />
          <el-table-column :label="$t('文件名')" :show-overflow-tooltip="true" sortable="custom" prop="name" width="350"
            fixed="left">
            <template #default="scope">
              <div v-if="editId === scope.row.id" class="flex items-center">
                <FileIcon :width="40" :height="30" :item="scope.row" class="mr-2"></FileIcon>
                <!-- <img class="file-icon" :src="scope.row.icon" alt=""> -->
                <el-input v-model="editName" class="edit-name" ref="mkdir2" size="small" :placeholder="$t('请输入文件夹名称')" clearable maxlength="128"
                  :minlength="1" show-word-limit autofocus @input="limitName" @keyup.enter="(e: any) => { e.target.blur() }">
                </el-input>
                <el-tooltip :content="$t('确认')" placement="top">
                  <el-icon class="action-icon" style="margin-left: 10px" @click="() => { confirmEditName(scope.row) }">
                    <component :is="$ElIcon['Check']" />
                  </el-icon>
                </el-tooltip>
                <el-tooltip :content="$t('取消')" placement="top">
                  <el-icon class="action-icon" @click="cancelEditName">
                    <component :is="$ElIcon['Close']" />
                  </el-icon>
                </el-tooltip>
              </div>
              <div v-else>
                <div class="dir-name" @click="($event: any) => handleClickFileName(scope.row, $event)" draggable="true"
                  @dragstart="dragstart($event, scope.row)">
                  <FileIcon :width="40" :height="30" :item="scope.row"></FileIcon>
                  <div class="ml-2 table-cell-filename" v-ellipsis="scope.row.name">{{ scope.row.name }}</div>
                  <el-progress :percentage="scope.row.progress || 0" class="progress" :format="progressFormat"
                    v-if="!scope.row.progressError && (scope.row.progress === 0 || scope.row.progress)">
                  </el-progress>
                  <el-progress v-if="scope.row.progressError" :percentage="scope.row.progress" class="progress">
                    <span class="text-red-500 text-xs">{{ $t('转换失败') }}</span>
                  </el-progress>
                  <div v-if="scope.row.noteHaveAlter || scope.row.fileHaveAlter" class="file-note-new">
                    <img v-if="scope.row.noteHaveAlter" src="/assets/imgs/note-new.png">
                    <img v-if="scope.row.fileHaveAlter" src="/assets/imgs/file-new.png">
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('特色功能')" width="220">
            <template #default="scope">
              <MenuList :menuList="menuList" :items="[scope.row]" :isQuick="true" v-if="scope.row.id" :key="reload">
              </MenuList>
            </template>
          </el-table-column>
          <el-table-column property="ownerName" :label="$t('创建者')" min-width="140">
            <template #default="scope">
              <span class="table-cell-text">{{ scope.row.ownerName }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column property="version" label="当前版本" min-width="120" /> -->
          <el-table-column property="lastModifyTime" sortable="custom" min-width="160" :label="$t('修改时间')">
            <template #default="scope">
              <span class="table-cell-text">{{ scope.row.lastModifyTime }}</span>
            </template>
          </el-table-column>
          <el-table-column property="size" sortable="custom" :label="$t('文件大小')" min-width="120">
            <template #default="scope">
              <span class="table-cell-text">{{ scope.row.fileType !== 1 ? formatSize(scope.row.size) : '' }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column property="commentSize" sortable="custom" label="批注大小">
            <template #default="scope">
              {{ formatSize(scope.row.commentSize) }}
            </template>
          </el-table-column> -->
          <el-table-column property="createTime" min-width="160" :label="$t('创建时间')">
            <template #default="scope">
              <span class="table-cell-text">{{ scope.row.createTime }}</span>
            </template>
          </el-table-column>
        </el-table>

        <FileItemBlock :style="{ height: table_max_height }" v-show="viewType === 'block'" :items="tableData"
          :menuList="menuList" @selectRowChange="handleSelectionChange" @goToItem="handleClickFileName"
          :isSelectAll="isSelectAll" :editInfo="{
            editId, editName, editOldName
          }" :cancelEditName="cancelEditName" :confirmEditName="confirmEditName"
          :handleClickFileName="handleClickFileName" :isIndeterminate="isIndeterminate">
          <template #inputName="{ item }">
            <el-input v-model="editName" style="width: 75%" ref="mkdir" size="small" :placeholder="$t('请输入文件夹名称')" clearable maxlength="128"
              :minlength="1" show-word-limit autofocus @input="limitName" @blur="() => { confirmEditName(item) }" @keyup.enter="(e: any) => { e.target.blur() }">
            </el-input>
          </template>
        </FileItemBlock>

        <!-- 分页 -->
        <div class="pagination">
          <Pagination @nextPage="nextPage" @changePage="changePage" :currPage="currPage" :pageArr="pageArr"
            @size-change="handleSizeChange" @previousPage="previousPage" :total="totalCount" :key="reload"
            :pageSize="pageSize" :nextFlag="nextFlag" :previousFlag="previousFlag"> </Pagination>
        </div>
      </DropZone>

      <!-- 树形组件 -->
      <el-dialog v-model="treeFlag" :title="actionTitle" width="500px" :close-on-click-modal="false"
        :key="reloadTreeDialog" :before-close="handleClose">
        <el-input v-if="currentActionType === actionType.convert" class="mb-4" v-model="filePWD"
          :style="{ width: '200px' }" clearable :placeholder="$t('如果pdf有密码则输入密码')" />
        <el-tree ref="treeRef" style="width: 100%" :key="treeLoad + 1" v-loading="treeLoading" :load="loadNode"
          :props="treeProps" lazy @node-click="handleCheckChange" :default-expanded-keys="[PARENT_ID]"
          :highlight-current="true" :current-node-key="toFolderId" auto-expand-parent node-key="folderId">
          <template #default="{ node }">
            <TipText :content="node.label" maxWidth="100%">
              <template v-slot:content>
                <span class="truncate">{{ node.label
                }}</span>
              </template>
            </TipText>
          </template>
        </el-tree>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleClose" :title="$t('取消')">{{ $t('取消') }}</el-button>
            <el-button type="primary" @click="handelConfirm" :title="$t('确认')">
              {{ $t('确认') }}
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 权限弹框 -->
      <SetRight v-if="rightFlag" :rightFlag="rightFlag" :setRightId="setRightId" :defaultRightValue="defaultRightValue"
        :key="reloadSetRight" @setTightClose="setTightClose" @setTightConfirm="setTightConfirm">
      </SetRight>

      <!-- 历史版本列表 -->
      <HistoryDialog v-if="historyFlag" :key="reloadHistory" :historyFlag="historyFlag" :historyName="historyName"
        @recoverSuccess="refreshFileList(false)" @setHistoryClose="setHistoryClose" :historyId="historyId"
        :functions="specialFunctions">
      </HistoryDialog>
      <CreateFileDialog v-model:dialogVisible="showCreateFileDialog" @createSuccess="refreshFileList()" :folder-id="parentId"/>

    </div>
  </div>
</template>

<script lang="ts" setup name="filesFile">
import { ref, onMounted, onUnmounted, computed, watchEffect, watch, nextTick, h } from 'vue';
import type { ElTable } from 'element-plus'
import Pagination from '@/components/Pagination.vue'
import gstarMessage from "@/utils/showMessage";
import DownloadManager from '@/utils/DownloadManager'

import {
  type FileItem,
  type DownloadQuery,
  type SortChange,
  type SortKey,
  type SortFieIdKey,
  type RenameFileQuery,
  type CreateFolderQuery,
  type CrumbsListItem,
  type DeleteFileQuery,
  FILETYPE,
  REFRESH_LABEL_NAME,
  FILE_TYPE_NAME,
  TRANSLATE_TASK_TYPE,
} from '@/model/file'
import {
  getFileList,
  downloadFile,
  renameFile,
  createFolder,
  deleteFile,
  getDirTree,
  moveFile,
  copyFile,
  getFileTypes,
} from '@/services/file'
import { formatSize, getExt, getShortName } from '@/utils'
import { useRouter } from 'vue-router';
import type Node from 'element-plus/es/components/tree/src/model/node'
import SetRight from './SetRightDialog.vue'
import HistoryDialog from './HistoryDialog.vue'
import CreateFileDialog from './CreateFileDialog.vue'
import { Base64 } from "js-base64";
import socket from '@/utils/socket';
import { useUpload } from '@/hooks/useUpload';
import { getMenuAuth } from '@/utils/permission';
import { useStoreRouter } from '@/stores/router'
import { useUserStore } from "@/stores/user";
import { emitter } from '@/utils/mitt';
import { createVersionBaseLineApi } from '@/services/version';
import { checkIsConvert, initTask } from "@/services/task";
import { editFile, gotoItem } from '@/utils/file';
import { getLicense } from '@/services/system';
import CloudCadUtils from "@/utils/CloudCadUtils"
import { $t } from '@/locales'

interface Tree {
  folderId: number
  name: string
  children?: never[]
}

const progressFormat = (percentage: number) => (percentage === 100 ? $t('转换完成') : `${$t('转换中,进度')}:${percentage}%`)

const messageOnce = new gstarMessage()
const userStores = useUserStore();

const initCadUtilsParams:any = {
  serverHost: window.$globalConfig.serverHost,
  token: userStores.token,
  uid: userStores.id,
  uname: userStores.user_name
}
console.log("useUserStore", userStores.token)
const cloudCadUtils = new CloudCadUtils(initCadUtilsParams)

const menuList = [{
  label: $t('删除'),
  key: 'delete',
  icon: 'Delete',
  click: (item: FileItem) => handelClick('delete', item)
}, {
  label: $t('重命名'),
  key: 'rename',
  svgIcon: 'rename',
  icon: 'Edit',
  click: (item: FileItem) => handelClick('edit', item)
}, {
  label: $t('移动'),
  key: 'move',
  icon: 'Folder',
  click: (item: FileItem) => handelClick('move', item)
}, {
  label: $t('拷贝'),
  key: 'copy',
  svgIcon: 'copy',
  icon: 'Files',
  click: (item: FileItem) => handelClick('copy', item)
}, {
  label: $t('下载'),
  key: 'download',
  icon: 'Download',
  click: (item: FileItem) => handelClick('download', item)
}, {
  label: $t('文件授权'),
  key: 'grant',
  icon: 'Lock',
  click: (item: FileItem) => handelClick('lock', item)
}, {
  label: $t('文件快照'),
  key: 'snapshot',
  icon: 'Camera',
  click: (item: FileItem) => createSnapshot(item)
}, {
  label: $t('转换'),
  key: 'refresh',
  icon: 'Refresh',
  hide: (item: FileItem) => {
    return !Object.keys(REFRESH_LABEL_NAME).includes(String(item.fileType))
  },
  click: (item: FileItem) => ConvertPDF(item)
}, {
  label: $t('文件历史版本'),
  key: 'history',
  icon: 'Clock',
  click: (item: FileItem) => handelClick('history', item)
}, {
  label: $t('编辑'),
  key: 'cloudcad',
  svgIcon: 'edit',
  icon: 'Edit',
  click: (item: FileItem) => editFile({
    disabledEdit: disabledEdit.value,
    item: item,
    bsList: crumbsList.value,
    isLastCrumbName: true,
    cloudCadUtils
  })
},]

const viewType = ref('list')
const isSelectAll = ref(false);

const mkdir = ref<HTMLInputElement>()
const mkdir2 = ref<HTMLInputElement>()

const specialFunctions = ref()

const limitTypes = ref('')

const filePWD = ref('')

const loading = ref(false)

const reload = ref(Math.random())

// 属性相关数据定义
const treeFlag = ref(false)
const treeData = ref([])
const treeRef = ref()
const treeProps = {
  value: 'folderId',
  label: "name",
  children: 'children'
}

// 每页数量
const pageSize = ref(10)

const totalCount = ref(0)

const pageArr = ref<string[]>([])

// 下一页 上一页
const nextFlag = ref(true)
const previousFlag = ref(true)
const currPage = ref(0)
const resDataList = ref<any[]>([])

// 编辑名称
const editName = ref('')

const translateFile = ref()

const table_content_height = ref('86px');

// 表格最大高度
const table_max_height = ref("400px")

const isIndeterminate = ref(true)

// 定义父文件夹默认值
const PARENT_ID = 2

// 定义编辑着的ID
const EDIT_ID = -1

// 编辑数据的id
const editId = ref(EDIT_ID)

// 父ID
const parentId = ref(PARENT_ID)

// 编辑数据的原始名字
const editOldName = ref('')

const updateTime = ref([])

// 目标ID 用于移动/copy
const toFolderId = ref(PARENT_ID)

// 树组件初次加载数据时loading
const treeLoading = ref(false)

// 树形组件重新加载
const treeLoad = ref(Math.random())

// 定义权限弹窗开关
const rightFlag = ref(false)

// 定义历史列表弹窗开关
const historyFlag = ref(false)

// 新建文件弹窗开关
const showCreateFileDialog = ref(false)

// 定义获取历史版本的文件id
const historyId = ref(0)

// 定义获取历史版本的文件名称
const historyName = ref('')

// 定义常量操作类型
const actionType = {
  move: 1,
  copy: 2,
  convert: 3
}

// 定义当前操作变量
const currentActionType = ref(0)

// 重新加载上传组件
const reloadUploadFile = ref(Math.random())

// 批量移动文件列表
const moveOrCopyFileArr = ref<FileItem[]>([])

// 定义设置文件权限id
const setRightId = ref(0)

// 定义文件默认的权限大类值
const defaultRightValue = ref(0)

// 定义上传类型  1=上传文件  2=上传文件夹
const uploadType = ref(1)

// 定义树形组件弹窗刷新值
const reloadTreeDialog = ref(Math.random())

// 文件类型枚举
enum EFILETYPE {
  dir = 1,
  dwg,
  rvt,
  stl,
  zip,
}

const $router = useRouter();

// 定义列表接口查询参数
const listQueryData = ref({
  folderId: parentId.value,
  direction: 0,
  limit: pageSize.value,
  searchName: '',
  searchBeginTime: '',
  searchEndTime: '',
  fileType: 0,
  sortField: 0,
  ascOrDesc: 'desc',
  lastFileId: 0,
  lastFileSize: 0,
  lastFileModifyTime: '',
  lastFileName: '',
  reqPage: 1,
  reqPageIdArr: ''
})

const enableUpload = computed(() => {
  return getMenuAuth('添加')
})

// query参数编码
function encodeQueryParams(params: { [key: string]: string | any }): string {
  // return JSON.stringify(params)
  return Base64.encode(JSON.stringify(Object.keys(params).map(k => params[k])))
}

// query参数解码
function decodeQueryParams(paramsStr: string): { [key: string]: string } {
  const params = JSON.parse(JSON.stringify(listQueryData.value))
  const vals = JSON.parse(Base64.decode(paramsStr))
  Object.keys(params).forEach((k, i) => params[k] = vals[i])
  return params
}

// 分页切换每页数量
const handleSizeChange = async (val: number) => {
  console.log(`${val} items per page`)
  pageSize.value = val;
  listQueryData.value.limit = pageSize.value
  listQueryData.value.reqPage = 1;
  $router.push({ name: 'filesFile', query: { params: encodeQueryParams(listQueryData.value) } })
}

// 下一页
function nextPage() {
  currPage.value++
  listQueryData.value.reqPage = currPage.value
  listQueryData.value.reqPageIdArr = resDataList.value[currPage.value].map((v: any) => v.id).join()

  listQueryData.value.direction = 0
  listQueryData.value.lastFileId = tableData.value.length > 0 ? tableData.value[tableData.value.length - 1].id : 0
  listQueryData.value.lastFileSize = tableData.value.length > 0 ? tableData.value[tableData.value.length - 1].size : 0
  listQueryData.value.lastFileModifyTime = tableData.value.length > 0 ? tableData.value[tableData.value.length - 1].lastModifyTime : ''
  listQueryData.value.lastFileName = tableData.value.length > 0 ? tableData.value[tableData.value.length - 1].name : ''
  $router.push({ name: 'filesFile', query: { params: encodeQueryParams(listQueryData.value) } })
}

// 上一页
function previousPage() {
  if (currPage.value > 1) {
    currPage.value--
  } else {
    currPage.value = 1
  }
  listQueryData.value.reqPage = currPage.value
  listQueryData.value.reqPageIdArr = resDataList.value[currPage.value].map((v: any) => v.id).join()

  listQueryData.value.direction = 1
  listQueryData.value.lastFileId = tableData.value.length > 0 ? tableData.value[0].id : 0
  listQueryData.value.lastFileSize = tableData.value.length > 0 ? tableData.value[0].size : 0
  listQueryData.value.lastFileModifyTime = tableData.value.length > 0 ? tableData.value[0].lastModifyTime : ''
  listQueryData.value.lastFileName = tableData.value.length > 0 ? tableData.value[0].name : ''
  $router.push({ name: 'filesFile', query: { params: encodeQueryParams(listQueryData.value) } })
}


function changePage(pageKey: string) {
  console.log("pageKey", pageKey)
  listQueryData.value.reqPage = parseInt(pageKey)
  listQueryData.value.reqPageIdArr = resDataList.value[parseInt(pageKey)].map((v: any) => v.id).join()
  listQueryData.value.direction = parseInt(pageKey) > currPage.value ? 0 : 1
  console.log("listQueryData", listQueryData.value)
  $router.push({ name: 'filesFile', query: { params: encodeQueryParams(listQueryData.value) } })
}

// 目录id
onMounted(() => {
  const params: any = $router.currentRoute.value.query.params
  if (params) {
    try {
      listQueryData.value = { ...decodeQueryParams(params) } as unknown as (typeof listQueryData.value)
    } catch (err) {
      console.error('query params error!', err)
    }
  }
  getTableContentHeight()
  refreshFileList()
  geDisabledRefreshStatus()
  // 监听file 事件
  socket.on('file', fileChange)
  socket.on('task', taskChange)
  window.addEventListener("resize", onResize);
});

onUnmounted(() => {
  // 取消file事件监听
  socket.off('file', fileChange)
  socket.off('task', taskChange)
  window.removeEventListener("resize", onResize);
})

const dragstart = (e: DragEvent, item: FileItem) => {
  const dataTransfer = e.dataTransfer;
  const target = e.target as HTMLElement;
  if (dataTransfer && target) {
    const obj = {
      folderId: item.id,
      direction: 0,
      limit: pageSize.value,
      searchName: '',
      searchBeginTime: '',
      searchEndTime: '',
      fileType: 0,
      sortField: 0,
      ascOrDesc: 'desc',
      lastFileId: 0,
      lastFileSize: 0,
      lastFileModifyTime: '',
      lastFileName: '',
    }
    const url = encodeQueryParams(obj)
    const data = JSON.stringify({ ...item, routeUrl: url })
    dataTransfer.setData("item", data);
  }
}

const onResize = () => {
  getTableContentHeight()
}

/** 暂定解决 */
const getTableContentHeight = () => {
  setTimeout(() => {
    const filterDom = document.querySelector('.layout-vertical-filter');
    table_content_height.value = `calc(100% - ${(filterDom?.clientHeight || 0) + 6}px)`
  }, 50);
  nextTick(() => {
    setTimeout(() => {
      const f = document.querySelector('.fileDrop');
      if (f) {
        table_max_height.value = `${f?.clientHeight - 52}px`
      }
    }, 100);
  })
}

const limitName = (e: string) => {
  if (!e) {
    ElMessage({
      type: 'error',
      message: $t('名称不能为空'),
    })
  } else {
    const reg = /[<>:*:?？：""|/\\]/g
    if (reg.test(e)) {
      messageOnce.error($t('名称不能包含下列任何字符：\\ /:?*"<>|'))
    }
    editName.value = e.replace(reg, '')
  }
}

// 文件相关socketio事件处理
const taskChange = (ename: string, data: any) => {
  switch (ename) {
    case 'task.progress.change':
    case 'task.cancel.ok':
    case 'task.end.error':
      if (tableData.value && data) {
        // 如果是rvt开图
        if ([6, 31, 33].includes(data.subType)) {
          tableData.value = tableData.value.map((v: FileItem) => {
            if (v.id === data.fileId) {
              if (data.newProgress === undefined && !data.redo_type) {
                v.progressError = true
              } else {
                v.progressError = false
                v.progress = (v?.progress || 0) < data.newProgress ? data.newProgress : v.progress
                if (data.newProgress === 100) {
                  // 刷新页面
                  setTimeout(() => {
                    refreshFileList(false)
                  }, 1000);
                }
              }
            }
            return v
          })
        }
      }
      break
  }
}

// 文件相关socketio事件处理
const fileChange = (ename: string, data: any) => {
  console.log('file 事件 接收：', ename, data)
  switch (ename) {
    case 'file.add':
      // 当前列表中有添加则刷新
      // if (data.parentId === parentId.value) {
      //   const isAllFolder = tableData.value.length > 0 && tableData.value.every((v) => {
      //     return v.fileType === FILETYPE.FOLDER
      //   })
      //   // 如果是上传的是文件，且当前列表里都是文件夹，则不需要刷新
      //   if (data.fileType !== FILETYPE.FOLDER && isAllFolder) {
      //     return;
      //   }
      //   refreshFileList(false)
      // }
      if (data.parentId === parentId.value) {
        refreshFileList(false)
      }
      break
    case 'file.delete': {
      // 有删除事件，如果是当前列表中的文件则刷新列表
      let isBreak = false
      for (const item of tableData.value) {
        if (!isBreak) {
          for (const fileName of data.fileName) {
            if (item.name === fileName && item.parentId === data.parentId) {
              refreshFileList(false)
              isBreak = true
              break
            }
          }
        }
      }
      break
    }
    case 'file.thumnail.change':
      // 缩略图转换完成
      if (data.resultId) {
        // 如果有返回thumbnail字段则直接替换
        tableData.value = tableData.value.map(v => {
          if (v.id === data?.fileId) {
            v.thumbnail = JSON.stringify(data.resultId)
          }
          return v
        })
      } else {
        // 没有则刷新文件列表
        refreshFileList(false)
      }
      break
    case 'file.note.change':
      tableData.value = tableData.value.map((v: FileItem) => {
        if (v.id === data.fileId) {
          v.noteHaveAlter = true
        }
        return v
      })
      break
  }
}

const actionTitle = computed(() => {
  const types = [$t('移动'), $t('拷贝'), $t('转换')];
  return `${types[currentActionType.value - 1]}${$t('操作')}`
})


const disableDelete = computed(() => {
  return multipleSelection.value.length === 0 || !multipleSelection.value.every(item => item.specialFunctions?.includes('delete'))
})

const disableMove = computed(() => {
  return multipleSelection.value.length === 0 || !multipleSelection.value.every(item => item.specialFunctions?.includes('move'))
})

// 监听路由
watchEffect(() => parentId.value = listQueryData.value.folderId)


watch(() => $router.currentRoute.value.query, () => {
  const params: any = $router.currentRoute.value.query.params

  if (params) {
    try {
      listQueryData.value = { ...decodeQueryParams(params) } as unknown as (typeof listQueryData.value)
    } catch (err) {
      console.error('query params error!')
    }
  } else {
    // reset
    listQueryData.value = {
      folderId: PARENT_ID,
      direction: 0,
      limit: pageSize.value,
      searchName: '',
      searchBeginTime: '',
      searchEndTime: '',
      fileType: 0,
      sortField: 0,
      ascOrDesc: 'desc',
      lastFileId: 0,
      lastFileSize: 0,
      lastFileModifyTime: '',
      lastFileName: '',
      reqPage: 1,
      reqPageIdArr: ''
    }
  }
  editId.value = EDIT_ID
  refreshFileList()
})

watch(
  () => viewType.value,
  () => {
    getTableContentHeight()
  },
);

// 处理面包屑点击
const handleBreadcrumbChange = (item: CrumbsListItem) => {
  openDir(item.folderId)
}

// 切换目录
const openDir = (folderId: number | string, searchName?: string) => {
  // 如果切换目录就是当前目录则无效果，但是根目录下点击当前目录切换为刷新到第一页
  if (!searchName && folderId == listQueryData.value.folderId && folderId != PARENT_ID) {
    return
  }
  currPage.value = 1
  listQueryData.value.reqPage = currPage.value
  listQueryData.value.reqPageIdArr = ''

  listQueryData.value.lastFileId = 0
  listQueryData.value.lastFileModifyTime = ''
  listQueryData.value.lastFileSize = 0
  listQueryData.value.direction = 0
  listQueryData.value.folderId = parseInt(folderId as string)
  // 重置搜索条件
  listQueryData.value.fileType = 0
  listQueryData.value.searchName = searchName || ''
  listQueryData.value.searchBeginTime = ''
  listQueryData.value.searchEndTime = ''
  updateTime.value = []
  $router.push({ name: 'filesFile', query: { params: encodeQueryParams(listQueryData.value) } })
}

// 点击文件名
const handleClickFileName = (item: FileItem, _event: MouseEvent) => {
  const ext = getExt(item.name)
  if (ext === 'slddrw') {
    openTransformTips(item)
    return
  }
  gotoItem(item, () => openDir(item.id))
}

const openTransformTips = (item: FileItem) => {
  ElMessageBox.confirm(
    $t('工程图文件需转换为DWG格式后才可查看。是否立即转换？转换成功后会在该文件同目录下生成名称相同，后缀为DWG的文件。'),
    $t('提示'),
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: $t('确认'),
      cancelButtonText: $t('取消')
    }
  )
    .then(() => {
      console.log(item)
      translateFile.value = item
      toTranslate()
    })
}

// 绑定表格ref
const multipleTableRef = ref<InstanceType<typeof ElTable>>()

// 定义选择数据类型
const multipleSelection = ref<FileItem[]>([])

// 选择数据方法
const handleSelectionChange = (val: FileItem[]) => {
  multipleSelection.value = val
}

// 表格数据
const tableData = ref<FileItem[]>([])

// 面包屑层级
const crumbsList = ref<CrumbsListItem[]>([])

// 定义权限弹窗重新渲染值
const reloadSetRight = ref(Math.random())
const reloadHistory = ref(Math.random())

const selectedRows = computed(() => {
  return tableData.value.filter((item: any) => item.isSelected)
})

watch(
  () => tableData.value,
  (v: FileItem[]) => {
    const len = selectedRows.value.length
    isSelectAll.value = len === v.length
    isIndeterminate.value = len > 0 && len < v.length
  },
  { deep: true }
);

const handleCheckAllChange = (_val: boolean) => {
  isIndeterminate.value = false
}

const getSelectVal = (val: string) => {
  listQueryData.value.fileType = parseInt(val) || 0
}

// 改变搜索时间
const getTime = (val: string[]) => {
  if (val?.length > 1) {
    listQueryData.value.searchBeginTime = val[0]
    listQueryData.value.searchEndTime = val[1]
  } else {
    listQueryData.value.searchBeginTime = ''
    listQueryData.value.searchEndTime = ''
  }
}

// 获取列表数据
async function refreshFileList(isLoading = true) {
  try {
    loading.value = isLoading
    // const res = await getFileList({ ...listQueryData.value, limit: viewType.value === 'block' ? 30 : listQueryData.value.limit })
    const res = await getFileList(listQueryData.value)
    currPage.value = res.data.currPage

    tableData.value = res.data?.paging[currPage.value] || []
    pageArr.value = Object.keys(res.data.paging)
    resDataList.value = res.data.paging

    crumbsList.value = res.data.crumbsList.map((crumb: any, index: number) => {
      if (!index) crumb.folderName = $t('文件库')
      return crumb
    })
    parentId.value = crumbsList.value.length ? crumbsList.value[crumbsList.value.length - 1].folderId : PARENT_ID
    reload.value = Math.random()
    nextFlag.value = res.data.lastPage
    previousFlag.value = res.data.firstPage
    totalCount.value = res.data.total
  } catch (error) {
    reload.value = Math.random()
    totalCount.value = 0
    console.log(error)
  } finally {
    loading.value = false
    if (editId.value === 0) {
      editId.value = EDIT_ID
      createDir()
    }
  }
}

interface FileTypeListItem {
  value: number
  label: string
}

// 文件类型
const fileTypeList = ref<FileTypeListItem[]>([])

// 获取文件类型列表
async function getFileTypeList() {
  try {
    const res = await getFileTypes()
    if (res.code === 0) {
      const arr = Object.keys(res?.data?.typeList)
      if (arr?.length) {
        fileTypeList.value = arr.map(v => {
          return {
            value: res?.data?.typeList[v],
            label: v
          }
        })
        fileTypeList.value.unshift({
          value: 0,
          label: $t('全部')
        })
        fileTypeList.value = fileTypeList.value.sort((a, b) => {
          return a.value - b.value
        })
        limitTypes.value = arr.filter(v => v !== 'dir').map(v => '.' + v).join()
      } else {
        fileTypeList.value.unshift({
          value: 0,
          label: $t('全部')
        })
      }
    }
  } catch (error) {
    fileTypeList.value.unshift({
      value: 0,
      label: $t('全部')
    })
    console.log(error)
  }
}

getFileTypeList()

const onSubmit = () => {
  listQueryData.value.reqPage = 1;
  $router.push({ name: 'filesFile', query: { params: encodeQueryParams(listQueryData.value) } })
}

const onReset = () => {
  listQueryData.value.fileType = 0
  listQueryData.value.searchName = ''
  listQueryData.value.searchBeginTime = ''
  listQueryData.value.searchEndTime = ''
  currPage.value = 1
  listQueryData.value.reqPage = currPage.value
  listQueryData.value.reqPageIdArr = ''
  updateTime.value = []
  $router.push({ name: 'filesFile', query: { params: encodeQueryParams(listQueryData.value) } })
}

// 关闭树形弹窗
const handleClose = () => {
  treeFlag.value = false
  treeLoading.value = false
  treeData.value = []
  toFolderId.value = PARENT_ID
  moveOrCopyFileArr.value = []
  rightFlag.value = false
  setRightId.value = 0
  defaultRightValue.value = 0
  reloadTreeDialog.value = Math.random()
  filePWD.value = ''
  cancelEditName()
}

// 树形弹窗确定
const handelConfirm = async () => {
  if (currentActionType.value === actionType.move) { // 移动
    moveFileDir()
  } else if (currentActionType.value === actionType.copy) { // 复制
    copyFileDir()
  } else if (currentActionType.value === actionType.convert) { // 转换
    toTranslate()
  }
}

// 转换
const toTranslate = async () => {
  const userStores = useUserStore();
  try {
    const config = {
      transopt: false,
      password: filePWD.value,
      folderId: parentId.value,
      ownerId: userStores.id,
      ownerName: userStores.user_name,
    }
    const { data } = await initTask({
      fileId: translateFile.value.id,
      type: TRANSLATE_TASK_TYPE[translateFile.value.fileType],
      version: translateFile.value.version,
      config
    })
    emitter.emit('addTaskToAsync', {
      id: data.id,
      status: 0,
      name: translateFile.value.name,
      process: '0%'
    })
    handleClose()
  } catch (e) {
    console.log(e)
  }
}

// 树形加载自目录
const loadNode = async (node: Node, resolve: (data: Tree[]) => void) => {
  try {
    if (node.level === 0) {
      treeLoading.value = false
      return resolve([{ name: $t('全部文件'), folderId: PARENT_ID }])
    }
    const id = node.data.folderId || PARENT_ID
    toFolderId.value = id
    treeLoading.value = true
    const res = await getDirTree({ folderId: id })
    treeLoading.value = false

    if (res.code === 0) {
      resolve(res.data.dirTree)
    } else {
      resolve([])
    }
    
  } catch (error) {
    treeLoading.value = false
    resolve([])
  }
}

// 选择目标目录
const handleCheckChange = (data: Tree) => {
  toFolderId.value = data.folderId
}

// 拷贝文件
const copyFileDir = async () => {
  if (parentId.value === toFolderId.value) {
    ElMessage({
      type: 'error',
      message: $t('同一路径不能复制'),
    })
    return
  }
  try {
    const obj = {
      fileNameArr: moveOrCopyFileArr.value.map(v => v.name),
      fileIdArr:moveOrCopyFileArr.value.map(v => v.id),
      fromFolderId: parentId.value,
      toFolderId: toFolderId.value
    }

    // 检查重复文件名
    const duplicatedName = await getDuplicatedName(obj.toFolderId, obj.fileNameArr)
    if (duplicatedName.length > 0) {
      const isConfirm = await ElMessageBox.alert(
        `${$t('目标文件/文件夹')}：
        ${duplicatedName.join('、')}
        ${$t('是否更新')}?`,
        $t('提示！'),
        {
          confirmButtonText: $t('确认'),
          cancelButtonText: $t('取消'),
          type: 'warning'
        }
      )
        .then(() => Promise.resolve(true))
        .catch(() => Promise.resolve(false))
      // 如果取消重名覆盖，则取消此次操作
      if (!isConfirm) {
        return
      }
    }

    loading.value = true
    treeFlag.value = false
    const res = await copyFile(obj)
    if (res.code === 0) {
      handleClose()
      await refreshFileList()
      ElMessage({
        type: 'success',
        message: $t('拷贝成功'),
      })
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}

const isSubsetInTree = async (node: Node, targetFolderId: any) => {
  if (node?.data?.folderId === targetFolderId) {
    return true
  }
  if (node?.childNodes && node.childNodes.length > 0) {
    for (const child of node.childNodes) {
      if (await isSubsetInTree(child, targetFolderId)) {
        return true;
      }
    }
  }
  return false;
}

// 移动文件
const moveFileDir = async () => {
  if (parentId.value === toFolderId.value || moveOrCopyFileArr.value.filter(v => v.id === toFolderId.value).length > 0) {
    ElMessage({
      type: 'error',
      message: $t('同一路径不能移动'),
    })
    return
  }
  const selectedNode = treeRef.value.getCurrentNode();
  if (selectedNode) {
    const targetFolderId = selectedNode.folderId;
    for (let i = 0; i < moveOrCopyFileArr.value.length; i++) {
      console.log(1111111111111111, moveOrCopyFileArr.value)
      const node = treeRef.value.getNode(moveOrCopyFileArr.value[i].id)
      console.log(1111111111111111, node)
      const result = await isSubsetInTree(node, targetFolderId)
      if (result) {
        return ElMessage.error($t('不能移动到子集文件夹中'));
      }
    }
  }
  try {
    const obj = {
      fileNameArr: moveOrCopyFileArr.value.map(v => v.name),
      fileIdArr:moveOrCopyFileArr.value.map(v => v.id),
      fromFolderId: parentId.value,
      toFolderId: toFolderId.value
    }
    // 检查重复文件名
    const duplicatedName = await getDuplicatedName(obj.toFolderId, obj.fileNameArr)
    if (duplicatedName.length > 0) {
      const isConfirm = await ElMessageBox.alert(
        `${$t('目标文件/文件夹')}：
        ${duplicatedName.join('、')}
        ${$t('是否更新')}?`,
        $t('提示！'),
        {
          confirmButtonText: $t('确认'),
          cancelButtonText: $t('取消'),
          type: 'warning'
        }
      )
        .then(() => Promise.resolve(true))
        .catch(() => Promise.resolve(false))
      // 如果取消重名覆盖，则取消此次操作
      if (!isConfirm) {
        return
      }
    }

    loading.value = true
    treeFlag.value = false
    const res = await moveFile(obj)
    if (res.code === 0) {
      handleClose()
      await refreshFileList()
      ElMessage({
        type: 'success',
        message: $t('移动成功'),
      })
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}

const ConvertPDF = async (item: FileItem) => {
  if (disabledRefresh.value) return ElMessage.error($t('该任务类型未获得授权'))

  currentActionType.value = actionType.convert
  try {
    const { data } = await checkIsConvert({
      fileId: item.id,
      version: item.version,
      type: TRANSLATE_TASK_TYPE[item.fileType as number]
    })
    const fileTypeName = FILE_TYPE_NAME[item.fileType as number]
    if (data.isConvert) {
      // 如果没有转换则通过目录树进行
      translateFile.value = item
      if (fileTypeName === 'pdf') {
        ElMessageBox.prompt(`${fileTypeName}${$t('转换默认dwg存储路径为当前目录下，如果存在同名文件，则会增加版本')}`, `${$t('转换')}${fileTypeName}`, {
          confirmButtonText: $t('确认'),
          cancelButtonText: $t('取消'),
          inputPlaceholder: `${$t('如果有密码则输入密码')}`
        })
          .then(async ({ value = "" }) => {
            filePWD.value = value
            toTranslate()
          })
      } else {
        ElMessageBox.confirm(`${fileTypeName}${$t('转换默认dwg存储路径为当前目录下，如果存在同名文件，则会增加版本')}`, `${$t('转换')}${fileTypeName}`, {
          confirmButtonText: $t('确认'),
          cancelButtonText: $t('取消')
        }).then(() => {
          toTranslate()
        })
      }

    } else {
      const props = { style: { color: "blue" } }
      ElMessageBox({
        title: `${$t('转换')}${fileTypeName}`,
        message: h('p', null, [
          h('span', null, $t('该文件正处于转换中或已转换完成,请点击')),
          h('i', props, $t('确认')),
          h('span', null, $t('按钮进行跳转查看')),
        ]),
      }).then(() => {
        openDir(data.folderId, data.searchName)
      }).catch((e) => {
        console.log(e);
      })
    }
  } catch (error) {
    console.log(error)
  }
}

// 创建快照
const createSnapshot = (item: FileItem) => {
  ElMessageBox.alert(`${$t('你确定要生成的快照吗')}${item.name}?`, $t('快照'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    type: "warning",
  }).then(async () => {

    ElMessageBox.prompt($t('请输入快照名称,如果不填写,则默认是文件名'), $t('生成快照'), {
      confirmButtonText: $t('确认'),
      cancelButtonText: $t('取消'),
      //校验方法
      inputValidator: (val: string) => {
        if ((val && val.length > 128) || /[<>:*:?？：""|/\\]/g.test(val)) {
          return false;
        }
        return true
      },
      inputErrorMessage: $t('名称格式为0到128位不包含<>:*?？: 特殊字符'),
    })
      .then(async ({ value = "" }) => {
        try {
          const stores = useStoreRouter()
          const extension = getExt(item.name)
          await createVersionBaseLineApi({
            fileId: item.id,
            fileVersion: item.version,
            name: value ? item.fileType === FILETYPE.FOLDER ? value : `${value}.${extension}` : item.name,
            fileEtag: item.etag,
            fileType: item.fileType,
            fileThumbnail: item.thumbnail
          })
          stores.getVersionBaseLineList()
        } catch (e) {
          console.log(e);
        }
      })
  });
}

// 特色功能操作
const handelClick = (type: string, row: FileItem) => {
  cancelEditName()
  switch (type) {
    case 'edit':
      parentId.value = row.parentId
      editId.value = row.id
      editName.value = (row.fileType === FILETYPE.FOLDER ? row.name : getShortName(row.name))
      editOldName.value = row.name
      nextTick(() => {
        mkdir.value?.focus()
        mkdir.value?.select()
        mkdir2.value?.focus()
        mkdir2.value?.select()
      })
      break;

    case 'download':
      downloadFileReq({
        fileName: row.name,
        fileId: row.id
      })
      break;

    case 'delete':
      ElMessageBox.alert(
        `${$t('确定要删除吗')} ${row.name} ?`,
        $t('删除文件'),
        {
          confirmButtonText: $t('确认'),
          cancelButtonText: $t('取消'),
          type: 'warning',
        }
      )
        .then(() => {
          deleteFiles([row])
        })
      break;

    case 'move':
      moveOrCopyFileArr.value = [row]
      treeLoad.value = Math.random()
      treeFlag.value = true
      treeLoading.value = true
      currentActionType.value = actionType.move
      break;

    case 'copy':
      moveOrCopyFileArr.value = [row]
      treeLoad.value = Math.random()
      treeFlag.value = true
      treeLoading.value = true
      currentActionType.value = actionType.copy
      break;

    case 'lock':
      setRightId.value = row.id
      defaultRightValue.value = row.permType
      rightFlag.value = true
      break;

    case 'history':
      historyId.value = row.id
      historyName.value = row.name
      specialFunctions.value = row.specialFunctions || []
      historyFlag.value = true
      break;
  }
}

// 权限弹框取消
const setTightClose = () => {
  rightFlag.value = false
  setRightId.value = 0
  reloadSetRight.value = Math.random()
}

// 历史弹窗取消
const setHistoryClose = () => {
  historyFlag.value = false
  historyId.value = 0
  reloadHistory.value = Math.random()
}

// 权限弹框确认
const setTightConfirm = () => {
  rightFlag.value = false
  setRightId.value = 0
  reloadSetRight.value = Math.random()
  refreshFileList()
}

// 下载文件请求
const downloadFileReq = async (data: DownloadQuery) => {
  try {
    const downloadManager = await new DownloadManager({fileId: data.fileId, fileName: data.fileName, token: userStores.token})
    await downloadManager.start()

    // await downloadFile(data)
  } catch (error) {
    console.log(error)
  }
}

// 修改文件名请求
const editFileNameReq = async (item?: any) => {
  // 如果名称为空
  if (!item.rename && !editName.value) {
    ElMessage({
      type: 'error',
      message: $t('名称不能为空'),
    })
    return;
  }

  let fullName = ''
  if (item.fileType === FILETYPE.FOLDER) {
    fullName = item.rename || editName.value
  } else {
    const extStart = item.name.lastIndexOf(".");
    const ext = item.name.substring(extStart + 1, item.name.length)
    fullName = `${item.rename || editName.value}.${ext}`
  }

  if (fullName === editOldName.value ) {
    editId.value = EDIT_ID
    editName.value = ''
    editOldName.value = ''
    return
  }

  const renameFileQuery: RenameFileQuery = {
    folderId: parentId.value,
    newName: fullName,
    oldName: editOldName.value
  }

  // 检查重复文件名
  const duplicatedName = await getDuplicatedName(renameFileQuery.folderId, [renameFileQuery.newName])
  if (duplicatedName.length > 0) {
    ElMessage({
      type: 'error',
      message: $t('文件名已存在'),
    })
    return
  }

  try {
    loading.value = true
    const res = await renameFile(renameFileQuery)

    if (res.code === 0) {
      editId.value = EDIT_ID
      editName.value = ''
      editOldName.value = ''

      ElMessage({
        type: 'success',
        message: $t('修改成功'),
      })
      await refreshFileList()
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }

  } catch (error) {
    loading.value = false
    console.log(error)
  }
}

// 创建文件
const createFile = () => {
  showCreateFileDialog.value = true
}

// 创建文件夹
const createDir = () => {
  if (editId.value === 0) {
    return
  }
  const obj = {
    lastModifyTime: '',
    size: 0,
    commentSize: 0,
    id: 0,
    name: $t('新建文件夹'),
    createTime: '',
    parentId: parentId.value,
    ownerId: '',
    ownerName: '',
    version: '',
    fileType: 1,
    permType: 1,
    etag: '',
  }

  editId.value = obj.id
  editName.value = obj.name
  tableData.value.unshift(obj)
  nextTick(() => {
    mkdir.value?.focus()
    mkdir.value?.select()
    mkdir2.value?.focus()
    mkdir2.value?.select()
  })
}

// 创建文件夹请求
const createDirReq = async (item?: any) => {
  const createFolderQuery: CreateFolderQuery = {
    parentId: parentId.value,
    folderName: item.rename || editName.value
  }
  // 如果名称为空
  if (!createFolderQuery.folderName) {
    ElMessage({
      type: 'error',
      message: $t('名称不能为空'),
    })
    return;
  }

  // 检查重复文件名
  const duplicatedName = await getDuplicatedName(createFolderQuery.parentId, [createFolderQuery.folderName])
  if (duplicatedName.length > 0) {
    ElMessage({
      type: 'error',
      message: $t('文件名已存在'),
    })
    mkdir.value?.focus()
    mkdir2.value?.focus()
    return
  }

  try {
    loading.value = true
    const res = await createFolder(createFolderQuery)
    if (res.code === 0) {
      editName.value = ''
      editId.value = EDIT_ID
      ElMessage({
        type: 'success',
        message: $t('创建成功'),
      })
      await refreshFileList()
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }

  } catch (error) {
    loading.value = false
    console.log(error)
  }
}

// 确认修改文件名
const confirmEditName = async (item?: any) => {
  // 如果有编辑id 判定为修改文件名  否则为新建文件夹
  if (editId.value) {
    editFileNameReq(item)
  } else {
    createDirReq(item)
  }

}

// 取消修改文件名
const cancelEditName = () => {
  editId.value = EDIT_ID
  editName.value = ''
  editOldName.value = ''
  const item = tableData.value[0]
  if (item?.id === 0) {
    tableData.value.shift()
  }
}

// 定义修改时间文件排序字段值
const sortKey: SortKey = {
  descending: 'desc',
  ascending: 'asc',
  default: ''
}

// 定义排序查询的字段值
const sortField = {
  lastModifyTime: 1,
  size: 2,
  name: 3,
  commentSize: 4
}

// 监听表格排序
const sortChange = (data: SortChange) => {
  listQueryData.value.ascOrDesc = data.order ? sortKey[data.order as keyof SortKey] : 'desc'
  listQueryData.value.sortField = sortField[data.prop as keyof SortFieIdKey]
  $router.push({ name: 'filesFile', query: { params: encodeQueryParams(listQueryData.value) } })
}

// 批量删除
const batchDelete = () => {
  ElMessageBox.alert(
    $t('确定要批量删除选择的文件吗'),
    $t('删除文件'),
    {
      confirmButtonText: $t('确认'),
      cancelButtonText: $t('取消'),
      type: 'warning',
    }
  )
    .then(() => {
      deleteFiles(multipleSelection.value)
    })

}

// 批量移动
const batchMove = () => {
  moveOrCopyFileArr.value = multipleSelection.value
  treeLoad.value = Math.random()
  currentActionType.value = actionType.move
  treeFlag.value = true
  treeLoading.value = true
}

// 删除文件
const deleteFiles = async (items: FileItem[]) => {
  const obj: DeleteFileQuery = {
    fileNameArr: items.map(v => v.name),
    fileIdArr: items.map(v => v.id),
    folderId: parentId.value
  }
  try {
    await deleteFile(obj)
    ElMessage({
      type: 'success',
      message: $t('删除成功'),
    })
  } catch (error) {
    console.log(error)
  }
}

// 上传/移动/COPY 重名文件检查,只检查一级
const getDuplicatedName: (toFolderId: number, names: string[]) => Promise<string[]> = (toFolderId: number, names: string[]) => {
  return new Promise((resolve, _reject) => {
    getFileList(Object.assign(JSON.parse(JSON.stringify(listQueryData.value)), {
      folderId: toFolderId,
      limit: '1000000'
    }))
      .then((res) => {
        names = [...new Set(names)]
        const duplicatedList: string[] = []
        res.data.fileList.forEach((file: any) => {
          names.forEach(n => {
            if (file.name === n) {
              duplicatedList.push(n)
            }
          })
        })
        resolve(duplicatedList)
      })
      .catch(err => {
        ElMessage({
          type: 'error',
          message: $t('重名校验失败') + ':' + err.message
        })
        resolve([])
      })
  })
}

const uploadFileHandle = (files: File[]) => {
  const uploadFiles = files.map((file) => {
    return {
      raw: file,
      name: file.name,
      size: file.size,
      type: file.type
    }
  })
  changeFiles(uploadFiles)
}

const changeFiles = async (uploadFiles: any) => {
  // 上传改为直接上传当前目录，不进行选择
  toFolderId.value = listQueryData.value.folderId
  const { createUploadReq, fileList } = useUpload({ toFolderId: parentId.value, fileList: uploadFiles, replace: true, type: 1 })
  const fileNameArr: string[] = fileList.value.map((file: any) => {
    if (file.raw) {
      // 先判断相对路径是否存在，如果存在使用相对路径第一级作为重名计算对象
      if (file.raw.webkitRelativePath) {
        return file.raw.webkitRelativePath.split('/')[0] || ''
      } else { // 相对路径不存在，使用filename作为重名计算对象
        return file.name || ''
      }
    }
    return ''
  }).filter((v: any) => v) || []

  // 检查重复文件名
  const duplicatedName = await getDuplicatedName(toFolderId.value, fileNameArr)
  if (duplicatedName.length > 0) {
    const isConfirm = await ElMessageBox.alert(
      `${$t('目标文件/文件夹已存在，是否更新')}：
${duplicatedName.join('、')}`,
      $t('提示！'),
      {
        confirmButtonText: $t('确认'),
        cancelButtonText: $t('取消'),
        type: 'warning'
      }
    )
      .then(() => Promise.resolve(true))
      .catch(() => Promise.resolve(false))
    // 如果取消重名覆盖，则取消此次操作
    if (!isConfirm) {
      return
    }
  }

  // 判断无文件上传
  if (fileList.value && fileList.value.length > 0) {
    createUploadReq()
    handleClose()
  } else {
    ElMessage({
      type: 'warning',
      message: $t("无文件上传！"),
    })
  }
}

const clearAll = () => {
  multipleTableRef.value?.clearSelection()
  isSelectAll.value = false
}

const isRowSelectable = (row: FileItem) => {
  const operateKey = ['delete', 'move']
  return operateKey.some(val => (row.specialFunctions || []).includes(val));
}

const disabledRefresh = ref(false)
const disabledEdit = ref(false)
const geDisabledRefreshStatus = async () => {
  try {
    const { data = {} } = await getLicense()
    disabledRefresh.value = !data.modules.some((i: any) => ['ConvertPDF', 'ConvertDWF'].includes(i.type) && i.usable)
    disabledEdit.value = !data.modules.some((i: any) => ['MakeDWGIncrMerge'].includes(i.type) && i.usable)
  } catch (error) {
    console.log(error);
  }
}

</script>

<style lang="scss" scoped>
.upload-button {
  display: inline-block;
  margin-right: 12px;
}

.folder {
  :deep(.el-form-item__label) {
    width: 100px !important;
  }
}

.layout-select {
  padding-bottom: 6px !important;
  :deep(.el-select__wrapper) {
    width: 220px;
  }
}

.list {
  height: v-bind(table_content_height);
  margin-bottom: 0px !important;
}

.fileDrop {
  height: calc(100% - 100px);
}

.file-table {
  height: calc(100% - 72px - 72px);
}

.action-icon {
  cursor: pointer;
  margin-right: 10px;
}


:deep(.el-input__wrapper) {
  width: 220px;
}
.edit-name {
  width: 300px;
}

.size::after {
  content: '132132132';
}

:deep(.el-breadcrumb__inner) {
  cursor: pointer;
}

.dir-name {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.el-loading-parent--relative {
  overflow: hidden;
}

:deep(.el-upload-list__item-info) {
  width: 350px;
}

.file-icon {
  height: 30px;
  max-width: 40px;
  vertical-align: middle;
  margin-right: 10px;
}

:deep(.el-dialog__body) {
  height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.preview-img {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;

  img {
    height: calc(100% - 30px);
    width: auto;
  }
}

.progress {
  width: 150px;
  position: absolute;
  bottom: 2px;
  margin-left: 45px;

  :deep(.el-progress__text) {
    font-size: 12px !important;
    color: #606266;
  }
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.file-note-new {
  position: relative;
  top: -12px;
  width: 26px;
  height: 12px;
  cursor: text;
}
</style>