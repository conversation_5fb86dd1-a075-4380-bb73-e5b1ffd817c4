<template>
  <el-dialog
    v-model="showFlag"
    :title="$t('{name}文件历史版本', { name: props.historyName })"
    width="50%"
    :before-close="handleClose"
  >
    <el-table  v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column prop="name" :label="$t('缩略图')" width="80">
        <template #default="scope">
          <FileIcon :width="40" :height="30" :item="scope.row"></FileIcon>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="name" label="历史版本名称"  /> -->
      <el-table-column prop="version" :label="$t('版本名称')"  >
        <template #default="scope">
          {{ scope.row.userTag || scope.row.version }}
        </template>
      </el-table-column>
      <el-table-column prop="lastEditor" :label="$t('修改人')"  />
      <el-table-column prop="lastModifyTime" :label="$t('修改时间')" width="160" />
      <el-table-column prop="size" :label="$t('大小')">
        <template #default="scope">
          {{ formatSize(scope.row.size) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('操作')" width="150">
        <template #default="scope">
          <el-button :disabled="!scope.row.etag || !scope.row.sid" link size="small" @click="handelClick('look', scope.row)">{{ $t('查看') }}</el-button>
          <el-button :disabled="!scope.row.etag || !scope.row.sid" link size="small" v-if="functions && functions.includes('recover') && scope.$index !== 0" @click="handelClick('recover', scope.row)">{{ $t('恢复') }}</el-button>
          <el-button :disabled="!scope.row.etag || !scope.row.sid" link size="small" v-if="functions && functions.includes('download')" @click="handelClick('download', scope.row)">{{ $t('下载') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, onMounted, withDefaults, defineEmits } from 'vue'
import { getFileHistory, downloadFileByStorageId, recoverFile } from '@/services/file'
import { downloadByUrl, escapedUrl, formatSize, isType } from '@/utils'
import type { recoverQuestData, GetFileByStorageIdQuery, FileItem } from '@/model/file'
import { useUserStore } from "@/stores/user"
import { gotoItem } from '@/utils/file'
import { $t } from '@/locales'
import { getHistoryTagList } from '@/services/record'
interface Props {
  historyId: number
  historyName: string
  historyFlag: boolean
  functions: string[]
}

interface TableItem extends FileItem{
  id: number
  version: string
  lastEditor: string
  lastModifyTime: string
  size: number
  fileType: number
  etag: string
  name: string
  verId: number
  sid: number
}

const props = withDefaults(defineProps<Props>(), {
  historyId: 0,
  historyFlag: false,
  historyName: ''
})

enum EFILETYPE {
  dir = 1,
  dwg,
  rvt,
  stl,
  zip
}

const showFlag = ref(props.historyFlag)

const tableData = ref<TableItem[]>([])

const loading = ref(false)

const userStores = useUserStore()

// 获取文件历史版本
const getFileHistoryList = async () => {
  loading.value = true
  try {
    if (isType({name: props.historyName} as FileItem, "2D")) {
      const { data } = await getHistoryTagList({status: 2, fileId: props.historyId, sign: '2'})
      tableData.value = data?.list || []
    } else {
      const res = await getFileHistory(props.historyId)
      tableData.value = res?.data || []
    }
    // const res = await getFileHistory(props.historyId)
    tableData.value = tableData.value.map((v) => {
      v.isHistory = true
      return v
    })
    loading.value = false
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}

onMounted(() => {
  getFileHistoryList()
})

const emit = defineEmits(['setHistoryClose', 'recoverSuccess'])

const handleClose = () => {
  if(loading.value) {
    return
  }
  showFlag.value = false
  emit('setHistoryClose')
}

// 下载文件请求
const downloadFileReq = async (data: GetFileByStorageIdQuery) => {
  try {
    const url = `/_st/_storage/_download?storageId=${data.storageId}&fileName=${data.fileName}&type=1`;
    const translate_url = escapedUrl(url);
    downloadByUrl(translate_url);
  } catch (error) {
    console.log(error)
  }
}

// 恢复版本
const recoverFileReq = async (data: recoverQuestData) => {
  loading.value = true
  try {
    const res = await recoverFile(data)
    loading.value = false

    if(res.code === 0) {
      emit('recoverSuccess')
      handleClose()
    } else {
      ElMessage({
      type: 'error',
      message: res.msg
    })
    }
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}

const open = (data: recoverQuestData) => {
  ElMessageBox.confirm(
    $t('您确定将图纸版本恢复到此版本吗？'),
    '',
    {
      confirmButtonText: $t('确定'),
      cancelButtonText: $t('取消'),
      type: 'warning',
    }
  )
    .then(() => {
      recoverFileReq(data)
    })
}

const handelClick = (type: string, row:TableItem) => {
  switch (type) {
    case 'look':
      gotoItem(row, null)
      handleClose()
    break;

    case 'recover':
    open({
      fileId: row.id,
      toVerId: row.verId
    })
    

    break;

    case 'download':
    
      downloadFileReq({
        fileName: row.name,
        storageId: row.sid,
        Authorization: userStores.token
      })
    break;
  }
}

</script>

<style lang='' scoped></style>