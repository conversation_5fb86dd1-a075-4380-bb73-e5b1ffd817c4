<template>
  <el-dialog :title="$t('新建文件')" v-model="dialogVisible" @close="dialogClose" :width="840" destroy-on-close>
    <div class="h-full">
      <el-form class="h-full" ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="100px" label-position="left"
        size="default" status-icon>
        <el-form-item :label="$t('文件名称')" prop="fileName">
          <el-input v-model="ruleForm.fileName" :placeholder="$t('请输入文件名称')" clearable maxlength="128" :minlength="1"
            show-word-limit :style="{ width: '50%' }"></el-input>
        </el-form-item>
        <div class="list">
          <div class="flex">
            <el-form :inline="true" :label-width="90" label-position="left">
              <el-form-item :label="$t('模板名称')" class="folder">
                <el-input v-model="search.searchName" clearable :placeholder="$t('请输入模板名称')" />
              </el-form-item>
              <el-form-item :label="$t('文件格式')" class="fileType">
                <el-select v-model="search.fileType" placeholder="Select" filterable :style="{width: '220px'}">
                  <el-option v-for="item in fileTypeList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-form>
            <el-button type="primary" @click="getTemplateList">{{ $t('查询') }}</el-button>
          </div>
          <div v-if="items.length > 0" class="block-item-container" v-loading="loading">
            <div class="block-item-wrap" v-for="item in items" :key="item.id" @click="selectItem(item)"
              :class="{ 'block-selected': item.isSelected }">
              <div class="block-top">
                <FileIcon :item="item" :isBlock="true" :size="item.thumbnail ? null : 120"
                  :class="item.thumbnail ? 'block-file-thumbnail' : 'block-file-icon'">
                </FileIcon>
                <div class="preview" @click="gotoItem(item as any, null)">{{ $t('预览模板') }}</div>
              </div>
              <div class="block-name">
                <div class="file-name" :title="item.name">
                  <div class="file-name-left">{{ getShortName(item.name) }}</div>
                  <div class="file-name-right">{{ `.${getExt(item.name)}` }}</div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-text">
            {{ $t('暂无数据') }}
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="createTemplate(ruleFormRef)">{{ $t('新建') }}</el-button>
        <el-button type="default" @click="dialogClose">{{ $t('取消') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang='ts'>
import type { FormInstance, FormRules } from 'element-plus';
import { computed, reactive, ref, watch } from 'vue';
import { getTemplateFileList, getTemplateTypeList, createFileByTemplate } from '@/services/template'
import { getExt, getShortName } from "@/utils";
import { gotoItem } from '@/utils/file';
import type { TemplateItem } from '@/model/template';
import { $t } from '@/locales';

const emits = defineEmits(['update:dialogVisible', 'createSuccess'])
interface createFileProps {
  dialogVisible: boolean
  folderId: number
}
interface Item extends TemplateItem {
  isSelected: boolean
}
const props = withDefaults(defineProps<createFileProps>(), {
  dialogVisible: false
})

const fileTypeList = ref<any>([])

const ruleFormRef = ref<FormInstance>()

const items = ref<Item[]>([]);

const loading = ref(false);

const ruleForm = reactive({
  fileName: ''
})

const search = reactive({
  searchName: '',
  fileType: 0,
})

const validateName = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error($t('请输入文件名称')))
  } else if (/[<>:*:?？：""|/\\]/g.test(value)) {
    callback(new Error($t('名称不能包含下列任何字符：\\ /:?*"<>|')))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  fileName: [
    { required: true, message: $t('请输入文件名称'), trigger: 'change' },
    { validator: validateName, trigger: 'change' }
  ]
})

const dialogVisible = computed(() => props.dialogVisible)

const selectRows = computed(() => items.value.filter(item => item.isSelected))

watch(() => props.dialogVisible, (val: boolean) => { 
  if (val) {
    getTypeList()
    getTemplateList()
  }
})

const formatSelectData = (val: Recordable, root_select = {
  label: $t('全部'),
  value: 0
}) => {
  const bas = []
  for (const [key, value] of Object.entries(val)) {
    bas.push({
      label: key,
      value
    })

  }
  bas.unshift(root_select)
  return bas
}

const dialogClose = () => {
  ruleForm.fileName = "";
  search.fileType = 0;
  search.searchName = "";
  emits('update:dialogVisible', false)
}

const createTemplate = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  if (selectRows.value.length === 0) {
    ElMessage({
      type: 'warning',
      message: $t('请选择模板'),
    })
    return;
  }
  formEl.validate(async (valid, fields) => {
    if (valid) {
      const selected = selectRows.value[0];
      const ext = getExt(selected.name);
      await createFileByTemplate({
        etag: selected.etag,
        fileName: `${ruleForm.fileName}.${ext}`,
        fileSize: selected.size,
        folderId: props.folderId,
        id: String(selected.id),
        replace: false
      })
      ElMessage({
        type: 'success',
        message: $t('创建文件成功'),
      })
      emits('createSuccess')
      dialogClose()
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 获取任务类型类型列表
const getTypeList = async () => {
  try {
    const { data } = await getTemplateTypeList();
    fileTypeList.value = formatSelectData(data.typeList)
  } catch (e) {
    console.log(e);
  }
}

const getTemplateList = async () => {
  loading.value = true
  try {
    const { data } = await getTemplateFileList({
      page: 1,
      limit: 10000,
      enable: 1,
      ...search
    })
    items.value = data.templateFileList
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false
  }
}

const limitName = (e: string) => {
  if (e) {
    ruleForm.fileName = e.replace(/[<>:*:?？：""|/\\]/g, '')
  }
}

const selectItem = (item: Item) => {
  item.isSelected = !item.isSelected;
  if (items.value) {
    items.value = items.value.map((v: Item) => {
      if (v.id !== item.id) {
        v.isSelected = false;
      }
      return v
    })
  }
}

</script>
<style lang='scss' scoped>
.list {
  display: flex;
  flex-direction: column;
  height: calc(100% - 32px);
  border: 1px solid var(--el-border-color);
  padding: 12px;
}

.block-item-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  overflow: auto;
  align-content: flex-start;
}

.block-item-wrap {
  display: inline-block;
  word-wrap: normal;
  width: 240px;
  margin-right: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    border: 0.5px solid #0069ff;

    .block-top {
      background-color: #f5f9ff;
    }
  }
}

.block-selected {
  border: 0.5px solid #0069ff !important;

  .block-top {
    background-color: #f5f9ff !important;
  }
}

.block-top {
  height: 180px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: #f7f7fb;

  &:hover {
    .preview {
      display: flex;
    }
  }
}

.block-file-thumbnail {
  height: 100%;
  width: 100%;
}

.block-file-icon {
  position: relative;
}

.preview {
  position: absolute;
  bottom: 0px;
  width: 100%;
  background: #979797;
  color: #0052cc;
  font-weight: 600;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  display: none;
}

.block-name {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 4px 16px 4px 16px
}

.file-name {
  display: inline-flex;
  width: 90%;
  line-height: 20px;
  color: #585858;
}

.file-name-left {
  text-overflow: ellipsis;
  overflow: hidden;
  min-width: 10px;
  max-width: 70%;
}

.file-name-right {
  margin-right: 4px;
  display: inline-block;
  text-overflow: ellipsis;
  max-width: 30%;
  min-width: 45px;
  overflow: hidden;
}

.empty-text {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  height: 100%;
  width: 100%;
}
</style>