<template>
  <div class="h-full">
    <DropZone @importFile="uploadFileHandle" class="h-full flex-1 list" :enableDrop="true">
      <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading"
        :getList="getListHandle" :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList"
        :showRowItemIcon="true" :quickMenuList="quickMenuList" :enableBlockView="true">
        <template #tableHeader>
          <el-upload class="upload-button" ref="uploadRef" v-model:file-list="fileList" action="" :accept="limitTypes"
            :show-file-list="false" :auto-upload="true" id="upload-dom" :multiple="false" :limit="1"
            :on-exceed="handleExceed" :http-request="uploadTemplateReq" :key="reloadUpload" :before-upload="beforeUpload">
            <el-button type="primary" :title="$t('上传模板')">
              <span class="btn-text">{{ $t('上传模板') }}</span>
            </el-button>
          </el-upload>
        </template>
        <template #enable="{ row }">
          <el-switch v-model="row.enable" :active-value="1" :inactive-value="0" style="--el-switch-on-color: #13ce66"
            :before-change="() => beforeSwitchChange(row)" />
        </template>
        <template #action="{ row }">
          <div class="layout-action">
            <span class="action-text" @click="view(row)" v-if="hasPermission(row, 'view')" :title="$t('查看')">{{ $t('查看') }}</span>
            <span class="action-text" @click="download(row)" v-if="hasPermission(row, 'download')" :title="$t('下载')">{{ $t('下载') }}</span>
            <span class="action-text" @click="del([row])" v-if="hasPermission(row, 'delete')" :title="$t('删除')">{{ $t('删除') }}</span>
          </div>
        </template>
        <template #menuExtra="{ item }">
          <el-switch @click.prevent.stop v-model="item.enable" :active-value="1" :inactive-value="0" style="--el-switch-on-color: #13ce66"
            :before-change="() => beforeSwitchChange(item)" />
        </template>
      </GList>
    </DropZone>
  </div>
</template>

<script lang="ts" setup name="filesFile">
import { onBeforeMount, ref } from 'vue'
import type { UploadUserFile, UploadProps, UploadRawFile, UploadInstance } from 'element-plus'
import { genFileId } from 'element-plus'
import { getTemplateFileList, setTemplateEnable, uploadTemplateFile, getTemplateTypeList } from '@/services/template'
import { downloadFile } from '@/services/file'
import { getFileSha1, formatSize, getExt } from '@/utils'
import { gotoItem } from '@/utils/file';
import { deleteFile } from '@/services/file'
import type {DeleteFileQuery, FileItem } from '@/model/file'
import type { SortChange, SortFieIdKey, SortKey, TableColumn, TableDataParams, TablePagination, TableSearchType } from '@/model/table'
import type { TemplateItem } from '@/model/template'
import UploadManager from "@/utils/UploadManager"
import { useUserStore } from "@/stores/user";
import DownloadManager from '@/utils/DownloadManager'
import { $t } from '@/locales';

const userStores = useUserStore();

// 定义排序查询的字段值
const sortFieldKey = {
  lastModifyTime: 1,
  size: 2,
}

const refresh = ref(Date.now())
const folderId = ref(9)
const sortField = ref(sortFieldKey.lastModifyTime)

const ascOrDesc = ref('desc')
// 上传dom
const uploadRef = ref<UploadInstance>()

// 文件列表
const fileList = ref<UploadUserFile[]>()
const searchList = ref<TableSearchType[]>([]);
// 上传loading
const loading = ref(false)

// 文件后缀限制
const limitTypes = ref('');

// 重新渲染上传组件
const reloadUpload = ref(Math.random())



const columns = [{
  type: "selection",
  align: 'center',
  width: "55"
}, {
  label: $t('模板名称'),
  property: 'name',
  showOverflowTooltip: true,
}, {
  label: $t('文件大小'),
  property: 'size',
  format: (row: any, column: any, cellValue: any, index: any) => { return formatSize(cellValue) || "" }

}, {
  label: $t('启用'),
  slotName: 'enable',
  property: 'enable',
}, {
  label: $t('创建者'),
  property: 'ownerName',
}, {
  label: $t('创建时间'),
  property: 'createTime',
  width: "160",
}, {
  label: $t('操作'),
  slotName: 'action',
  fixed: 'right',
  width: "220",
}] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
  key: 'batchDelete',
  label: $t('删除'),
  click: (items: FontTableItem[]) => deleteFiles(items)
}]

const quickMenuList = [{
  label: $t('查看'),
  key: 'view',
  icon: 'View',
  click: (item: any) => view(item)
}, {
  label: $t('下载'),
  key: 'download',
  icon: 'Download',
  click: (item: any) => download(item)
}, {
  label: $t('删除'),
  key: 'delete',
  icon: 'Delete',
  click: (item: FontTableItem) => deleteFiles([item])
}]

const formatSelectData = (val: Recordable, root_select = {
  label: $t('全部'),
  value: 0
}) => {
  const bas = []
  for (const [key, value] of Object.entries(val)) {
    bas.push({
      label: key,
      value
    })

  }
  bas.unshift(root_select)
  return bas
}

// 获取任务类型类型列表
const getTypeList = async () => {
  try {
    const { data } = await getTemplateTypeList();
    if (data.typeList) {
      limitTypes.value = Object.keys(data.typeList).map(v => `.${v}`).toString()
    }
    return formatSelectData(data.typeList)
  } catch (e) {
    console.log(e);
    return []
  }
}

onBeforeMount(async () => {
  const fileTypeList = await getTypeList()
  searchList.value = [{
    key: 'searchName',
    type: 'input',
    name: $t('模板名称'),
    placeholder: $t('请输入')
  }, {
    key: 'fileType',
    type: 'select',
    name: $t('文件格式'),
    placeholder: $t('请选择'),
    options: fileTypeList as []
  }, {
    key: 'status',
    type: 'select',
    name: $t('启用状态'),
    options: [
      {
        label: $t('全部'),
        value: 2,
      }, {
        value: 1,
        label: $t('启用')
      },
      {
        value: 0,
        label: $t('未启用')
      }
    ] as any
  }]
})

// 定义修改时间文件排序字段值
const sortKey: SortKey = {
  descending: 'desc',
  ascending: 'asc',
  default: ''
}

// 监听表格排序
const sortChange = (data: SortChange) => {
  ascOrDesc.value = sortKey[data.order as keyof SortKey]
  sortField.value = sortFieldKey[data.prop as keyof SortFieIdKey]
  refresh.value = Date.now();
}

const beforeSwitchChange = async (val: TemplateItem) => {
  const title = !val.enable ? $t('启用') : $t('禁用');
  return new Promise<boolean>((resolve) => {
    ElMessageBox.alert(
      $t('你确定要{action}{name}模板吗', { action: title, name: val.name || $t("这个") }),
      title,
      {
        confirmButtonText: $t("确认"),
        cancelButtonText: $t("取消"),
        type: "warning",
      }
    )
      .then(async () => {
        try {
          await setTemplateEnable({
            enable: val.enable === 1 ? 0 : 1,
            fileId: val.id
          });
          ElMessage({
            type: "success",
            message: $t('{action}成功', { action: title }),
          });
          resolve(true);
        } catch (e) {
          console.log(e);
          resolve(false);
        }
      })
      .catch(() => {
        resolve(false);
      })
      .finally(() => {
        refresh.value = Date.now();
      });
  });
}

const getListHandle = async (params: TableDataParams) => {
  const { page, limit, search } = params;
  loading.value = true;
  let req = {
    page,
    limit,
  } as any
  if (search) {
    req = {
      ...req,
      searchName: search.searchName,
      fileType: search.fileType,
      enable: search.status,
    }
  }
  try {
    const { data } = await getTemplateFileList(req)
    return {
      list: data.templateFileList,
      total: data.totalTemplateNums,
    }
  } catch (e) {
    return {
      list: [],
      total: 0,
    }
  } finally {
    loading.value = false;
  }
}

const hasPermission = (val: TemplateItem, action: string) => {
  return val.specialFunctions && val.specialFunctions.includes(action)
}

const view = (val: FileItem) => {
  val.hideFunction = true
  gotoItem(val, null)
}

const download = async (val: FileItem) => {
  try {
    const downloadManager = await new DownloadManager({fileId: val.id, fileName: val.name, token: userStores.token})
    await downloadManager.start()

    // await downloadFile({
    //   fileName: val.name,
    //   fileId: val.id,
    // })
  } catch (error) {
    console.log(error)
  }
}

const del = async (selectRows: Recordable[] = []) => {
  const fileNames = selectRows.map(v => v.name)
  const text = fileNames.length > 1 ? $t('这{count}个模板', { count: fileNames.length }) : $t('{name}模板', { name: fileNames[0] })
  ElMessageBox.alert(
    $t('你确定要删除{text}吗', { text }),
    $t("删除"),
    {
      confirmButtonText: $t("确认"),
      cancelButtonText: $t("取消"),
      type: "warning",
    }
  ).then(async () => {
    const obj: DeleteFileQuery = {
      fileNameArr: fileNames,
      fileIdArr: selectRows.map(v => v.id),
      folderId: folderId.value
    }
    try {
      await deleteFile(obj)
      ElMessage({
        type: 'success',
        message: $t('删除成功'),
      })
      refresh.value = Date.now();
    } catch (error) {
      console.log(error)
    }
  });
}

// 表格的每一条数据类型
type FontTableItem = {
  id: number
  name: string
  fileType: number
  ownerName: string
  size: number
  lastModifyTime: string
  createTime: string
}

// 限制上传文件数量
const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const ext = getExt(rawFile.name)
  const types = limitTypes.value.split(',').map(v => {
    return v.slice(1)
  })
  if (!types.includes(ext)) {
    ElMessage({
      type: 'error',
      message: $t('模板文件格式只包括({types})', { types: limitTypes.value }),
    })
    return false;
  } else return true
}

const uploadFileHandle = (files: File[]) => {
  const uploadFiles = files.map((file) => {
    return {
      raw: file,
      name: file.name,
      size: file.size,
      type: file.type
    }
  }) as any
  if (beforeUpload(files[0] as UploadRawFile)) {
    fileList.value = uploadFiles
    uploadTemplateReq()
  }
}

// 上传字体请求
const uploadTemplateReq = async () => {
  if (fileList.value!.length > 0 && fileList.value![0]) {
    try {
      loading.value = true

      const upload = new UploadManager({
        file: fileList.value![0].raw || '',
        type: 12,
        folderId: folderId.value,
        replace: false,
        path: "",
        token: userStores.token
      })
       
      await upload.start();

      // const obj = {
      //   etag: await getFileSha1(fileList.value![0].raw as UploadRawFile),
      //   replace: false,
      //   file: fileList.value![0].raw || ''
      // }
      // await uploadTemplateFile(obj)
      fileList.value = []
      reloadUpload.value = Math.random()
      ElMessage({
        type: 'success',
        message: $t('上传成功'),
      })
      refresh.value = Date.now();
    } catch (error) {
      loading.value = false
      fileList.value = []
      reloadUpload.value = Math.random()
      console.log(error)
    }
  }

}

// 删除文件模板
const deleteFiles = async (selectRows: Recordable[] = []) => {
  const fileNames = selectRows.map(v => v.name)
  const text = fileNames.length > 1 ? $t('这{count}个模板', { count: fileNames.length }) : $t('{name}模板', { name: fileNames[0] })
  ElMessageBox.alert(
    $t('你确定要删除{text}吗', { text }),
    $t("删除"),
    {
      confirmButtonText: $t("确认"),
      cancelButtonText: $t("取消"),
      type: "warning",
    }
  ).then(async () => {
    const obj: DeleteFileQuery = {
      fileNameArr: fileNames,
      fileIdArr: selectRows.map(v => v.id),
      folderId: folderId.value
    }
    try {
      await deleteFile(obj)
      ElMessage({
        type: 'success',
        message: $t('删除成功'),
      })
      refresh.value = Date.now();
    } catch (error) {
      console.log(error)
    }
  });
}

</script>

<style lang="scss" scoped>
.list {
  height: 100%;
}

// 上传按钮样式由全局统一管理
.upload-button {
  margin: 0; // 由父容器的gap统一管理间距
}

:deep(.el-upload-dragger) {
  padding: 0;
  vertical-align: middle;
}

:deep(.el-input__wrapper) {
  width: 220px;
}
</style>