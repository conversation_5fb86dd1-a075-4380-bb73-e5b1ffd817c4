<template>
  <div class="h-full">
    <div class="p-6 mb-2 bg-white">
      <TipsModel>
        <template v-slot>
          <p>{{ $t('支持上传字体文件') }}</p>
          <p>{{ $t('上传字体格式包括：ttf,ttc,otf,shx') }}</p>
        </template>
      </TipsModel>
    </div>
    <DropZone @importFile="uploadFileHandle" class="h-full flex-1 list" :enableDrop="true">
      <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading"
        :getList="getListHandle" :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList"
        :sortChange="sortChange" :showRowItemIcon="true" :quickMenuList="quickMenuList" :enableBlockView="true">
        <template #tableHeader>
          <el-upload class="upload-button" ref="uploadRef" v-model:file-list="fileList" action="" :accept="limitTypes"
            :show-file-list="false" :auto-upload="true" id="upload-dom" :multiple="false" :limit="1"
            :on-exceed="handleExceed" :http-request="uploadFontReq" :key="reloadUpload" :before-upload="beforeUpload">
            <el-button type="primary" :title="$t('上传字体')">
              <span class="btn-text">{{ $t('上传字体') }}</span>
            </el-button>
          </el-upload>
        </template>
        <template #action="{ row }">
          <MenuList :menuList="quickMenuList" :items="[row]" :isQuick="true"></MenuList>
        </template>
      </GList>
    </DropZone>
  </div>
</template>

<script lang="ts" setup name="filesFile">
import { ref } from 'vue'
import type { UploadUserFile, UploadProps, UploadRawFile, UploadInstance } from 'element-plus'
import { genFileId } from 'element-plus'
import { getFontList, uploadFont, downloadFontFile, downloadFile } from '@/services/file'
import { getFileSha1, formatSize, downloadByData, getExt } from '@/utils'
import { $t } from '@/locales'
import UploadManager from "@/utils/UploadManager"
import { deleteFile } from '@/services/file'
import { FILETYPE, type DeleteFileQuery, type GetFontListQuery } from '@/model/file'
import type { SortChange, SortFieIdKey, SortKey, TableColumn, TableDataParams, TablePagination } from '@/model/table'
import { useUserStore } from "@/stores/user";
import DownloadManager from '@/utils/DownloadManager'


// 定义排序查询的字段值
const sortFieldKey = {
  lastModifyTime: 1,
  size: 2,
}
const userStores = useUserStore();

const refresh = ref(Date.now())
const folderId = ref(7)
const sortField = ref(sortFieldKey.lastModifyTime)
const ascOrDesc = ref('desc')
// 上传dom
const uploadRef = ref<UploadInstance>()

// 文件列表
const fileList = ref<UploadUserFile[]>()

// 上传loading
const loading = ref(false)

// 文件后缀限制
const limitTypes = '.ttf,.ttc,.otf,.shx'

// 重新渲染上传组件
const reloadUpload = ref(Math.random())
const columns = [{
  type: "selection",
  align: 'center',
  width: "55"
}, {
  label: $t('字体文件名'),
  property: 'name',
  showOverflowTooltip: true,
}, {
  label: $t('特色功能'),
  property: 'action',
  slotName: 'action',
  width: "110"
}, {
  label: $t('字体类型'),
  property: 'fileType',
  format: (row: any, column: any, cellValue: any, index: any) => { return filterFileType(cellValue) || "" }
}, {
  label: $t('创建者'),
  property: 'ownerName',
}, {
  label: $t('文件大小'),
  property: 'size',
  sortable: 'custom',
  format: (row: any, column: any, cellValue: any, index: any) => { return formatSize(cellValue) || "" }

}, {
  label: $t('修改时间'),
  property: 'lastModifyTime',
  sortable: 'custom',
  width: "160",
}, {
  label: $t('创建时间'),
  property: 'createTime',
  width: "160",
},] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
  key: 'batchDelete',
  label: $t('删除'),
  click: (items: FontTableItem[]) => deleteFiles(items)
}]

const quickMenuList = [{
  label: $t('删除'),
  key: 'delete',
  icon: 'Delete',
  click: (item: FontTableItem) => deleteFiles([item])
}, {
  label: $t('下载'),
  key: 'download',
  icon: 'Download',
  click: (item: FontTableItem) => downloadFont(item.name, item.id)
}]

// 文件类型
const fileTypeList = [
  {
    label: $t('全部'),
    value: 0,
  }, {
    value: FILETYPE.TTF,
    label: 'ttf'
  },
  {
    value: FILETYPE.TTC,
    label: 'ttc'
  },
  {
    value: FILETYPE.OTF,
    label: 'otf'
  },
  {
    value: FILETYPE.SHX,
    label: 'shx'
  }
]

// 搜索栏配置
const searchList = [{
  key: 'searchName',
  type: 'input',
  name: $t('字体名称'),
  placeholder: $t('请输入')
}, {
  key: 'fileType',
  type: 'select',
  name: $t('文件格式'),
  placeholder: $t('请选择'),
  options: fileTypeList
}, {
  key: 'rangeTime',
  type: 'dataPicker',
  name: $t('修改时间'),
}]

// 定义修改时间文件排序字段值
const sortKey: SortKey = {
  descending: 'desc',
  ascending: 'asc',
  default: ''
}

// 监听表格排序
const sortChange = (data: SortChange) => {
  ascOrDesc.value = sortKey[data.order as keyof SortKey]
  sortField.value = sortFieldKey[data.prop as keyof SortFieIdKey]
  refresh.value = Date.now();
}

const getListHandle = async (params: TableDataParams) => {
  const { page, limit, search } = params;
  loading.value = true;
  let req = {
    page,
    limit,
    folderId: folderId.value,
    ascOrDesc: ascOrDesc.value,
    sortField: sortField.value
  } as GetFontListQuery
  if (search) {
    req = {
      ...req,
      searchName: search.searchName,
      fileType: search.fileType,
      searchBeginTime: search.rangeTime && search.rangeTime[0],
      searchEndTime: search.rangeTime && search.rangeTime[1]
    }
  }
  try {
    const { data } = await getFontList(req)
    return {
      list: data.fontFileList,
      total: data.totalFontNums,
    }
  } catch (e) {
    return {
      list: [],
      total: 0,
    }
  } finally {
    loading.value = false;
  }
}

// 表格的每一条数据类型
type FontTableItem = {
  id: number
  name: string
  fileType: number
  ownerName: string
  size: number
  lastModifyTime: string
  createTime: string
}

// 过滤文件类型
const filterFileType = (val: number) => {
  const arr = fileTypeList.filter(v => v.value === val)

  return arr.length ? arr[0].label : ''
}

// 限制上传文件数量
const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const ext = getExt(rawFile.name)
  const types = limitTypes.split(',').map(v => {
    return v.slice(1)
  })
  if (!types.includes(ext)) {
    ElMessage({
      type: 'error',
      message: $t('字体文件格式只包括(ttf,ttc,otf,shx)'),
    })
    return false;
  } else return true
}

const uploadFileHandle = (files: File[]) => {
  const uploadFiles = files.map((file) => {
    return {
      raw: file,
      name: file.name,
      size: file.size,
      type: file.type
    }
  }) as any
  if (beforeUpload(files[0] as UploadRawFile)) {
    fileList.value = uploadFiles
    uploadFontReq()
  }
}

// 上传字体请求
const uploadFontReq = async () => {
  if (fileList.value!.length > 0 && fileList.value![0]) {
    try {
      loading.value = true
      // const obj = {
      //   folderId: folderId.value,
      //   etag: await getFileSha1(fileList.value![0].raw as UploadRawFile),
      //   replace: false,
      //   file: fileList.value![0].raw || ''
      // }
      // await uploadFont(obj)

      const upload = new UploadManager({
        file: fileList.value![0].raw || '',
        type: 1,
        folderId: folderId.value,
        replace: false,
        path: "",
        token: userStores.token
      })
       
      await upload.start();

      fileList.value = []
      reloadUpload.value = Math.random()
      ElMessage({
        type: 'success',
        message: $t('上传成功'),
      })
      refresh.value = Date.now();
    } catch (error) {
      loading.value = false
      fileList.value = []
      reloadUpload.value = Math.random()
      console.log(error)
    }
  }

}

// 下载字体
const downloadFont = async (name: string, id: number) => {
  // DownloadManager
  const downloadManager = await new DownloadManager({fileId: id, fileName: name, token: userStores.token})
  await downloadManager.start()

  // downloadFile({fileName: name, fileId: id})

  // const data = await downloadFontFile(name, id);
  // downloadByData(data, name)
}

// 删除字体
const deleteFiles = async (selectRows: Recordable[] = []) => {
  const fileNames = selectRows.map(v => v.name)
  const text = fileNames.length > 1 ? $t('这{count}个字体', { count: fileNames.length }) : $t('{name}字体', { name: fileNames[0] })
  ElMessageBox.alert(
    $t('你确定要删除{text}吗?', { text }),
    $t('删除'),
    {
      confirmButtonText: $t('确认'),
      cancelButtonText: $t('取消'),
      type: "warning",
    }
  ).then(async () => {
    const obj: DeleteFileQuery = {
      fileNameArr: fileNames,
      fileIdArr: selectRows.map(v => v.id),
      folderId: folderId.value
    }
    try {
      await deleteFile(obj)
      ElMessage({
        type: 'success',
        message: $t('删除成功'),
      })
      refresh.value = Date.now();
    } catch (error) {
      console.log(error)
    }
  });
}

</script>

<style lang="scss" scoped>
.list {
  height: calc(100% - 114px - 8px);
}

.upload-button {
  display: inline-block;
  margin-right: 12px;
  vertical-align: middle;
}

:deep(.el-upload-dragger) {
  padding: 0;
  vertical-align: middle;
}

:deep(.el-input__wrapper) {
  width: 220px;
}
</style>