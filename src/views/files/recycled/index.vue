<template>
  <div class="h-full">
    <div class="p-6 mb-2 bg-white">
      <TipsModel>
        <template v-slot>{{ $t('回收站为你保存 {days} 天内删除的文件，支持原路径还原文件。', { days: remainTime }) }}</template>
      </TipsModel>
    </div>
    <div class="list">
      <GList :columns="columns" :loading="loading" :getList="getListHandle" :refresh="refresh" :sortChange="sortChange"
        :showRowItemIcon="true" :quickMenuList="quickMenuList" :enableBlockView="true" :showFlipPage="true"
        @handleSelectionChange="handleSelectionChange" @goto="gotoFile">
        <template #tableHeader>
          <el-button :disabled="multipleSelection.length === 0" type="primary"
            @click.stop="deleteBatchFile" :title="$t('永久删除')">
            <span class="btn-text">{{ $t('永久删除') }}</span>
          </el-button>
          <el-button :disabled="multipleSelection.length === 0" @click.stop="restoreBatch" :title="$t('还原')">
            <span class="btn-text">{{ $t('还原') }}</span>
          </el-button>
        </template>
        <template #name="{ row }">
          <div class="flex items-center">
            <FileIcon :width="40" :height="30" :item="row"></FileIcon>
            <div class="ml-2 table-cell-filename" v-ellipsis="row.name">{{ row.name }}</div>
            <div v-if="row.noteHaveAlter || row.fileHaveAlter" class="file-note-new">
              <img v-if="row.noteHaveAlter" src="/assets/imgs/note-new.png">
              <img v-if="row.fileHaveAlter" src="/assets/imgs/file-new.png">
            </div>
          </div>
        </template>
        <template #action="{ row }">
          <MenuList :menuList="quickMenuList" :items="[row]" :isQuick="true"></MenuList>
        </template>
        <template #remainTime="{ row }">
          <span :class="getClass(row)"> {{ row.remainTime }}</span>
        </template>
        <template #pageChange>
          <Pagination @nextPage="nextPage" @changePage="changePage" :pageSize="pageSize" :currPage="currPage" @previousPage="previousPage" :key="reload" :nextFlag="nextFlag"
            :previousFlag="previousFlag" @size-change="handleSizeChange" :showTotal="false"> </Pagination>
        </template>
      </GList>
    </div>
    <el-dialog v-model="backFlag" :title="$t('重名文件')" width="500px" :modal="false" :close-on-click-modal="false"
      :before-close="handleClose">
      <el-table :data="isReplaceTable">
        <el-table-column property="fileName" :label="$t('重名文件文件名')" />
      </el-table>
      <p class="notice">{{ $t('注：已选择的文件还原目标目录存在同名文件，点击确定后将以当前文件创建最新版本') }}</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose" :title="$t('取消')">
            <span class="btn-text">{{ $t('取消') }}</span>
          </el-button>
          <el-button type="primary" @click="handelConfirm" :title="$t('确认')">
            <span class="btn-text">{{ $t('确认') }}</span>
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import type { ElTable } from 'element-plus'
import type {
  RecycledTableItem,
  GetRecycledListQuery,
  SortKey,
  SortChange,
  RecycleSortFieIdKey,
  fileInfoItem
} from '@/model/file'
import { getRecycledList, deleteRecycledBatch, restoreRecycledBatch } from '@/services/file'
import { formatSize } from '@/utils'
import Pagination from '@/components/Pagination.vue'
import { getErrorMessage } from '@/utils/http-message';
import { $t } from '@/locales';

const loading = ref(false)
const refresh = ref(Date.now())
// 每页数量
const pageSize = ref(10)

const columns = [{
  type: "selection",
  align: 'center',
  width: "55"
}, {
  label: $t('名称'),
  property: 'name',
  slotName: 'name',
  showOverflowTooltip: true,
}, {
  label: $t('特色功能'),
  property: 'action',
  slotName: 'action',
}, {
  label: $t('删除时间'),
  property: 'deleteTime',
  sortable: 'custom',
  width: "160",
}, {
  label: $t('文件大小'),
  property: 'size',
  sortable: 'custom',
  format: (row: any, column: any, cellValue: any, index: any) => { return row.fileType !== 1 ? formatSize(row.size) : '' }

}, {
  label: $t('保留天数'),
  property: 'remainTime',
  slotName: 'remainTime',
}]


const quickMenuList = [{
  label: $t('删除'),
  key: 'delete',
  icon: 'Delete',
  click: (item: RecycledTableItem) => handelClick('delete', item)
}, {
  label: $t('还原'),
  key: 'recover',
  icon: 'RefreshLeft',
  click: (item: RecycledTableItem) => handelClick('restore', item)
}]

// 定义上一下 下一页初始数据
const nextFlag = ref(true)
const previousFlag = ref(true)
const currPage = ref(0)

// 定义保留天数的初始数据
const remainTime = ref(0)
const reload = ref(Math.random())

const backFlag = ref(false)

const isReplaceTable = ref<fileInfoItem[]>([])

// 下一页
function nextPage() {
  currPage.value ++
  getRecycledListQuery.reqPage = currPage.value
  getRecycledListQuery.reqPageIdArr = tableData.value.map(v => v.id).join()

  getRecycledListQuery.direction = 0
  getRecycledListQuery.lastFileId = tableData.value.length > 0 ? tableData.value[tableData.value.length - 1].id : 0
  getRecycledListQuery.lastFileSize = tableData.value.length > 0 ? tableData.value[tableData.value.length - 1].size : 0
  getRecycledListQuery.lastFileDeleteTime = tableData.value.length > 0 ? tableData.value[tableData.value.length - 1].deleteTime : ''
  refresh.value = Date.now();
}

// 上一页
function previousPage() {
  if(currPage.value > 1){
    currPage.value --
  } else {
    currPage.value = 1
  }
  getRecycledListQuery.reqPage = currPage.value
  getRecycledListQuery.reqPageIdArr = tableData.value.map(v => v.id).join()

  getRecycledListQuery.direction = 1
  getRecycledListQuery.lastFileId = tableData.value.length > 0 ? tableData.value[0].id : 0
  getRecycledListQuery.lastFileSize = tableData.value.length > 0 ? tableData.value[0].size : 0
  getRecycledListQuery.lastFileDeleteTime = tableData.value.length > 0 ? tableData.value[0].deleteTime : ''
  refresh.value = Date.now();
}

function handleSizeChange(val: number) {
  console.log(`${val} items per page`)
  pageSize.value = val;
  getRecycledListQuery.limit = val
  getRecycledListQuery.sortField = 0,
  getRecycledListQuery.ascOrDesc = 'desc',
  getRecycledListQuery.lastFileId = 0,
  getRecycledListQuery.lastFileSize = 0,
  getRecycledListQuery.lastFileDeleteTime = '',
  getRecycledListQuery.reqPage = 1,
  getRecycledListQuery.reqPageIdArr = ''
  refresh.value = Date.now();
}

function changePage(pageKey:string) {
  getRecycledListQuery.reqPage = parseInt(pageKey)
  getRecycledListQuery.reqPageIdArr = tableData.value.map(v => v.id).join()
  refresh.value = Date.now();
}

// 定义选择数据类型
const multipleSelection = ref<RecycledTableItem[]>([])

// 选择数据方法
const handleSelectionChange = (val: RecycledTableItem[]) => {
  multipleSelection.value = val
}

// 表格数据
const tableData = ref<RecycledTableItem[]>([])

// 定义排序查询的字段值
const sortField = {
  deleteTime: 1,
  size: 2
}

// 定义修改时间文件排序字段值
const sortKey: SortKey = {
  descending: 'desc',
  ascending: 'asc',
  default: ''
}


// 定义列表请求参数
const getRecycledListQuery: GetRecycledListQuery = reactive({
  direction: 0,
  limit: pageSize.value,
  sortField: 0,
  ascOrDesc: 'desc',
  lastFileId: 0,
  lastFileSize: 0,
  lastFileDeleteTime: '',
  reqPage: 1,
  reqPageIdArr: ''
})


const gotoFile = () => {
  ElMessage({
      type: 'warning',
      message: $t('该文件已被删除，无法浏览,请还原后查看'),
    })
}

// 监听表格排序
const sortChange = (data: SortChange) => {
  getRecycledListQuery.ascOrDesc = sortKey[data.order as keyof SortKey]
  getRecycledListQuery.sortField = sortField[data.prop as keyof RecycleSortFieIdKey]
  refresh.value = Date.now();
}

const getListHandle = async () => {
  loading.value = true;
  try {
    const { data } = await getRecycledList(getRecycledListQuery)
    nextFlag.value = data.lastPage
    previousFlag.value = data.firstPage
    remainTime.value = data.remainDays
    reload.value = Math.random()
    tableData.value = data.recycleBinList
    return {
      list: data.recycleBinList,
      total: data.total,
    }
  } catch (e) {
    console.log(e)
    reload.value = Math.random()
    return {
      list: [],
      total: 0,
    }
  } finally {
    loading.value = false
  }
}

// 批量删除
const deleteBatch = async (fileIdArr: number[]) => {
  try {
    if (fileIdArr.length) {
      await deleteRecycledBatch({ fileIdArr })
    } else {
      const fileIdArr = multipleSelection.value.map(v => v.id)
      await deleteRecycledBatch({ fileIdArr })
    }
    ElMessage({
      type: 'success',
      message: $t('删除成功'),
    })
    refresh.value = Date.now();
  } catch (error) {
    ElMessage({
      type: 'error',
      message: $t('删除失败'),
    })
    console.log(error)
  }
}

// 确定要批量删除？
const deleteBatchFile = () => {
  ElMessageBox.alert(
    $t('确定要批量删除选择的文件吗'),
    $t('删除文件'),
    {
      confirmButtonText: $t('确认'),
      cancelButtonText: $t('取消'),
      type: 'warning',
    }
  )
    .then(() => {
      deleteBatch([])
    })
}



// 批量还原
const restoreBatchFile = async (fileIdArr: fileInfoItem[]) => {
  try {
    let arr = []
    if (fileIdArr.length) {
      arr = fileIdArr
    } else {
      arr = multipleSelection.value.map(v => {
        return {
          fileId: v.id,
          fileName: v.name
        }
      })
    }
    await restoreRecycledBatch({ fileInfoArr: arr })
    backFlag.value = false
    isReplaceTable.value = []
    ElMessage({
      type: 'success',
      message: $t('还原成功'),
    })
    refresh.value = Date.now();
  } catch (error: any) {
    if (error?.code === 400602) {
      backFlag.value = true
      isReplaceTable.value = error.data?.needReplaceList || []
    } else {
      const errorMessage = getErrorMessage(error);
      ElMessage.error(errorMessage);
    }
  }
}

// 确定要批量还原？
const restoreBatch = () => {
  ElMessageBox.alert(
    $t('确定要批量还原选择的文件吗'),
    $t('还原文件'),
    {
      confirmButtonText: $t('确认'),
      cancelButtonText: $t('取消'),
      type: 'warning',
    }
  )
    .then(() => {
      restoreBatchFile([])
    })
}

function handelClick(type: string, row: RecycledTableItem) {
  switch (type) {
    case 'delete':
      ElMessageBox.alert(
        $t('确定要删除 {name} 吗', { name: row.name }),
        $t('删除文件'),
        {
          confirmButtonText: $t('确认'),
          cancelButtonText: $t('取消'),
          type: 'warning',
        }
      )
        .then(() => {
          deleteBatch([row.id])
        })
      break;

    case 'restore':
      ElMessageBox.alert(
        $t('确定要还原 {name} 吗', { name: row.name }),
        $t('还原文件'),
        {
          confirmButtonText: $t('确认'),
          cancelButtonText: $t('取消'),
          type: 'warning',
        }
      )
        .then(() => {
          restoreBatchFile([{ fileId: row.id, fileName: row.name }])
        })
      break;

  }
}

const handleClose = () => {
  backFlag.value = false
  isReplaceTable.value = []
}

const handelConfirm = () => {
  const arr = isReplaceTable.value.map(v => {
    return {
      fileId: v.fileId,
      fileName: v.fileName,
      replace: true
    }
  })
  restoreBatchFile(arr)
}

const getClass = (item: RecycledTableItem) => {
    if (item.deleteTime) {
        const interval = new Date(item.deleteTime).getTime() + 10 * 24 * 60 * 60 * 1000 - new Date().getTime()
        if (interval <= (7 * 24 * 60 * 60 * 1000)) return 'expire'
    }
    return ''
}

</script>

<style lang="scss" scoped>
.list {
  height: calc(100% - 96px - 6px);
}

.upload-button {
  display: inline-block;
  margin-right: 12px;
}

.folder {
  :deep(.el-form-item__label) {
    width: 100px !important;
  }
}

.notice {
  font-size: 12px;
  margin-top: 20px;
}
.expire {
    color: red;
}
.file-note-new {
  position: relative;
  top: -12px;
  width: 26px;
  height: 12px;
  cursor: text;
}
</style>