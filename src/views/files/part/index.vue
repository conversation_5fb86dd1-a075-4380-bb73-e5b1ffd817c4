<template>
  <div class="h-full">
    <DropZone @importFile="uploadFileHandle" class="h-full flex-1 list" :enableDrop="true">
      <GList ref="listRef" :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading"
        :getList="getListHandle" :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList"
        :sortChange="sortChange" :showRowItemIcon="true" :quickMenuList="quickMenuList" :enableBlockView="true">
        <template #tableHeader>
          <UploadFile class="upload-button" :key="reloadUploadFile" buttonType="primary"
          :accept="limitTypes" :folderId="folderId" :buttonTitle="$t('上传零件')" @changeFiles="uploadFontReq">
          </UploadFile>
        </template>
        <template #action="{ row }">
          <MenuList :menuList="quickMenuList" :items="[row]" :isQuick="true"></MenuList>
        </template>
      </GList>
    </DropZone>
  </div>
</template>

<script lang="ts" setup name="filesFile">
import { ref, onUnmounted, onMounted } from 'vue'
import { getPartList, uploadPart, downloadFontFile, downloadFile } from '@/services/file'
import { formatSize, downloadByData, getExt } from '@/utils'
import { deleteFile } from '@/services/file'
import type { DeleteFileQuery, GetFontListQuery } from '@/model/file'
import type { SortChange, SortFieIdKey, SortKey, TableColumn, TableDataParams, TablePagination } from '@/model/table'
import { useFileType } from '@/hooks/useFileType'
import { useUpload } from '@/hooks/useUpload'
import socket from '@/utils/socket';

import DownloadManager from '@/utils/DownloadManager'
import { useUserStore } from "@/stores/user";
import { $t } from '@/locales';

const userStores = useUserStore();

// 定义排序查询的字段值
const sortFieldKey = {
  lastModifyTime: 1,
  size: 2,
}

const refresh = ref(Date.now())
const folderId = ref(10)
const sortField = ref(sortFieldKey.lastModifyTime)
const ascOrDesc = ref('desc')

const listRef = ref<any>(null)

const { fileTypes, limitTypes } = useFileType()

// 重新加载上传组件
const reloadUploadFile = ref(Math.random())

// 上传loading
const loading = ref(false)

const columns = [{
  type: "selection",
  align: 'center',
  width: "55"
}, {
  label: $t('零件文件名'),
  property: 'name',
  showOverflowTooltip: true,
}, {
  label: $t('特色功能'),
  property: 'action',
  slotName: 'action',
  width: "110"
}, {
  label: $t('零件类型'),
  property: 'fileType',
  format: (row: any, column: any, cellValue: any, index: any) => { return (fileTypes.value && fileTypes.value[cellValue]) ?? getExt(row.name) }
}, {
  label: $t('创建者'),
  property: 'ownerName',
}, {
  label: $t('文件大小'),
  property: 'size',
  sortable: 'custom',
  format: (row: any, column: any, cellValue: any, index: any) => { return formatSize(cellValue) || "" }

}, {
  label: $t('修改时间'),
  property: 'lastModifyTime',
  sortable: 'custom',
  width: "160",
}, {
  label: $t('创建时间'),
  property: 'createTime',
  width: "160",
},] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
  key: 'batchDelete',
  label: $t('删除'),
  click: (items: FontTableItem[]) => deleteFiles(items)
}]

const quickMenuList = [{
  label: $t('删除'),
  key: 'delete',
  icon: 'Delete',
  click: (item: FontTableItem) => deleteFiles([item])
}, {
  label: $t('下载'),
  key: 'download',
  icon: 'Download',
  click: (item: FontTableItem) => downloadFont(item.name, item.id)
}]

// 搜索栏配置
const searchList = [{
  key: 'searchName',
  type: 'input',
  name: $t('零件名称'),
  placeholder: $t('请输入')
}, {
  key: 'rangeTime',
  type: 'dataPicker',
  name: $t('修改时间'),
}]

// 定义修改时间文件排序字段值
const sortKey: SortKey = {
  descending: 'desc',
  ascending: 'asc',
  default: ''
}

// 监听表格排序
const sortChange = (data: SortChange) => {
  ascOrDesc.value = sortKey[data.order as keyof SortKey]
  sortField.value = sortFieldKey[data.prop as keyof SortFieIdKey]
  refresh.value = Date.now();
}

const getListHandle = async (params: TableDataParams) => {
  const { page, limit, search } = params;
  loading.value = true;
  let req = {
    page,
    limit,
    folderId: folderId.value,
    ascOrDesc: ascOrDesc.value,
    sortField: sortField.value
  } as GetFontListQuery
  if (search) {
    req = {
      ...req,
      searchName: search.searchName,
      searchBeginTime: search.rangeTime && search.rangeTime[0],
      searchEndTime: search.rangeTime && search.rangeTime[1]
    }
  }
  try {
    const { data } = await getPartList(req)
    return {
      list: data.partFileList,
      total: data.totalPartNums,
    }
  } catch (e) {
    return {
      list: [],
      total: 0,
    }
  } finally {
    loading.value = false;
  }
}

// 表格的每一条数据类型
type FontTableItem = {
  id: number
  name: string
  fileType: number
  ownerName: string
  size: number
  lastModifyTime: string
  createTime: string
}

const uploadFileHandle = (files: File[]) => {
  const uploadFiles = files.map((file) => {
    return {
      raw: file,
      name: file.name,
      size: file.size,
      type: file.type
    }
  }) as any
  uploadFontReq(uploadFiles)
}

// 上传零件请求
const uploadFontReq = async (uploadFiles: any) => {
  const { createUploadReq, fileList } = useUpload({
    toFolderId: folderId.value,
    fileList: uploadFiles,
    type: 14,
    replace: true
  })
  const fileNameArr: string[] = fileList.value.map((file: any) => {
    if (file.raw) {
      // 先判断相对路径是否存在，如果存在使用相对路径第一级作为重名计算对象
      if (file.raw.webkitRelativePath) {
        return file.raw.webkitRelativePath.split('/')[0] || ''
      } else { // 相对路径不存在，使用filename作为重名计算对象
        return file.name || ''
      }
    }
    return ''
  }).filter((v: any) => v) || []

  // 检查重复文件名
  const duplicatedName = await getDuplicatedName(fileNameArr)
  if (duplicatedName.length > 0) {
    ElMessage({
      type: 'error',
      message: $t('目标文件：{files}已存在', { files: duplicatedName.join('、') })
    })
    return
  }

  if (fileList.value && fileList.value.length > 0) {
    createUploadReq()
  } else {
    ElMessage({
      type: 'warning',
      message: $t("无文件上传！"),
    })
  }
}

// 上传/移动/COPY 重名文件检查,只检查一级
const getDuplicatedName: (names: string[]) => Promise<string[]> = (names: string[]) => {
  return new Promise((resolve, reject) => {
    getPartList({
      folderId: folderId.value,
      limit: 1000000
    })
      .then((res) => {
        names = [...new Set(names)]
        const duplicatedList: string[] = []
        res.data.partFileList.forEach((file: any) => {
          names.forEach(n => {
            if (file.name === n) {
              duplicatedList.push(n)
            }
          })
        })
        resolve(duplicatedList)
      })
      .catch(err => {
        ElMessage({
          type: 'error',
          message: $t('重名校验失败') + ':' + err.message
        })
        resolve([])
      })
  })
}

// 下载零件
const downloadFont = async (name: string, id: number) => {
  // downloadFile({fileName: name, fileId: id})

  const downloadManager = await new DownloadManager({fileId: id, fileName: name, token: userStores.token})
  await downloadManager.start()
}

// 删除零件
const deleteFiles = async (selectRows: Recordable[] = []) => {
  const fileNames = selectRows.map(v => v.name)
  const text = fileNames.length > 1 ? $t('这{count}个零件', { count: fileNames.length }) : $t('{name}零件', { name: fileNames[0] })
  ElMessageBox.alert(
    $t('你确定要删除{text}吗', { text }),
    $t("删除"),
    {
      confirmButtonText: $t("确认"),
      cancelButtonText: $t("取消"),
      type: "warning",
    }
  ).then(async () => {
    const obj: DeleteFileQuery = {
      fileNameArr: fileNames,
      fileIdArr: selectRows.map(v => v.id),
      folderId: folderId.value
    }
    try {
      await deleteFile(obj)
      ElMessage({
        type: 'success',
        message: $t('删除成功'),
      })
      refresh.value = Date.now();
    } catch (error) {
      console.log(error)
    }
  });
}

const updateFiles = () => {
  reloadUploadFile.value = Math.random()
  refresh.value = Date.now()
}

const fileChange = (ename: string, data: any) => {
  console.log('file 事件 接收：', ename, data)
  switch (ename) {
    case 'file.add':
      // 当前列表中有添加则刷新
      if (data.parentId === folderId.value) {
        updateFiles()
      }
      break
    case 'file.thumnail.change':
      // 缩略图转换完成
      if (data.resultId) {
        const listData = listRef.value.tableData
        // 如果有返回thumbnail字段则直接替换
        listRef.value.tableData.value = listData.map((v: any) => {
          if (v.id === data?.fileId) {
            v.thumbnail = JSON.stringify(data.resultId)
          }
          return v
        })
      } else {
        updateFiles()
      }
      break
  }
}

onMounted(() => {
  socket.on('file', fileChange)
})

onUnmounted(() => {
  socket.off('file', fileChange)
})
</script>

<style lang="scss" scoped>
.list {
  height: 100%;
}

// 上传按钮样式由全局统一管理
.upload-button {
  margin: 0; // 由父容器的gap统一管理间距
}

:deep(.el-upload-dragger) {
  padding: 0;
  vertical-align: middle;
}

:deep(.el-input__wrapper) {
  width: 220px;
}
</style>