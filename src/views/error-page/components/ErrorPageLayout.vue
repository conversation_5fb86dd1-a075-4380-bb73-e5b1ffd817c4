
<template>
    <div class="error">
        <img class="error-img" :src="imgSrc" alt="">
        <div class="ml-16">
            <div class="tip1 mb-8">{{ $t('出错啦!') }}</div>
            <div class="tip2 mt-4">{{ errorMessage }}</div>
            <div class="tip3 mt-4">{{ $t('请检查您输入的URL是否正确，或单击下面的按钮返回主页') }}</div>
            <el-button type="primary" class="mt-8" @click="gotoHome">{{ $t('返回首页') }}</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useStoreRouter } from "@/stores/router";
import { useRouter } from 'vue-router'
import { $t } from '@/locales'
const router = useRouter()
interface ErrorProps {
    type: string;
    errorMessage: string;
}
const routerStores = useStoreRouter();
const props = defineProps<ErrorProps>();
const imgSrc = computed(() => `${import.meta.env.VITE_PUBLIC_PATH}/assets/imgs/${props.type}.png`)
const routePath = computed(() => routerStores.redirectRoot)

const gotoHome = () => {
    window.location.href = '/'
}

</script>

<style lang='scss' scoped>
.error {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.error-img {
    width: 400px;
}

.tip1 {
    color: #2684ff;
    font-size: 32px;
    font-weight: 800;
}

.tip2 {
    color: #222222;
    font-size: 20px;
    font-weight: 800;
}

.tip3 {
    color: #ADB0B8;
    font-weight: 500;
}
</style>