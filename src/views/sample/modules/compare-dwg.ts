const html = `
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <title>H5版浏览CAD图纸V5</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    #webcad {
      width: 100%;
      height: 95%;
    }
  </style>
</head>

<body>
  <script src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/GStarSDK.js"></script>

  <button type="button" class="btn btn-info" onclick="duibi()">图纸对比</button>
  <button type="button" class="btn btn-info" onclick="gstarSDK.func.comparison.close()">关闭对比</button>
  <div id="webcad"></div>

  <script>
    // 初始化GStarSDK
    var gstarSDK = new GStarSDK({
      wrapId: 'webcad',  //容器ID，CAD图纸将这个id对应的容器里面显示
      language: 'zh', //语言设置，可选'zh'（简中）/'zh-tw'(繁体)/'en'(英语)/'ko'(韩语),默认'zh'

      isHideMarks: true, //是否隐藏软件自带的书签功能
    })

    // 初始化功能模块 cadServer为验证服务域名或者ip，是必填项，值可以为空，表示当前服务域名
    // 如：gstarSDK.initFunction({cadServer: 'https://example-cad-server.com'})
    gstarSDK.initFunction({cadServer: ''}) // 用空表示当前服务域名


    gstarSDK.enableZoom(5)  // 开启鼠标滚轮缩放功能,数字是缩放的速度
    gstarSDK.enablePan()    // 开启鼠标左键、滚轮平移功能

    //gstarSDK.enableMap() //默认状态开启小地图
    gstarSDK.disableMap() //默认状态关闭小地图

    //测试用的ocf样例数据，实际项目中，需要后端返回当前图纸生成的josn数据
    //一个cad图纸有多个布局，每个布局对应一个ocf文件
    var demoocfurl = {
      'default': '${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/0000012863.ocf',
      '*MODEL_SPACE': '${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/0000012863.ocf',
      '*PAPER_SPACE': '${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/0000012863.257.ocf',
    }

    showcad(demoocfurl['default']) // 加载显示图纸默认ocf文件

    //var defaultocfurl = window.location.protocol + '//' + window.location.host + '/file/ocf/1001/' + dwgname + '.ocf' //默认显示的ocf文件url地址
    //showcad(defaultocfurl)

    // 点击前端页面布局按钮，切换显示布局对应的ocf文件
    gstarSDK.on('switchLayout', (eventName, obj) => {
      //obj.globalName是布局名称，根据这个布局名，从json数据里面得到对应ocf图纸链接
      showcad(demoocfurl[obj.globalName])

      //var makeocfurl = 'http://127.0.0.1/transformh5.ashx?filename=' + dwgname + '&layout=' + obj.globalName //根据布局globalName获取相应布局名称，由于布局图形必须有对应的布局名称，后台才能转换生成，需要将布局名传给后台，然后后台返回转换后的ocf地址
      //var urlocf = getUrlReturn(makeocfurl)
      //showcad(urlocf)
    })

    function duibi() {
      var oldurl = '${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/duibi1.ocf'
      var newurl = '${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/duibi2.ocf'
      getDrawSheet(oldurl, function (arraybuffer1) {
        getDrawSheet(newurl, function (arraybuffer2) {
          window.buffer1 = arraybuffer1
          window.buffer2 = arraybuffer2
          gstarSDK.func.comparison.open(arraybuffer1, arraybuffer2)
        })
      })
    }

    function showcad(ocfurl) {
      console.time("解析ocf共计用时：")
      getDrawSheet(ocfurl, function (arraybuffer) {
        gstarSDK.render('ocf', arraybuffer, '123', true).then(() => {
          console.log('图纸渲染显示完成')//此处可以添加需要加载完ocf以后才能进行的操作
          //chongdie() //将多个ocf重叠到一块显示
          console.timeEnd("解析ocf共计用时：")
        })
      })
    }

    // 下载ocf图纸数据
    function getDrawSheet(url, cb) {
      //gstarSDK.Tips.showProgress(0, '下载中${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}.')
      var req = new XMLHttpRequest()
      req.open('GET', url, true)
      req.responseType = 'arraybuffer'
      req.send(null)

      req.addEventListener("progress", function (evt) {
        if (evt.lengthComputable) {
          gstarSDK.Tips.showProgress(0, "下载中  " + (evt.loaded / 1048576).toFixed(1) + "M/" + (evt.total / 1048576).toFixed(1) + "M")
        }
      }, false);

      req.onload = function () {
        if ((req.status >= 200 && req.status < 300) || req.status === 304) {
          cb(req.response)
        }
        else {
          gstarSDK.Tips.showProgress(0, url + ' 图纸下载失败')
        }
      }
    }

    //获取浏览器信息
    function getBrowerInfo() {
      var userAgent = window.navigator.userAgent.toLowerCase()
      var browserType = ''
      // 浏览器类型IE
      if (userAgent.match(/msie/) != null || userAgent.match(/trident/) != null) {
        browserType = 'IE'
      }
      return browserType
    }
	
    window.onload = function () {
      var browser = getBrowerInfo()
      if (browser == 'IE') {
        alert('不支持IE浏览器,请换其他浏览器打开')
      }
    }

  </script>
</body>

</html>
`;

export default html