const html = `
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <title>H5版浏览CAD图纸V3</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    #webcad {
      width: 100%;
      height: 95%;
    }

  </style>
</head>

<body>
  <script src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/GStarSDK.js"></script>
  
  <button type="button" onclick="noteshow()">加载批注列表</button>
  <button type="button" onclick="noteshowone()">加载单个批注</button>
  <button type="button" onclick="gstarSDK.notes.clear()">清除批注</button>
  <button type="button" onclick="console.log(JSON.stringify(gstarSDK.notes.data))">获取批注信息</button>
  <button type="button" onclick="selectnote()">定位选中批注</button>
  <div id="webcad"></div>

  <script>

    //需要隐藏的功能按钮，如果不需要对应功能，取消该功能注释即可。
    const hideFunction = {
	  notes: [ // 批注
		//'noteCustomizeLine', // 手绘线
		//'noteArrow', // 箭头
		//'noteWord', // 文字
		//'noteCloudLine', // 云线
		//'noteImage', // 图片
		//'noteGuideLine', // 引线
		//'noteStraightLine', // 直线
		//'noteRectangle', // 矩形
		//'noteOval', // 椭圆
		  'noteCheckView', // 审图
	      'noteStamp' // 图章
	  ],
	  save: [ // 保存
		'saveAs', // 另存为
		'savePDF', // 另存PDF
		'saveImg', // 截图
		'print' // 打印
	  ],
	    measures: [ // 测量
		'measureLength', // 长度
		'measureArea', // 面积
	    'measureCoordinate', // 坐标
		'measureArc', // 弧长
		'measureAngle', // 角度
		'measureSetProportion' // 设比例
	  ],
	  viewport: [ // 视口相关
		//'zoomE', // 显示全图
		//'custom', // 窗口缩放
		'fullScreen', // 全屏
		//'switchBackground'//切换背景色
	  ],
	  marks: [], // 书签
    }
    // 初始化GStarSDK
    var gstarSDK = new GStarSDK({
      wrapId: 'webcad',  //容器ID，CAD图纸将这个id对应的容器里面显示
      language: 'zh', //语言设置，可选'zh'（简中）/'zh-tw'(繁体)/'en'(英语)/'ko'(韩语),默认'zh'
	  toolbarHideItems: hideFunction, // 隐藏hideFunction里面的功能
    })

    // 初始化功能模块 cadServer为验证服务域名或者ip，是必填项，值可以为空，表示当前服务域名
    // 如：gstarSDK.initFunction({cadServer: 'https://example-cad-server.com'})
    gstarSDK.initFunction({cadServer: ''}) // 用空表示当前服务域名

    gstarSDK.enableZoom(5)  // 开启鼠标滚轮缩放功能,数字是缩放的速度
    gstarSDK.enablePan()    // 开启鼠标左键、滚轮平移功能


    //测试用的ocf样例数据，实际项目中，需要后端返回当前图纸生成的josn数据
    //一个cad图纸有多个布局，每个布局对应一个ocf文件
    var demoocfurl = {
      '*MODEL_SPACE': '${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/0000012863.ocf',
      '*PAPER_SPACE': '${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/0000012863.257.ocf',
    }

    showcad(demoocfurl['*MODEL_SPACE']) // 加载显示图纸默认ocf文件


    // 布局切换事件，点击页面右上方布局按钮，切换显示布局对应的ocf文件
    gstarSDK.on('switchLayout', (eventName, obj) => {
      //obj.globalName是布局名称，根据这个布局名，从json数据里面得到对应ocf图纸链接
      //如果后端将所有布局对应的ocf文件链接都返回了，就可以直接加载。如果只返回了默认布局的ocf，切换布局后需要参考下面注释掉的代码，向后端发送请求，等待后端返回当前切换布局的ocf文件链接。
      showcad(demoocfurl[obj.globalName])

    })


    function showcad(ocfurl) {
      console.time("解析ocf共计用时：")
      getDrawSheet(ocfurl, function (arraybuffer) {
        gstarSDK.render('ocf', arraybuffer, '111', true).then(() => {
          console.log('图纸渲染显示完成')//此处可以添加需要加载完ocf以后才能进行的操作，例如加载批注
          console.timeEnd("解析ocf共计用时：")
        })
      })

    }

    // 下载ocf图纸数据
    function getDrawSheet(url, cb) {
      var req = new XMLHttpRequest()
      req.open('GET', url, true)
      req.responseType = 'arraybuffer'
      req.send(null)

      req.addEventListener("progress", function (evt) {
        if (evt.lengthComputable) {
          gstarSDK.Tips.showProgress(0, "下载中  " + (evt.loaded / 1048576).toFixed(1) + "M/" + (evt.total / 1048576).toFixed(1) + "M")
        }
      }, false);

      req.onload = function () {
        if ((req.status >= 200 && req.status < 300) || req.status === 304) {
          cb(req.response)
        }
        else {
          gstarSDK.Tips.showProgress(0, url + ' 图纸下载失败')
        }
      }
    }

    //以下是批注相关的代码

      //点击选中批注以后执行的操作
    gstarSDK.notes.cbIsSelectNote = function (noteData) {
        //如果需要对批注编辑功能进行权限控制，这里写入自己的权限判断逻辑，返回false则用户无法编辑批注，返回true可以正常编辑批注。
        //返回false时，也可以在这里自定义各类弹出页面显示，比如要实现点击批注，弹出该批注的属性信息，就可以用这种方式实现。
        console.log('选中的批注是：', noteData)
        //return false
        return true
      }

    //增加、删除、修改批注后的操作函数
    gstarSDK.on('noteChange', (eventName, note) => {
	  console.log("批注动作是：" + JSON.stringify(note.action));
      console.log("批注内容是：" + JSON.stringify(note.data));
      })

    function noteshow() {
      //正常的流程应该是从服务器里面读取该图纸的批注数据，然后加载。这里是演示，为了简化，直接本地创建。
	  var all = '[{"attachSize":0,"changeCount":0,"content":{"layoutname":"Model","spacename":"2","linetype":"Continuous","lineshape":"straight","linecolor":240,"lineweight":2,"ptend":"-42205.20841942149,-10686.574475878571,0","ptstart":"-71018.37955191114,24213.88436032832,0","cloudlineinterval":8116.385781609195,"rotation":0,"drawbackground":0,"hatchcolor":11},"createDate":"","fileId":0,"id":0,"lastModifyDate":"","localId":"1","noteType":"HCNoteRect","noteVersion":2,"state":1,"userId":0,"userName":""},{"attachSize":0,"changeCount":0,"content":{"layoutname":"Model","spacename":"2","linetype":"Continuous","linecolor":240,"lineweight":2,"ptend":"47075.040160123965,20561.51076119038,0","ptstart":"-9130.934513817145,-21237.875984499275,0"},"createDate":"","fileId":0,"id":0,"lastModifyDate":"","localId":"2","noteType":"HCNoteLine","noteVersion":2,"state":1,"userId":0,"userName":""}]'
      gstarSDK.notes.show(JSON.parse(all)) //这个功能会先自动删除所有批注，然后再加载批注
    }
	
	function noteshowone() {
	  var onenote = {"attachSize":0,"changeCount":0,"content":{"layoutname":"Model","spacename":"2","linetype":"Continuous","linecolor":240,"lineweight":2,"ptend":"608.7289675878134,-7845.839454326854,0","ptstart":"-7913.47657864153,3111.281343086941,0","rotation":0,"drawbackground":0,"hatchcolor":11},"createDate":"","fileId":0,"id":0,"lastModifyDate":"","localId":"1","noteType":"HCNoteEllipse","noteVersion":2,"state":1,"userId":0,"userName":""}
	  gstarSDK.notes.showIncrement(onenote)  //只能加载显示一个批注，增量添加，不会删除已存在的批注
	}
    function clearnote() {
      gstarSDK.notes.clear(); //清除所有批注
    }
	
	function selectnote() {
		gstarSDK.notes.focus('1') //根据批注localId，定位到对应批注视图位置
		gstarSDK.notes.select('1') //根据批注localId，选中批注
    }

    //获取浏览器信息
    function getBrowerInfo() {
      var userAgent = window.navigator.userAgent.toLowerCase()
      var browserType = ''
      // 浏览器类型IE
      if (userAgent.match(/msie/) != null || userAgent.match(/trident/) != null) {
        browserType = 'IE'
      }
      return browserType
    }
	
    window.onload = function () {
      var browser = getBrowerInfo()
      if (browser == 'IE') {
        alert('不支持IE浏览器,请换其他浏览器打开')
      }
    }

  </script>
</body>

</html>
`;
export default html

