const html = `
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <title>H5版浏览CAD图纸</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    #webcad {
      width: 100%;
      height: 100%;
    }
  </style>
</head>

<body>
  <script src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/GCAD.umd.js"></script>
  <div id="webcad"></div>

  <script>
    // 快速集成方案：提供GCAD.js，封装GStarSDK实例化与图纸转换下载等逻辑，方便快速览图集成
    // 调用GCAD，异步返回gstarSDK实例，gstarSDK提供其他属性与方法与其他样例一致
    async function start(){
      var gstarSDK = await GCAD({
        vendorcode:"htest:8aecc6105bab1d988c87afd7697e257d", //必须，开发者的编码.(由服务器指定生成）
        element: "webcad",  //容器ID，CAD图纸将这个id对应的容器里面显示
        param: {
          // 图纸服务相关参数
          cadServer: '', // 可选,浩辰CAD服务器地址。 当不指定是：https://cloudapi.gstarcad.com
          fileDownLoadUrl: '', // 可选（与fileId二选一）待转换文件的下载地址
          fileId: '7605', // 可选（与fileDownLoadUrl二选一）此FILEID是开发商服务器向浩辰服务器PUSH文件时返回的文件ID
          fileToken: '', // 可选,文件信息。用于从数据服务器获取文件。
          fileName: '', // 可选,显示的文件名称。（该值会参与文件校验）
          etag: '', // 可选,文件的Etag标识数据。（该值会参与文件校验）
          version: '', // 可选,文件版本,默认V1
          size: '', // 可选,文件的大小。（该值会参与文件校验）
          userToken: '', // 可选,用户的信息。用于从数据服务器获取文件。（该值不会参与文件校验）

          // 支持GStarSDK实例化构造函数的其他属性,参见其他样例。例：语言配置
          language: 'zh' // GStarSDK属性，设置语言为简中
        }
      })
    }
    
    start()

  </script>
</body>

</html>
`;
export default html

