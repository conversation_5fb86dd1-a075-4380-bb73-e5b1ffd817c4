const html = `
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <title>编辑CAD图纸</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }

    #wrap {
      width: 100%;
      height: 100%;
    }
  </style>
</head>

<body>
  <script type="text/javascript" src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/GCADCloudCAD.umd.js"></script>
  <div id="wrap"></div>

  <script>
    async function start(){
      const webCAD = await GCADCloudCAD({
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJnaWQiOiIiLCJ1c2VyX25hbWUiOiJhZG1pbiIsInJvb3QiOjEsInJvbGVfaWQiOiIiLCJzeXN0ZW0iOmZhbHNlLCJhdXRoIjo2NywiY3JlYXRlX3RpbWUiOiIyMDI1LTAyLTEzVDE2OjA3OjIxLjUxODQ5MTEwOSswODowMCIsImV4cCI6MTc3MTU3NDg0MSwiaWF0IjoxNzM5NDM0MDQxfQ.xqq_i4DkbotPCINFtrJmd4Kjoffv2B43jPZ2UB_LaHU', //必须，开发者的编码.(由服务器指定生成，参见后端部分）
        element: 'wrap', //必须，容器id
        param: {
          cadServer: 'https://gcad-dev-cn.51ake.com:8516', // 可选,浩辰CAD服务器地址。 当不指定是：https://cloudapi.gstarcad.com
          fileId: 4105, // 必须，CAD服务中对应图纸文件的id
          fileName: 'sample (5).dwg', // 必须，CAD服务中对应图纸文件的图纸文件名
        }
      })

      // 

      // 加载进度通知
      webCAD.on('loadProcess', (msg, payload) => {
        // payload: {
        //   process: number  加载进度0-100
        // }
        console.log('***加载进度***', payload)
      })

      // 接口错误，保存等涉及服务接口的错误
      webCAD.on('interfaceError', (msg, payload) => {
        // payload: any // 具体接口错误可能不同
        console.log('***接口错误***', payload)
      })

      // 加载完成通知
      webCAD.on('loadFinish', (msg, payload) => {
        // payload: {
        //   date: number, // 时间戳,事件触发时间
        //   did: string, // 编辑的标识
        //   loadTime: number // 加载所用时间 ms
        // }
        console.log('***加载完成***', payload)
      })

      // 加载错误，加载失败
      webCAD.on('loadError', (msg, payload) => {
        // payload: {
        //   date: number, // 时间戳,事件触发时间
        //   did: string, // 编辑的标识
        //   msg: string // 错误信息
        // }
        console.log('***加载错误***', payload)
      })

      // 主动保存版本
      webCAD.on('saveVersion', (msg, payload) => {
        // payload: {
        //   date: number, // 时间戳,事件触发时间
        //   did: string, // 编辑的标识
        //   result: bool // 是否保存成功
        // }
        console.log('***主动保存版本***', payload)
      })

      // 自动保存版本
      webCAD.on('autoSaveVersion', (msg, payload) => {
        // payload: {
        //   date: number, // 时间戳,事件触发时间
        //   did: string, // 编辑的标识
        //   result: bool // 是否保存成功
        // }
        console.log('***自动保存版本***', payload)
      })

      // webCAD被关闭
      webCAD.on('close', (msg, payload) => {
        // payload: {
        //   msg: string, // 被关闭的提示语
        // }
        console.log('***webCAD被关闭***', payload)
      })

      // 执行CAD命令
      const eHandle = webCAD.on('executeCommand', (msg, payload) => {
        // payload: {
        //   date: number, // 时间戳,事件触发时间
        //   did: string, // 编辑的标识
        //   command: string // 命令
        // }
        console.log('***执行CAD命令***', payload)
      })

      // 取消事件监听 "执行CAD命令"
      // webCAD.off(eHandle)
    }
    
    start()
  </script>
</body>

</html>
`;
export default html

