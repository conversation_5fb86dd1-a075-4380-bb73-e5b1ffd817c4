const html = `<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <title>H5版浏览CAD图纸V3</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    #webcad {
      width: 100%;
      height: 95%;
    }

    #modal,
    #modalstamp {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, .5);
      z-index: 200;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    #modal section,
    #modalstamp section {
      width: 500px;
      height: 300px;
      background-color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  </style>
</head>

<body>
  <script src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/GStarSDK.js"></script>
  
  <button type="button" onclick="noteshow()">加载批注</button>
  <button type="button" onclick="gstarSDK.notes.clear()">清除批注</button>
  <button type="button" onclick="console.log(JSON.stringify(gstarSDK.notes.data))">获取批注信息</button>
  <button type="button" onclick="createmarks()">创建书签</button>
  <button type="button" onclick="applymarks()">应用书签</button>
  <button type="button" onclick="console.log(JSON.stringify(gstarSDK.layoutInfo))">当前布局</button>
  <button type="button" onclick="console.log(JSON.stringify(gstarSDK.layouts))">所有布局</button>
  <button type="button" onclick="getalllayer()">获取所有图层</button>
  <button type="button" onclick="gstarSDK.disableMap()">关闭小地图</button>
  <button type="button" onclick="gstarSDK.enableMap()">打开小地图</button>
  <button type="button" onclick="alert(gstarSDK.version)">版本</button>
  <div id="webcad"></div>

  <div id="modal">
    <section>
      <textarea id="input" rows="3" cols="30"></textarea>
      <button id="ok">确定</button>
    </section>
  </div>

  <div id="modalstamp">
    <section>
      <p>这块可以自定义的逻辑</p>
      <input id="inputstamp" type="text" />
      <button id="stampok">确定</button>
    </section>
  </div>

  <script>

    //需要隐藏的功能按钮，如果不需要对应功能，取消该功能注释即可。
    const hideFunction = {
      save: [ // 保存
        //'saveAs', // 另存为
        //'savePDF', // 另存PDF
        'saveImg', // 截图
        //'print', // 打印
      ],
      measures: [ // 测量
        //'measureLength', // 长度
        //'measureArea', // 面积
        'measureCoordinate', // 坐标
        //'measureArc', // 弧长
        //'measureAngle', // 角度
        //'measureSetProportion',// 设比例
      ],
      viewport: [ // 视口相关
        //'zoomE', // 显示全图
        //'custom', // 窗口缩放
        'fullScreen', // 全屏
        //'switchBackground'//切换背景色
      ]
    }
    // 初始化GStarSDK
    var gstarSDK = new GStarSDK({
      wrapId: 'webcad',  //容器ID，CAD图纸将这个id对应的容器里面显示
      language: 'zh', //语言设置，可选'zh'（简中）/'zh-tw'(繁体)/'en'(英语)/'ko'(韩语),默认'zh'

      toolbarHideItems: hideFunction, // 隐藏hideFunction里面的功能
      isHideMarks: true, //是否隐藏软件自带的书签功能

      apiHost: window.location.protocol + '//' + window.location.host,  //API域名，转换PDF、另存功能需要向该域名发送请求，如果没有用的转PDF、版本转换功能，则无须关注这个参数。
    })

    gstarSDK.enableZoom(5)  // 开启鼠标滚轮缩放功能,数字是缩放的速度
    gstarSDK.enablePan()    // 开启鼠标左键、滚轮平移功能

    //gstarSDK.enableMap() //默认状态开启小地图
    gstarSDK.disableMap() //默认状态关闭小地图

    gstarSDK.measures.accuracy = 3 //测量结果显示的小数精度


    gstarSDK.apiRename.saveAs = '/saveAsdwg?key=5427&cad=test'   //自定义图纸另存接口,可以添加自定义参数
    gstarSDK.apiRename.saveAsPdf = '/saveAsPdf?key=5427&cad=test'  //自定义转pdf接口,可以添加自定义参数

    function getalllayer() {
      console.log(gstarSDK.layers);
    }

    //测试用的ocf样例数据，实际项目中，需要后端返回当前图纸生成的josn数据
    //一个cad图纸有多个布局，每个布局对应一个ocf文件
    var demoocfurl = {
      '*MODEL_SPACE': '${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/0000012863.ocf',
      '*PAPER_SPACE': '${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/0000012863.257.ocf',
    }

	showcad(demoocfurl['*MODEL_SPACE']) // 加载显示图纸默认ocf文件
	
	
	//功能按钮触发事件
	 gstarSDK.on('functionTrigger', (eventName,functionName) => {
	   console.log('点击的功能是：'+functionName)
	 })


    // 布局切换事件，点击页面右上方布局按钮，切换显示布局对应的ocf文件
    gstarSDK.on('switchLayout', (eventName, obj) => {
      //obj.globalName是布局名称，根据这个布局名，从json数据里面得到对应ocf图纸链接
	  //如果后端将所有布局对应的ocf文件链接都返回了，就可以直接加载。如果只返回了默认布局的ocf，切换布局后需要参考下面注释掉的代码，向后端发送请求，等待后端返回当前切换布局的ocf文件链接。
      showcad(demoocfurl[obj.globalName]) 

      //var makeocfurl = 'http://127.0.0.1/transformh5.ashx?filename=' + dwgname + '&layout=' + obj.globalName //根据布局globalName获取相应布局名称，由于布局图形必须有对应的布局名称，后台才能转换生成，需要将布局名传给后台，然后后台返回转换后的ocf地址
      //var urlocf = getUrlReturn(makeocfurl)
      //showcad(urlocf)
    })


    function showcad(ocfurl) {
      console.time("解析ocf共计用时：")
      getDrawSheet(ocfurl, function (arraybuffer) {
        gstarSDK.render('ocf', arraybuffer, '111', true).then(() => {
          console.log('图纸渲染显示完成')//此处可以添加需要加载完ocf以后才能进行的操作，例如加载批注
          console.timeEnd("解析ocf共计用时：")
        })
      })

    }

    // 下载ocf图纸数据
    function getDrawSheet(url, cb) {
      var req = new XMLHttpRequest()
      req.open('GET', url, true)
      req.responseType = 'arraybuffer'
      req.send(null)

      req.addEventListener("progress", function (evt) {
        if (evt.lengthComputable) {
          gstarSDK.Tips.showProgress(0, "下载中  " + (evt.loaded / 1048576).toFixed(1) + "M/" + (evt.total / 1048576).toFixed(1) + "M")
        }
      }, false);

      req.onload = function () {
        if ((req.status >= 200 && req.status < 300) || req.status === 304) {
          cb(req.response)
        }
        else {
          gstarSDK.Tips.showProgress(0, url + ' 图纸下载失败')
        }
      }
    }



    // 获取布局图纸转换请求链接返回的ocf地址
    function getUrlReturn(url) {
      var req
      if (window.XMLHttpRequest) {
        req = new XMLHttpRequest()
      } else {
        req = new ActiveXObject('Microsoft.XMLHTTP')
      }

      req.open('GET', url, false)
      //req.responseType = 'text'
      req.send(null)

      req.onreadystatechange = function () {
        if (req.status == 200 && req.readyState == 4) {
          alert(req.responseText)
        }
      }
      return req.responseText
    }

    //以下是批注相关的代码

      //点击选中批注以后执行的操作
      gstarSDK.notes.cbIsSelectNote = function (noteData) {
        //如果需要对批注编辑功能进行权限控制，这里写入自己的权限判断逻辑，返回false则用户无法编辑批注，返回true可以正常编辑批注。
        //返回false时，也可以在这里自定义各类弹出页面显示，比如要实现点击批注，弹出该批注的属性信息，就可以用这种方式实现。
        console.log('选中的批注是：', noteData)
        //return false
        return true
      }

    //增加、删除、修改批注后的操作函数
    gstarSDK.on('noteChange', (eventName, note) => {
	  console.log("批注动作是：" + JSON.stringify(note.action));
      console.log("批注内容是：" + JSON.stringify(note.data));
      })

    function noteshow() {
      //正常的流程应该是从服务器里面读取该图纸的批注数据，然后加载。这里是演示，为了简化，直接本地创建。
      var all = '[{"attachSize":0,"changeCount":0,"content":{"layoutname":"Model","spacename":"1F","linetype":"Continuous","lineshape":"straight","linecolor":240,"lineweight":2,"ptend":"63408.1172,8218.3477,0.0000","ptstart":"42270.0742,14435.418,0.0000","cloudlineinterval":4521.505376344086,"rotation":0,"drawbackground":0,"hatchcolor":11},"createDate":"","fileId":0,"id":0,"lastModifyDate":"","localId":"1","noteType":"HCNoteRect","noteVersion":2,"state":1,"userId":0,"userName":""}]'
      //gstarSDK.notes.clear(); //加载前，需要先清空已有的批注
      gstarSDK.notes.show(JSON.parse(all))
    }

    function clearnote() {
      gstarSDK.notes.clear(); //清除所有批注
    }

    function createmarks() {
      //书签是json数据定义的，可以将创建书签后生产的json字符串保存到数据库中
      //创建当前视图状态的书签
      aa = gstarSDK.marks.create("test");
      alert(JSON.stringify(aa));
    }

    function applymarks() {
      //打开图纸的时候，加载数据库中该图纸保存的json书签数据，想要跳转到哪个对应书签视图位置，就将对应书签内容传给该接口
      var cc = JSON.parse(JSON.stringify(aa))
      gstarSDK.marks.apply(cc);
    }



    // 审图批注自定义界面
    var _DOM_Modal = document.getElementById('modal')
    _DOM_Modal.setAttribute('style', 'display:none')
    var _DOM_Input = document.getElementById('input')
    var _DOM_OK = document.getElementById('ok')
    _DOM_OK.onclick = function () {
      var text = _DOM_Input.value
      gstarSDK.notes.noteCheckView.setText(text)
      _DOM_Modal.setAttribute('style', 'display:none')
      _DOM_Input.value = ''
    }

    // 审图批注需要输入文字回调
    gstarSDK.notes.noteCheckView.inputTextCB = function (text) {
      //gstarSDK.notes.noteCheckView.setText("审图批注文字") //也可以直接赋值批注文字，不通过弹出界面手动输入的方式
      _DOM_Modal.setAttribute('style', '') //显示自定义的文字输入框
      // 如果是编辑文字
      if (text) {
        _DOM_Input.value = text
      }
    }
    // 审图批注返回截图回调
    gstarSDK.notes.noteCheckView.screenshotCB = function (imgBase64) {
      console.log(imgBase64)
    }


    gstarSDK.notes.noteStamp.params.width = 60  // 图章宽度值
    gstarSDK.notes.noteStamp.params.height = 60 // 图章高度值

    // 自定义图章,图片支持base64和url，可以添加修改成自己的图片。
    gstarSDK.notes.noteStamp.maps = {
      home: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAgAElEQVR4Xu1dB3gbRfb/jeSWuKY4lmSnQarlFI6SSKYkcAQIHJ0Axx29/IErHHCFenCUu4OjHP1oR6+BIwQSSoAAkUwSQprlJKQntiTbKU7iJG7S/L8nx0HWrrRFq5Vk7/s+fSr7ZubNm/1pdmZeYTAoaRooGX/scHMgcAJH8CIGNpgDZQCaweGFCSt4kL3ur3HNTZqARsNghg7010Bx+ZS8DFPrreDsFhmtf8s5+7sBFBmaSgCLAZAEKDVWlRb7MeUMgdcAHKasafYPn8clB1DKqjW4Y2rAAIiON8gBcLwDwK6y2Vd9HvfFKssaxVRowACICqWpLWK1Oz8FME1t+VA5zm711bj+HlcdRmHZGjAAIltV8THayisf5IzfHF8todLNjPNjvTVVSzWoy6hCQgMGQHS4RcomOEoDHWwVgHyx5i4bVIQLBxbg0Jws7A4EsLm1HY/7duKLXXujSfesz+O+RgfRe30TBkB0uAVsdsf1HOwJsaa8R4yMKsGtWxrwUsMu8esm0xG+lQuW6CB+r27CAIgOw2+1O2lhfl5kU7eWDcRvLP2iSrCptR2/WLUV2zsCAh7G2AXeatfbOojfq5swAKLD8Fvtzq3oPATsRnPHDsaE3JyYEty0qR5vbtstAhD83lvtfkwH8Xt1EwZAdBh+q91Jd7hg/bHuZ4eir8kUU4LZO5txzXqfgIdz3O2vcd+lg/i9ugkDIDoMv9XuXANgVGRTSycMR0lmRkwJaA1Ca5FIMgCiw8ABhqmJHmq22h1fAWxKZFsfjCnDUXl9Yopw7QY/Zu3YYwBEj4ESacOYQXRQvLW88hYwfn9kUycU5uLVkbaoEkR7vKICwSDG1a9yV+sgfq9uwgCIDsNvq3BO4xx0ii6gSwcV4uqSfhiWndnt2op9rTi5ZouodAx8oddTNVkH0Xt9EwZAdLoFoj1mUfMDMsyYVpSLKYW52NLajmV7W/DxzuboknF+ja+m6lmdRO/VzRgA0Wn4bRWOKzhnz8fdHOdLfDVVR8Rdj1GBLA0YAJGlJm2YLHbnWww4P57aOAtO9Vd/Nz+eOoyy8jVgAES+rjThtNmdH3HgVDWVGeBQo7X4yhgAiU9/qkpbyx1Xg7H/yC/M/BymE/yeb2vklzE4tdCAARAttKiwjmjbvjGqaWMcM7w17lkKmzLY49SAAZA4FaikeHH5FEsGayPDxWOUlDvIy/kzvpqqa1WVNQqp0oABEFVqU17IVu68jDO8qLykoMQmDszwe9yLNajLqEJCAwZAEn2LTJmSYW1se0PM3D2y6anTb8SalZ/Du3WlpFQc/E6/p+oeSUaDIS4NGACJS32xC1vKK09hjNMjVV4szmLLSJxx4T+RX1gSYlu84DUsmPe0HMnc5gw+o3Z5VZ0cZoNHuQYMgCjXmawSlgrnY4zjt1LMk467DM6pVwrYdjRuwqw3/4ymHbVSVYAxdqW32vWCJKPBoFgDBkAUqyx2AVu54zAw9g4HRsTizM0fGJo1SmxjYlb4zaePY0nVW3KknJm9N+vXmzbNb5HDbPDI04ABEHl6ksVlrai8FZzfJ8U8/ogzccJpf5RiO3i9dtNSfPjmn9HaGjWIQxdvM+dshhGFUbZqJRkNgEiqSJrBWl45BIy/DuDoWNwmkxmnX/hPDB/pkK5UhOOT9/+GVStEjYK7cXOGx/3V7t+pasQo1E0DBkDivCFsFY7LOWeSz/8jxh6HX5wvcAlR3PqPni/x8bt3yCm31sQxo67GvUwOs8EjrgEDICrvjIGjK/MzMzida5wrVcVJZ96G8onTpdhkX+9ob8WHb92CzesXSpfh/BZfTdU/pBkNDjENGABRcV9YxlVOZ0G8A/DcWMVtQ8bj9Av+gT59C1W0Il1k+aL38eWch6QZOb4G2MW+Gpe4B5Z0Db2WwwCIwqG32B2PM7DfSBU75sTrcETlRVJscV/fvcuP2W/dggbfj5J1cfBL/J6qVyQZDYaDGjAAIvNmKCmfPMnMTK9z4NBYRfoNGByaNfoXD5NZszZsVfNfwHfzZVmyvNnewa7ZtsYljAShjSg9qhYDIDKG01rhvA0c90qxHu68EMdOk5xcpKpRfb3euxofvvUXNO9ulKpjJ2O4wFvt/kyKsbdfNwAS4w4oHXP0qKCZFuK8MtaNkp2TFzr0Kx06MSXupy8+/hdWLP6fpCyM4SFvtVuLiPOSbaUrgwGQKCNnq6i8gnMu6UNePuEUnHTW7Sk3/pvWfRcyVQkGOqRk84CxS33Vru+lGHvjdQMgEaNeMt45yBTAUwDOkbohTptxH0aWC+LBSRXT7TrnHB+/ezvW1ki7sHOOm/01bhlbYrqJnxINGQAJGwbbWMeZ3MToRLxvrNEZNmIyTptxLzKzYkdFTIkRBlCzfC4+/Z/kEgrgfF7QbL6mfuWCDakie7LlMAACoLy8PKuJ9XuYg18vNSDHn3ozJhx5lhRbyl3ft3cnPnrnNtRtXi4tG2e/8tW46I+i11OvB4hlnOM4FmS0P3pIrLthkHU0Tjv/PhQWWdP6plnifgPffPakjD6wVzJN7IYtKxfslMHcY1l6NUCsdietriW98hxTrsDkKZf3mJtge8NGzH7nVuzcJnmw3sCD7DL/KtecHtN5hR3plQAptR89IYggLcSdsfSVXzAoNGtYSssVqjU92L/9/El87yJvYCnqvTnaex1ALBXOaxkP7VLFpAlHno3jT71Jii3tr9du+gGz374NLfuFWawiOrcsGGTX1q9yfZf2nVbQgV4DEOtY51CY+MMAOzuWfszmTPzigvsxfGTMyUWBitOD9dMP7kPNMjlPUuxGn8f1SHr0Kn4pewVAbHbHhRwhn42Y+7Ijy6di+rl3gxybeiPJ9zXhc00B8w11qxdIW0imuSJ7NED6HXJ4YZ8+2fdz4DqpcdLaZ0OqvVS93ta6F3Nm/hUb11ZJiUjnkJf7a9wvSTGm8/UeCxCr3XEyQGsNNjzWAJUNnYjp5/0NuXkD0nkcNZd9+eL38eXHcg7W+Qu8NeMW/7pvJS0kNRdShwp7JEBs9sq/cXBJv9RjTrweR1T+Ugc1p2cTTTvqMGfmnSArYQmqY2DXeT2uD6UY0+16jwJIydjKyYzxhxlDzKgI/QcOxann3YOBJTFdO9JtLBMmL/mZkL+JNLF/+Mptt+PddwPSvOnB0WMAYrU7bwQg+UxAXn7k7WeQMg34aj2hYBF7dtVLFVwMzm701bgWSDGmw/W0B4htnGMMD5ruB3hMA6mcvoU47bx7MHj44ekwLikr45dzHsbyRe9JyscZ+5O/2vWgJGOKM6Q1QA5ETKdDv5xYerZPPBXTzrw1xYcifcTbsMYVMqPv6GiTEnoWh/nWdE78k5YAGVQxqcTMzWS/LQxqGzFkdK4xuuLnUgNpXFeogUCgHRTIjs5OJKiNgV3v9bgknc+kKkrG9bQDiLXCcRY4exhAzKgIw0c5ccrZdyI7Jz8Zeu01bXqWfoTPZv1dsr+c48UObr5926pvfZLMKcSQNgAZMeKU7H1Zu+/ljEv6UE+d/gdMPEoynlsKDUN6i9K8pxFz37sbFENYgjaBsZt81a73pRhT5XpaAMRir5wKxu9nHJNjKc5SOhYnn/1XUOgdg/TXgNy8Joyzf/VtK7h93bq5rfpLqazFlAeI3Ijpk4+7DA6RPBvK1GFwx6uBRv/a0GyyvXGjVFVuzky3+asXSDvMS9WUwOspC5DScudEznCvVE7xgiILTjnnLtgGj0ugmoyqlWrgm8+ewBL3m9LFOLvVV+OSXsRI15QQjpQEiMXuvI4BtBDPjtXr8UeehRNOlVySJERxRqXSGtiyfjHmvHcX9u9risnMwWabwe6o8yyQ4TAv3a6WHCkFEMuYycNYhulecMQMapuRmY3p59yNQ8eoy6aspQKNuqQ1QBFVKLKKBDWDsz/6alzPSDHqeT1lAGKzO37JwSiBxtBYCiCfjZPPvhMZGVl66sloK04NrF75Oea+d5eMWtgrjJlv91Z/s1UGc8JZkg6QYROnFLW0t9/DwCWD2p54+l9Q8bNfJFwpRgOJ0QC59c59/2/YJO1rspYzdru/2kUZgpNKSQWIZWzldGbm94DjZ7G0UDbsMJx81h0H0yQnVWNG43FrYOl372L+J4/KqIc9GjTn3l6/4jPJ5IwyKlPFkjSAWO1OCrcjGdS28oRrcNQxF6vqnFEodTWwY9vmkKmKlK8JA1sIE7vdu3LBvGT0RneAlI5zOngwtH17fKwODygeFlprUMA2g3quBtxfPouF37ws2UEOdqff45KMYSZZkUIGXQFiKXfexBhoIR5zhf0zx/k47iQjSavCsUxb9roty0Ozye4mv0Qf2Cc0m/hWLliiV2d1AYi1YvJYcHavVMidPrlFOOWsOzF0xCS9+m+0k0IamDf7AaxcMiumRJxjl4mx270e1xN6iJ5wgNgqHFeAs3s4EDOo7djxJ4UeqQzq3RpYt+rr0GzS3t4iNZu8ETSbb69f8Y2kTUs8GtUUIJaKyVMYN9Fz4gQAhp15PCNjlFWqgR85+Gd+T9VvlRaMxa8ZQGz2yZUcph7hh6ylgo26dNYAwwe+ardm+Sk0A4jF7pzFgNN1VofRnKEBgQY4x93+GrecY3tJ7WkCkLIJjtJAB6uVbM1gMDSgiwb4+z5PlWQKPTmiaAKQA2uPr+Q0aPAYGki8Bvh8n6dqqhbtJBQgzvz0yOGnhSKNOpKjAfee/SINpxFAZo4uS47mjFZ7hQZs3681ANIrRtropCoNGABRpTajUG/RgAGQ3jLSRj9VacAAiCq1GYV6iwYMgPSWkTb6qUoDBkBUqc0o1Fs0YACkt4y00U9VGjAAokptRqHeogEDIL1lpI1+qtKAARBVajMK9RYNpDVAHHl98N4Yw9Skt9ysyehnWgPkiD5ZeGlIf2RmZIReOZmZydCh0WYP00BbRwfaOzpA7/bVYvl40sRYkQDyQllRt+Exm0zIMJs7QWM2hz4bZGgglgYIDO2BQAgU9OJhzBPWNogUTWOARPaG7O0JJF2vrMxMmJgmVvjGXZemGmhtb0cHAeIAKGJ1o8cDRKzzjLFOwByYbbrAk6bjbYgdRQOc8xAQusBA74FgUJG+eiVAommIgNL1iBb+rkijBnNSNBAMA0OAQBEMhoARLxkAkaHBLuAQaOhF6xt6Nyg5GuiaEbqAQO8EkESQAZA4tEprGQKK6QBwusBD341VThyKBUKPQl2PRF3v9Bs9NulJaQ+QswtzMCY7A4dmZeipN8m2aJ0TAo4IiOj33r5REAwGQyAIf3X9lqjZQHLQwhhWtrRjdWsH7m3Yk767WF2SZzBgdHZmCCj0OqpPFoZlpfb2Ls0wXUBhBwBDoAkB68A7Pdp1fVcyuMnipX93urm7Hnnoc9dvdPN3fVe6UNajPyta2rF0fyco6LWhrSNGsym2zVsyfnyuKZDXrERRBBB7diaO6puFI/tkojQztQEjt2/hACKQ0feDrwPfqa7QbyLfcWCLm651Pax0PbbQe+i3A+9d30Pv4b91fQ/7Xa78qcK3qrUDi/a1YUkIFO2o71Cyu8Ue9Xlcf9CiL5o9itvsjgc42B/VCjUow4SKnExUZGfCnpMR+pxn0kw8tWIZ5XTQwNb2ANYcmBnofen+NuwJql/LmDj/eV1N1RdaiK7ZHVg29tiRAXPgKXD+cy0EozpoliHAEFi6QGPsTWmlXf3roTmAANAJhvaDn/fGAYbIXjDgaa/HfZ1WvdMMIF0CWeyOBxhMvwa4RSshw+spz+6cXTpfqbf4T0Sf07HO3UGONQdAQGsGAsWPrbHWDXH2kmMlwJ/w1VQ9G2dN3YprDpCu2q12x8mA6TjG+BTOMVlLocPrymEsBJbBmWaUZZq7vReYE9a9RHUn7erdFQhiS3vg4KtrhvC2x38IGEMZ2wEsAeNLOGc/BBmWNlS71ydCebrdQaX2oycEePBMxqBJ1G05yigwm0SBQ0CyZBgPa3J0SDyRINja9hMg6JqexMAfNGXg37XLq+r0aFc3gFBnLHbnmwy4QI+OSbWRyYCyzIyDAKJNgiIzvRiKTD99LjSb0JOhtK0jiIZAEA0dgdBOUUPoFQi9d34PQMs1gtS4dF0v6l+Gph0iCQMY5viq3afKrSdePl0BYrU7lwKYGC50QZEVV9wwE5TIsW7z8oPv7W1iQYnj7a668jQTFZnYAQB1goiA0wmkn37PYkAWY8hmDN0+mzp/TxTQmoMce4JBNAc46PPB712fA8Fuv+8O8IM3v77//0L9Z2bno2DACOT3H4GBltEYP24SCgv7o9G/Fq89c6nYgG32edzD1I2k8lJ6A4Tu+pxwMcuGHYbzLhXmY6QcdfV1q+CvW4V676oQePY206Nn+hLNWgSUThCFfTZ1AooGo4MDAfDQe+eLg5a2oXf6TmYeBz93/pYOxJgJ+QNGoKD/ocjvfyjy+g1FbuFgUdEPKRuInIwAXnj0XLS2CE/KfR63bvetbg2VjJ+Wawo0Cw4TyydOx0ln3iZrjPfvbYLfuwr1dTXYuX3rwVdb615Z5Q0mfTTQt8CG/H7DkVs0FHn06jcUWTndHeekJLENKsK8d3+HBt+PAtYOnpXfWDNf0cG0VHvRrusGEOu4ow9HMPh9pCCTp1wOx5Qr1MofKreveQd27ugETFMYcOhzINAeV91GYXENmDOykF8wCBnZRcjJHxwCQm6/TkDQbKGU+uRkIbdPFvL7ZiOvbw4yzCbMfvtWUNbbSOLgk/yeqkVK21DDrx9AyisvAuOvRQo57czbYJ84XY3ssspQcvqDwDkAoubdDdi7Zzv272uSVUdvY8rMzEFewSDkFRSHQJBXOAj5BcWh30LfC4rRp28RvI1N2LO39YBBI9l4ybfmzcwwo6igL3JzspDXNxv0PZK+/vRx/FD1luB3xnG5t8b9Xz3GRT+A2J2UHvr2yE6dd+njKBv2Mz36KmgjGAxg755tobVNY6MPW2q3oK1lJ9r2N6G1pQlt+zs/t7U0gfNkL2e1UVFWdt9uN3rnDR8GhoJi5PQpUNVYMMhDYOl6b2nrwGav+LqxdFARBvWPnSl82cKZ+GruI0KAMDzkrXbfrEpIhYX0BMg7AM6LlI92sGgnK9m0Z28L1m1tjCpGW8uuEFA6AdMJnNwcIMMUREd7CzraW0Ov9g56P/A97HM7Xe9oQaBD+SNfRmY2MjP7IDMr58B7jM+ZOcjMout9QDNB1+e+ef1DQMjOztVV1etrt2F3s3BH8rAx4gv0cOE2rHFh1pt/EpGXfeLzuE7RoyP6AaTcuQIM48I7VVBkwRU3vKdHPyXb2Ll7HzZF+beLVnjCqDKYFBpU0kz0E5h+AhaBiix5DwIhdIN3AkHNM71kh3ViaGltx6qN/m6t9Svoi2G2AZISbGvYgFef+rUY3xafxz1UsgINGHQDiMXubGNAt8BY0bZ4NeiXoirqGprQsEPM8SZ6Nfm5ORgxuFhRO72V2b9tF3zbdh/s/uhhJeibkyWpDtrqf+GRs7F/3y4Br15bvboAZNjEKUWt7W07I3tZPvEUnHSmYFkiqTitGFrpGdm3HXv3tymusjCvD2i/3iB5Gli1wY+WtvbQ7tS4kaXyCgF449krUO9dLeDPzszqt2nZ/ITvsugCEIvdcRQDWxjZSy22eGVrOoJRzSNVZFu24kKUDFC3oFUrd7qWa9qzDxvrtqOspB+K++XJ7sZH79yOtTVfCfiDQeaoX+X6TnZFKhn1AsjFDOzlSBmnnXEr7IfpZlZzsHlaaxBAtKBDBxejgFbrBklqgAAyvFR67RFe0befP4nvXW8I6maMX+mtrnpBstE4GXQBiLXCcT84uyVS1nMveRyDh+u3xUv79Lv3toRetGvV3qGNSbb9UCuyMlMrKEWc90VCitOCPSdbWXzm5Yvfx5cfPyQmz8M+j/umhAgaVqk+ALE73gPY2ZGdSeYWL4GleV8r9tBrb0vo+Vgt9aGAFMMT4h+mVqQeU27j2ip88LrokcenPo/75ER3VC+AeABWHt6ZgkILrvhDamzxhoNlx669isNfUr/kbl0mekDjqZ8O+VIt4N6Oxk14+cmLxLpV6/O4pQ9T4lEIBdWIs7ys4la7IwB0N9ApGzoR5132pKzyejJtqN2GXSIHWyRDft8cMFNn2B86/uj63BW1hH6zDCzUU1xN26J+0+5cKhFZOzz38Jkhe7tI0mOrN+EAsY2aMpBntgmOqMsnnIKTzkreFm+0m6B++254G4X77kX5fRUvMFPpRpOSZf3WxtD6gExAUo3eev5q+Go9ArFYe1ax98f52xIpb8IBUjrO6QgG4Y7sxOTjLoNj6pWJ7JuquulM5MfN9d3K0oxhH2EL7eH3RPI17oJ/++7Q4R0d4qUazZn5V6ypnicECOOV3uoqwb2lpfwJB4it3HkZZ3gxUuhkbfHKUd7aLQ2hBXwX0YFgqj16yOmHHJ6mPfuxse6nP2E6xEu1P4IF857B4gWvCgECdpXX43peTj/V8iQcIBa7858MEFicnXvJYxg8/HC1cie0XLhpBFmcpuJjhxYKaGsPYNVGX8j6NpX/DFZ8PwtffPSASJe1i6AYTZ8JB4jV7vwAwBmRAlz5h/eRX5h60znJ2fWYpdQsQoubVs86ImdKarukfz7Imy+VaPP6hXj/1RvFRPrc53FPS6SsegCEDGlGh3eCgEEASWVas7keQ639kZOl7GArVfpEfhi7mluQlWkOHWLSKzOjMz9kRoYp9AgpZqCZ2ycbo4YOSpVuhORo2lGH/z42QyATB7x+j1u+YZeKXukBEEFYgdKhEzEjBbd4w/VHj1npvGVLp9Y0Q3SoiFtVMcIm6uGn4v7SrMhzD52B5j3CDatEb/UmFCDF5VMsGaxNkKd37ISTcfJZd4SUt2dfS+h8wSDtNRBty1qqJdrSHtQ/DzSbpAq9/cL/wbt1pUCcDp5lbayZ393hREOhEwoQa3nlMWD8m0h5w7d4a+ubkJ+b3WN3iTQcK1VVia0z5FaUkWHGoH55GFiUB3OSt7jnvn83Vq/4TCg6Z8f6alzfyu2TUr6EAsRmr7ySgz8XKdS0M26B/bDTQj93WdbS837/Qn3dQZUqKx35xTz61PSDLJYJKIX5yTlpd3/5LBZ+IzAIp1wp12gdsDpcP4kFSIXzX5xDYHEZbsVLfuBkLEgkx5FfzeD29jKRHn1q9UG7egOKaEbJ1d16ufqH2fj8w3+IzCB4zFfj/r3aPkmVSyxA7M7ZHOicKsLoqhv/FwogQLR6ox/7W3+ypLUMKIC1OLo9Ey069+5vxf6WtrReREsNjNbXyS+cZhO11L+gbwgcFKInGbR14xLMfPl3gqY58IXf49YsJ01kAwkFiMXuXMuAEeGNRm7xVq/zCvwyaCofbOkXKkaDSibpBIp9LW0gN1n6Jxs1tATZKZYYNBk3jtw2w2dquWWIj3RsHVgYslZOJu3Z3YDnHz5LIAIDfF6P25Yo2RIKEKvdKbLFOwEzLnsq1B+aDVauFY9iT1H2ovmKD+yXh8ElnQDqKbS9cSNqNy1FUb9SWAdXIEvD8Dw0Q9NMrZRKBuSHXIpTxQT++UfOxp5d3e3kqE+J3OpNGEBKx08qCwbMWyMHJXyLl5yUyJlfKZFzEjkp9RRatmgmvprzU4C0QdZROOfXjyKnrzam85t9O0B+LnKJ8oiOHFISCgWaSvTOf69H3eZlApFM5sDguhULRXIlxC99wgBSYncebwIEiRQnHXcpnFOvCklOp7m0DamEeprZeTSPuZHlU3DajPuUqEaUt6MjgJoNfkVOYKlqYvPp/+5BzfJPBP0MAifUe9xfxq0skQoSBhBrueMaMPZMZJsnnnELyieeCjKxVhqLiuqSG1MpEcpKRJ2ffXA/PMs+Fq36wqueh6V0bFzNdpmyK6mEzFLIzz7VqOqr5/Hd1yIheTm71lfjEtxrWsifOIDYHY8A7AYBQM55EB1ZQ0J5vZVSTwvWRs/T5E4aLVnQEZW/xDEnXq9UTd34I3cJ6SL5fdCiO8h56I8qkijS+pgU9AvxLP0Yn826XyAvZ3jcX+0WbnHFpbnOwgkESOUcgAvipzrOeBrZfdQtsEcOGZS0bUYNdC2oYknVW/jm08ejVk0xiy++/rVQjF01tH3XXmzx7QjZVRXk5aAor2/oPZwadzajtr57TD/ayiVdpxp5t6zA2y9eKxCLAV96Pe4TEiFvIgGyAeDDw4XO7jsAjtPV+6EfWlYsGOBEKEWvOt96/hr4aqtjNkfJhSjJkBqim78wL0fyUI9ihFGEya5JPVWjRu7buxP/eVBwrEaqqfd53AkJK5NAgAi3eAuLx+CwE9Qnuc3OzEB5Cj4bq7l5N69fhPdf/YNk0eEjHTjzon9J8sXLQFvqG2obQ1vvZPJDpj+pSJSWbXeTwP41YVu9CQFIyfijhpsCGRsiFTx63DRUOH8LmvrVUk8xR5k3+59YueRDWWrQYrEupyEKpLduS2PIeJRChKYizXzpt9i66QeBaEFzxyH1KxZt1FrmhADEVuGcxjk+jRR20rGXwnn8VaFMRBQkQM0uFtU59hBL2joykfyU3YoW52IJKsUGWIvFupIbhxbuscx9lNSlNS8t0mmxHkmM4SRvtVvE3Dc+CRICEIvdcT0DE6SuFQvUIGZqItWlAYW5GJKijwBSstP1pQvfxfy5j4qxUg4GQdqleBfrcmRKFx7a5qXt3kji4L/xe6rUL3CjKCAxAKlwPsY4fhvZ5ozLn0LpkAndfqZ4TBQrVylRApZk2wcplbmL/53/XhdKay3yL/iQmPUz8cWzWFcrZyqWW73yM8x9726h7sCe9Hpcv9Fa5oQAxGp30lR3YqSwV988G7l53Rd/FKSNPN8iqbgfBQ8oDBknHny1tx/8TKe9I4YMSrkQNVIDtHXjD5j5suC/I1TMFDCNDpqCd4DhV5H16LVYl5I/2dfr61bhjefE4qnx+eqnNG0AABIsSURBVD5P1VSt5UsUQDYDGBIuLJm3k5l7JHXljQj/PVW3GbVQPkUqp4jlIs8IX/tq3FOieWESv16LdS36mag6Wlua8fQD08GDEZH5ORp9NW7ND28SBRChFe+Q8Zhx+dMCvZGt0Mp13oO/U/jL0UNLFOf+S9SAaFkvpZ1+5clfgfbzBY9X4Bd5PVWhRBg2e+VyDj4+kkfvxbqWfdeyLopwQpFOIikRVr2aA6Rs7KSRAZP5x0jhx44/CSeffaeonjzrfWhr7wiBgmyt0jXUjtRNsGLx//DFx2JnGnyvz1N1MO2Sze64notschiL9U4Nv/fK77Flw/cCdZuDgVG1qxaulRoHJdc1B4hlbOV0ZuKCfbhJx14C5/FXi8rW5Zfe07M1kUccecaJPF894vNUHYyMNmzYlJzW3DZh7mRjsR5SHbnekgtuJPEgO9W/yjVHCQCkeDUHiLXC8XtwJtjDpGSdlLRTjLbtbA5ZhZEnYU+lui3L8c6L14l2j5n4WO/Kqm6ZKq1257MAOv0CwshYrAOLvn0Fri/+I9Ql4zf4qqv+reU9pDlAbHbnkxwQ3AnnX/EMbIO7pUk/2A/yeOtJDlBiA0TnHnT+ISCGb3zV7uMif7dWVB4BzheL1ZVOi3V6OpCTE13JTU1JPSm5ZyQx4Gmvxy3+L6SkgTBezQFisTu/YMDxkfJc88eP0Dc3Nc0XVOpOdjHaeXnlyYtEIwOCs1/5alyvi1VmtTspM/BRkdfSZbHe5VKtdQDwxvp1eO3pS4Qq4wjtBMoeGBmMmgPEaneS62O3eKl5BcW46kaKYd07KWrIGmCfz+OOGgzMVlF5BedccGycLov1XXv2Y8OB1Apa2tB1tLfiqX+chEAgMkoL2+bzuIq1vMu0BciUKRnWRmE2TNuQ8ThfZItXy46kcl0UmZwilIv85T3q81RFN+k9/PBMa0s2mZ8IYu2kw8l6XUNTN3u7obYBoPBBWtBLj1+Indu3CKryFWdlYv78Di3aoDo0BYi1YvJYcFNNpHBjxk/DKWf/VSuZ06oeSh1GKcTEiHNW4a9xCXOLhTFbopjtpMNiXSzsqVY7le+/diM2rxP502HBcl/1d6u0ukk0BYjNXnk6B58VKVwqpFtLVgbXbz57Akvcb4qN17c+j/tYqYEsq5g0PsDNQsOtNDhZX/FjnSBYBMX4HTG4OOT220U0Nu3tgVB8tK4XhRuKRV989CBWfC98bGdgZ3g9Lnl+BFLK13oGsZQ7b2IMgpMwiuRO4X6SSTTd650pinzN6eR89y6R0EYMv/ZVu1+ToxNrufNrMAjAlMqLdbFcj1197TwI5mjrCHTLbkXX6bB4xOBBkiGH6E+H/nwiiXPc7K9xPyRHr3J4NJ1BrBWVz1Aw4ciGL7jyWVjL7HLkSQjP7ub9WF+7LfTPRYEf9KKaZXPw6QeioXv2+8pL8/HuuxEGReKSWcsrLwLjAjCl8mJ9W1MztvqFJjVSuqcojpaBsWcPqmPDmgWY9eafhdUx9h9ftev/pNqRe11bgJQ754NBsKf/f3+agz4aBUGT27FwvjWb6kNhS/XOnvTBG3/Exh9Fk7D+2+dxCyK+xOibyWp3bgcgyI2Waot1CgbYsKMZ25uaFQ8VRVMZPXQQKO+8FO3YthkvP/FLMTZZj65S9Xddl5ZEbk0ArPZKH8C7Oc/n5RfjqpuSt8XbEQhg5dqfjCHJU44CZCea6r2r8cazV4g2Y+I4rK7GLQwRGEOoaMlQU2WxvmP3PtRv2w0CiFpSEpSD8yCeuP/noC3fCNrh87gHqJUhspxmACkrc/QJFLJ9kQ3Q6TmdoieLxPxNyg+xJjzw9YJ5T2PxArElBnP5PK6jlerDUl5pZ4yLhkBJ1sk6GZj6t+2OK8ZAlx7I+U3piTu5Le9o3CRQpXkX71tbWyVqy6ZU75oBZFDFpPFmkd2WZG/xLl0tCA8cymZFuc8TRXSARSfnYibZHPwSv6fqFTVtW+1Oirt5UmTZZC3WKVwQASSeWaOrL2ryIn7w+s2g0K2RFGCBCQ3VC1eo0XHCZhCr3XkOgJmRDTimXI7JU8QfNbToQKw6mvbsx8YDJ7mRfHRo1ScrAyaTCWYTC73TDooWRKnCKGWYCLWYd/H+av/dLHbnuQwQGHQle7EezStUri6tAwtU5Xr5au4jWLZQcMtRs+f6PO735LYfi0+bO4LWH+WOv4Cxv0c2RgeENIvoSbSXTvlEaLFI73LIxFgoDx+F+iegUOgbW7G6fOEfvnUL1q8WpGakY9nHfNXxZUMSW+dR/5K9WCeDU29Dk6r4AqTz8aOUZ3OOjIp/cJw5v8VXUyWSjkrOndCdRzOAWMqdLzCGyyNFuPCq52ApLVcumYISwSAPZculaPEEiGh5ReRWSXn4DilV9wgW1ZAutPPPJ/k9VYvkyiHGZ7M77uZgAs+zlFms79oLmlHoT0oJDbH0x4AiZTkq6SSdTtQjiXO86K9xa/LYohlArHYnZRoVLD6v/fMnyOkjiGSjRHdReSn3HmWfImBoReTyO3a4+iiW7i+fw8JvXhITx+3zuCvjldM2zjGGB5moKUWyFutifVr+Y63gEDBW39XofddOL17893ki1arbCBGTTzuAVDgbwNHNkjIvfyCuuklgeRLvPdKtPC0UyedAC6L993EjbaozKtHWI52c0x59JDHGr/BWV72ohZxWu5P2zc+IrCtZi3WxPtVs8IUi0CihUUPpBF1ZDsQn7z8RbW2CzdMmn8etiW+FJgAZOLoyPzODC2L36LXFS49VG2q3heLKxkPx5h750fMFPn5X1O++rbU1ULxj3UJhfCMVAlsrHGeBM0FolGQv1sO7oiY/O6WaJmNGJfTq0xdjW/16QZH2DlawbY2LLKHjIk0AYhs7+WfcZBI4W4enW4tLShmF29oD2OzdjmaZi/LIKrUIREdebuTtFkkc/Am/p0o8GJaMvomxWO1OikM7LPJashfrXfJsrNsOCukUTrT5QXom71FK/U2Px/Qe/sdGi3UlORE/fPMvWL+Gnu67EwsGD/eu+k4YxFehvrUBSEXl+ZzztyLbdky9EmTJqycpzcdHsqndZgzv1/bGTaGzDzFiCB7t9Xzn0lIP1orKW8G5wNArVRbrlHOE0i90UUaGGYeUDhB9hKIdsL20ltzfGsplosSoNJq1NGPsAm+16+14da4JQKx2JzkI3xMpzPRz78boioSlsI7ad6X78lrYaEWNGctR5a9xO+MdqMjyZXbHiACYaIibVFis0waKb1vnEyWlrThkcDFyEpC2e+WSWZg3+wEx9d7h87jvjVfvGgGk8mWAXxwpzC+vfgEltjHxyqi4/O7mFqyvbVRUbvzI0tA5iFp69amLsa1B+CyMBObPs9idbzLggkiZU2Gx3pW5ih6nEhkillIhUEoEIbFXfB6XiOO6shHWBCCWcqebMTgim77uL58iO0f/UD5kOEfrESU0vHQAKIOuGlq36mvMfvtWQVEOtJsDHda61YuUCSNTiGgOaqmwWKcxoHBOtDOVSGre3YjnHj5TqHuNZm5NAHLAFLtbVOrc/IG4OsFbvNEUL5Z3T2qQBhTlYYhF3c7gnPfuwpqVnwuaYAmKOB7ekNXuJBdnQSrcZC/WKWI/7UrpQRTAgSLHdCOGXb5qtzpTiLCK4gbIkHFH92sPBndEKkKvLV6xAQh//g2/ToHpKMVx4849gpPerEwz7IfaFI/nzu1bQ2cfwaBwz58z01R/9YL5iitVUMBmd/6ZAwKzilRZrCvoimrW1565FI1+4XIs02Tqv2XlAuVeW1oCxGJ3HMXABN7zFEWRoikmg2rrm0Ig6CLaXiTTkXBvQjJkJJ7wU3g15yDRovwxhu+81W7BY6fW+igZf+xwU6BDkO6O2kmFxbrW/RWr7+N378CPni9FHnHjN+2JewaJ5g5KcXgpHm8yKHyrlxJSDi7pF9VSl85P6nfsDj0v24oLIRUsILI/r//nMjT4BLG6wTh+561xR8/xrKFibHbHSxxMoOxUWKxr2M2oVVEYUvqjEiIkelA+uXLFDRBbhfMuziGI6XPqeX/DKHtCUldL9o1O1Xc178dgSz9F8X4puSild5NLUf2igY72oHnItlXfCtOxyq1cAZ9lXOV0FhQGDE+FxbqCbqhmjeb7zxju9la71adV1iKqidVe+TrABc7BF13zXwyyjlLd6XgKkpkDZWlNdLzfT/93D2qWkw+TgJ7zedziwbDi6ViMsla7k06ND4tkSfZiPUHd7Vatd+tKvP2CWJwG9obP4xI/vZUpWNwziNXuJPPtIyPbu/6Wz5GVrW7bVKbsSWWjXN3k8iniE01BB6Z5q13Cba0ESmwpd9zMGHswsonesFinxETPPHCqmHYX+zxuQWxjJcMQP0AqnE3gKAxvNDd/AK6+SbPYXUr6oxvv96438O3noklVF/k87km6CXKgIWt55RCw4EaACU47e8Ni/el/TkfL/l2Rat/j87jjitARF0Ashx1TzNoCDZFSUSZbymjbk+nN566Cv04QZZW6fJPP4344GX2P5rTWGxbr0caDZ5kH+Zd+q8ysImzw4gKIrcLh5JwJjPBGjzsZx57yp2TcI7q0uXXDInwy8y/CthgCCLJDfDUuYVRlHSSz2h0nAUywKMovtODcy19ERqY+B3c6dFXQxFcf3Y91NfMEvzPGK73VVaLByeTIGRdALPbKSxi4wH3ukPEXYEi58PhfjkDpwLNm0X/g2yA0a2fAS16PW1/z5QiFRTP7GTPpWliGC2L6pYO6Zcm4qfo9bKoWJijiYJf6Pa6XZVUiwhQXQKx2J1lL3hZZr73yBhQPnqxWppQu17pvBxbPvQkd7cKwS5zhNH+1W5CfUc8OWUn54I9EtjnAdhjGHSsSqlNP4RLYVsNmF2qqRI+d7vN53KpPrOMFyDsABE7BR5z8T+QVDU2gOpJXde2aOVi3VOxQii/x1VQdkTzJOlsum+AoDXSY1gBccKBz+LT7kN//0GSLmJD29+zYgCWfCQ1GAbzr87hnqG00XoAsBTAxsvFjzn0Z5gxlvsVqO6B3uaXz/opd29YIm9Uw1Ey8fYqWAHTwmF/g0IlxHQvEK1rCytOMvuA90afbZT6PW3A+JFeQOAHioPS03f6psvv0g+OMp+W2n1Z8O+tXYvlXotHagwGGUQ3VbhGHEP27aBs7+efcZBKcw+TkFuPIU/7VY/+83B9cg7aWyK3e7jnolY6GaoAMHl1p68jgdZENFg0ai4nH98xsUj9+/zy864Q7JWB43Vft/pVS5SeSP1oYpp68WF/6xV3Y1dgtm3ZIxRkdrHTrGtdPEcwVKF41QCzjHMexIBOYclsPmYrRRwlShCgQKTVZ6Z9p8Zyb0N4mDOvPTPws78qq5IWwF1GZrdz5W87wWOSlnrxYX7PoGfg2CL0LuIlP8a+s+lrNnaUaIDZ75ZUc/LnIRg+Z8EsMGXu6GllSukzd2k+xdsl/BTJysBV+j2tCqglfXH6kJYNlUjR4QSqAnrpY37JqFjYsF6a7Y2BXeT0uQbZgOWOmGiCWcuddjAmteO1H34jisrjMX+TIrTvPsi//hqYG4ck5B7/T76kSBKzQXUCxWcTufIoD10Ze6qmL9cbaRfAsEBoxcI67/TXqrHoNgMi4k7eu/gjrl0VJJ6hxVlUZ4shmsdgrpzJwgSeRyZQROhPpZxknu650YEwLgAyrOBf06glE642GTQuw9gfRWLvUxbj22PXQkTVKWjza0Ro+/nyUDFWcy0cPsVW1sal6JugVSSk1g3QJVzQosRHdVWlQYSGxR6rwKoKM/6K+uuojhdXqyh5trdglRGZWHnKLhugqUyIaizVWSQEIpkzJsDS0fSMW7icRCki1OjnYbL/HlRa7ERa7cx4DkuPemeSB4xT+Z1DWsZg/X1kk7QNyq16DUHlLhfNUxpHS/6AJGp+2IOPnpPrs0dX3A/GzyJIvK0H6SNlq47WPiwsgnSBx3My40JMtZTUWv2Bbg8Cl9R63MIxG/HUnrIYSu/N4E0CLqcEJayTFKuaM/9FfXfWveMSKGyDU+IF/KHKvK4tHmDQo+66J436lKZxTpV+l5c6JQUbZqfhZqSJTguSoZWDXez2uuN1aNQEIdbJknGOcKWj6OcApFhS90h4sDFhPB4Gc8xVg5nf8nm9FXQgTNMgJq5Yc3RA0ncUZHw+ADjlLEtaYfhXXAqgCWFXQFJxXv7JqpRZN/z/23wvm7C/ORQAAAABJRU5ErkJggg==',
      dog: 'data:image/png;base64,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',
      cat: 'data:image/png;base64,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',
      flag: 'data:image/png;base64,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',
    }
    // 自定义图章编辑界面显示的内容，可以将自己的业务逻辑需要显示的内容添加到这里
    var _DOM_Modal_Stamp = document.getElementById('modalstamp')
    _DOM_Modal_Stamp.setAttribute('style', 'display:none')
    var _DOM_Input_Stamp = document.getElementById('inputstamp')
    var _DOM_OK_Stamp = document.getElementById('stampok')

    // 图章批注需要输入文字回调
    gstarSDK.notes.noteStamp.detailCB = function (currentData) {
      // 显示弹框，返回当前图章的自定义数据
      _DOM_Modal_Stamp.setAttribute('style', '')
      if (currentData.textstamp) {
        _DOM_Input_Stamp.value = currentData.textstamp
      }
    }
    _DOM_OK_Stamp.onclick = function () {
      var textstamp = _DOM_Input_Stamp.value
      gstarSDK.notes.noteStamp.setData({ textstamp })
      _DOM_Modal_Stamp.setAttribute('style', 'display:none')
      _DOM_Input_Stamp.value = ''
    }





    //获取浏览器信息
    function getBrowerInfo() {
      var userAgent = window.navigator.userAgent.toLowerCase()
      var browserType = ''
      // 浏览器类型IE
      if (userAgent.match(/msie/) != null || userAgent.match(/trident/) != null) {
        browserType = 'IE'
      }
      return browserType
    }

    //获取IE浏览器版本
    function getIeVersion() {
      var IEMode = document.documentMode
      var rMsie = /(msie\\s|trident.*rv:)([\\w.]+)/;
      var ma = window.navigator.userAgent.toLowerCase()
      var match = rMsie.exec(ma);
      try {
        return match[2];
      } catch (e) {
        return IEMode;
      }
    }

    window.onload = function () {
      var browser = getBrowerInfo()
      var ieversion = getIeVersion();
      if (browser == 'IE') {
     
          alert('不支持IE浏览器，请换其他浏览器打开')
        
      }
    }

  </script>
</body>

</html>`
export default html