const html = `
<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>三维demo</title>
  <style>
    html,body,#wrap {
      width: 100%;
      height: 100%;
    }
  </style>
  <!-- 引入Visualize.js -->
  <script src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/hoops-web-viewer-monolith.umd.js"></script>
</head>
<body>
  <!-- 览图dom容器.js -->
  <div id="wrap"></div>
  <!-- 引入main.js -->
  <script src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/WebCAD.js"></script>
  <script>
    // 页面加载完成后
    window.onload = () => {
      // 实例化三维览图应用
      const wcad = new WebCAD({
        wrapId: 'wrap' // 必填，dom容器ID
      })
      // 初始化,异步promise
      wcad.init({
        languagePath: '', // 选填，界面语言字典url，默认中文，可以设置其他字典，参考en.json
        treeLanguagePath: '', // 选填，结构树语言字典url，将结构树节点中关键字替换，参考tree-zh.json
      })
      .then(async () => {
        // 初始化成功，调用render

        // 获取轻量化图纸数据
        const ocf4Buffer = await fetch('${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/demo/three/sample.ocf4').then(res => res.arrayBuffer())
        // render异步，返回promise
        return wcad.render({
          fileSource: ocf4Buffer, // 必填，绘图资源；
        })
      })
      .then(() => {
        console.log('render 完成')
      })
      .catch(err => { // 捕获错误输出
        console.error('error', err)
      })
    }
  </script>
</body>
</html>
`
export default html