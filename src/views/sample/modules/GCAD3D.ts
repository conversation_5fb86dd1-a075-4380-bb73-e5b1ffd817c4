const html = `
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <title>H5版浏览CAD图纸</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    #webcad {
      width: 100%;
      height: 100%;
    }
  </style>
</head>

<body>
  <script type="text/javascript" src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/crypto-js.min.js"></script>
  <script type="text/javascript" src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/hoops-web-viewer-monolith.umd.js"></script>
  <script type="text/javascript" src="${import.meta.env.VITE_PUBLIC_PATH ? import.meta.env.VITE_PUBLIC_PATH : '..'}/assets/GCAD3D.umd.js"></script>
  <div id="webcad"></div>

  <script>
    // 快速集成方案：提供GCAD3D.js，封装WebCAD实例化与图纸转换下载等逻辑，方便快速览图集成
    async function start(){
      var wcad = await GCAD3D({
        vendorcode:"htest:03d6140844f77bc14cec06a54f5919c0", //必须，开发者的编码.(由服务器指定生成）
        element: "webcad",  //容器ID，CAD图纸将这个id对应的容器里面显示
        param: {
          // 图纸服务相关参数
          fileName: '', // 必填,显示的文件名称。（通过该值区分文件类型，该值会参与文件校验）
          cadServer: '', // 可选,浩辰CAD服务器地址。 当不指定是：https://cloudapi.gstarcad.com
          fileDownLoadUrl: '', // 可选（与fileId二选一）待转换文件的下载地址
          fileId: '9386', // 可选（与fileDownLoadUrl二选一）此FILEID是开发商服务器向浩辰服务器PUSH文件时返回的文件ID
          fileToken: '', // 可选,文件信息。用于从数据服务器获取文件。
          etag: '', // 可选,文件的Etag标识数据。（该值会参与文件校验）
          version: '', // 可选,文件版本,默认V1
          size: '', // 可选,文件的大小。（该值会参与文件校验）
          userToken: '', // 可选,用户的信息。用于从数据服务器获取文件。（该值不会参与文件校验）

          languagePath: '', // 可选，界面语言字典url，默认中文，可以设置其他字典，参考en.json
          treeLanguagePath: '', // 可选，结构树语言字典url，将结构树节点中关键字替换，参考tree-zh.json
        }
      })
    }
    
    start()

  </script>
</body>

</html>
`;
export default html

