import { $t } from '@/locales';

// 功能菜单
export const fnMenuList = [
  // {
  //   key: 'test',
  //   title: '这是测试菜单'
  // },
  {
    key: '一',
    title: $t('一、图纸显示'),
    children: [
      {
        key: 'dwg-show',
        title: $t('图纸显示')
      },
      {
        key: 'open-local-ocf',
        title: $t('打开本地ocf')
      },
      {
        key: 'show-map',
        title: $t('打开小地图')
      },
      {
        key: 'hide-side',
        title: $t('隐藏菜单')
      },
      {
        key: 'custom-side',
        title: $t('自定义菜单')
      },
      {
        key: 'watermarks',
        title: $t('水印')
      },
      {
        key: 'GCAD',
        title: $t('快速集成')
      }
    ]
  },

  {
    key: '二',
    title: $t("二、功能接口"),
    children: [
      {
        key: 'button-click',
        title: $t('按钮点击事件')
      },
      {
        key: 'layer-layout',
        title: $t('图层布局')
      },
      {
        key: 'save-pdf',
        title: $t('另存、转PDF')
      },
    ]
  },
  
  {
    key: '三',
    title: $t("三、批注功能"),
    children: [
      {
        key: 'check-view',
        title: $t('审图、图章')
      },
      {
        key: 'note-action',
        title: $t('批注操作')
      },
      {
        key: 'view-position',
        title: $t('视图定位')
      }
    ]
  },
  {
    key: '四',
    title: $t("四、扩展功能"),
    children: [
      {
        key: 'overlay-dwg',
        title: $t('叠图功能')
      },
      {
        key: 'compare-dwg',
        title: $t('图纸对比')
      }
    ]
  },

] as any

export const threeMenuList = [
  {
    key: 'demo3d',
    title: $t('3d样例')
  },
  {
    key: 'GCAD3D',
    title: $t('快速集成')
  }
] as any

export const cloudCADMenuList = [
  {
    key: 'GCADCloudCAD',
    title: $t('快速集成')
  }
] as any

const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  {
      eager: true,
  }
);

export const getCodeByKey = (key: string) => {
  console.log("modules", modules);
  for (const path in modules) {
    if (path.includes(key)) {
      return modules[path].default
    }
  }
}