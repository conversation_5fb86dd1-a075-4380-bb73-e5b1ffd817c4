<template>
  <div class="login">
    <div class="login-container">
      <div class="login-content shadow-2xl">
        <h4 class="login-content-title">
          {{ name }}
        </h4>
        <div class="login-content-main">
          <el-tabs v-model="activeName" class="login-tabs" @tab-click="handleClick">
            <el-tab-pane :label="$t('账号登录')" name="first">
              <el-form ref="ruleFormRef" :model="ruleForm" status-icon :rules="rules" size="large" class="login-form">
                <el-form-item prop="user_name">

                  <el-input v-model="ruleForm.user_name" autocomplete="off" :placeholder="$t('用户名/邮箱/手机号')">
                    <template #prefix>
                      <el-icon class="el-input__icon">
                        <component :is="$ElIcon['User']" />
                      </el-icon>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item prop="password">
                  <el-input v-model="ruleForm.password" type="password" autocomplete="off" show-password>
                    <template #prefix>
                      <el-icon class="el-input__icon">
                        <component :is="$ElIcon['Unlock']" />
                      </el-icon>
                    </template>
                  </el-input>
                </el-form-item>
              </el-form>
              <el-button v-noBlur class="login-submit" @click="login" round type="primary" :loading="loading" :title="$t('登录')">
                <span class="btn-text">{{$t('登录')}}</span>
              </el-button>
              </el-tab-pane>
              <a class="go-doc" target="_blank" rel=noopener href="/sdk">{{ $t('开发文档') }}</a>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, onMounted, onBeforeUnmount } from 'vue'
import type { FormInstance, TabsPaneContext } from 'element-plus'
import { useRoute } from 'vue-router'
import type { LocationQuery } from 'vue-router'
import { useUserStore } from "@/stores/user"
import { useStoreConfig } from "@/stores/config"
import type { ILoginData } from '@/services/auth'
import { validatePassWord } from '@/utils/validate'
import { redirectRoot } from '@/utils/const'
import { emitter } from "@/utils/mitt"
import { $t } from '@/locales'

const activeName = ref('first')
const ruleFormRef = ref<FormInstance>()
const loading = ref(false)
const route = useRoute()
const userStores = useUserStore()
const configStores = useStoreConfig()
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}

/** 登录表单数据 */
const ruleForm: ILoginData = reactive({
  type: 'user_name',
  user_name: "",
  password: "",
  redirect: '',
  otherQuery: {},
})

const name = computed(() => {
  return configStores.site_name
});

watch(() => route.query, query => {
  if (query) {
    ruleForm.redirect = query.redirect?.toString() ?? ''
    ruleForm.otherQuery = getOtherQuery(query)
  }
}, { immediate: true, deep: true })

const validateUserName = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error($t('请输入用户名')))
  } else {
    callback()
  }
}
const validatePassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error($t('请输入密码')))
  } else if (!validatePassWord(value)) {
    callback(new Error($t('密码格式不正确，长度在6到32位之间')))
  } else {
    callback()
  }
}

const rules = reactive({
  user_name: [{ validator: validateUserName, trigger: 'blur' }, { validator: validateUserName, trigger: 'change' }],
  password: [{ validator: validatePassword, trigger: 'blur' }, { validator: validatePassword, trigger: 'change' }],
})

/** 使用公共函数，避免`removeEventListener`失效 */
function onkeypress({ code }: KeyboardEvent) {
  if (code === "Enter") {
    login();
  }
}

onMounted(() => {
  window.document.addEventListener("keypress", onkeypress);
});

onBeforeUnmount(() => {
  window.document.removeEventListener("keypress", onkeypress);
});

function getOtherQuery(query: LocationQuery) {
  return Object.keys(query).reduce((acc, cur) => {
    if (cur !== 'redirect') {
      acc[cur] = query[cur]
    }
    return acc
  }, {} as LocationQuery)
}

const login = async () => {
  try {
    loading.value = true;
    (ruleFormRef.value as any).validate(async (valid: boolean) => {
      if (valid) {
        await userStores.login({
          type: ruleForm.type,
          user_name: ruleForm.user_name,
          password: ruleForm.password,
        });
        // 登录成功事件
        emitter.emit('loginSuccessful', {
          to: {
            path: ruleForm.redirect || redirectRoot,
            query: ruleForm.otherQuery as LocationQuery
          }
        })
      } else {
        return false
      }
    })
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false
  }
}
</script>
<style lang="scss" scoped>
.login {
  height: 100%;
  width: 100%;
  // background: url('/imgs/login/bg.jpg') no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;

  &-content {
    width: 400px;
    padding: 20px;
    position: absolute;
    // right: 200px;
    top: 50%;
    transform: translateY(-50%) translate3d(0, 0, 0);
    background-color: var(--el-color-white);
    // border: 5px solid var(--el-color-primary-light-5);
    border-radius: 8px;
    overflow: hidden;
    z-index: 1;
    height: 460px;

    &-title {
      color: var(--el-text-color-primary);
      font-weight: 500;
      font-size: 22px;
      text-align: center;
      letter-spacing: 4px;
      margin: 15px 0 30px;
      white-space: nowrap;
      z-index: 5;
      position: relative;
      transition: all 0.3s ease;
      white-space: normal;
    }

    &-main {
      margin: 0 auto;
      width: 80%;
    }
  }

}

.login-container {
  width: 50%;
  position: absolute;
  left: 50%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login {
  background: url('/assets/imgs/login/login-819-410.png') 0% 0%/100% 100% no-repeat;
}

.login-tabs :deep(.el-tabs__item) {
  letter-spacing: 2px !important;
}

.el-form-item--large {
  margin-bottom: 30px;
}

.el-form-item--large :deep(.el-form-item__error) {
  padding-top: 8px;
  font-size: 14px;
}

.login-form {
  margin-top: 20px;
}

.login-submit {
  width: 100%;
  letter-spacing: 2px;
  font-weight: 300;
  margin-top: 15px;
  height: 36px;
}

.login-forget {
  text-align: right;
  margin: 10px;
}

.go-doc {
  margin-top: 15px;
  display: inline-block;
  float: right;
  color: var(--el-color-primary);
}
</style>
