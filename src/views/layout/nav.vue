<template>
  <div class="layout-nav" @drop="onDrop" @dragover="ondragover">
    <el-menu :default-active="activeNavPath" class="el-menu-vertical-demo" :collapse="!isNavExpand" @select="menuSelect">
      <template v-for="(item, i) in navList">
        <el-sub-menu :index="item.path" :key="item.path + i" v-if="item.children && item.children.length !== 0">
          <template #title>
            <el-icon>
              <component :is="$ElIcon[item.meta.icon]" />
            </el-icon>
            <span class="nav-menu-text" :title="$t(item.meta.title)">{{ $t(item.meta.title) }}</span>
          </template>
          <el-menu-item v-for="(child) in item.children" :index="child.path" :key="child.path">
            <el-icon>
              <component :is="$ElIcon[child.meta.icon]" />
            </el-icon>
            <span class="nav-menu-text" :title="$t(child.meta.title)">{{ $t(child.meta.title) }}</span>
          </el-menu-item>
        </el-sub-menu>
        <el-menu-item :index="item.path" :key="item.path" v-else>
          <el-icon>
            <component :is="$ElIcon[item.meta.icon]" />
          </el-icon>
          <template #title>
            <span class="nav-menu-text" :title="$t(item.meta.title)">{{ $t(item.meta.title) }}</span>
          </template>
        </el-menu-item>
      </template>
    </el-menu>
    <el-divider v-if="folderMenuList.length > 0 || fileMenuList.length > 0"></el-divider>
    <el-scrollbar :key="key" v-if="activeMenuPath === '/files'">
      <div v-for="folder in folderMenuList" :key="folder.id" class="folderList" @click="handleClickFileName(folder)"
        :title="folder.name">
        <FileIcon :width="40" :height="30" :item="folder" type="shortcuts"></FileIcon>
        <div class="ml-2 truncate quickMenuLabel">{{ folder.name }}</div>
        <el-icon class="action-icon" @click.stop="delMenu(folder)">
          <component :is="$ElIcon['Close']" />
        </el-icon>
      </div>
      <div v-for="file in fileMenuList" :key="file.id" class="folderList" @click="handleClickFileName(file)"
        :title="file.name">
        <FileIcon :width="40" :height="30" :item="file" type="shortcuts"></FileIcon>
        <div class="w-full quickMenuLabel">
          <div class="ml-2 truncate">{{ file.name }}</div>
          <div class="ml-2 mt-1 text-xs text-slate-400">{{ file.version }}</div>
        </div>
        <el-icon class="action-icon" @click.stop="delMenu(file)">
          <component :is="$ElIcon['Close']" />
        </el-icon>
      </div>
      <el-divider v-if="versionBaseLineList.length > 0"></el-divider>
      <div v-for="vb in versionBaseLineList" :key="vb.id" class="folderList" @click="handleClickVersion(vb)"
        :title="vb.name">
        <FileIcon :width="40" :height="30" :item="vb" type="snapshot"></FileIcon>
        <div class="w-full quickMenuLabel">
          <div class="ml-2 truncate">{{ vb.name }}</div>
          <div class="ml-2 mt-1 text-xs text-slate-400">{{ vb.version }}</div>
        </div>
        <el-icon class="action-icon" @click.stop="delVersion(vb)">
          <component :is="$ElIcon['Close']" />
        </el-icon>
      </div>
    </el-scrollbar>
    <snapshot-dialog></snapshot-dialog>
    <span class="layout-top-back" @click="toogleNav">
      <el-icon>
        <component :is="$ElIcon[ isNavExpand ? 'CaretLeft' : 'CaretRight']" />
      </el-icon>
    </span>
  </div>
</template>

<script setup lang="ts">
import { useStoreRouter } from '@/stores/router'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { useStoreConfig } from '@/stores/config'
import { emitter } from '@/utils/mitt';
import { computed, onMounted, onUnmounted } from 'vue';
import type { FileItem } from '@/model/file';
import { encodeQueryParams } from '@/utils';
import { checkShortcut } from '@/services/shortcut';
import { downloadFileByStorageId } from '@/services/file';
import { gotoItem } from '@/utils/file';
import socket from '@/utils/socket';
import { $t } from '@/locales';

const configStores = useStoreConfig()
const { isNavExpand } = storeToRefs(configStores)
const router = useRouter()
const stores = useStoreRouter()
const { activeNavPath, navList, folderMenuList, fileMenuList, versionBaseLineList, activeMenuPath } = storeToRefs(stores)

const key = computed(() => router.currentRoute.value.path)

const toogleNav = () => {
  configStores.toggleNavExpand()
}

onMounted(() => {
  // 获取快捷菜单
  stores.getQuickMenuList();
  // 获取快照列表
  stores.getVersionBaseLineList()
  // 监听file事件
  socket.on('file', fileChange)
})

onUnmounted(() => {
  // 取消file事件监听
  socket.off('file', fileChange)
})

// 文件相关socketio事件处理
const fileChange = (ename: string, data: any) => {
  switch (ename) {
    case 'file.delete': {
      const isExitFolderShut = folderMenuList.value.some(v => data.fileName.includes(v.name))
      const isExitFileShut = fileMenuList.value.some(v => data.fileName.includes(v.name))
      if (isExitFileShut || isExitFolderShut) {
        // 如果删除的文件在快捷方式列表里，则刷新
        stores.getQuickMenuList();
      }
      const isExitFileVersion = versionBaseLineList.value.some(v => data.fileName.includes(v.name))
      if (isExitFileVersion) {
        // 如果删除的文件在快照列表里，则刷新
        stores.getVersionBaseLineList()
      }
    }
  }
}

// 删除快捷文件
const delMenu = (item: FileItem) => {
  ElMessageBox.alert($t('你确定要删除{name}快捷文件吗', { name: item.name }), $t('删除快捷文件'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    type: "warning",
  }).then(async () => {
    stores.delQuickMenu(item)
  });
}

// 删除快照文件
const delVersion = (item: FileItem) => {
  ElMessageBox.alert($t('你确定要删除{name}快照文件吗', { name: item.name }), $t('删除快照文件'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    type: "warning",
  }).then(async () => {
    stores.delVersionBaseLine(item)
  });
}

const menuSelect = (index: string) => {
  router.push(index)
}

// 点击快捷文件
const handleClickFileName = async (item: FileItem) => {
  try {
    await checkShortcut(item.id)
    gotoItem(item, () => gotoShortcutFolder(item))
  } catch (e: any) {
    console.log(e)
    if (e.code === 401003 || e.code === 401002) {
      setTimeout(() => {
        stores.delQuickMenu(item)
      }, 100);
    }
  }
}

// 点击快照文件
const handleClickVersion = async (item: any) => {
  console.log("item", item)
  gotoItem(item, () => gotoSnapshotFolder(item))
}

// 快捷方式文件夹跳转
const gotoShortcutFolder = (item: FileItem) => {
  const obj = {
    folderId: item.fileId,
    direction: 0,
    limit: '10',
    searchName: '',
    searchBeginTime: '',
    searchEndTime: '',
    fileType: 0,
    sortField: 0,
    ascOrDesc: 'desc',
    lastFileId: 0,
    lastFileSize: 0,
    lastFileModifyTime: '',
    lastFileName: '',
  }
  const routeUrl = encodeQueryParams(obj)
  router.push({ name: 'filesFile', query: { params: routeUrl } })
}

// 快照文件夹弹窗
const gotoSnapshotFolder = async (item: any) => {
  try {
    const data = await downloadFileByStorageId({
      storageId: item.treeId,
      fileName: item.name,
    }, "json")
    console.log("data", data)
    emitter.emit("snapshotDialogShow", { item: { ...item, children: data }, width: '85%' });
  } catch (e) {
    console.log(e)
  }
}

const onDrop = (e: any) => {
  const dataTransfer = e.dataTransfer;
  const item = dataTransfer.getData('item');
  if (item) {
    stores.addQuickMenuList(JSON.parse(item))
  }
}

const ondragover = (e: any) => {
  e.preventDefault();
}

</script>

<style lang="scss" scoped>
.layout-nav {
  width: var(--app-width-nav);
  position: fixed;
  padding-top: var(--app-height-header);
  top: 0;
  left: 0;
  // min-height: 100vh;
  height: 100vh;
  max-height: 100vh;
  overflow-x: hidden;
  background: var(--el-menu-bg-color);
  overflow-y: auto;
  box-shadow: 2px 0 8px 0 rgb(29 35 41 / 5%);
  z-index: 10;
  --el-menu-item-height: 46px;
  --el-menu-sub-item-height: 46px;
  transition: width var(--el-transition-duration) .3s;
  display: flex;
  flex-direction: column;

  h3 {
    text-align: center;
    font-size: 18px;
    padding: 20px 0;
    letter-spacing: 1px;
    height: 64px;
    transition: .3s;
  }

  .el-menu {
    border-right: 0;
    margin-top: 56px;

    .el-menu-item {
      color: var(--app-color-nav-text);
    }

    .el-menu-item.is-active {
      color: var(--app-color-primary);
      background: var(--app-color-nav-item-active);
    }

    .nav-menu-text {
      display: inline-block;
      max-width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: middle;
    }
  }

  .horizontal-collapse-transition {
    overflow: visible !important
  }

  .el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 200px;
    // height: 100%;
  }

  .folderList {
    display: flex;
    align-items: center;
    padding-left: 20px;
    padding-right: 20px;
    margin-bottom: 10px;
    cursor: pointer;

    &:hover {
      color: var(--app-color-primary);
      background: var(--app-color-nav-item-active);

      .action-icon {
        display: block;
      }
    }

    .quickMenuLabel {
      width: calc(100% - 40px - 16px);
      max-width: calc(100% - 40px - 16px);
    }

    .action-icon {
      display: none;
    }
  }

  .title {
    color: #0052cc;
    margin-left: 4px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .layout-top-back {
    background-color: #ecf5ff;
    position: absolute;
    right: 0px;
    top: 50%;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    display: flex;
    // background: #E6E8EB;
    // border: 1px solid #e6e8e8;
    color: #666;
    // margin-right: 12px;
    font-size: 20px;
    cursor: pointer;
    transition: .2s;

    &:hover {
      background: #E6E8EB;
      color: var(--app-color-primary);
    }
  }
}</style>
