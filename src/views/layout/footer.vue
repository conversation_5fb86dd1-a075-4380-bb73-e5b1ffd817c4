<template>
  <div class="layout-footer">
    {{ record }}
    <!-- Copyright © 1992-2023 苏州浩辰软件股份有限公司 版权所有 苏ICP备12077906号 -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useStoreConfig } from "@/stores/config"
import { storeToRefs } from "pinia";
const configStores = useStoreConfig()
const { site_beian, site_copyright } = storeToRefs(configStores);
const record = computed(() => `${site_copyright.value} ${site_beian.value}`)
</script>

<style lang="scss" scoped>
.layout-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  height: 34px;
  color: #0c030c;
  margin-left: var(--app-width-nav)
}
</style>
