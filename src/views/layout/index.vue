<template>
  <div class="app-layout">
    <layout-header></layout-header>
    <div class="app-layout-main">
      <layout-router-view></layout-router-view>
    </div>
  </div>
</template>
<script setup lang="ts">
  import layoutHeader from '@/views/layout/header.vue'
  import layoutRouterView from './view.vue' 
</script>
<style lang="scss" scoped>
  .app-layout-main {
    padding-top: var(--app-height-header);
  }
</style>
