<template>
  <div class="default-view" :class="{ 'expand': !isNavExpand }">
    <layout-nav></layout-nav>
    <div class="default-view-content">
      <layout-top></layout-top>
      <div class="default-view-content-wrapper">
        <layout-router-view></layout-router-view>
      </div>
    </div>
    <layout-footer></layout-footer>
  </div>
</template>
<script setup lang="ts">
import layoutNav from './nav.vue'
import layoutRouterView from './view.vue'
import layoutTop from './top.vue'
import layoutFooter from './footer.vue'
import { storeToRefs } from 'pinia'
import { useStoreConfig } from '@/stores/config'
const configStores = useStoreConfig()
const { isNavExpand } = storeToRefs(configStores)
</script>
<style lang="scss" scoped>
.default-view {
  &-content {
    // background: #fff;
    // margin: var(--app-padding-min);
    // margin-bottom: 0;
    // min-height: calc(100vh - var(--app-height-header) - var(--app-padding-min) - 28px);
    padding: var(--app-padding-min);
    margin-left: var(--app-width-nav);
    transition: var(--el-transition-duration) .3s;
    padding-bottom: 0;

    .default-view-content-wrapper {
      // padding: var(--app-padding-mid);
      // background: #fff;
      height: calc(100vh - var(--app-height-header) - var(--app-padding-min) - var(--app-margin-min) * 2 - 36px - 28px);
    }
  }

  // transition: padding var(--el-transition-duration) .3s;
}

// 左侧导航是否伸展
.expand {
  .default-view-content {
    margin-left: var(--app-width-narrow-nav);
    transition: var(--el-transition-function-ease-in-out-bezier) .3s;
  }

  .layout-nav {
    width: var(--app-width-narrow-nav);
    transition: width var(--el-transition-function-ease-in-out-bezier) .3s;
  }
}
</style>
