<template>
  <div class="default-router-view">
    <!-- <router-view v-slot="{ Component, route }">
      <transition mode="out-in">
        <keep-alive :include="cachedViews">
          <component :is="Component" :key="route.name" />
        </keep-alive>
      </transition>
    </router-view> -->
    <router-view></router-view>
  </div>
</template>
<script setup lang="ts">
import { useStoreRouter } from '@/stores/router'
import { computed } from 'vue';
const stores = useStoreRouter()

const cachedViews = computed(() => {
  return stores.cachedViews;
})

</script>
<style lang="scss" scoped>
.default-router-view {
  height: 100%;
}
</style>
