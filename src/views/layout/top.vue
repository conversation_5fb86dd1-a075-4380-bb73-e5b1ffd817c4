<template>
  <div class="layout-top">
    <!-- <span class="layout-top-back" @click="toogleNav">
      <el-icon>
        <component :is="$ElIcon[ isNavExpand ? 'CaretLeft' : 'CaretRight']" />
      </el-icon>
    </span> -->
    <el-breadcrumb separator="/" class="ml-4">
      <el-breadcrumb-item
        v-for="item in breadcrumbList"
        :to="{ path: item.path }"
        :key="item.path"
        v-show="item.meta.title"
      >
        {{ $t(item.meta.title || '') }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>
<script setup lang="ts">
  import { useStoreRouter } from '@/stores/router'
  import { storeToRefs } from 'pinia'
  import { useRouter } from 'vue-router'
  import { useStoreConfig } from '@/stores/config'
  import { $t } from '@/locales';
  
  const router = useRouter()
  const routerStores = useStoreRouter()
  const configStores = useStoreConfig()
  const {  breadcrumbList } = storeToRefs(routerStores)
  const {  isNavExpand } = storeToRefs(configStores)


  const back = () => {
    router.go(-1)
  }
  const toogleNav = () => {
    configStores.toggleNavExpand()
  }
</script>
<style lang="scss" scoped>
  .layout-top {
    display: flex;
    align-items: center;
    height: 36px;
    background: #fff;
    margin-bottom: var(--app-margin-min);
    display: flex;
    &-back {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 36px;
      width: 36px;
      // background: #E6E8EB;
      border-right: 1px solid #e6e8e8;
      color: #000;
      margin-right: 12px;
      font-size: 20px;
      cursor: pointer;
      transition: .2s;
    }
    &-back:hover {
      background: #E6E8EB;
      color: var(--app-color-primary);
    }
  }
</style>
