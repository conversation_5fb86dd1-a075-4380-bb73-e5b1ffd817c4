<template>
  <el-header class="layout-header">
    <div class="layout-header-left">
      <img :src="logo_url">
    </div>
    <el-menu router ref="menuRef" mode="horizontal" class="horizontal-header-menu" :default-active="activeMenuPath">
      <!-- <el-menu-item :index="'/home'">首页</el-menu-item> -->
      <el-menu-item :index="item.path" v-for="item in menuList" :key="item.path" v-show="!item?.meta?.isBtn">
        <span class="header-menu-text" :title="$t((item?.meta?.title) as string || '')">
          {{ $t((item?.meta?.title) as string || '') }}
        </span>
      </el-menu-item>
    </el-menu>
    <div class="layout-header-right">
      <!-- 退出登录 -->
      <el-dropdown trigger="click">
        <span class="logoutBtn">
          <el-avatar :size="30" :src="avatarUrl" />
          <div v-if="username" class="dark:text-white ml-2">{{ username }}</div>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="getUserBasicInfo">
              {{ $t('基本信息') }}
            </el-dropdown-item>
            <el-dropdown-item @click="editPassword">
              {{ $t('修改密码') }}
            </el-dropdown-item>
            <el-dropdown-item @click="loginOutFn">
              {{ $t('登出') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <!-- <el-avatar :size="30" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
      <el-button class="login-out" @click="loginOutFn">登出</el-button> -->
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useStoreRouter } from '@/stores/router'
import { storeToRefs } from 'pinia'
import { useUserStore } from "@/stores/user"
import { useStoreConfig } from '@/stores/config'
import { emitter } from "@/utils/mitt"
import { $t } from '@/locales'

const userStores = useUserStore()

const stores = useStoreRouter()
const configStores = useStoreConfig()
const { site_logo, site_host } = storeToRefs(configStores);
const { menuList, activeMenuPath } = storeToRefs(stores)
const { user_name, email, phone } = storeToRefs(userStores);

const username = computed(() => {
  return user_name.value || phone.value || email.value || ''
})
const logo_url = computed(() => `${site_host.value}/${site_logo.value}`)
const avatarUrl = computed(() => userStores.avatarUrl)


const loginOutFn = async () => {
  emitter.emit('logout')
}

// 跳转到用户基本信息页
const getUserBasicInfo = () => {
  emitter.emit('showUserBasic')
}

const editPassword = async () => {
  emitter.emit('changePWDDialogShow')
}

</script>

<style lang="scss" scoped>
.layout-header {
  position: fixed;
  top: 0;
  width: 100%;
  height: var(--app-height-header);
  z-index: 11;
  display: flex;
  color: var(--app-color-menu-text);
  justify-content: space-between;
  background: var(--app-color-menu-bg);

  .horizontal-header-menu {
    height: 100%;
    min-width: 0;
    flex: 1;
    align-items: center;
    justify-content: center;
    background-color: inherit;
    border: inherit;

    &>li {
      color: #fff !important;
    }

    .header-menu-text {
      display: inline-block;
      max-width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  &-left {
    display: flex;
    align-items: center;

    img {
      // width: 163px;
      width: auto;
      height: 22px;
      margin-right: 52px;
    }

    h3 {
      font-size: 16px;
      font-weight: 400;
    }

    ul {
      margin-left: 200px;
      font-size: 14px;
      color: var(--app-color-menu-text);
      display: flex;
      align-items: center;

      li {
        padding: 0 24px;
        height: var(--app-height-header);
        line-height: var(--app-height-header);
        transition: .3s;
      }

      li:hover {
        cursor: pointer;
        background: var(--app-color-menu-li-hover);
      }

      li.active {
        background: var(--app-color-menu-li-active);
      }

      .menu-btn {
        a {
          padding: 4px 10px;
          background: var(--app-color-menu-btn);
          border-radius: 3px;
          cursor: pointer;
        }

        a:hover {
          filter: var(--app-color-menu-btn-hover);
        }
      }

      .menu-btn:hover {
        background: none;
        cursor: auto;
      }
    }
  }

  &-right {
    display: flex;
    align-items: center;
    justify-content: space-between;

  }

  .login-out {
    margin-left: 16px;
  }

  .logoutBtn {
    display: flex;
    align-items: center;
    color: #fff;
    cursor: pointer;

    &>p {
      margin-left: 12px;
    }
  }
}
</style>
