<template>
  <CreateComForm ref="comForm" :title="$t('定时任务')" :default_form="default_value"
    @onCreateOrUpdateSubmit="handleCreateTaskType" :rules="rules" :updateShowReset="false" labelWidth="150px">
    <template #default="{ scope, isUpdate }">
      <el-form-item :label="$t('任务名称')" prop="name">
        <el-input v-model="scope.name" clearable :disabled="isUpdate" maxlength="128" show-word-limit />
      </el-form-item>
      <el-form-item :label="$t('任务类型')" prop="type">
        <el-select v-model="scope.type" class="w-full" :placeholder="$t('请选择任务类型')" :disabled="isUpdate">
          <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.type" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('重复周期')">
        <el-radio-group v-model="scope.period" @change="changeRadio(scope)">
          <el-radio label="day">{{ $t('按天') }}</el-radio>
          <el-radio label="week">{{ $t('按星期') }}</el-radio>
          <el-radio label="month">{{ $t('按月') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" v-if="scope.period === 'week'" prop="week">
        <span>{{ $t('每星期') }}<el-input-number v-model="scope.week" :min="1" :max="7" controls-position="right" style="width: 100px; margin: 0 6px;" />{{ $t('执行') }}</span>
      </el-form-item>
      <el-form-item label="" v-if="scope.period === 'month'" prop="day">
        <span>{{ $t('每月') }}<el-input-number v-model="scope.day" :min="1" :max="31" controls-position="right" style="width: 100px; margin: 0 6px;" />{{ $t('号执行') }}</span>
      </el-form-item>
      <el-form-item :label="$t('执行时间')" prop="time">
        <el-time-picker v-model="scope.time" :placeholder="$t('请选择')" value-format="HH_mm_ss"/>
      </el-form-item>
      <el-form-item :label="$t('备注')" prop="userComment">
        <el-input v-model="scope.userComment" autosize type="textarea" clearable maxlength="128" show-word-limit />
      </el-form-item>
      <el-form-item :label="$t('数据过期时间（天）')" prop="expireTime" v-if="scope.type === ETASKTYPE.DATA_CLEAR_TIMEOUT_DATA">
        <el-input-number v-model="scope.expireTime" :min="1" controls-position="right" />
      </el-form-item>
      <el-form-item :label="$t('备份文件名（范例）')" v-if="scope.type === ETASKTYPE.BACKUP">
        <span>{{ scope.backupFileName }}</span>
      </el-form-item>
      <el-form-item :label="$t('备份文件前缀')" prop="prefix" v-if="scope.type === ETASKTYPE.BACKUP">
        <el-input v-model="scope.prefix" clearable maxlength="128" show-word-limit />
      </el-form-item>
      <el-form-item :label="$t('备份文件日期样式')" v-if="scope.type === ETASKTYPE.BACKUP">
        <span>{{ scope.backupFileTime }}</span>
      </el-form-item>
      <el-form-item :label="$t('备份路径')" prop="backup_path" v-if="scope.type === ETASKTYPE.BACKUP">
        <el-input v-model="scope.backup_path" clearable maxlength="128" show-word-limit />
      </el-form-item>
      <el-form-item :label="$t('备份内容')" prop="checked_content" v-if="scope.type === ETASKTYPE.BACKUP">
        <el-checkbox-group v-model="scope.checked_content">
          <el-checkbox v-for="item in scope.backContentList" :key="item.type" :label="item.type" :disabled="['db', 'file'].includes(item.type)"> {{ item.name }} </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </template>
  </CreateComForm>
</template>

<script setup lang="ts">
import { ref, reactive, toRaw } from 'vue'
import CreateComForm from '@/components/CreateComForm.vue'
import { getTaskTypeApi, createTaskApi, editTaskApi } from "@/services/taskScheduler"
import type { Explose } from '@/model/table'
import type { TaskType, CreateTaskTypeParams, PlanTaskType } from '@/model/taskScheduler'
import { getRequireRules } from '@/utils'
import { ETASKTYPE } from '@/model/task'
import { $t } from '@/locales'

const emits = defineEmits(['update'])

const comForm = ref<Explose | null>(null)
const typeOptions = ref<TaskType[]>([])

const rule = {
  ...getRequireRules([
    { key: 'name' },
    { key: 'type', action: $t('选择') },
    { key: 'expireTime'},
    { key: 'prefix' },
    { key: 'backup_path' },
    { key: 'checked_content', action: $t('选择') },
    { key: 'week' },
    { key: 'month' },
    { key: 'time' }
  ]),
  week: [
    { required: true, message: $t('请输入日期'), trigger: 'change' }
  ],
  month: [
    { required: true, message: $t('请输入日期'), trigger: 'change' }
  ],
  time: [
    { required: true, message: $t('请选择时间'), trigger: 'change' }
  ]
}

const rules = reactive(rule)

// 节点表单默认值
const default_value = reactive({
  id: '',
  name: '',
  type: '',
  period: 'day',
  week: 0,
  day: 0,
  time: '',
  userComment: '',
  expireTime: 0,
  prefix: '',
  backup_path: '',
  checked_content: ['db', 'file'],
  backContentList: [],
  backupFileName: '',
  backupFileTime: ''
})

const openDrawer = async (data: PlanTaskType) => {
  const params = getData(data)
  if (!params.id) {
    default_value.backContentList = params.backContentList
    default_value.backupFileName = params.backupFileName
    default_value.backupFileTime = params.backupFileTime
    default_value.backup_path = params.backup_path
    default_value.prefix = params.prefix
  }
  if (comForm.value) {
    comForm.value.show(params.id ? params : null);
    getTypeList()
  }
}

const getData = (data: PlanTaskType) => {
  if (data.type) {
    const { id, type, name, userComment = '', execPolicy = '0_0_0_00_00_00', taskParam = '{}', backContentList, backupFileName, backupFileTime } = data
    let parseTask
    try {
      parseTask = taskParam ? JSON.parse(taskParam) : {}
    } catch (error) {
      parseTask = {}
      console.log(error)
    }
    const { prefix = '', backup_path = '', checked_content = [], expireTime = '' } = parseTask
    const { day, week, time, period } = setPeriodData(execPolicy)
    return { id, name, type, period, week, day, time, userComment, expireTime, prefix, backup_path, checked_content, backContentList, backupFileName, backupFileTime }
  } else {
    const { backContentList, backupFileName, backupFileTime, backup_path, prefix } = data
    return { backContentList, backupFileName, backupFileTime, backup_path, prefix }
  }
}

const setPeriodData = (data: string) => {
  let day = 0, week = 0, time = '', period = 'day'
  if (data) {
    const [, dateStr, weekStr, hourStr, minuteStr, secondStr ] = data.split('_')
    day = Number(dateStr)
    week = Number(weekStr)
    time = `${hourStr}_${minuteStr}_${secondStr}`
    period = getPeriod(dateStr, weekStr, hourStr)
  }
  return { day, week, time, period }
}

const getPeriod = (date: string, week: string, hour: string) => {
  if (date !== '0') {
    return 'month'
  }
  if (week !== '0') {
    return 'week'
  }
  if (hour !== '0') {
    return 'day'
  }
  return 'none'
}

const changeRadio = (scope: any) => {
  scope.day = 0;
  scope.week = 0;
  scope.time = ''
}

// 获取任务类型类型列表
const getTypeList = async () => {
  try {
    const { data } = await getTaskTypeApi();
    if (data?.list && Array.isArray(data.list)) {
      typeOptions.value = data.list;
    }
  } catch (e) {
    console.log(e);
  }
}

// 创建/编辑任务类型
const handleCreateTaskType = async (data: CreateTaskTypeParams, isUpdate: boolean) => {
  try {
    const { id, name, type, period, week, day, time, userComment, expireTime, prefix, backup_path, checked_content } = toRaw(data)
    const taskParam = getTaskParam({ type, prefix, backup_path, checked_content, expireTime })
    const params = {
      id,
      type,
      name,
      taskParam,
      userComment,
      execPolicy: getExecPolicy(day, week, time),
      execStatus: Number(period !== 'none')
    }
    isUpdate ? await editTaskApi(params.id as number, params) : await createTaskApi(params);
    if (comForm.value) {
      comForm.value.isShow = false
    }
    emits('update')
    ElMessage({
      type: 'success',
      message: $t(`{action}成功`, { action: isUpdate ? $t('修改定时任务') : $t('创建定时任务') }),
    })
  } catch (e) {
    console.log(e);
  }
}

const getTaskParam = (data: any) => {
  const { type, prefix, backup_path, checked_content, expireTime } = data
  let params = null
  switch (type) {
    case ETASKTYPE.DATA_CLEAR_TIMEOUT_DATA:
      params = JSON.stringify({ expireTime })
      break;
    case ETASKTYPE.BACKUP:
      params = JSON.stringify({ prefix, backup_path, checked_content })
      break;
    default:
      params = ''
      break;
  }
  return params
}

// 获取定时策略
const getExecPolicy = (day: unknown, week: unknown, time: unknown) => {
  return `0_${day || 0}_${week || 0}_${time || '00_00_00'}`
}

defineExpose({
  openDrawer
})
</script>

<style lang="scss" scoped>
</style>async async 