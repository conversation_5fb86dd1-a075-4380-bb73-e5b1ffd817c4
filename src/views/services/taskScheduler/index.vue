<template>
  <div class="h-full">
    <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading" :getList="getListHandle"
      :showFilter="true" :refresh="refresh" :searchList="searchList">
      <template #tableHeader>
        <el-button v-noBlur type="primary" @click="addTaskType">{{ $t('新增定时任务') }}</el-button>
      </template>
      <template #type="{ row }">
        <span>{{ typeObj[row.type] ?? '--' }}</span>
      </template>
      <template #period="{ row }">
        <span>{{ getColumnData(row.execPolicy, 'period') ?? '--' }}</span>
      </template>
      <template #time="{ row }">
        <span>{{ getColumnData(row.execPolicy, 'time') ?? '--' }}</span>
      </template>
      <template #execStatus="{ row }">
        <el-switch v-model="row.execStatus" :active-value="1" :inactive-value="0" style="--el-switch-on-color: #13ce66"
            :before-change="() => beforeSwitchChange(row)" />
      </template>
      <template #action="{ row }">
        <div class="layout-action">
          <span @click="editTask(row)">{{ $t('编辑') }}</span>
          <span @click="runTask(row)">{{ $t('立即执行') }}</span>
          <span @click="runRecord(row)">{{ $t('记录') }}</span>
          <span @click="deleteTask(row)">{{ $t('删除') }}</span>
        </div>
      </template>
    </GList>
    <TaskDrawer ref="taskDrawer" @update="updateList" />
    <RecordDialog ref="recordDialog" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, toRaw } from 'vue'
import GList from '@/components/GList.vue'
import TaskDrawer from './taskDrawer.vue'
import RecordDialog from './recordDialog.vue'
import { getPlanTaskListApi, getTaskTypeApi, delTaskApi, runTaskApi, editTaskApi } from "@/services/taskScheduler"
import type { TableColumn, TablePagination, TableDataParams } from '@/model/table'
import { useForm } from "@/hooks/useForm"
import type { PlanTaskType, TaskType, SelectOptionsType } from '@/model/taskScheduler'
import { getSystemInfo } from "@/services/system"
import { $t } from '@/locales'

interface TYPE_OBJ {
  [name: string]: any
}

const week_zh: TYPE_OBJ = {
  '1': $t('一'),
  '2': $t('二'),
  '3': $t('三'),
  '4': $t('四'),
  '5': $t('五'),
  '6': $t('六'),
  '7': $t('日')
}

const refresh = ref(Date.now())
const taskDrawer = ref<InstanceType<typeof TaskDrawer> | null>(null)
const recordDialog = ref<InstanceType<typeof RecordDialog> | null>(null)

const { getListHandle, loading } = useForm({
  getListApi: (params: TableDataParams) => getPlanTaskListApi(params),
  val: null,
  name: ''
})

const taskInitData = reactive({
  backupFileName: '',
  backupFileTime: '',
  backContentList: [],
  prefix: '',
  backup_path: ''
})

// 表格列配置
const columns = [
  {
    label: 'ID',
    property: 'id',
    width: 60,
  },
  {
    label: $t('任务名称'),
    property: 'name',
    showOverflowTooltip: true
  },
  {
    label: $t('任务类型'),
    property: 'type',
    slotName: 'type',
  },
  {
    label: $t('重复周期'),
    slotName: 'period',
  },
  {
    label: $t('执行时间'),
    slotName: 'time',
  },
  {
    label: $t('状态'),
    property: 'execStatus',
    slotName: 'execStatus',
  },
  {
    label: $t('最后一次执行'),
    property: 'lastRunTime',
  },
  {
    label: $t('下次执行'),
    property: 'nextRunTime',
  },
  {
    label: $t('平均执行时间（秒）'),
    property: 'averageRunTime',
  },
  {
    label: $t('操作'),
    slotName: 'action',
    width: 240,
    fixed: 'right',
  }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 搜索栏配置
const searchList: Recordable[] = reactive([{
  key: 'type',
  type: 'select',
  name: $t('任务类型'),
  placeholder: $t('请选择任务类型'),
  options:[]
}])

const typeObj: TYPE_OBJ = reactive({})

onMounted(() => {
  getTaskTypeList()
  getTaskInitData()
})

const getTaskTypeList = async () => {
  try {
    const { data } = await getTaskTypeApi()
    if (data?.list && Array.isArray(data.list)) {
      const taskTypeOptions: SelectOptionsType[] = [{ label: $t('全部'), value: '' }]
      data.list.map((item: TaskType) => {
        taskTypeOptions.push({
          label: item.name,
          value: item.type
        })
        typeObj[item.type] = item.name
      })
      searchList[0].options = taskTypeOptions
    }
  } catch (error) {
    console.log(error)
  }
}

// 显示创建任务类型弹窗
const addTaskType = () => {
  showTaskDrawer(toRaw(taskInitData))
}

const editTask = (data: PlanTaskType) => {
  showTaskDrawer({...toRaw(taskInitData), ...toRaw(data)})
}

const showTaskDrawer = (data: PlanTaskType) => {
  if (taskDrawer.value) {
    taskDrawer.value.openDrawer(data)
  }
}

const getTaskInitData = async () => {
  try {
    const res = await getSystemInfo()
    if (res.code === 0) {
      taskInitData.backupFileName = res?.data?.backup?.example_name ?? ''
      taskInitData.backupFileTime = res?.data?.backup?.filename_date_format ?? ''
      taskInitData.backContentList = res?.data?.backup?.content ?? []
      taskInitData.prefix = res?.data?.backup?.prefix ?? ''
      taskInitData.backup_path = res?.data?.backup?.backup_path ?? ''
    }
  } catch (error) {
    console.log(error)
  }
}

// 删除任务类型
const deleteTask = async (val: PlanTaskType) => {
  const { deleteHandle } = useForm({
    delApi: (id) => delTaskApi(id),
    val: val,
    name: $t('任务')
  })
  deleteHandle({ params: val.id }).then((v: number) => {
    refresh.value = v;
  });
}

// 执行任务
const runTask = async (val: PlanTaskType) => {
  const { commonHandle } = useForm({
    commonApi: (id) => runTaskApi(id),
    val: val,
    name: $t('任务'),
    action: $t('立即执行')
  })
  commonHandle({ params: val.id }).then((v: number) => {
    refresh.value = v;
  });
}

const updateList = () => {
  refresh.value = Date.now()
}

const beforeSwitchChange = (val: PlanTaskType) => {
  const title = !val.execStatus ? $t('开启') : $t('关闭');
  return new Promise<boolean>((resolve) => {
    ElMessageBox.alert(
      $t('你确定要{title}该定时任务吗?', { title }),
      title,
      {
        confirmButtonText: $t("确认"),
        cancelButtonText: $t("取消"),
        type: "warning",
      }
    )
      .then(async () => {
        try {
          const { id, type, name, taskParam, execPolicy, execStatus, userComment } = val
          const params = {
            id,
            type,
            name,
            taskParam,
            userComment,
            execPolicy,
            execStatus: execStatus === 0 ? 1 : 0
          }
          await editTaskApi(id as number, params);
          ElMessage({
            type: "success",
            message: $t('{title}成功', { title }),
          });
          resolve(true);
        } catch (e) {
          resolve(false);
        }
      })
      .catch(() => {
        resolve(false);
      })
      .finally(() => {
        updateList()
      });
  });
}

const runRecord = (data: PlanTaskType) => {
  if (recordDialog.value) {
    recordDialog.value.openRecord(Number(data.id))
  }
}

const getColumnData = (data: string, key: 'time' | 'period') => {
  const result: TYPE_OBJ = {
    time: $t('未设置'),
    period: $t('未设置')
  }
  if (data) {
    const [, date, week, hour, minute, second ] = data.split('_')
    result.time = `${hour}:${minute}:${second}`
    result.period = getPeriod(date, week, hour)
  }
  return result[key]
}

const getPeriod = (date: string, week: string, hour: string) => {
  if (date !== '0') {
    return $t('每月{date}号', { date })
  }
  if (week !== '0') {
    return $t('每周{week}', { week: week_zh[week] })
  }
  if (hour !== '0') {
    return $t('每天')
  }
}

</script>

<style lang="scss" scoped>
</style>