<template>
  <el-dialog v-model="showFlag" :title="$t('计划任务执行记录')" :before-close="handleClose">
    <GList class="record-dialog" :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading" :refresh="refresh" :getList="getListHandle" />
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref } from 'vue'
import GList from '@/components/GList.vue'
import type { TableColumn, TableDataParams, TablePagination } from '@/model/table'
import { useForm } from "@/hooks/useForm"
import { getRecordListApi } from "@/services/taskScheduler"
import { $t } from '@/locales'

const showFlag = ref<boolean>(false)
const taskId = ref<number>()
const refresh = ref(Date.now())

// 表格列配置
const columns = [
  {
    label: $t('开始时间'),
    property: 'beginTime',
  },
  {
    label: $t('结束时间'),
    property: 'endTime',
  },
  {
    label: $t('耗时（秒）'),
    property: 'costTime',
  },
  {
    label: $t('结果'),
    property: 'status',
    format: (row: any, column: any, cellValue: number, index: any) => ( cellValue === 1 ? $t('成功') : $t('失败') )
  }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

const { getListHandle, loading } = useForm({
  getListApi: (params: TableDataParams) => getRecordListApi({ id: taskId.value ,...params}),
  val: null,
  name: ''
})

const openRecord = (id: number) => {
  showFlag.value = true
  taskId.value = id
  refresh.value = Date.now()
}

const handleClose = () => {
  if(loading.value) {
    return
  }
  showFlag.value = false
}

defineExpose({
  openRecord
})

</script>

<style lang='scss'>
.record-dialog {
  .layout-table-content {
    padding: 0 var(--app-padding-mid) !important;
    .layout-table-header {
      margin: 0 !important;
    }
  }
}
</style>