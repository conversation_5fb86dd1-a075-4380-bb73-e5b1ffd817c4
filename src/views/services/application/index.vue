<template>
  <div class="h-full" v-loading.fullscreen.lock="installLoading" :element-loading-text="$t('正在安装中,请耐心等待...')">
    <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading" :getList="getListHandle"
      :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList">
      <template #tableHeader>
        <el-upload class="upload-demo" :show-file-list="false" :on-change="uploadFile" :auto-upload="false"
          accept=".zip, .rar">
          <el-button v-noBlur type="primary">{{ $t('安装应用') }}</el-button>
        </el-upload>
      </template>
      <template #action="{ row }">
        <div class="layout-action">
          <span @click="showDetail(row)">{{ $t('详情') }}</span>
          <span @click="uninstall(row)">{{ $t('卸载') }}</span>
        </div>
      </template>
    </GList>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import GList from '@/components/GList.vue'
import type { LoadingOptionsResolved, UploadFile, UploadFiles, UploadProps } from 'element-plus'
import type { TableColumn, TablePagination, TableDataParams } from '@/model/table'
import type { Application } from '@/model/application'
import { getApplicationList, uninstallApplication } from "@/services/application"
import { installApplication, checkFileExit } from "@/services/file"
import { formatObjectData, getFileSha1 } from '@/utils'
import { emitter } from "@/utils/mitt";
import { useForm } from "@/hooks/useForm"
import { calcFileSha1 } from "@/utils/sha1"
import { calcFileWASMSha1 } from "@/utils/sha1/wasm"
import type { UploadQuery } from '@/model/file'
import { $t } from '@/locales'

let loadingInstance:any;
const refresh = ref(Date.now())
const installLoading = ref(false)
const showDetailKeys = ['id', 'name', 'type', 'appSandbox', 'version', 'developer', 'installTime']

const { getListHandle, loading } = useForm({
  getListApi: (params: TableDataParams) => getApplicationList(params),
  val: null,
  name: ''
})

// 表格列配置
const columns = [
  // {
  //   type: "selection",
  //   width: "55"
  // },
  {
    label: 'ID',
    property: 'id',
    width: "100"
  },
  {
    label: $t('名称'),
    property: 'name',
    showOverflowTooltip: true,
  },
  {
    label: $t('类型'),
    property: 'type',
    showOverflowTooltip: true,
  },
  {
    label: $t('沙盒'),
    property: 'appSandbox',
    showOverflowTooltip: true,
    format: (row: any, column: any, cellValue: any, index: any) => { return cellValue || "-" }
  },
  {
    label: $t('版本'),
    property: 'version',
    width: "80",
  },
  {
    label: $t('开发者'),
    property: 'developer',
  },
  {
    label: $t('安装时间'),
    property: 'installTime',
    width: "160",
  },
  {
    label: $t('操作'),
    slotName: 'action',
    width: "120",
    fixed: 'right',
  }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
  key: 'batchDelete',
  label: $t('批量卸载'),
  click: (items: Application[]) => batchDelApp(items)
}]

// 搜索栏配置
const searchList = [{
  key: 'name',
  type: 'input',
  name: $t('名称'),
  placeholder: $t('应用名称')
}, {
  key: 'type',
  type: 'input',
  name: $t('类型'),
  placeholder: $t('应用类型')
}, {
  key: 'appSandbox',
  type: 'input',
  name: $t('沙盒'),
  placeholder: $t('应用沙盒'),
}]

// 批量删除
const batchDelApp = async (items: Application[]) => {
  ElMessage({
    type: 'success',
    message: $t('批量删除成功'),
  })
  refresh.value = Date.now();
}

// 显示详情内容
const showDetail = (item: Application) => {
  const data = formatObjectData(item, showDetailKeys);
  emitter.emit("detailViewShow2", { data, title: $t('应用详情'), width: '30%' });
}

// 安装应用
const uploadFile: UploadProps['onChange'] = async (file: UploadFile, uploadFiles: UploadFiles) => {
  // installLoading.value = true

  const uploadQuery: UploadQuery = {
    etag: '',
    file: file.raw ? file.raw : ''
  }

  try {
    loadingInstance = ElLoading.service({
      lock: true,
      text: $t('文件检验中...'),
      // background: 'rgba(0, 0, 0, 0.7)',
      customClass: 'loading-custom-class'
    });
    if (file.raw) {
      const logKey = $t('计算sha1 {name} (size:{size}) {random}', { name: file.name, size: file.raw.size, random: Math.random() })
      console.time(logKey)
      // uploadQuery.etag = await calcFileSha1(file.raw, 0,
      // prog => {
      //   getSHA1Progress(prog)
      // },
      // null)
      uploadQuery.etag = await calcFileWASMSha1(file.raw,
      prog => {
        getSHA1Progress(prog)
      },
      null)
      
      // uploadQuery.etag = await getFileSha1(file.raw)
      console.timeLog(logKey, ' result:', uploadQuery.etag)
      const res = await checkFileExit(uploadQuery.etag)
      if (res.data && res.data.installStatus) {
        // etag存在，则提示应用已安装
        ElMessage({
          type: 'warning',
          message: $t('应用已安装'),
        })
        loadingInstance.close()
        return
      }
    }
    loadingInstance.setText($t('上传应用中...'))
    await installApplication(uploadQuery, getUploadProgress)
    ElMessage({
      type: 'success',
      message: $t('安装成功'),
    })
    refresh.value = Date.now();
  } catch (error) {
    console.log(error);
  } finally {
    // installLoading.value = false
    loadingInstance.close()
  }
}

const getSHA1Progress = (progress: number) => {
  loadingInstance.setText($t('文件检验进度：{progress}%', { progress: (progress).toFixed(2) }))
}

const getUploadProgress = (evt: ProgressEvent) => {
  const progressPercent = (evt.loaded / evt.total * 100).toFixed(2);
  if (progressPercent == '100.00') {
    // 应用上传完成，等待安装中
    loadingInstance.setText($t('应用上传完成，正在安装中，请耐心等待'))
  } else {
    loadingInstance.setText($t('上传进度：{progress}%', { progress: progressPercent }))
  }
}

// 卸载应用
const uninstall = (item: Application) => {
  // TODO 二次确认弹窗,需要弹窗公共组件 调用卸载接口
  ElMessageBox.alert(
    $t('你确定要卸载{name}应用吗?', { name: item.name }),
    $t('卸载'),
    {
      confirmButtonText: $t('确认'),
      cancelButtonText: $t('取消'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await uninstallApplication(item.id)
        ElMessage({
          type: 'success',
          message: $t('卸载成功'),
        })
        refresh.value = Date.now();
      } catch (e) {
        console.log(e)
      }
    })

}

</script>