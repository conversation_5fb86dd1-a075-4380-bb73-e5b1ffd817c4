<template>
    <el-drawer v-model="isShow" size="30%" direction="rtl" :title="$t('应用详情')">
        <div class="content-box">
            <el-row :gutter="20" justify="center" v-for="(value, key) in item" :key="key">
                <el-col :span="12">
                    <div class="grid grid-label">{{ key }}</div>
                </el-col>
                <el-col :span="12">
                    <div class="grid grid-value">{{ value }}</div>
                </el-col>
            </el-row>
        </div>
    </el-drawer>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { $t } from '@/locales'
export interface Example {
    isShow: boolean
    show: (v: object) => void
}
interface DetailProps {
    item?: any
}
const isShow = ref(false)
const item = ref()

const show = (data: object) => {
    item.value = data
    isShow.value = true
}

defineExpose({
    isShow,
    show
})
const props = withDefaults(defineProps<DetailProps>(), {
    item: {},
    isShow: false,
})

</script>
<style scoped>
.content-box {
    border: 0.5px solid #999;
}

.grid {
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
}

.grid-label {
    color: #909399;
}

.grid-value {
    color: #606266;
}
</style>
