<template>
  <div class="h-full">
    <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading" :getList="getListHandle"
      :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList">
      <!-- <template #tableHeader>
        <el-button v-noBlur type="primary" @click="addTaskType">{{ $t('添加任务类型') }}</el-button>
      </template> -->
      <template #action="{ row }">
        <div class="layout-action">
          <span @click="showDetail(row)">{{ $t('详情') }}</span>
          <span @click="updateTaskType(row)" v-if="row.category !== 6">{{ $t('编辑') }}</span>
          <!-- <span @click="deleteTaskType(row)">删除</span> -->
        </div>
      </template>
    </GList>
    <CreateComForm ref="comForm" :title="$t('任务类型')" :default_form="default_value"
      @onCreateOrUpdateSubmit="handleCreateTaskType" :rules="rules" :updateShowReset="false">
      <template #default="{ scope, isUpdate }">
        <el-form-item :label="$t('名称')" prop="name" required>
          <el-input v-model="scope.name" clearable :disabled="isUpdate" />
        </el-form-item>
        <el-form-item :label="$t('类别')" prop="category" required>
          <el-select v-model="scope.category" clearable :placeholder="$t('请选择任务类别')" class="w-full" :disabled="isUpdate">
            <el-option v-for="item in TASK_TYPE_CATEGORY" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('类型')" prop="type" required>
          <el-select v-model="scope.type" class="w-full" :loading="selectLoading" :placeholder="$t('请选择任务类型')"
            :loading-text="$t('加载中...')" @visible-change="getTypeList" @change="getAppList" :disabled="isUpdate">
            <el-option v-for="item in typeOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('功能')" prop="function" required>
          <el-input v-model="scope.function" clearable :disabled="isUpdate" />
        </el-form-item>
        <el-form-item :label="$t('沙盒')" prop="application" required>
          <el-select v-model="scope.application" :placeholder="$t('请选择沙盒')" class="w-full">
            <el-option v-for="item in appOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <template v-if="scope.category === 6">
          <el-form-item :label="$t('重复周期')">
            <el-radio-group v-model="period" @change="changeRadio">
              <el-radio label="none">{{ $t('不设置') }}</el-radio>
              <el-radio label="day">{{ $t('按天') }}</el-radio>
              <el-radio label="week">{{ $t('按星期') }}</el-radio>
              <el-radio label="month">{{ $t('按月') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" v-if="period === 'week'">
            <span>{{ $t('每星期{week}执行', { week: '' }) }}<el-input-number v-model="week" :min="1" :max="7" controls-position="right" /></span>
          </el-form-item>
          <el-form-item label="" v-if="period === 'month'">
            <span>{{ $t('每{month}月{day}号执行', { month: '', day: '' }) }}<el-input-number v-model="month" :min="1" :max="12" controls-position="right" /><el-input-number
                v-model="day" :min="1" :max="31" controls-position="right" /></span>
          </el-form-item>
          <el-form-item :label="$t('执行时间')" v-if="period !== 'none'">
            <el-time-picker v-model="time" :placeholder="$t('请选择')" value-format="HH_mm_ss"/>
          </el-form-item>
        </template>
        <el-form-item :label="$t('备注')" prop="comment">
          <el-input v-model="scope.comment" autosize type="textarea" clearable />
        </el-form-item>
      </template>
    </CreateComForm>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import GList from '@/components/GList.vue'
import CreateComForm from '@/components/CreateComForm.vue'
import { getTaskTypeList, createTaskTypeApi, editTaskType, delTaskType, getTypeListInTaskType, getAppListByTaskType, editSysTaskType } from "@/services/taskType"
import type { TableColumn, TablePagination, TableDataParams, Explose } from '@/model/table'
import { emitter } from "@/utils/mitt";
import { useForm } from "@/hooks/useForm"
import { getTaskTypeCategory } from "@/utils/const"
import type { CreateTaskTypeParams, TaskType } from '@/model/taskType'
import { formatExecPolicyTime, formatObjectData, getRequireRules } from '@/utils'
import { $t } from '@/locales'

const selectLoading = ref(false)
const refresh = ref(Date.now())
const comForm = ref<Explose | null>(null)
const typeOptions = ref([]);
const appOptions = ref([]);
const period = ref('none');
const time = ref()
const day = ref()
const month = ref()
const week = ref()
const showDetailKeys = ['id', 'type', 'name', 'category', 'function', 'comment', 'appSandbox', 'version', 'createTime']

// 获取国际化的常量
const TASK_TYPE_CATEGORY = getTaskTypeCategory()

const rule = getRequireRules([{ key: 'name' }, { key: 'category', action: $t('选择') }, { key: 'type', action: $t('选择') }, { key: 'function' }, { key: 'application', action: $t('选择') }, { key: 'execPolicy', action: $t('选择') }]) || {}

const rules = reactive(rule)

const { getListHandle, loading } = useForm({
  getListApi: (params: TableDataParams) => getTaskTypeList(params),
  val: null,
  name: ''
})

// 节点表单默认值
const default_value = {
  id: undefined,
  name: '',
  type: '',
  category: '',
  function: '',
  application: '',
  comment: '',
  execPolicy: '',
  execStatus: 0,
} as unknown as TaskType

// 表格列配置
const columns = [
  // {
  //   type: "selection",
  //   width: "55"
  // },
  {
    label: 'ID',
    property: 'id',
    showOverflowTooltip: true,
    width: "100"
  },
  {
    label: $t('类型'),
    property: 'type',
  },
  {
    label: $t('名称'),
    property: 'name',
    showOverflowTooltip: true,
  },
  {
    label: $t('类别'),
    property: 'category',
    format: (row: any, column: any, cellValue: number, index: any) => { return TASK_TYPE_CATEGORY.find(v => v.value === cellValue)?.label || "-" }
  },
  {
    label: $t('功能'),
    property: 'function',
    showOverflowTooltip: true,
  },
  {
    label: $t('沙盒'),
    property: 'appSandbox',
    showOverflowTooltip: true,
  },
  {
    label: $t('版本'),
    property: 'version',
    width: "60"
  },
  {
    label: $t('创建时间'),
    property: 'createTime',
    width: "160",
  },
  {
    label: $t('操作'),
    slotName: 'action',
    fixed: 'right',
    width: "120",
  }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
  key: 'batchDelete',
  label: $t('批量删除'),
}]

// 搜索栏配置
const searchList = [{
  key: 'name',
  type: 'input',
  name: $t('名称'),
  placeholder: $t('应用名称')
}, {
  key: 'type',
  type: 'input',
  name: $t('类型'),
  placeholder: $t('应用类型')
}, {
  key: 'appSandbox',
  type: 'input',
  name: $t('沙盒'),
  placeholder: $t('应用沙盒'),
}]

const changeRadio = () => {
  day.value = null;
  month.value = null;
  week.value = null;
  time.value = ''
}

// 获取任务类型类型列表
const getTypeList = async (val: any) => {
  if (val && !typeOptions.value.length) {
    selectLoading.value = true;
    try {
      const { data } = await getTypeListInTaskType();
      typeOptions.value = data;
    } catch (e) {
      console.log(e);
    } finally {
      selectLoading.value = false;
    }
  }
}

const getAppList = async (val: string) => {
  try {
    const { data } = await getAppListByTaskType(val);
    appOptions.value = data;
  } catch (e) {
    console.log(e)
    appOptions.value = [];
  }
}

// 显示创建任务类型弹窗
const addTaskType = () => {
  if (comForm.value) {
    comForm.value.show();
  }
}

// 创建/编辑任务类型
const handleCreateTaskType = async (data: CreateTaskTypeParams | TaskType, isUpdate: boolean) => {
  console.log("data", data)
  if (data.category === 6) {
    // 如果是后台系统任务类型
    handleUpdateSysTask(data as TaskType)
    return;
  }
  try {
    isUpdate ? await editTaskType((data as TaskType).id, data as TaskType) : await createTaskTypeApi(data as CreateTaskTypeParams);
    if (comForm.value) {
      comForm.value.isShow = false
    }
    refresh.value = Date.now();
    ElMessage({
      type: 'success',
      message: $t('{action}成功', { action: isUpdate ? $t('修改任务类型') : $t('创建任务类型') }),
    })
  } catch (e) {
    console.log(e);
  }
}

// 更新系统后端任务
const handleUpdateSysTask = async (data: TaskType) => {
  try {
    const params = {
      execPolicy: period.value !== 'none' ? getExecPolicy() : '',
      execStatus: Number(period.value !== 'none'),
      id: data.id,
      name: data.name,
      type: data.type
    }
    await editSysTaskType(data.id, params)
    if (comForm.value) {
      comForm.value.isShow = false
    }
    refresh.value = Date.now();
    ElMessage({
      type: 'success',
      message: $t('更新{name}定时任务成功', { name: data.name }),
    })
  } catch (e) {
    console.log(e)
  }
}

// 获取定时策略
const getExecPolicy = () => {
  return `${month.value || 0}_${day.value || 0}_${week.value || 0}_${time.value || '00_00_00'}`
}

// 展示任务类型详情弹窗
const showDetail = (val: TaskType) => {
  const formatData = {
    'category': (val: number) => {
      return TASK_TYPE_CATEGORY.find(v => v.value === val)?.label || "-"
    }
  }
  const data = formatObjectData(val, showDetailKeys, formatData);
  const execPolicy = formatExecPolicyTime(val.execPolicy || '')
  if (val.category === 6) {
    data.push({
      key: 'execStatus',
      value: val.execStatus ? $t('执行') : $t('不执行')
    }, {
      key: 'execPolicy',
      value: val.execStatus ? execPolicy : $t('无')
    })
  }
  emitter.emit("detailViewShow2", { data, title: $t('任务类型详情'), width: '30%' });
}

// 编辑任务类型
const updateTaskType = (val: TaskType) => {
  if (comForm.value) {
    val.application = `${val.appSandbox}/${val.version}`
    if (val.category === 6) {
      if (val.execStatus && val.execPolicy) {
        const data = val.execPolicy.split('_')
        month.value = parseInt(data[0]) || undefined
        week.value = parseInt(data[2]) || undefined
        day.value = parseInt(data[1]) || undefined
        time.value = `${data[3]}_${data[4]}_${data[5]}`
      }
      period.value = getPeriod(val.execStatus)
    }
    comForm.value.show(val);
    if (val.type) getAppList(val.type);
  }
}

const getPeriod = (execStatus?: number) => {
  if (!execStatus) {
    // 如果是没有定时
    return 'none'
  } else if (month.value || day.value) {
    return 'month'
  } else if (week.value) {
    return 'week'
  } else {
    return 'day'
  }
}

// 删除任务类型
const deleteTaskType = async (val: TaskType) => {
  const { deleteHandle } = useForm({
    delApi: (id) => delTaskType(id),
    val: val,
    name: $t('任务类型')
  })
  deleteHandle({ params: val.id }).then((v: number) => {
    refresh.value = v;
  });
}

</script>

<style lang="scss" scoped>
</style>