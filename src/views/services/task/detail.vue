<template>
  <div>
    <el-drawer v-model="isShow" :title="$t('任务详情')" destroy-on-close @close="activeName = 'stdout'">
        <el-descriptions :column="1" border>
            <el-descriptions-item v-for="(value, key) in item" :key="key" :label="$t(`model.${key}`)">
                <div v-if="String(key) === 'status'" class="status"
                    :class="TASK_STATUS[value - 1] ? `status-${value - 1}` : ''">{{
                        TASK_STATUS[value - 1] || value
                    }}</div>
                <TipText :content="value" width="100%" v-else>
                    <template v-slot:content>
                        <span class="grid-value">{{ String(key) === 'priority' ? TASK_PRIORITY[value - 1] : value
                        }}</span>
                    </template>
                </TipText>
            </el-descriptions-item>
        </el-descriptions>
        <div class="flex-1" style="overflow: hidden;">
            <el-tabs v-model="activeName" class="log-tabs">
                <el-tab-pane :label="$t('日志标准输出')" name="stdout">
                    <v-ace-editor
                      :readonly="true"
                      :value="logs.stdout"
                      :lang="aceConfig.lang" :theme="aceConfig.theme"
                      :options="aceConfig.options"
                      style="height: 100%"
                    />
                </el-tab-pane>
                <el-tab-pane :label="$t('日志错误输出')" name="stderr">
                    <v-ace-editor
                      :readonly="true"
                      :value="logs.stderr"
                      :lang="aceConfig.lang" :theme="aceConfig.theme"
                      :options="aceConfig.options"
                      style="height: 100%"
                    />
                </el-tab-pane>
                <el-tab-pane :label="$t('任务参数')" name="taskParams">
                    <v-ace-editor
                      :readonly="true"
                      :value="logs.taskParams"
                      :lang="aceConfig.lang" :theme="aceConfig.theme"
                      :options="aceConfig.options"
                      style="height: 100%"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="debounceExport" :disabled="isDisabled">
                    {{ $t('导出') }}
                </el-button>
            </span>
        </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, reactive, computed } from 'vue'
import { getTaskErrorLog, getTaskStandardLog, getTaskInfo } from '@/services/task'
import { debounce } from '@/utils/index'
import { getTaskStatus, getTaskPriority } from "@/utils/const"
import { $t } from '@/locales'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-json';
import 'ace-builds/src-noconflict/theme-chrome';

const showDetailKeys = ['id', 'type', 'priority', 'createTime', 'startTime', 'endTime', 'progress', 'status', 'userName', 'tags', 'retryTimes', 'nodeName']

// 获取国际化的常量
const TASK_STATUS = getTaskStatus()
const TASK_PRIORITY = getTaskPriority()

const isShow = ref(false)
const item = ref<Recordable>({})
const activeName = ref<'stdout' | 'stderr' | 'taskParams'>('stdout')
const logs = reactive({
    stdout: '',
    stderr: '',
    taskParams: ''
})
const t = inject<any>('t');

// VAceEditor config
const aceConfig = {
  lang: 'json',
  theme: 'chrome',
  'min-lines': 1,
  'max-lines': 999999,
  options: {
    wrap: true,
    tabSize: 2,
    showPrintMargin: false,
    fontSize: 12,
    hScrollBarAlwaysVisible: false,
    vScrollBarAlwaysVisible: false,
    customScrollbar: true
  }
}

const isDisabled = computed(() => {
    return !logs[activeName.value] || logs[activeName.value] === $t('任务暂无输出')
})

const show = (data: Recordable) => {
    item.value = Object.keys(data)
    .filter(key => {
        return showDetailKeys.includes(key)
    } )
    .reduce( function (result, ObjectKey){
        result[ObjectKey] = data[ObjectKey];
        return result;
    }, {} as Recordable )
    isShow.value = true
    getLog('stdout');
    getLog('stderr');

    // 获取详情
    getTaskInfo(data.id)
    .then(({data}) => {
      const param = JSON.parse(data.param)
      const authorization = param?.input?.firstInput?.authorization
      if (authorization) {
        param.input.firstInput.authorization = tokenTransform(authorization)
      }
      logs.taskParams = JSON.stringify(param, null, 2)

      // 再次更新任务详情
      item.value = Object.keys(data)
      .filter(key => {
          return showDetailKeys.includes(key)
      })
      .reduce( function (result, ObjectKey){
          result[ObjectKey] = data[ObjectKey];
          return result;
      }, {} as Recordable )

    })
    .catch(err => {
      logs.taskParams = err.message || $t('获取失败')
    })
}

const tokenTransform = (data: string) => {
  const startIndex = 1;
  const endIndex = data.length - 2;
  const replacedString = data.substring(0, startIndex) + "*".repeat(8) + data.substring(endIndex + 1);
  return replacedString
}

const getLog = async (type: 'stdout' | 'stderr') => {
    try {
        const fn = type === 'stdout' ? getTaskStandardLog : getTaskErrorLog;
        const { data } = await fn(item.value.id);
        // 如果默认提示直接赋值返回
        if (data.data === $t('任务暂无输出')) {
          return logs[type] = data.data
        }
        // 其他当作JSON格式化处理
        try {
          if (data.data.substr(0, 1) === "[" && data.data.substr(data.data.length - 1, 1) !== "]") {
            data.data = data.data + "]"
          }

          let jsonData = JSON.parse(data.data)
          // 判断如果是特定的数组 begin data格式，格式化data中的换行
          if (jsonData.length && jsonData[0].Data) {
            jsonData = jsonData.map(({Begin, Data}: {Begin: string, Data: string}) => {
              return {
                Begin,
                Data: Data.split('\n')
              }
            })
          }
          logs[type] = JSON.stringify(jsonData, null, 2)
        } catch (e) {
          logs[type] = data.data
        }
       
    } catch (e) {
        console.error($t('日志获取/解析错误：{error}', { error: '' }), e)
        logs[type] = $t('日志获取/解析错误：{error}', { error: (e as Error).message })
    }
}

// {{ $t('导出') }}日志
const exportLog = async () => {
    try {
        // const fn = activeName.value === 'stdout' ? exportTaskStandardLog : exportTaskErrorLog;
        // const data = await fn(item.value.id);
        const content = new Blob([logs[activeName.value]], {
          type: 'text/plain;charset=utf-8'
        })
        //生成url对象
        const urlObject = window.URL || window.webkitURL || window
        const url = urlObject.createObjectURL(content)
        //生成<a></a>DOM元素
        const el = document.createElement('a')
        //链接赋值
        el.href = url
        el.download = $t('{id}任务{type}日志', { id: item.value.id, type: activeName.value });
        document.body.appendChild(el)
        //必须点击否则不会下载
        el.click()
        ElMessage({
            type: "success",
            message: $t('导出成功'),
        });
        //移除链接释放资源		
        el.parentNode?.removeChild(el)
        urlObject.revokeObjectURL(url)

    } catch (e) {
        console.log(e)
    }
}

const debounceExport = debounce(exportLog, 1000)

defineExpose({
    isShow,
    show
})

</script>

<style scoped lang="scss">
.grid-value {
    color: #606266;
}
:deep(.el-descriptions__table) {
  table-layout: fixed;
}

:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
    white-space: nowrap !important;
}

:deep(.el-drawer__body) {
  display: flex;
  flex-direction: column;
}

:deep(.log-tabs){
  display: flex;
  flex-direction: column;
  height: 100%;
}
:deep(.el-tabs__content) {
  flex: 1;
}
:deep(.el-tab-pane) {
  height: 100%;
}
.ace_editor {
  width: auto;
}
</style>
