<template>
  <div class="h-full">
    <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading" :getList="getList"
      :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList" ref="rowList">
      <template #tableHeader>
        <el-button v-noBlur type="primary" @click="clearAll" :disabled="!listData || listData.length <= 0">{{ $t('清空') }}</el-button>
      </template>
      <template #status="{ row }">
        <div class="status" :class="TASK_STATUS[row.status - 1] ? `status-${row.status - 1}` : ''">{{
          TASK_STATUS[row.status - 1] || row.status
        }}</div>
      </template>
      <template #action="{ row }">
        <div class="layout-action">
          <span @click="showDetail(row)">{{ $t('详情') }}</span>
          <span @click="deleteTask(row)" v-if="row.status !== 2">{{ $t('删除') }}</span>
          <span @click="cancel(row)" v-if="row.status === 1 || row.status === 2">{{ $t('取消') }}</span>
          <span @click="retry(row)" v-if="![1, 2].includes(row.status) && ![5, 6].includes(row.category) && !['MakeDWGIncrMerge'].includes(row.type)">{{ $t('重新执行') }}</span>
        </div>
      </template>
    </GList>
    <Detail ref="detailRef"></Detail>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import GList from '@/components/GList.vue'
import { getTaskList, delTask, cancelTask, retryTask, clearAllTask } from "@/services/task"
import { getTypeListInTaskType } from "@/services/taskType"
import type { TableColumn, TablePagination, TableDataParams, TableSearchType } from '@/model/table'
import { useForm } from "@/hooks/useForm"
import { getTaskStatus, getTaskPriority } from "@/utils/const"
import type { Explose } from '@/model/table';
import Detail from './detail.vue'

import type { Task } from '@/model/task'
import { getNodeInfo } from '@/services/node'
import socket from '@/utils/socket'
import { getCurrentTime } from '@/utils'
import { $t } from '@/locales'

const router = useRouter()
const loading = ref(false)
const refresh = ref(Date.now())
const searchList = ref<TableSearchType[]>([]);
const rowList = ref<any>(null)
const listData = ref([]);
const detailRef = ref<Explose | null>(null)

// 获取国际化的常量
const TASK_STATUS = getTaskStatus()
const TASK_PRIORITY = getTaskPriority()

const root_select = {
  label: $t('全部'),
  value: undefined
}

const nodeId = computed(() => {
  const { query } = router.currentRoute.value;
  return query.id || ''
})

onBeforeMount(async () => {
  const res = await getTypeList()
  // const { data = '' } = nodeId.value && await getNodeInfo(nodeId.value as string);
  searchList.value = [{
    key: 'userName',
    type: 'input',
    name: $t('用户名称'),
    placeholder: $t('请输入')
  }, {
    key: 'nodeName',
    type: 'input',
    name: $t('节点名称'),
    placeholder: $t('请输入'),
    // defaultValue: data.name || '',
  }, {
    key: 'type',
    type: 'select',
    name: $t('类型'),
    placeholder: $t('请选择'),
    options: res as []
  }, {
    key: 'status',
    type: 'select',
    name: $t('状态'),
    placeholder: $t('请选择'),
    options: formatSelectData(TASK_STATUS) as []
  }, {
    key: 'priority',
    type: 'select',
    name: $t('优先级'),
    placeholder: $t('请选择'),
    options: formatSelectData(TASK_PRIORITY) as []
  }, {
    key: 'rangeTime',
    type: 'dataPicker',
    name: $t('创建时间'),
  }]
})

onMounted(() => {
  // 监听task 事件
  socket.on('task', taskChange)
});

onUnmounted(() => {
  // 取消file事件监听
  socket.off('task', taskChange)
})

// 文件相关socketio事件处理
const taskChange = (ename: string, data: any) => {
  switch (ename) {
    case 'task.status.change':
      if (rowList.value && data) {
        rowList.value.tableData = rowList.value.tableData.map((v: Task) => {
          if (v.id === data.taskId) {
            v.status = data.newStatus
            if (v.status === 3) {
              // 判断是否完成,完成刷新页面获取结束时间
              v.endTime = getCurrentTime()
            }
          }
          return v
        })
      }
      break
  }
}

const formatSelectData = (val: string[], pre = false) => {
  const bas = val.map((v, index) => {
    return {
      label: v,
      value: pre ? v : index + 1 as any
    }
  })
  bas.unshift(root_select)
  return bas
}

// 获取任务类型类型列表
const getTypeList = async () => {
  try {
    const { data } = await getTypeListInTaskType();
    return formatSelectData(data, true);
  } catch (e) {
    console.log(e);
    return []
  }
}

// 表格列配置
const columns = [
  // {
  //   type: "selection",
  //   width: "55"
  // },
  {
    label: 'ID',
    property: 'id',
    showOverflowTooltip: true,
    width: "100"
  },
  {
    label: $t('类型'),
    property: 'type',
  },
  {
    label: $t('优先级'),
    property: 'priority',
    format: (row: any, column: any, cellValue: number, index: any) => { return TASK_PRIORITY[cellValue - 1] || "-" }
  },
  {
    label: $t('状态'),
    property: 'status',
    slotName: 'status',
    width: "105",
    // align: 'center'
  },
  {
    label: $t('运行节点'),
    property: 'nodeName',
  },
  {
    label: $t('所属用户'),
    property: 'userName',
  },
  {
    label: $t('创建时间'),
    property: 'createTime',
    width: "160",
  },
  {
    label: $t('开始时间'),
    property: 'startTime',
    width: "160",
  },
  {
    label: $t('结束时间'),
    property: 'endTime',
    width: "160",
  },
  {
    label: $t('操作'),
    slotName: 'action',
    fixed: 'right',
    width: "220",
  }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
  key: 'batchDelete',
  label: $t('批量删除'),
}]

const getList = async (params: TableDataParams) => {
  const { page, limit, search } = params;
  if (nodeId.value && !search.nodeName) return {
    list: [],
    total: 0,
  };
  loading.value = true;
  let req = {
    page,
    limit,
  }
  if (search) {
    req = {
      ...req,
      ...search
    }
  }
  try {
    const { data } = await getTaskList(req)
    listData.value = listData.value.concat(data.list || []);
    return {
      list: data.list,
      total: data.total,
    }
  } catch (e) {
    return {
      list: [],
      total: 0,
    }
  } finally {
    loading.value = false;
  }
}

// 展示任务类型详情弹窗
const showDetail = (val: Task) => {
  if (detailRef.value) {
    detailRef.value.show(val)
  }
}

// 清空任务
const clearAll = async () => {
  ElMessageBox.alert($t('你确定要清空所有任务吗?'), $t('清空'), {
    confirmButtonText: $t("确认"),
    cancelButtonText: $t("取消"),
    type: "warning",
  }).then(async () => {
    try {
      loading.value = true;
      await clearAllTask();
        ElMessage({
          type: "success",
          message: $t("清空成功"),
        });
        refresh.value = Date.now();
      } catch (e) {
        console.log(e);
      } finally {
        loading.value = false;
      }
  });
}

// 删除任务
const deleteTask = async (val: Task) => {
  const { deleteHandle } = useForm({
    delApi: (id) => delTask(id),
    val: val,
    name: $t('任务')
  })
  deleteHandle({params: val.id}).then((v: number) => {
    refresh.value = v;
  });
}

// 取消任务
const cancel = (val: Task) => {
  ElMessageBox.alert($t('你确定要取消{id}任务吗?', { id: val.id }), $t('取消'), {
    confirmButtonText: $t("确认"),
    cancelButtonText: $t("取消"),
    type: "warning",
  }).then(async () => {
    try {
      const { code } = await cancelTask(val.id);
      if (code === 0) {
        ElMessage({
          type: "success",
          message: $t("取消成功"),
        });
        refresh.value = Date.now();
      }
    } catch (e) {
      console.log(e);
    }
  });
}

// 重新执行
const retry = async (val: Task) => {
  try {
    await retryTask(val.id)
    ElMessage({
      type: "success",
      message: $t("重新执行成功"),
    });
    refresh.value = Date.now();
  } catch (e) {
    console.log(e);
  }
}


</script>

<style></style>