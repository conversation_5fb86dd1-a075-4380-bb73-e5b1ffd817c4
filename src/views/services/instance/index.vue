<template>
  <div class="h-full">
    <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading" :getList="getListHandle"
      :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList" ref="rowList">
      <template #tableHeader>
        <el-button v-noBlur type="primary" @click="addNode">{{ $t('添加节点') }}</el-button>
      </template>
      <template #action="{ row }">
        <div class="layout-action">
          <span @click="showDetail(row)">{{ $t('详情') }}</span>
          <span @click="updateNode(row)">{{ $t('编辑') }}</span>
          <span @click="deleteNode(row)" v-if="row.enable === 1 && !row.build_in">{{ $t('删除') }}</span>
          <span @click="check(row)" v-if="row.status === 1">{{ $t('检测') }}</span>
          <span @click="binding(row)" v-if="row.status === 3">{{ $t('绑定') }}</span>
        </div>
      </template>
      <template #nodeName="{ row }">
        <div class="flex items-center">
          <svgIcon :name="getOSType(row)" :width="40" :height="30"></svgIcon>
          <div class="truncate">{{ row.name }}</div>
        </div>
      </template>
      <template #status="{ row }">
        <div class="status" :class="formatNodeStatus(row.status)">{{ formatNodeStatus(row.status, true) }}</div>
      </template>
      <template #machineStatus="{ row }">
        <div class="flex items-center">
          <svgIcon :name="getMachineStatus(row.faliedTaskPercent)" :size="24"></svgIcon>
        </div>
      </template>
      <template #enable="{ row }">
        <el-switch v-model="row.enable" :active-value="0" :inactive-value="1" style="--el-switch-on-color: #13ce66"
          :before-change="() => beforeSwitchChange(row)" />
      </template>
    </GList>
    <CreateComForm ref="comForm" :title="$t('节点')" :default_form="default_value" @onCreateOrUpdateSubmit="handleCreateNode"
      :rules="rules" :updateShowReset="false">
      <template #default="{ scope, isUpdate }">
        <el-form-item :label="$t('节点ID')" :title="$t('节点ID')" prop="id" required>
          <el-input v-model="scope.id" clearable maxlength="128" :minlength="1" show-word-limit :disabled="isUpdate" />
        </el-form-item>
        <el-form-item :label="$t('节点名称')" :title="$t('节点名称')" prop="name" required>
          <el-input v-model="scope.name" clearable maxlength="128" :minlength="1" show-word-limit />
        </el-form-item>
        <el-form-item :label="$t('最大并发数')" :title="$t('最大并发数')" prop="concurrent" required>
          <el-input-number v-model="scope.concurrent" :min="1" :max="10" controls-position="right" :precision="0" />
        </el-form-item>
        <el-form-item :label="$t('标签(tags)')" :title="$t('标签(tags)')" prop="tags">
          <el-input v-model="scope.tags" clearable maxlength="256" :minlength="1" show-word-limit />
        </el-form-item>
        <el-form-item :label="$t('备注')" :title="$t('备注')" prop="comment">
          <el-input v-model="scope.comment" autosize type="textarea" clearable maxlength="256" :minlength="1"
            show-word-limit />
        </el-form-item>
        <el-form-item :label="$t('状态')" :title="$t('状态')" prop="enable">
          <el-radio-group v-model="scope.enable">
            <el-radio :label="0">{{ $t('启用') }}</el-radio>
            <el-radio :label="1">{{ $t('禁用') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </template>
    </CreateComForm>
    <CheckNodeDialog ref="checkNodeRef"></CheckNodeDialog>
    <BindNodeDialog ref="bindNodeRef" @finishBind="refreshList"></BindNodeDialog>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import GList from '@/components/GList.vue'
import CreateComForm from '@/components/CreateComForm.vue'
import { getNodeList, createNodeApi, editNode, delNode, downloadClient } from "@/services/node"
import type { TableColumn, TablePagination, TableDataParams, Explose } from '@/model/table'
import type { CreateNodeParams, Node } from '@/model/node'
import { downloadByUrl, formatNodeStatus, formatObjectData, getRequireRules } from '@/utils/index'
import { emitter } from "@/utils/mitt";
import { useForm } from "@/hooks/useForm"
import CheckNodeDialog from './checkNodeDialog.vue'
import BindNodeDialog from './bindNodeDialog.vue'
import type { checkNodeType } from './checkNodeDialog.vue'
import type { bindNodeType } from './bindNodeDialog.vue'
import socket from '@/utils/socket'
import { validateUserName } from '@/utils/validate'
import { $t } from '@/locales'

const showDetailKeys = [
  "id",
  "name",
  "concurrent",
  "enable",
  "lastLinkTime",
  "firstLinkTime",
  "createTime",
  "tags",
  "comment",
  "mem",
  "cpu",
  "os",
  "platform",
  "ip",
  "adapter",
  "executions",
  "totalTaskCount",
  "status"
]
let loadingInstance:any;
const rule = getRequireRules([{ key: 'name' }], $t('节点')) || {}
const formNodeId = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error($t('请输入节点ID')))
  }
  if (!validateUserName(value)) {
    callback(new Error($t("节点ID为2~128个字符，只能使用字母、数字、下划线，需要以字母开头")))
  } else {
    callback()
  }
}
rule.id = [{ validator: formNodeId, trigger: 'change' }, { validator: formNodeId, trigger: 'blur' }]
const router = useRouter()

const refresh = ref(Date.now())
const comForm = ref<Explose | null>(null)
const checkNodeRef = ref<checkNodeType | null>(null)
const bindNodeRef = ref<bindNodeType | null>(null)
const oldId = ref('');
const rowList = ref<any>(null)
const rules = reactive(rule)

onMounted(() => {
  // 监听node 事件
  socket.on('node', nodeChange)
});

onUnmounted(() => {
  // 取消node事件监听
  socket.off('node', nodeChange)
})

// 节点相关socketio事件处理
const nodeChange = (ename: string, data: any, topic: any) => {
  switch (ename) {
    case 'node.taskcount.change':
      if (rowList.value && data && topic) {
        rowList.value.tableData = rowList.value.tableData.map((v: Node) => {
          if (v.id === topic) {
            v.executions = data.newExecutionCount
            v.totalTaskCount = data.newTotalCount
            v.faliedTaskPercent = data.newFailedPercent
          }
          return v
        })
      }
      break
    case 'node.status.change':
      if (rowList.value && data && topic) {
        rowList.value.tableData = rowList.value.tableData.map((v: Node) => {
          if (v.id === topic) {
            v.status = data.newStatus
          }
          return v
        })
      }
      break
  }
}

const { getListHandle, loading } = useForm({
  getListApi: (params: TableDataParams) => getNodeList(params),
  val: null,
  name: ''
})

// 节点表单默认值
const default_value = {
  id: '',
  name: '',
  concurrent: 10,
  tags: '',
  comment: '',
  enable: 1,
} as unknown as Node

// 表格列配置
const columns = [
  // {
  //   type: "selection",
  //   width: "55"
  // },
  {
    label: 'ID',
    property: 'id',
    width: "200",
    showOverflowTooltip: true,
    format: (row: any, column: any, cellValue: number, index: any) => { return <div onClick={() => gotoTaskMng(row)} class={'cursor-pointer truncate text-blue-500 hover:font-bold'}>{cellValue || "0"}</div> }
  },
  {
    label: $t('名称'),
    property: 'name',
    slotName: 'nodeName',
    width: "240",
    showOverflowTooltip: true,
  },
  {
    label: $t('机器状态'),
    property: 'faliedTaskPercent',
    slotName: 'machineStatus',
    width: "90",
  },
  {
    label: $t('状态'),
    property: 'status',
    slotName: 'status',
    width: "95"
  },
  {
    label: $t('标签'),
    property: 'tags',
  },
  {
    label: $t('并发数'),
    property: 'concurrent',
    width: "80"
  },
  {
    label: $t('任务数'),
    property: 'executions',
    width: "80",
    format: (row: any, column: any, cellValue: any, index: any) => { return cellValue || "0" }
  },
  {
    label: $t('历史任务数'),
    property: 'totalTaskCount',
    width: "100",
    format: (row: any, column: any, cellValue: any, index: any) => { return cellValue || "0" }
  },
  {
    label: $t('启用'),
    property: 'enable',
    slotName: 'enable',
    width: "65"
  },
  {
    label: $t('创建时间'),
    property: 'createTime',
    width: "160",
  },
  {
    label: $t('首次连接时间'),
    property: 'firstLinkTime',
    width: "160",
  },
  {
    label: $t('最后连接时间'),
    property: 'lastLinkTime',
    width: "160",
  },
  {
    label: $t('操作'),
    slotName: 'action',
    fixed: 'right',
    width: "220",
  }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
  key: 'batchDelete',
  label: $t('批量删除'),
}]

// 搜索栏配置
const searchList = [{
  key: 'name',
  type: 'input',
  name: $t('节点名称'),
  placeholder: $t('节点名称')
}, {
  key: 'tags',
  type: 'input',
  name: $t('标签'),
  placeholder: $t('节点tag标签')
}, {
  key: 'status',
  type: 'select',
  name: $t('状态'),
  placeholder: $t('请选择'),
  options: [{
    label: $t('全部'),
    value: 0,
  }, {
    label: $t('已连接'),
    value: 1,
  }, {
    label: $t('未连接'),
    value: 2,
  }, {
    label: $t('未绑定'),
    value: 3,
  }]
}, {
  key: 'enable',
  type: 'select',
  name: $t('启用状态'),
  placeholder: $t('请选择'),
  options: [{
    label: $t('全部'),
    value: 2,
  }, {
    label: $t('启用'),
    value: 0,
  }, {
    label: $t('禁用'),
    value: 1,
  }]
}]

// 显示创建节点弹窗
const addNode = () => {
  if (comForm.value) {
    comForm.value.show();
  }
}

// 获取机器状态
const getMachineStatus = (faliedTaskPercent: number) => {
  if (faliedTaskPercent < 20) {
    // 显示机器正常
    return 'machine-normal'
  } else if (faliedTaskPercent <= 50 && faliedTaskPercent >= 20) {
    // 显示机器部分异常
    return 'machine-part-abnormal'
  } else {
    // 显示机器异常
    return 'machine-abnormal'
  }
}

const getOSType = (val: Node) => {
  if (val.os) {
    let type = val.os;
    if (type.toLowerCase().includes("windows")) {
      type = "windows"
    } else if (type.toLowerCase().includes("linux")) {
      type = "linux"
    }
    return `${type}_${val.status}`
  } else {
    return 'unknow_machine'
  }
}

const refreshList = () => {
  refresh.value = Date.now();
}

// 创建/编辑节点
const handleCreateNode = async (data: CreateNodeParams | Node, isUpdate: boolean) => {
  try {
    if (isUpdate) {
      await editNode(oldId.value, { ...data as Node, id: oldId.value, newId: data.id })
    } else {
      await createNodeApi(data as CreateNodeParams)
    }
    if (comForm.value) {
      comForm.value.isShow = false
    }
    refresh.value = Date.now();
    ElMessage({
      type: 'success',
      message: $t('{action}节点成功', { action: isUpdate ? $t('修改') : $t('创建') }),
    })
  } catch (e) {
    console.log(e);
  }
}

const beforeSwitchChange = async (val: Node) => {
  val.newId = val.id;
  const { editHandle } = useForm({
    editApi: (id, data) => editNode(id, data),
    val: val,
    name: $t('节点')
  })
  const result = await editHandle();
  return result;
};

// 展示Node详情弹窗
const showDetail = async (val: Node) => {
  const infos = formatObjectData(val, showDetailKeys);
  infos.map(v => {
    if (v.key === 'status') {
      v.isHtml = true;
      v.value = <div class={`status ${formatNodeStatus(v.value)}`}>{formatNodeStatus(v.value, true)}</div>
    } else if (v.key === 'enable') {
      v.isHtml = true;
      v.value = <el-switch style="--el-switch-on-color: #13ce66" active-value="0" inactive-value="1" model-value={String(v.value)}
      ></el-switch>
    }
    return v;
  })
  const downLoadBtn = <div class="footer_button_wrap">
    {downloadClientDom(val)}
    <el-button onClick={() => downloadConfig(val)}>{$t('客户端秘钥文件下载')}</el-button>
  </div>
  emitter.emit("detailViewShow2", { data: infos, title: $t('节点详情'), width: '30%', drawerFooter: downLoadBtn });
}

const downloadClientDom = (val: Node) => {
  return <el-dropdown trigger={'click'} v-slots={{
    dropdown: () => (
      <el-dropdown-menu>
        <el-dropdown-item onClick={() => handleCommand(val.id, 'exe')}>Windows</el-dropdown-item>
        <el-dropdown-item>
          <el-dropdown v-slots={{
            dropdown: () => (
              <el-dropdown-menu>
                <el-dropdown-item onClick={() => handleCommand(val.id, 'deb')}>deb</el-dropdown-item>
                <el-dropdown-item onClick={() => handleCommand(val.id, 'rpm')}>rpm</el-dropdown-item>
              </el-dropdown-menu>
            )
          }}>
            <span>Linux</span>
          </el-dropdown>
        </el-dropdown-item>
      </el-dropdown-menu>
    ),
  }}>
    <el-button type="primary">
      {$t('节点客户端下载')}
    </el-button>
  </el-dropdown>
}

const handleCommand = async (id: string, command: string) => {
  loadingInstance = ElLoading.service({
      lock: true,
      text: $t('打包中...'),
      // background: 'rgba(0, 0, 0, 0.7)',
      customClass: 'loading-custom-class'
  });
  try {
    const data = await downloadClient(id, command, getDownProgress)
    const blob = new Blob([data], { type: 'application/zip' })
    if ((navigator as any).msSaveOrOpenBlob) {
      (navigator as any).msSaveOrOpenBlob(blob, `gstarcad_cloud_client_${command}.zip`)
    } else {
      const a = document.createElement('a')
      a.href = window.URL.createObjectURL(blob)
      a.setAttribute('download', `gstarcad_cloud_client_${command}.zip`)
      document.body.appendChild(a)
      a.click();
      (a.parentNode as any).removeChild(a)
    }
  } catch (e) {
    console.log(e)
    loadingInstance.close()
  }
}

const getDownProgress = (evt: ProgressEvent) => {
  const progressPercent = (evt.loaded / evt.total * 100).toFixed(2);
  if (progressPercent == '100.00') {
    loadingInstance.setText($t('下载完成'))
    loadingInstance.close()
  } else{
    loadingInstance.setText($t('下载进度：{progress}%', { progress: progressPercent }))
  }  
}

// 客户端秘钥文件下载
const downloadConfig = async (val: Node) => {
  const url = `/_node/_client_config_file/${val.id}`
  downloadByUrl(url, '?');
}

// 编辑节点
const updateNode = (val: Node) => {
  if (comForm.value) {
    oldId.value = val.id;
    comForm.value.show(val);
  }
}

// 删除节点
const deleteNode = async (val: Node) => {
  const { deleteHandle } = useForm({
    delApi: (id) => delNode(id),
    val: val,
    name: $t('节点')
  })
  deleteHandle({ params: val.id }).then((v: number) => {
    refresh.value = v;
  });
}

// 检测节点
const check = (val: Node) => {
  if (checkNodeRef.value) {
    checkNodeRef.value.show(val)
  }
}

// 绑定节点
const binding = (val: Node) => {
  // TODO
  if (bindNodeRef.value) {
    bindNodeRef.value.show(val)
  }
}

const gotoTaskMng = (val: Node) => {
  router.push({ name: 'servicesExecuteTask', query: { nodeName: val.name } })
}

</script>

<style>
.footer_button_wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>