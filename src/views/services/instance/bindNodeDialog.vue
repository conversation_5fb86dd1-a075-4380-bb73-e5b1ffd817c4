<template>
    <el-dialog v-model="dialogVisible" :title="$t('节点绑定')" width="50%" destroy-on-close>
        <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading"
            :getList="getListHandle" :showFilter="false" :refresh="refresh">
            <template #action="{ row }">
                <div>
                    <el-button type="primary" @click="bind(row)">{{ $t('绑定') }}</el-button>
                </div>
            </template>
        </GList>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false" v-noBlur>{{ $t('返回') }}</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { TableColumn, TablePagination, TableDataParams } from '@/model/table'
import GList from '@/components/GList.vue'
import { getNotBindNodes, bindNode } from '@/services/node'
import { useForm } from "@/hooks/useForm"
import { $t } from '@/locales'

export interface bindNodeType {
    dialogVisible: boolean
    show: (v: object) => void
}

const dialogVisible = ref(false)
const item = ref()
const refresh = ref(Date.now())

const { getListHandle, loading } = useForm({
    getListApi: (params: TableDataParams) => getNotBindNodes(params),
    val: null,
    name: ''
})

const columns = [
    {
        label: $t('编号'),
        property: 'index',
        width: "200"
    },
    {
        label: 'IP',
        property: 'ip',
    },
    {
        label: $t('指纹'),
        property: 'finger',
        'showOverflowTooltip': true
    },
    {
        label: $t('操作'),
        slotName: 'action',
        width: "120"
    },
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
    currentPage: 1,
    pageSizes: [10, 20, 30, 40],
    limit: 10,
    layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

const show = (data: object) => {
    item.value = data
    dialogVisible.value = true

}

defineExpose({
    dialogVisible,
    show
})

const emit = defineEmits<{
    (e: "finishBind"): void
}>()

//绑定
const bind = async (val: any) => {
    try {
        await bindNode(item.value.id, val.index);
        ElMessage({
            type: 'success',
            message: $t('绑定成功'),
        })
        dialogVisible.value = false
        emit('finishBind');
    } catch (e) {
        console.log(e);
    }
}

</script>
