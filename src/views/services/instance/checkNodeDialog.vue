<template>
    <el-dialog v-model="dialogVisible" :title="$t('节点检测')" width="50%" destroy-on-close @close="reset">
        <el-steps :active="active" align-center>
            <el-step v-for="step in steps" :key="step.title" :title="step.title" :description="step.description"
                :status="step.status" />
        </el-steps>
        <el-progress :text-inside="true" :stroke-width="20" :percentage="progress" class="mt-4" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false" v-noBlur>{{ $t('返回') }}</el-button>
                <el-button type="primary" @click="check" v-noBlur :disabled="loading">
                    {{ $t('开始检测') }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, inject, reactive, watch } from 'vue'
import { checkNode } from "@/services/node"
import { getTaskProgress } from "@/services/task"
import { sleep } from '@/utils/httpUtil'
import { $t } from '@/locales'

export interface checkNodeType {
    dialogVisible: boolean
    show: (v: object) => void
}

const default_steps = [{
    title: $t('提交任务'),
    description: $t('任务已提交，进入任务队列'),
    status: 'wait',
}, {
    title: $t('任务执行'),
    description: $t('任务执行中'),
    status: 'wait',
}, {
    title: $t('完成'),
    description: $t('成功或失败'),
    status: 'wait',
},]

const dialogVisible = ref(false)
const item = ref()
const loading = ref(false);
const active = ref(0)
const progress = ref(0);

const steps = reactive(JSON.parse(JSON.stringify(default_steps)))

const t = inject<any>('t');

const show = (data: object) => {
    item.value = data
    dialogVisible.value = true
    progress.value = 0
}

defineExpose({
    dialogVisible,
    show
})

watch(
    () => dialogVisible.value,
    (v) => {
        if (!v) {
            reset()
        }
    },
);

// 后续重构
const check = () => {
    reset();
    loading.value = true;
    steps[0].status = 'success'
    setTimeout(async () => {
        active.value++;
        let data
        try {
            const res = await checkNode(item.value.id)
            data = res.data
            progress.value = 30
            steps[1].status = 'success'
        } catch (e: any) {
            loading.value = false
            steps[0].status = 'error'
            steps[0].description = `${e.msg || $t('错误')}`
            return
        }
        try {
            await getTaskResult(data.id)
        } catch (e) {
            goToFinishStep('error')
        }
    }, 100);
}

const getTaskResult = async (id: number) => {
    if (!id) return;
    let flag = true
    while (flag && dialogVisible.value) {
        try {
            const { data } = await getTaskProgress(id);
            progress.value = data.progress * 0.7 + 30;
            if (data.progress >= 100) {
                flag = false
                goToFinishStep('success')
                return
            }
        } catch (e) {
            flag = false
            goToFinishStep('error')
            return
        }
        // 轮询间隔
        await sleep(3000)
    }
}

const goToFinishStep = (type: 'success' | 'error') => {
    active.value++;
    active.value++;
    loading.value = false
    steps[2].description = `${$t('任务执行')}${t(`common.${type}`)}`
    steps[2].status = type
    progress.value = 100
}

const reset = () => {
    active.value = 0;
    progress.value = 0;
    loading.value = false
    default_steps.forEach((step, index) => {
        steps[index] = { ...step };
    })
}

</script>
