<template>
  <div class="h-full">
    <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading" :getList="getListHandle"
      :showFilter="true" :refresh="refresh" :searchList="searchList">
      <!-- <template #tableHeader>
        <el-button v-noBlur type="primary" @click="addTaskType">添加任务类型</el-button>
      </template> -->
      <template #action="{ row }">
        <div class="layout-action">
          <span class="action-text" @click="deleteRow(row)" :title="$t('删除')">{{ $t('删除') }}</span>
          <!-- <span @click="deleteTaskType(row)">删除</span> -->
        </div>
      </template>
    </GList>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import GList from "@/components/GList.vue"
import { getFilePermList, deleteFilePerm } from "@/services/file"
import type { TableColumn, TablePagination, TableDataParams, Explose } from '@/model/table'
import type { getFilePermListQuery, DeleteFilePermQuery } from '@/model/file'
import { useForm } from "@/hooks/useForm"
import { $t } from '@/locales'

const refresh = ref(Date.now())

const { getListHandle, loading } = useForm({
  getListApi: (params: TableDataParams) => getFilePermList(params),
  val: null,
  name: ''
})
const accountTypeOpts = [{
  label: $t('用户'),
  value: 1
}, {
  label: $t('用户组'),
  value: 2
}]
const grantTypeOpts = [{
  label: $t('限制编辑'),
  value: 2
}, {
  label: $t('限制编辑、限制查看'),
  value: 3
}]
const permOpts = [{
  label: $t('查看'),
  value: 1
}, {
  label: $t('编辑'),
  value: 2
}, {
  label: $t('查看、编辑'),
  value: 3
}]

// 表格列配置
const columns = [
  {
    label: $t('文件名'),
    property: 'fileName',
    showOverflowTooltip: true,
  },
  {
    label: $t('账户类型'),
    property: 'accountType',
    format: (row: any, column: any, cellValue: number, index: any) => { return accountTypeOpts.find(v => v.value === cellValue)?.label || "-" }
  },
  {
    label: $t('账户名称'),
    property: 'accountName',
    showOverflowTooltip: true
  },
  {
    label: $t('授权类型'),
    property: 'grantType',
    format: (row: any, column: any, cellValue: number, index: any) => { return grantTypeOpts.find(v => v.value === cellValue)?.label || "-" },
    showOverflowTooltip: true,
  },
  {
    label: $t('权限'),
    property: 'perm',
    format: (row: any, column: any, cellValue: number, index: any) => { return permOpts.find(v => v.value === cellValue)?.label || "-" },
    width: "100"
  },
  {
    label: $t('授权时间'),
    property: 'grantTime',
    width: "160",
  },
  {
    label: $t('操作'),
    slotName: 'action',
    fixed: 'right',
    width: "120",
  }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination


// 搜索栏配置
const searchList = [{
  key: 'searchFileName',
  type: 'input',
  name: $t('文件名称'),
  placeholder: $t('文件名称')
}, {
  key: 'accountType',
  type: 'select',
  name: $t('账户类型'),
  placeholder: $t('请选择'),
  options: [{
    label: $t('全部'),
    value: 0,
  }, {
    label: $t('用户'),
    value: 1,
  }, {
    label: $t('组'),
    value: 2,
  }]
}, {
  key: 'searchAccountName',
  type: 'input',
  name: $t('账户名称'),
  placeholder: $t('账户名称'),
}]


// 删除文件权限
const deleteRow = async (val: any) => {
  console.log('val', val)
  const { deleteHandle } = useForm({
    delApi: (params) => deleteFilePerm(params),
    val: val,
    name: $t('文件权限')
  })
  deleteHandle({
    params: [{
      accountType: val.accountType,
      fileId: val.fileId,
      grantType: val.grantType,
      identityId: val.identityId
    }]
  }).then((v: number) => {
    refresh.value = v;
  });
}

</script>

<style></style>