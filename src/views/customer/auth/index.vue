<template>
  <div class="h-full bg-white p-6">
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane :label="$t('用户权限设置')" name="用户">
        <PermissionView v-if="activeName === '用户'" :title="activeName" :auths="auths" :pagination="pagination"
          :getList="getUserPermissionList" :loading="loading" :default_bits="default_bits">
        </PermissionView>
      </el-tab-pane>
      <el-tab-pane :label="$t('用户组权限设置')" name="用户组">
        <PermissionView v-if="activeName === '用户组'" :title="activeName" :auths="auths" :pagination="pagination"
          :getList="getUserPermissionList" :loading="loading" :default_bits="default_bits">
        </PermissionView>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup lang="ts">
import { onBeforeMount, ref } from 'vue'
import { getAuth } from '@/services/auth'
import PermissionView from '../components/permissionView.vue'
import { getAuthUserList } from '@/services/user'
import { getAuthGroupList } from '@/services/group'
import type { TableDataParams, TablePagination } from '@/model/table'
import { $t } from '@/locales'

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

const loading = ref(false)
const auths = ref<Recordable[]>([]);
const permissionList = ref<Recordable>([]);
const activeName = ref('用户')
const default_bits = ref<any>([]);

onBeforeMount(async () => {
 getAuthData();
})

// 获取用户权限
const getUserPermissionList = async (params: TableDataParams) => {
  loading.value = true;
  const authArr: Recordable[] = [];
  try {
    const fn = activeName.value === '用户' ? getAuthUserList : getAuthGroupList;
    const { data } = await fn(params);
    data.list.forEach((v: any) => {
      const obj = { name: getName(v, activeName.value), id: v.id, permissions: {} }
      for (let i = 0; i < permissionList.value.length; i++) {
        const p = permissionList.value[i];
        (obj.permissions as any)[p.bit] = {
          value: !!(v && v.bits.includes(p.bit)),
          name: p.name,
          bit: p.bit as number,
        }
      }
      authArr.push(obj)
    })
    return {
      list: authArr,
      total: data.total,
    }
  } catch (e) {
    return {
      list: [],
      total: 0,
    }
  } finally {
    loading.value = false;
  }
}

const getName = (value: any, type: string) => {
  if (type === '用户') {
    return `${value['fullname']}-${value['user_name']}`
  } else {
    return value['name']
  }
}

const getAuthData = async () => {
  try {
    const { data } = await getAuth()
    auths.value = data;
    getDefaultBits(data)
    permissionList.value = auths.value.reduce((a: Recordable, e: Recordable) => a.concat(e.auths), [])
  } catch (e) {
    console.log(e)
  }
}

const getDefaultBits = (data: Recordable[]) => {
  if (!data) return []
  try {
    default_bits.value = data.reduce(function (prev, cur, index, arr) {
      const bits = cur.auths.filter((v: Recordable) => v.default).map((k: Recordable) => k.bit)
      return prev.concat(bits);
    }, []);
  } catch (e) {
    console.log(e)
  }
}

</script>
<style lang="scss" scoped>
.auth {
  :deep(.cell) {
    display: flex;
    justify-content: center;
  }
}
</style>
