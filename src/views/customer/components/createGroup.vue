<template>
    <el-drawer v-model="isShow" width="400px" direction="rtl" @close="resetForm(ruleFormRef)" :close-on-click-modal="false">
        <template #header>
            <div class="drawer-title">{{ $t("创建用户组") }}</div>
        </template>
        <template #default>
            <el-form :model="groupForm" label-position="left" ref="ruleFormRef" :rules="rules">
                <el-form-item :label="$t('父级用户组')" :label-width="formLabelWidth" prop="parentName" v-if="parentGroup.name">
                    <div class="truncate" :title="parentGroup.name || ''">{{ parentGroup.name }}</div>
                </el-form-item>
                <el-form-item :label="$t('用户组编号')" :label-width="formLabelWidth" prop="code">
                    <el-input v-model="groupForm.code" autocomplete="off" clearable maxlength="32" minlength="1"
                        show-word-limit />
                </el-form-item>
                <el-form-item :label="$t('用户组名称')" :label-width="formLabelWidth" prop="name">
                    <el-input v-model="groupForm.name" autocomplete="off" clearable maxlength="128" minlength="1"
                        show-word-limit />
                </el-form-item>
                <el-form-item :label="$t('描述')" :label-width="formLabelWidth" prop="description">
                    <el-input v-model="groupForm.description" autosize type="textarea" clearable maxlength="512"
                        minlength="1" show-word-limit />
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="isShow = false">{{ $t('取消') }}</el-button>
                <el-button type="primary" @click="submitForm(ruleFormRef)">
                    {{ $t('确定') }}
                </el-button>
            </span>
        </template>
    </el-drawer>
</template>

<script lang="ts" setup>

import { ref, reactive, onBeforeMount } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { CreateGroupParams, Group } from '@/model/group';
import { emitter } from "@/utils/mitt";
import { useForm } from "@/hooks/useForm"
import { getTreeRoot } from "@/utils/const"
import { validateGroupCode } from '@/utils/validate';
import { $t } from '@/locales';

const formLabelWidth = '140px'
const ruleFormRef = ref<FormInstance>()
const isShow = ref(false)
const isUpdate = ref(false);
const parentGroup = reactive({
    code: '',
    name: '',
    node: null,
});
const groupForm = reactive({
    code: '',
    name: '',
    description: '',
})

const emit = defineEmits<{
    (e: "onCreateGroupSubmit", data: CreateGroupParams | Group, isUpdate: boolean): void
}>()

const { resetForm, submitForm } = useForm({
    submitCallBack: () => { emit("onCreateGroupSubmit", { ...groupForm, parentCode: parentGroup.code }, isUpdate.value) },
    val: null,
    name: ''
})

const formGroupCode = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error($t('请输入编号')))
    } else if (!validateGroupCode(value)) {
        callback(new Error($t("编号1~32个字符, 只能使用字母、数字、下划线，需要以字母开头")))
    } else {
        callback()
    }
}


const rules = reactive<FormRules>({
    code: [
        { validator: formGroupCode, trigger: 'blur', required: true, },
        { validator: formGroupCode, trigger: 'change', required: true, },
    ],
    name: [
        { required: true, message: $t('请输入名称'), trigger: 'blur' },
        { required: true, message: $t('请输入名称'), trigger: 'change' },
    ],
    description: [
        { required: true, message: $t('请输入描述'), trigger: 'blur' },
        { required: true, message: $t('请输入描述'), trigger: 'change' },
    ],
})

onBeforeMount(() => {
  const TREE_ROOT = getTreeRoot()
    // 显示弹窗
    emitter.on("createGroupDialogShow", (params: { data: Recordable, isUp: boolean }) => {
        if (params.data) {
            parentGroup.name = params.data.fullname || params.data.name;
            parentGroup.code = params.data.code === TREE_ROOT.code ? undefined : (params.data.code || '');
            parentGroup.node = params.data.node as any;
        }
        isUpdate.value = params.isUp;
        isShow.value = true
    });
});

defineExpose({
    isShow,
})

</script>