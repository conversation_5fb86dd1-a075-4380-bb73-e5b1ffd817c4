<template>
    <el-drawer v-model="isShow" size="30%" direction="rtl" :title="$t('应用详情')" @close="resetForm(ruleFormRef)" :close-on-click-modal="false">
        <template #header>
            <div class="drawer-title">{{ formTitle }}</div>
        </template>
        <template #default>
            <div v-if="isShow">
                <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="left" label-width="120px"
                    style="max-width: 555px" size="default" status-icon>
                    <el-form-item :label="$t('姓氏')" prop="last_name" required>
                        <el-input v-model="ruleForm.last_name" clearable maxlength="32" minlength="1" show-word-limit />
                    </el-form-item>
                    <el-form-item :label="$t('名字')" prop="first_name" required>
                        <el-input v-model="ruleForm.first_name" clearable maxlength="32" minlength="1" show-word-limit />
                    </el-form-item>
                    <el-form-item :label="$t('用户名')" prop="user_name" required>
                        <el-input v-model="ruleForm.user_name" clearable maxlength="128" minlength="1" show-word-limit />
                    </el-form-item>
                    <el-form-item :label="$t('手机号')" prop="phone">
                        <el-input v-model="ruleForm.phone" clearable />
                    </el-form-item>
                    <el-form-item :label="$t('邮箱')" prop="email">
                        <el-input v-model="ruleForm.email" clearable maxlength="128" minlength="1" show-word-limit />
                    </el-form-item>
                    <el-form-item :label="$t('密码')" prop="password" v-if="!isUpdate">
                        <el-input v-model="ruleForm.password" required type="password" :placeholder="$t('请输入密码')" show-password
                            maxlength="32" minlength="6" clearable />
                    </el-form-item>
                    <el-form-item :label="$t('是否禁用')" prop="disable">
                        <el-checkbox v-model="ruleForm.disable" />
                    </el-form-item>
                    <el-form-item :label="$t('所属权限组')" prop="groups">
                        <el-select v-model="ruleForm.codes" multiple :placeholder="$t('请输入')" style="width: 240px" filterable remote
                            reserve-keyword remote-show-suffix>
                            <!-- <template #prefix>
                                <el-icon>
                                    <component :is="$ElIcon['Search']" />
                                </el-icon>
                            </template> -->
                            <el-option v-for="(item) in groupList" :key="item.code" :label="item.fullname" :value="item.code">

                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
        </template>
        <template #footer>
            <div style="flex: auto">
                <el-button type="primary" @click="submitForm(ruleFormRef)">{{ $t('确认') }}</el-button>
                <el-button type="primary" @click="resetForm(ruleFormRef)">{{ $t('重置') }}</el-button>
                <el-button @click="isShow = false">{{ $t('取消') }}</el-button>
            </div>
        </template>
    </el-drawer>
</template>
<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { CreateUserParams, User } from '@/model/user'
import { getGroupList } from "@/services/group"
import { validateUserName, validateEmail, validatePhone, validatePassWord } from '@/utils/validate'
import { useForm } from "@/hooks/useForm"
import { $t } from '@/locales'

const ruleFormRef = ref<FormInstance>()

interface defaultValue extends Recordable {
    id: any;
}

const default_value = {
    id: undefined,
    first_name: '',
    last_name: '',
    phone: '',
    email: '',
    user_name: '',
    password: '123456',
    codes: [],
    disable: false,
} as defaultValue

const emit = defineEmits<{
    (e: "onCreateUserSubmit", user: CreateUserParams | User, isUpdate: boolean): void
}>()

const { resetForm, submitForm } = useForm({
    submitCallBack: () => { emit("onCreateUserSubmit", ruleForm, isUpdate.value); },
    val: null,
    name: ''
})

interface DetailProps {
    item?: any
}
const isShow = ref(false)
const item = ref()
const groupList = ref<Recordable>([]);
const isUpdate = ref(false);

const ruleForm = reactive({ ...default_value })

const formTitle = computed(() => { return $t('{action}用户', { action: isUpdate.value ? $t('修改') : $t('添加') }) })

const formUserName = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error($t('请输入用户名')))
    }
    if (!validateUserName(value)) {
        callback(new Error($t("用户名2~128个字符，只能使用字母、数字、下划线，需要以字母开头")))
    } else {
        callback()
    }
}
const formPhone = (rule: any, value: any, callback: any) => {
    // if (ruleForm.email === '' && ruleForm.user_name === '' && value === '') {
    //     callback(new Error('请输入手机号'))
    // }
    if (!validatePhone(value)) {
        callback(new Error($t("手机号格式不正确")))
    } else {
        callback()
    }
}
const formEmail = (rule: any, value: any, callback: any) => {
    // if (ruleForm.user_name === '' && ruleForm.phone === '' && value === '') {
    //     callback(new Error('请输入邮箱'))
    // }
    if (!validateEmail(value)) {
        callback(new Error($t("邮箱格式不正确")))
    } else {
        callback()
    }
}
const formPassword = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error($t('请输入密码')))
    } else if (!validatePassWord(value)) {
        callback(new Error($t("密码格式为6~32任意字符")))
    } else {
        callback()
    }
}

const rules = reactive<FormRules>({
    first_name: [
        { required: true, message: $t('请输入名字'), trigger: 'blur' },
        { required: true, message: $t('请输入名字'), trigger: 'change' },
    ],
    last_name: [
        {
            required: true,
            message: $t('请输入姓氏'),
            trigger: 'blur',
        },
        {
            required: true,
            message: $t('请输入姓氏'),
            trigger: 'change',
        },
    ],
    user_name: [{ validator: formUserName, trigger: 'change' }, { validator: formUserName, trigger: 'blur' }],
    phone: [{ validator: formPhone, trigger: 'blur' }],
    email: [{ validator: formEmail, trigger: 'blur' }],
    password: [{ validator: formPassword, trigger: 'blur' }],
})

const getPermissionGroupList = async () => {
    try {
        const { data } = await getGroupList({ page: 1, limit: 100 })
        groupList.value = data.list || [];
    } catch (e) {
        console.log(e)
    }
}

const show = async (data?: Recordable, codes?: string[]) => {
    getPermissionGroupList()
    item.value = data
    if (data) {
        item.value = data
        for (const i in ruleForm) {
            ruleForm[i] = data[i];
        }
        isUpdate.value = true;
    } else {
        setDefault();

        isUpdate.value = false;
    }
    ruleForm.codes = codes
    isShow.value = true
}

const setDefault = () => {
    for (const i in ruleForm) {
        ruleForm[i] = default_value[i]
    }
}

defineExpose({
    isShow,
    show
})
const props = withDefaults(defineProps<DetailProps>(), {
    item: {},
    isShow: false,
})

</script>
<style scoped>
.create-type {
    width: 120px;
}
</style>
