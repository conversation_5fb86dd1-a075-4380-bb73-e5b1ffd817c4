<template>
    <div class="auth" v-loading="loading">
        <div class="mb-4">
            <el-button v-show="!isUpdate" v-noBlur type="primary" @click="openUpdate" :title="$t('编辑权限')">
                <span class="btn-text">{{ $t('编辑权限') }}</span>
            </el-button>
            <el-input v-show="!isUpdate" v-model="searchText" clearable maxlength="128" :minlength="1" show-word-limit
                :placeholder="$t('请输入搜索内容')" class="input-with-select" @keyup.enter="searchAuth">
                <template #append>
                    <el-icon class="action-icon cursor-pointer" @click="searchAuth">
                        <component :is="$ElIcon['Search']" />
                    </el-icon>
                </template>
            </el-input>

            <div v-show="isUpdate" class="layout-flex w-[450px]">
                <el-select v-model="searchValue" filterable remote reserve-keyword :placeholder="$t('请输入')"
                    :remote-method="remoteMethod" :loading="selectLoading" remote-show-suffix suffix-icon="search">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button v-noBlur type="primary" @click="addAuth" :title="$t('添加')">
                    <span class="btn-text">{{ $t('添加') }}</span>
                </el-button>
                <el-button v-noBlur type="primary" @click="saveAll" :title="$t('保存所有')">
                    <span class="btn-text">{{ $t('保存所有') }}</span>
                </el-button>
                <el-button v-noBlur type="primary" text @click="cancel" :title="$t('取消')">
                    <span class="btn-text">{{ $t('取消') }}</span>
                </el-button>
            </div>
        </div>
        <el-table @click.stop :data="tableData" style="width: 100%" max-height="60vh">
            <el-table-column prop="name" :label="$t('名称')" width="250" fixed="left" :show-overflow-tooltip="true">
                <template #default="scope">
                    <div class="table-name">
                        <div class="flex-1 truncate">{{ scope.row.name }}</div>
                        <div v-if="isUpdate" class="layout-flex gap-small">
                            <el-button type="primary" text @click="selectAll(scope.row)" :title="$t('全选')">
                                <span class="btn-text">{{ $t('全选') }}</span>
                            </el-button>
                            <el-button type="primary" text @click="clearAll(scope.row)" :title="$t('清空')">
                                <span class="btn-text">{{ $t('清空') }}</span>
                            </el-button>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column v-for="(value) in auths" :key="value.id" :label="value.menu" align="center">
                <el-table-column v-for="(v) in value.auths" :key="v.bit" :label="v.name" align="center">
                    <template #default="scope">
                        <el-icon :color="hasAuth(scope, v.bit) ? 'green' : 'red'" v-if="!isUpdate" :size="16" class="block">
                            <component :is="$ElIcon['CircleCheckFilled']" v-if="hasAuth(scope, v.bit)" />
                            <component :is="$ElIcon['CircleCloseFilled']" v-else />
                        </el-icon>
                        <el-checkbox v-else v-model="scope.row.permissions[v.bit].value"
                            @change="changeRole(scope.row, v.bit)" />
                    </template>
                </el-table-column>
            </el-table-column>
        </el-table>
        <el-pagination class="layout-table-pagination" v-if="totalCount" :total="totalCount" :current-page="currentPage"
            :page-sizes="pagination?.pageSizes" :page-size="pageSize" :layout="pagination?.layout"
            @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
</template>

<script setup lang="ts">
import type { TableDataParams, TablePagination } from '@/model/table'
import { $t } from '@/locales';
import { ref, onMounted, watch, computed } from 'vue'
import { getUserBySearch, updateUserAuthBatch } from '@/services/user'
import { getGroupBySearch, updateGroupAuthBatch } from '@/services/group'

interface PermissionProps {
    title: string,
    auths: Recordable[],
    pagination?: TablePagination,
    loading?: boolean,
    getList: (params: TableDataParams) => Promise<{ list: Array<object>, total: number }>, // 获取table data 方法，需要返回promise
    default_bits: number[]
}

interface ListItem {
    value: string
    label: string
}

const props = defineProps<PermissionProps>()

const isUpdate = ref(false);
const options = ref<ListItem[]>([])
const searchValue = ref(null)
const searchText = ref('')
const selectLoading = ref(false)
const totalCount = ref(0);
const tableData = ref<Recordable>([]);
const oldTableData = ref<Recordable>([]);
const refresh = ref(Date.now())
const currentPage = ref(props?.pagination?.currentPage || 1);
const pageSize = ref(props?.pagination?.pageSizes?.[0] || 10);

const isUser = computed(() => props.title === '用户')

onMounted(() => {
    getListData();
})

// 监听父组件refresh值,变化了刷新页面
watch(
    () => [refresh, props.auths],
    () => {
        isUpdate.value = false;
        getListData();
    },
    { deep: true }
);

// 获取表格数据
async function getListData(search?: string) {
    const params = { page: currentPage.value, limit: pageSize.value, keyword: search || '' };
    if (props.getList) {
        const { list = [], total } = await props.getList(params);
        tableData.value = JSON.parse(JSON.stringify(list));
        oldTableData.value = JSON.parse(JSON.stringify(list));
        totalCount.value = total;
    } else {
        tableData.value = [];
        totalCount.value = 0;
    }
}

// 处理权限联动,待优化和补充
const changeRole = (row: any, bit: any) => {
    const file_auths = props.auths.find((auth) => auth.menu === '文件管理')
    const file_bits = file_auths && file_auths.auths.map((v: Recordable) => {
        return v.bit
    })
    if (bit === 1) {
        // 点击查看
        if (!row.permissions[bit].value) {
            // 如果取消勾选查看,则其他文件权限按钮为不勾选状态
            tableData.value = tableData.value.map((v: Recordable) => {
                if (v.id === row.id) {
                    const newPermissions: Recordable = {};
                    for (const key in v.permissions) {
                        // 判断权限不是查看且属于文件权限范围
                        const check = Number(key) !== 1 && file_bits.includes(Number(key))
                        newPermissions[key] = { ...v.permissions[key], value: !check ? v.permissions[key].value : false }
                    }
                    v.permissions = newPermissions
                }
                return v;
            })
        }
    } else {
        if (row.permissions[bit].value && file_bits.includes(bit)) {
            tableData.value = tableData.value.map((v: Recordable) => {
                if (v.id === row.id) {
                    v.permissions[1].value = true
                }
                return v;
            })
        }
    }
}

// select搜索获取数据
const remoteMethod = async (query: string) => {
    if (query) {
        selectLoading.value = true
        try {
            const fn = isUser.value ? getUserBySearch : getGroupBySearch;
            const key = isUser.value ? 'fullname' : 'name';
            const { data } = await fn(query);
            options.value = data.map((v: any) => {
                return {
                    value: v.id,
                    label: isUser.value ? `${v[key]}-(${v.user_name})` : v[key]
                }
            })
        } catch (e) {
            console.log(e)
        } finally {
            selectLoading.value = false
        }
    } else {
        options.value = []
    }
}

// 分页切换每页数量
const handleSizeChange = (val: number) => {
    pageSize.value = val;
    getListData()
}

// 分页切换页数
const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    getListData();
}

const hasAuth = (scope: any, bit: number) => {
    return scope.row && scope.row.permissions[bit] && scope.row.permissions[bit].value
}

const formatData = (row: any, action: 'clear' | 'selectAll') => {
    tableData.value = tableData.value.map((v: Recordable) => {
        if (v.id === row.id) {
            const newPermissions: Recordable = {};
            for (const key in v.permissions) {
                newPermissions[key] = { ...newPermissions[key], value: action === 'selectAll' }
            }
            v.permissions = newPermissions
        }
        return v;
    })
}

// 全选
const selectAll = (row: any) => {
    formatData(row, 'selectAll')
}
// 清空
const clearAll = (row: any) => {
    formatData(row, 'clear')
}

// 搜索权限
const searchAuth = async () => {
    try {
        // TODO
        getListData(searchText.value || '')
    } catch (e) {
        console.log(e)
    }
}

// 添加权限
const addAuth = async () => {
    if (!searchValue.value) {
        ElMessage({
            type: 'error',
            message: $t('请输入名称'),
        })
        return;
    }
    try {
        if (tableData.value.some((v: Recordable) => v.id === searchValue.value)) {
            ElMessage({
                type: 'error',
                message: $t('{title}已存在', { title: props.title }),
            })
            return;
        }
        const fn = isUser.value ? updateUserAuthBatch : updateGroupAuthBatch;
        await fn(
            [{
                bits: props.default_bits,
                id: searchValue.value
            }]
        );
        ElMessage({
            type: 'success',
            message: $t('添加成功'),
        })
        getListData()
        // refresh.value = Date.now();
    } catch (e) {
        console.log(e)
    }
}

// 保存所有编辑
const saveAll = async () => {
    try {
        //TODO
        const req: Recordable[] = [];
        tableData.value.forEach((v: Recordable) => {
            const bits: number[] = [];
            Object.keys(v.permissions).forEach(bit => {
                if (v.permissions[bit].value) {
                    bits.push(Number(bit))
                }
            })
            req.push({
                id: v.id,
                bits: bits,
            })
        })
        const fn = isUser.value ? updateUserAuthBatch : updateGroupAuthBatch;
        await fn(req);
        ElMessage({
            type: 'success',
            message: $t('保存成功'),
        })
        refresh.value = Date.now();
        isUpdate.value = false;
        searchValue.value = null;
    } catch (e) {
        console.log(e)
    }
}

// 打开编辑
const openUpdate = () => {
    isUpdate.value = true;
    oldTableData.value = JSON.parse(JSON.stringify(tableData.value));
}

// 取消编辑
const cancel = () => {
    tableData.value = JSON.parse(JSON.stringify(oldTableData.value));
    isUpdate.value = false;
    searchValue.value = null;
}

</script>

<style lang="scss" scoped>
.auth {
    :deep(.cell) {
        display: flex;
        justify-content: center;
    }

    :deep(.el-table__row) {
        height: 49px;
    }

    .table-name {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

.layout-table-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.input-with-select {
    width: 300px;
    margin-left: 20px;
}
</style>