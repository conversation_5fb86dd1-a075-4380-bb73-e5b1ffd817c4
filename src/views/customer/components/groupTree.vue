/** 组织树组件,目前没有考虑做成通用tree组件,后续根据需要进行重构 */
<template>
    <div class="tree">
        <el-button type="primary" @click="() => createGroup()">{{ $t('创建用户组') }}</el-button>
        <el-input class="tree-search" v-model="query" :placeholder="$t('请输入关键字查找')" clearable @input="onQueryChanged" />
        <el-tree-v2 ref="treeRef" :data="treeData" :props="treeProps" :expand-on-click-node="false"
            :default-expanded-keys="defaultExpandedkeys" :highlight-current="true" :height="treeHeight"
            :current-node-key="currentNode" :filter-method="filterMethod" @nodeClick="changeItem" auto-expand-parent>
            <template #default="{ data, node }">
                <div class="tree-node">
                    <span class="grid-value truncate" :title="data.name || ''">{{ data.name || "-" }}</span>
                    <span @click.stop class="flex">
                        <el-dropdown trigger="click" v-if="showDrop">
                            <el-icon class="actionBtn">
                                <component :is="$ElIcon['MoreFilled']" />
                            </el-icon>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="createGroup(data, node)">
                                        {{ $t('添加子组') }}
                                    </el-dropdown-item>
                                    <el-dropdown-item @click="deleteGroup(data)" v-if="data.code !== rootId">
                                        {{ $t('删除') }}
                                    </el-dropdown-item>
                                    <el-dropdown-item @click="showGroupDetail(data)" v-if="data.code !== rootId">
                                        {{ $t('详情') }}
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </span>
                </div>
            </template>
        </el-tree-v2>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { getGroupTree, delGroup } from "@/services/group"
import type { ElTreeV2 } from "element-plus";
import type { TreeNode, TreeNodeData } from "element-plus/es/components/tree-v2/src/types";
import type { Group } from "@/model/group"
import { getTreeRoot } from "@/utils/const"
import { emitter } from "@/utils/mitt";
import { useForm } from "@/hooks/useForm"
import { formatObjectData } from '@/utils';
import { useStoreConfig } from "@/stores/config";
import { $t } from '@/locales';

const showDetailKeys = [
    "id",
    "code",
    "name",
    "description",
    "parentCode",
    "children"
]
// 获取国际化的常量
const TREE_ROOT = getTreeRoot()
const rootId = TREE_ROOT.code;
const configStores = useStoreConfig()

// 定义props
interface TreeProps {
    isSearch?: boolean // 是否显示搜索框
}
// 接受父组件参数，配置默认值
const props = defineProps<TreeProps>();

interface treeNode extends TreeNode {
    name: string;
}

const router = useRouter()

const rootGroupName = computed(() => {
    return configStores.site_name
});

const currentNode = ref(rootId);
const treeRef = ref<InstanceType<typeof ElTreeV2>>();
const query = ref("");
const treeHeight = ref(0);
const showDrop = ref(true);
const scrollContainer: any = ref(null);
let scrollTimer:NodeJS.Timeout|null = null;
const treeData = ref([{
    ...TREE_ROOT,
    children: [],
    name: rootGroupName.value
}])

const defaultExpandedkeys = reactive([rootId])

const parentGroup = reactive({
    code: '',
    name: rootGroupName.value || TREE_ROOT.name,
    node: null,
});

interface Tree {
    value: string
    label: string
    children?: Tree[]
}

const treeProps = {
    value: 'code',
    label: 'name',
    children: 'children',
}

const emit = defineEmits<{
    (e: "changeGroup", group: any): void
}>()

onMounted(async () => {
    await getTreeData();
    const { query } = router.currentRoute.value;
    if (query.code) {
        const callback = () => {
            // changeItem({ id: query.id, code: query.code})
        }
        expandNodeByCode(query.code as string, callback)
    }
    const dom = document.querySelector('.tree');
    if (dom) {
        treeHeight.value = dom.clientHeight - 32 - 32 - 64
    }
    scrollContainer.value = document.querySelector('.el-tree-virtual-list')
    scrollContainer.value && scrollContainer.value.addEventListener('scroll', handleScroll, true)
})

onUnmounted(() => {
    scrollContainer.value && scrollContainer.value.removeEventListener('scroll', handleScroll);
});

const handleScroll = () => {
    if (scrollTimer) {
        clearTimeout(scrollTimer);
        scrollTimer = null
    }
    showDrop.value = false
    scrollTimer = setTimeout(() => {
        showDrop.value = true
    }, 700);
}

// 根据子节点展开父节点
const expandNodeByCode = (code: string, callback?: () => void) => {
    setTimeout(() => {
        const treeNode = (treeRef as any).value.getNode(code);
        if (treeNode) {
            expandParents(treeNode);
            (treeRef as any).value.expandNode(treeNode);
        }
        currentNode.value = code as string
        if (callback) callback()
    }, 1);
}

const expandParents = (node: TreeNode) => {
    // 展开所有祖宗节点
    (treeRef as any).value.expandNode(node);
    if (node.parent) {
        expandParents(node.parent)
    }
}

const onQueryChanged = (query: string) => {
    (treeRef as any).value!.filter(query);
};

// 过滤方法
const filterMethod = (query: string, node: treeNode) => {
    return node.name.indexOf(query) !== -1;
};

// 显示创建用户组弹窗
const createGroup = (data?: Group, node?: TreeNode) => {
    parentGroup.name = `${rootGroupName.value}${data?.fullname ? `/${data.fullname}` : ''}`;
    parentGroup.code = data?.code || '';
    parentGroup.node = node as any;
    emitter.emit("createGroupDialogShow", { data: parentGroup, isUp: false });
}

// 删除用户组
const deleteGroup = async (data: Group) => {
    const treeNodeRef = (treeRef as any).value;
    const cn = treeNodeRef.getCurrentNode();
    const { deleteHandle } = useForm({
        delApi: (id) => delGroup(id),
        val: data,
        name: $t('用户组')
    })
    return new Promise<any>((resolve) => {
        deleteHandle({params: data.id}).then((v: any) => {
            if (cn && cn.code === data.code) {
                const { index, brotherNodes } = getBrotherNodes(treeData.value, data.code as string)
                let previousNode = null;
                if (brotherNodes.length <= 0) {
                    // 如果没有兄弟节点，则跳转到父级节点
                    previousNode = treeNodeRef.getNode(data.code).parent.data
                } else if (brotherNodes[index - 1]) {
                    // 如果有上一个节点，则跳转到上一个节点
                    previousNode = brotherNodes[index - 1]
                } else if (brotherNodes[index]) {
                    // 如果有下一个节点，则跳转到下一个节点
                    previousNode = brotherNodes[index]
                }
                if (previousNode) {
                    currentNode.value = previousNode.code;
                    const { name } = router.currentRoute.value;
                    changeItem(name === 'customerUser' ? { id: previousNode.id } : previousNode)
                }
            }
            getTreeData();
            resolve(v);
        });
    });
}

// 获取兄弟节点，返回数组和之前的下标
const getBrotherNodes = (list: Recordable[], code: string): Recordable => {
    for (let i = 0; i < list.length; i++) {
        if (list[i].code === code) {
            return {
                index: i,
                brotherNodes: list.filter(v => v.code !== code)
            }
        }
        if (list[i].children?.length > 0) {
            const { brotherNodes, index } = getBrotherNodes(list[i].children, code)
            if (brotherNodes && brotherNodes.length > 0) return {
                index,
                brotherNodes: brotherNodes.filter((v: Group) => v.code !== code)
            }
        }
    }
    return {
        index: 0,
        brotherNodes: []
    };
}

// 显示用户组详情
const showGroupDetail = (val: Group) => {
    const data = formatObjectData(val, showDetailKeys);
    const groupInfo = data.filter(v => v.key !== 'children' && !(v.key === 'parentCode' && !v.value))
    emitter.emit("detailViewShow2", { data: groupInfo, title: $t('用户组详情'), width: '30%' });
}

// 获取用户组列表
const getTreeData = async (data?: Group) => {
    try {
        const { data } = await getGroupTree()
        treeData.value = [{
            ...TREE_ROOT,
            children: data,
            name: rootGroupName.value
        }];
    } catch (e) {
        console.log(e);
    }
    if (data) {
        const callback = () => {
            changeItem(data)
        }
        expandNodeByCode(data.code as string, callback)
    }
}

const changeItem = (data: TreeNodeData) => {
    emit("changeGroup", data);
}

defineExpose({
    getTreeData,
    deleteGroup,
});

</script>

<style lang="scss" scoped>
.tree {
    width: 240px;
    min-width: 240px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    .tree-search {
        width: 160px;
        margin-top: 12px;
        margin-bottom: 12px;
    }

    .el-button {
        margin-top: 12px;
        width: 160px;
        // margin-bottom: 12px;
    }

    .el-tree {
        width: 100%;
        // height: calc(100% - 32px);
        // overflow-x: hidden;
    }

    .tree-node {
        width: calc(100% - 24px - 16px);
        max-width: calc(100% - 24px - 16px);
        justify-content: space-between;
        display: flex;
        align-items: center;

        &:hover {
            .actionBtn {
                // display: block;
                opacity: 1;
            }
        }
    }

    .actionBtn {
        // display: none;
        opacity: 0;
        transform: rotate(90deg);
        // margin-right: 12px;
    }
}
</style>