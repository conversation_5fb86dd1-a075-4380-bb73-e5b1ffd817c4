<template>
    <el-dialog destroy-on-close :title="$t('添加用户')" :width="805" v-model="dialogVisible" @close="reset">
        <el-transfer v-model="selectedUsers" filterable :filter-method="filterMethod" :filter-placeholder="$t('请输入')" :data="data"
            :titles="[$t('所有用户'), $t('选中用户')]">
            <template #default="{ option }">
                <TipText width="100%" :content="option.label">
                    <template v-slot:content>
                        <span>{{ option.label || "-" }}</span>
                    </template>
                </TipText>
            </template>
        </el-transfer>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">{{ $t('取消') }}</el-button>
                <el-button type="primary" @click="addUserToGroup" :disabled="selectedUsers.length <= 0">
                    {{ $t('确定') }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getUserList } from "@/services/user"
import type { User } from '@/model/user';
import { $t } from '@/locales';

export interface addUserToGroupType {
    dialogVisible: boolean
    show: (v?: any) => void
}
interface Props {
    isShow?: boolean,
}
const dialogVisible = ref(false)
const loading = ref(false);
const userList = ref([]);
const data = ref<Option[]>([])
const selectedUsers = ref([])
const disabledIds = ref<string[]>([])

const show = async (ids?: string[]) => {
    if (ids) {
        disabledIds.value = ids;
    }
    await getList();
    dialogVisible.value = true
}

defineExpose({
    dialogVisible,
    show
})

const emit = defineEmits<{
    (e: "addUserToGroup", data: string[]): void
}>()

const getList = async (page = 1) => {
    loading.value = true;
    try {
        while (true) {
            const { data } = await getUserList({ page, limit: 100 })
            userList.value = userList.value.concat(data.list)
            if (data.list.length === data.total) break;
            getList(++page)
        }
    } catch (e) {
        userList.value = [];
    } finally {
        loading.value = false;
    }
    data.value = generateData();
}


const props = withDefaults(defineProps<Props>(), {
    isShow: false,
})

interface Option {
    key: string
    label: string | undefined
    initial: string
    disabled?: boolean
}

const generateData = () => {
    const data: Option[] = []
    const initials = ['CA', 'IL', 'MD', 'TX', 'FL', 'CO', 'CT']
    userList.value.forEach((user: User, index: number) => {
        data.push({
            label: `${user.fullname}(${user.user_name || user.phone || user.email})`,
            key: user.id,
            initial: initials[index],
            disabled: disabledIds.value.includes(user.id) || parseInt(user.id) === 1
        })
    })
    return data
}

const filterMethod = (query: string, item: any) => {
    return item.label.toLowerCase().includes(query.toLowerCase())
}

// 添加用户到用户组
const addUserToGroup = async () => {
    emit('addUserToGroup', selectedUsers.value)
}

const reset = () => {
    userList.value = [];
    selectedUsers.value = []
    data.value = [];
}

</script>

<style lang="scss" scoped>
.el-transfer {
    // height: 100%;
}
</style>