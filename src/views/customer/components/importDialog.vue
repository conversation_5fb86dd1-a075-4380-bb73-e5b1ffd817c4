<template>
    <el-dialog v-model="isShow" :title="title" @close="reset" :width="type === 'user' ? '1100px' : '900px'"
        :close-on-click-modal="false">
        <div>
            <p class="mb-4">{{ $t('第一步，下载模板文件(UTF-8)') }}</p>
            <el-button v-noBlur class="mb-4" @click="exportTemplate">{{ $t('xls/xlsx文件') }}</el-button>
            <p class="mb-4">{{ $t('第二步，编辑模板文件') }}</p>
            <img class="template-img" :src="imgSrc" alt="" :class="`${type}_img`" />
            <p class="mb-4 mt-4">{{ $t('第三部步，上传编辑好的文件') }}</p>
            <DropZone @importFile="uploadFileHandle" v-if="isShow" class="importDrop">
                <p class="mt-8 cursor-pointer" @click="handleUpClick"> {{ $t('点击上传 或 拖拽到这里') }} </p>
                <input type="file" v-show="false" ref="input" :multiple="false" :accept="accept" @change="handleFiles" />
            </DropZone>
        </div>
        <div v-if="tableData && tableData.length > 0">
            <el-table :data="tableData" border highlight-current-row style="width: 100%;margin-top:20px;" max-height="200px"
                :row-class-name="getRowClass">
                <el-table-column v-for="item of tableHeader" :key="item" :prop="item" :label="item">
                    <template #default="scope">
                        <el-input v-show="scope.row.isEdit" v-model="scope.row[item]" clearable>

                        </el-input>
                        <el-tooltip :content="scope.row.errorMessage" placement="top" :disabled="!scope.row.errorMessage"
                            effect="light">
                            <p v-show="!scope.row.isEdit">{{ scope.row[item] }}</p>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('操作')" fixed="right" width="140">
                    <template #default="scope">
                        <div v-if="scope.row.result === 'success'" class="flex items-center">
                            <el-icon color="green" :size="16" class="block">
                                <component :is="$ElIcon['CircleCheckFilled']" />
                            </el-icon>
                            <p class="ml-4">{{ $t('导入成功') }}</p>
                        </div>
                        <div v-else>
                            <el-button size="small" @click="handleDelete(scope.$index, scope.row)"
                                type="danger">{{ $t('删除') }}</el-button>
                            <el-button size="small" type="primary" @click="handleEdit(scope.$index, scope.row)">{{
                                scope.row.isEdit ? $t("保存") : $t("编辑") }}</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-progress :percentage="successCount / tableData.length * 100" status="success" class="mt-4"
                v-if="showProgress" :stroke-width="24">
                <span>{{ `${successCount} / ${tableData.length}` }}</span>
            </el-progress>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button v-noBlur type="primary" @click="isShow = false">
                    {{ $t('取消') }}
                </el-button>
                <el-button v-noBlur type="primary" @click="importFn"
                    :disabled="!tableData || tableData.length <= 0 || errorCount > 0 || !allNotEdit || successCount === tableData.length">
                    {{ $t('批量导入') }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onBeforeMount, computed, inject } from 'vue'
import { downloadByData, encryptByMd5, isEqualByArrays } from "@/utils/index"
import { ContentType } from "@/utils/const"
import { emitter } from "@/utils/mitt";
import { createGroupApi, downloadImportGroupTemplate } from '@/services/group'
import { createUserApi, downloadImportUserTemplate } from '@/services/user'
import { utils, read } from "xlsx";
import { getErrorMessage } from '@/utils/http-message';
import { $t } from '@/locales';

// 目前后端返回的是中文,这边先强行hardcode
const user_table_keys = {
    "*名字": "first_name",
    "*姓氏": "last_name",
    "*用户名": "user_name",
    "手机号": "phone",
    "邮箱": "email",
    "*登录密码": "password",
    "*状态[正常、禁用]": "disable",
    "所属用户组编码(多个逗号隔开)": "codes",
} as Recordable

const group_table_keys = {
    "*用户组编号": "code",
    "*用户组名称": "name",
    "*用户组描述": "description",
    "*上一级用户组编号": "parentCode",
    "上一级用户组名称": "",
}

const isShow = ref(false)
const tableData = ref([]);
const tableHeader = ref([]);
const rangeValue = ref(1); // 解析excel表格range值
const importFile = ref<any>(null);
const type = ref('group');
const t = inject<any>('t');
const input = ref();
const accept = ref('.xls,.xlsx');
const showProgress = ref(false);

const title = computed(() => `批量导入${t(`common.${type.value}`)}`)
const imgSrc = computed(() => `${import.meta.env.VITE_PUBLIC_PATH}/assets/imgs/excel_import_${type.value}_template.png`)
const errorCount = computed(() => {
    return tableData.value.filter((v: Recordable) => v.errorMessage).length;
})
const successCount = computed(() => {
    return tableData.value.filter((v: Recordable) => v.result).length;
})
const allNotEdit = computed(() => {
    return tableData.value.every((v: Recordable) => !v.isEdit)
})

const table_keys = computed(() => {
    return type.value === 'user' ? user_table_keys : group_table_keys
})

onBeforeMount(() => {
    // 显示弹窗
    emitter.on("importDialogShow", (params: { type: string, }) => {
        type.value = params.type;
        isShow.value = true;
    });
});

const emit = defineEmits<{
    (e: "importFileHandle", data: File): void
}>()

// 导出模板
const exportTemplate = async () => {
    // TODO
    const fn = type.value === 'user' ? downloadImportUserTemplate : downloadImportGroupTemplate
    try {
        const data = await fn();
        downloadByData(data, `${t(`common.${type.value}`)}${$t('模板')}`, ContentType.SHEET)
    } catch (e) {
        console.log(e)
    }
}

// 校验是否是excel
const isExcel = (file: File) => /\.(xlsx|xls)$/.test(file.name)

const handleUpClick = () => {
    if (input.value) input.value.click();
}

// input事件
const handleFiles = (e: any) => {
    const files = e.target.files;
    uploadFileHandle(files);
}

// 导入模板
const uploadFileHandle = (files: File[]) => {
    if (files.length !== 1) {
        ElMessage.error($t('只支持上传一个文件'))
        return
    }
    const rawFile = files[0]
    if (!isExcel(rawFile)) {
        ElMessage.error($t('只支持上传 .xlsx, .xls, 后缀的文件'))
        return false
    }
    importFile.value = rawFile;
    readerData(rawFile);
}

const validateHeader = (header: string[]) => {
    const result = isEqualByArrays(header, type.value === 'user' ? Object.keys(user_table_keys) : Object.keys(group_table_keys))
    if (!result) {
        throw new Error($t('校验失败'))
    }
}

const generateData = (header: any, results: any) => {
    // TODO 处理表格
    validateHeader(header)
    tableData.value = results;
    tableHeader.value = header;
}

const getHeaderRow = (sheet: { [key: string]: any }) => {
    const headers: string[] = []
    const range = utils.decode_range(sheet['!ref'])
    const R = rangeValue.value
    for (let C = range.s.c; C <= range.e.c; ++C) {
        const cell = sheet[utils.encode_cell({ c: C, r: R })]
        let hdr = ''
        if (cell && cell.t) hdr = utils.format_cell(cell)
        if (hdr) {
            headers.push(hdr)
        }
    }
    return headers
}

const readerData = (rawFile: File) => {
    const reader = new FileReader()
    reader.onload = e => {
        try {
            const data = (e.target as FileReader).result
            const workbook = read(data, { type: 'array' })
            const firstSheetName = workbook.SheetNames[0]
            const worksheet = workbook.Sheets[firstSheetName]
            const header = getHeaderRow(worksheet)
            const results = utils.sheet_to_json(worksheet, { range: rangeValue.value })
            generateData(header, results)
        } catch (e) {
            ElMessage.error($t('文件内容格式不正确，请参考模板'))
        }
    }
    reader.readAsArrayBuffer(rawFile)
}

// 后续重构
const importFn = async () => {
    showProgress.value = true
    for (let i = 0; i < tableData.value.length; i++) {
        const item = tableData.value[i] as any;
        const params: Recordable = {}
        Object.keys(table_keys.value).forEach(v => {
            let data;
            if (table_keys.value[v] === 'disable') {
                data = item[v] === $t('禁用')
            } else if (table_keys.value[v] === 'codes') {
                if (item[v]) {
                    if (String(item[v]).includes(',')) {
                        data = String(item[v]).split(",")
                    } else {
                        data = [String(item[v])]
                    }
                } else {
                    data = []
                }
            } else if (table_keys.value[v] === 'password') {
                data = String(item[v])
            } else if (table_keys.value[v] === 'phone') {
                data = item[v] ? String(item[v]) : ''
            } else if (table_keys.value[v] === 'user_name') {
                data = item[v] ? String(item[v]) : ''
            } else if (v === $t('上一级用户组名称')) {
                return;
            } else data = item[v]
            params[table_keys.value[v]] = data;
        })
        try {
            if (!item.result) {
                const fn = type.value === 'user' ? createUserApi : createGroupApi
                await fn(params as any)
                item.result = 'success';
                emit('importFileHandle', importFile.value)
            }
        } catch (e: any) {
            const errorMessage = e.msg || getErrorMessage(e);
            item.errorMessage = errorMessage
            console.log(e);
        }
    }
}

const getRowClass = (data: Recordable) => {
    const { row } = data
    if (row.errorMessage) {
        return 'row_error';
    }
    else return ''
}

const handleEdit = (index: number, row: any) => {
    row.errorMessage = ''
    row.isEdit = !row.isEdit
}

const handleDelete = (index: number, row: any) => {
    tableData.value.splice(index, 1);
}

const reset = () => {
    tableData.value = [];
    tableHeader.value = [];
    importFile.value = null;
    showProgress.value = false
}

defineExpose({
    isShow,
})

</script>
<style scoped lang="scss">
.template-img {
    width: 100%;
    position: relative;
}

.importDrop {
    background-color: #f9f9f9;
    border: 1px solid #e4ebf2;
    border-radius: 5px;
    color: #999;
    height: 80px;
    width: 100%;
    text-align: center;
}

.row_error {
    color: red !important;

    p {
        color: red !important;
    }
}
</style>