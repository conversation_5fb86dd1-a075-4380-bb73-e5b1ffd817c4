<template>
  <div class="user_group_mng">
    <GroupTree @changeGroup="changeGroup" ref="tree"></GroupTree>
    <div class="group_content" :key="currentGroup.code">
      <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading" :getList="getList"
        :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList" @export="exportUserHandle"
        @import="importUserHandle" :enableImport="true" :enableExport="true">
        <template #tableHeader>
          <el-button v-noBlur type="primary" @click="addUser" :title="$t('创建用户')">
            <span class="btn-text">{{ $t('创建用户') }}</span>
          </el-button>
          <el-button v-noBlur type="primary" @click="showAddUserToGroupDialog" v-if="!isRoot" :title="$t('添加用户到用户组')">
            <span class="btn-text">{{ $t('添加用户到用户组') }}</span>
          </el-button>
        </template>
        <template #action="{ row }">
          <div class="layout-action">
            <span class="action-text" @click="showDetail(row)" :title="$t('详情')">{{ $t('详情') }}</span>
            <span class="action-text" @click="updateUser(row)" :title="$t('编辑')">{{ $t('编辑') }}</span>
            <span class="action-text" @click="deleteUser(row)" :title="$t('删除')">{{ $t('删除') }}</span>
            <span class="action-text" @click="reset(row)" :title="$t('重置密码')">{{ $t('重置密码') }}</span>
          </div>
        </template>
        <template #status="{ row }">
          <el-switch v-model="row.disable" :active-value="false" :inactive-value="true"
            style="--el-switch-on-color: #13ce66" :before-change="() => beforeSwitchChange(row)" />
        </template>
      </GList>
    </div>
    <AddUserToGroupDialog ref="addUserToGroupRef" v-if="!isRoot" @addUserToGroup="addUserToGroup">
    </AddUserToGroupDialog>
    <ImportDialog ref="importRef" @importFileHandle="importFileHandle"></ImportDialog>
    <CreateUser ref="drawerRef" @onCreateUserSubmit="handleCreateUser"></CreateUser>
    <CreateGroup ref="groupRef" @onCreateGroupSubmit="handleCreateGroup"></CreateGroup>
  </div>
</template>
<script setup lang="tsx">
import { computed, reactive, ref } from 'vue'
import GList from '@/components/GList.vue'
import type { TableColumn, TablePagination, TableDataParams, Explose } from '@/model/table'
import { getUserList, createUserApi, delUser, editUser, resetUserPwd, disableUser, enableUser, exportUser, getUserById } from "@/services/user"
import { createGroupApi, getUserListByGroupId, addUserToGroupBatch } from "@/services/group"
import type { User, CreateUserParams } from '@/model/user'
import CreateUser from "../components/createUser.vue"
import AddUserToGroupDialog from "../components/AddUserToGroupDialog.vue"
import GroupTree from '../components/groupTree.vue'
import CreateGroup from '../components/createGroup.vue';
import type { addUserToGroupType } from '../components/AddUserToGroupDialog.vue'
import ImportDialog from '../components/importDialog.vue'
import { useForm } from "@/hooks/useForm"
import type { CreateGroupParams, Group } from '@/model/group'
import { ContentType, getTreeRoot } from "@/utils/const"
import { emitter } from "@/utils/mitt";
import { downloadByData, formatObjectData, encryptByMd5 } from '@/utils'
import { useRouter } from 'vue-router'
import { $t } from '@/locales'

const showDetailKeys = [
    "id",
    "user_name",
    "first_name",
    "last_name",
    "fullname",
    "email",
    "phone",
    "root",
    "disable",
    "login_time",
    "login_ip",
    "ctime",
    "utime",
    "groups"
]
const router = useRouter()
const loading = ref(false)
const refresh = ref(Date.now())
const drawerRef = ref<Explose | null>(null)
const groupRef = ref<Explose | null>(null)
const importRef = ref<Explose | null>(null)
const currentPageUsers = ref([]);
const addUserToGroupRef = ref<addUserToGroupType | null>(null)
const tree = ref<{ [a: string]: (v?: any) => void } | null>(null)
const currentGroup = reactive({
  id: (router.currentRoute.value.query.id as string) || '',
  code: (router.currentRoute.value.query.code as string) || getTreeRoot().code
})

const isRoot = computed(() => {
  return currentGroup.code === getTreeRoot().code
})

const USER_STATUS = [$t("禁用"), $t("启用")]

// 表格列配置
const columns = [
  // {
  //   type: "selection",
  //   width: "55"
  // },
  {
    label: 'ID',
    property: 'id',
    showOverflowTooltip: true,
  },
  {
    label: $t('用户名'),
    property: 'user_name',
    showOverflowTooltip: true,
  },
  {
    label: $t('姓名'),
    showOverflowTooltip: true,
    property: 'fullname',
  },
  {
    label: $t('手机号'),
    property: 'phone',
    width: "120",
    format: (row: any, column: any, cellValue: any, index: any) => { return cellValue || "-" }
  },
  {
    label: $t('邮箱'),
    property: 'email',
    format: (row: any, column: any, cellValue: any, index: any) => { return cellValue || "-" }
  },
  {
    label: $t('状态'),
    slotName: 'status',
    property: 'status',
    width: "65",
  },
  {
    label: $t('创建时间'),
    property: 'ctime',
    width: "160",
  },
  {
    label: $t('最后登录时间'),
    property: 'login_time',
    width: "160",
  },
  {
    label: $t('操作'),
    slotName: 'action',
    fixed: 'right',
    width: "220",
  }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
  key: 'batchDelete',
  label: $t('批量删除'),
  click: (items: User[]) => {
    emitter.emit("batchResultDialogShow", { data: items, title: $t('批量删除用户'), width: '30%' });
  }
}]

// 搜索栏配置
const searchList = [
  {
    key: 'fullname',
    type: 'input',
    name: $t('姓名'),
    placeholder: $t('姓名')
  },
  {
    key: 'user_name',
    type: 'input',
    name: $t('用户名'),
    placeholder: $t('用户名')
  },
  {
    key: 'email',
    type: 'input',
    name: $t('邮箱'),
    placeholder: $t('邮箱')
  },
  {
    key: 'phone',
    type: 'input',
    name: $t('手机号'),
    placeholder: $t('手机号')
  },
  // {
  //   key: 'user',
  //   type: 'input',
  //   placeholder: '请输入',
  //   hasPrepend: true,
  //   preType: 'type',
  //   PreOptions: [{
  //     label: '姓名',
  //     value: 'fullname',
  //   }, {
  //     label: '用户名',
  //     value: 'user_name',
  //   }, {
  //     label: '邮箱',
  //     value: 'email',
  //   }, {
  //     label: '手机号',
  //     value: 'phone',
  //   }]
  // }
]

const getList = async (params: TableDataParams) => {
  loading.value = true;
  const { page, limit, search } = params;
  let req = {
    page,
    limit,
  };
  if (search) {
    req = {
      ...req,
      ...search,
    };
  }
  try {
    const { data } = isRoot.value ? await getUserList(req) : await getUserListByGroupId(currentGroup.id, { ...req })
    currentPageUsers.value = data.list || [];
    return {
      list: data.list || [],
      total: data.total,
    }
  } catch (e) {
    return {
      list: [],
      total: 0,
    }
  } finally {
    loading.value = false;
  }
}

// 添加用户到用户组
const showAddUserToGroupDialog = () => {
  if (addUserToGroupRef.value) {
    const ids = currentPageUsers.value.map((v: User) => v.id);
    addUserToGroupRef.value.show(ids)
  }
}

// 显示创建用户弹窗
const addUser = () => {
  if (drawerRef.value) {
    drawerRef.value.show(null, isRoot.value ? [] : [currentGroup.code]);
  }
}

// 新建/编辑用户
const handleCreateUser = async (data: CreateUserParams | User, isUpdate: boolean) => {
  try {
    isUpdate ? await editUser((data as User).id, data as User) : await createUserApi(data as CreateUserParams);
    if (drawerRef.value) {
      drawerRef.value.isShow = false
    }
    refresh.value = Date.now();
    ElMessage({
      type: 'success',
      message: $t('{action}用户成功', { action: isUpdate ? $t('修改用户') : $t('创建用户') }),
    })
  } catch (e) {
    console.log(e);
  }
}

const handleCreateGroup = async (params: CreateGroupParams | Group, isUpdate: boolean) => {
  try {
    const { data } = await createGroupApi(params as CreateGroupParams);
    if (groupRef.value) {
      groupRef.value.isShow = false
    }
    if (data) {
      currentGroup.id = data.id;
      currentGroup.code = data.code;
    }
    ElMessage({
      type: 'success',
      message: $t('{action}用户组成功', { action: isUpdate ? $t('修改用户组') : $t('创建用户组') }),
    })
    if (tree.value) tree.value.getTreeData({ ...params, id: data.id })
  } catch (e) {
    console.log(e);
  }
}

// 显示详情弹窗
const showDetail = async (val: User) => {
  let data = val;
  try {
    const res = await getUserById(val.id);
    data = res.data;
  } catch (e) {
    console.log(e);
  }
  const formatData = {
    'disable': (val: boolean) => {
      return val ? $t('是') : $t('否')
    },
    'root': (val: number) => {
      return val === 1 ? $t('是') : $t('否')
    },
    'groups': (val: Recordable[]) => {
      return val.length > 0 ? val.map(v => v.fullname).join(",") : ''
    },
  }
  const userInfos = formatObjectData(data, showDetailKeys, formatData);
  // data.map(v => {
  //   if (v.key === 'disable') {
  //     v.isHtml = true;
  //     v.value = <el-switch style="--el-switch-on-color: #13ce66" active-value={true} inactive-value={false} model-value={v.value}
  //     ></el-switch>
  //   }
  //   return v;
  // })
  emitter.emit("detailViewShow2", { data: userInfos, title: $t('用户详情'), width: '30%' });
}

const beforeSwitchChange = async (val: User) => {
  const title = val.disable ? $t('启用') : $t('禁用');
  const fn = val.disable ? enableUser : disableUser;
  return new Promise<boolean>((resolve) => {
    ElMessageBox.alert(
      $t('你确定要{action}{name}用户吗', { action: title, name: val.user_name || $t("这个") }),
      title,
      {
        confirmButtonText: $t("确认"),
        cancelButtonText: $t("取消"),
        type: "warning",
      }
    )
      .then(async () => {
        try {
          await fn(val.id);
          ElMessage({
            type: "success",
            message: $t('{action}成功', { action: title }),
          });
          resolve(true);
        } catch (e) {
          console.log(e);
          resolve(false);
        }
      })
      .catch(() => {
        resolve(false);
      });
  });
};

// 添加用户到用户组
const addUserToGroup = async (uids: string[]) => {
  try {
    await addUserToGroupBatch({
      gid: currentGroup.id,
      uids,
    })
    ElMessage({
      type: 'success',
      message: $t('添加成功'),
    })
    if (addUserToGroupRef.value) {
      addUserToGroupRef.value.dialogVisible = false;
    }
    refresh.value = Date.now();
  } catch (e) {
    console.log(e);
  }
}

// 修改用户??什么信息
const updateUser = async (val: User) => {
  if (drawerRef.value) {
    let data = val;
    let codes = [];
    try {
      const res = await getUserById(val.id);
      data = res.data;
      codes = (data.groups || []).map((v) => v.code);
    } catch (e) {
      console.log(e);
    }
    drawerRef.value.show(data, codes);
  }
}

// 删除用户
const deleteUser = (val: User) => {
  const { deleteHandle } = useForm({
    delApi: (id) => delUser(id),
    val: val,
    name: $t('用户')
  })
  deleteHandle({key: 'fullname',params: val.id}).then((v: number) => {
    refresh.value = v;
  });
}

// 重置用户名密码
const reset = (row: User) => {
  ElMessageBox.prompt($t('请输入新密码'), $t('重置密码'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    //校验方法
    inputValidator: (val: string) => {
      if (val === null || val.length < 6 || val.length > 32) {
        return false;
      }
      return true
    },
    inputErrorMessage: $t('密码格式为6到32位任意字符'),
  })
    .then(async ({ value = "" }) => {
      try {
        await resetUserPwd({ id: row.id || '', new_password: encryptByMd5(value) })
        ElMessage({
          type: 'success',
          message: $t('修改密码成功'),
        })
      } catch (e) {
        ElMessage({
          type: 'info',
          message: $t('重置密码失败'),
        })
      }
    })
}

// 树形组件选中的group发生变化时
const changeGroup = (data: any) => {
  if (data) {
    currentGroup.id = data.id
    currentGroup.code = data.code || TREE_ROOT.code
  }
  refresh.value = Date.now();
}

// 导入用户
const importFileHandle = async (file: File) => {
  try {
    // await importUser(file);
    ElMessage({
      type: 'success',
      message: $t('导入成功'),
    })
    refresh.value = Date.now();
    // if (importRef.value) {
    //   importRef.value.isShow = false
    // }
  } catch (e) {
    console.log()
  }
}

// 导出
const exportUserHandle = async (params: any) => {
  const { search } = params;
  let req = {
    code: isRoot.value ? '' : currentGroup.code,
  };
  if (search) {
    req = {
      ...req,
      ...search,
    };
  }
  try {
    const data = await exportUser(req);
    downloadByData(data, `${isRoot.value ? $t('所有') : currentGroup.code}${$t('用户组用户列表')}`, ContentType.SHEET);
    ElMessage({
      type: 'success',
      message: $t('导出成功'),
    })
  } catch (e) {
    console.log()
  }
}

const importUserHandle = () => {
  emitter.emit("importDialogShow", { type: 'user' });
}

const getStatus = (id: number, rev = false) => {
  const status = rev ? [...USER_STATUS] : USER_STATUS;
  return status[id || 0];
}

</script>
<style lang="scss" scoped>
.user_group_mng {
  height: 100%;
  display: flex;

  .group_content {
    flex: 1;
    margin-left: 12px;
    width: calc(100% - 252px);
  }
}
</style>
