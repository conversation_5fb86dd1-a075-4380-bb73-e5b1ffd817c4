/** 用户组管理初步思想,左边树形组管理,右边是每个组内的列表管理页 */
<template>
  <div class="user_group_mng">
    <GroupTree @changeGroup="changeGroup" ref="tree"></GroupTree>
    <div class="group_content" :key="currentGroup.code">
      <GList :getList="getListHandle" :columns="columns" :pagination="pagination" :showPagination="true"
        :loading="loading" :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList"
        :enableImport="true" :enableExport="true" @export="exportGroupHandle" @import="importGroupHandle">
        <template #tableHeader>
          <el-button v-noBlur type="primary" @click="addGroup" :title="$t('添加用户组')">
            <span class="btn-text">{{ $t('添加用户组') }}</span>
          </el-button>
        </template>
        <template #action="{ row }">
          <div class="layout-action">
            <span class="action-text" @click="addSubGroup(row)" :title="$t('添加子组')">{{ $t('添加子组') }}</span>
            <span class="action-text" @click="group_user_mng(row)" :title="$t('成员管理')">{{ $t('成员管理') }}</span>
            <span class="action-text" @click="deleteGroup(row)" :title="$t('删除')">{{ $t('删除') }}</span>
          </div>
        </template>
      </GList>
    </div>
    <CreateGroup ref="groupRef" @onCreateGroupSubmit="handleCreateGroup"></CreateGroup>
    <ImportDialog ref="importRef" @importFileHandle="importFileHandle"></ImportDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import type { TableColumn, TablePagination, TableDataParams, Explose } from '@/model/table'
import { getGroupList, createGroupApi, exportGroup } from "@/services/group"
import GroupTree from '../components/groupTree.vue'
import CreateGroup from '../components/createGroup.vue';
import ImportDialog from '../components/importDialog.vue'
import type { CreateGroupParams, Group } from '@/model/group';
import { useForm } from "@/hooks/useForm"
import { downloadByData } from "@/utils/index"
import { emitter } from "@/utils/mitt";
import { TREE_ROOT, ContentType } from "@/utils/const"
import { useStoreConfig } from '@/stores/config'
import { $t } from '@/locales'

const router = useRouter()
const configStores = useStoreConfig()
const rootGroupName = computed(() => {
    return configStores.site_name
});
const refresh = ref(Date.now())
const groupRef = ref<Explose | null>(null)
const importRef = ref<Explose | null>(null)
const tree = ref<{ [a: string]: (v?: any) => void } | null>(null)
const currentGroup = ref({
  ...TREE_ROOT,
  fullname: '',
  name: rootGroupName.value
})
const { getListHandle, loading } = useForm({
  getListApi: (params: TableDataParams) => getGroupList({ parentCode: currentGroup.value.code === TREE_ROOT.code ? undefined : currentGroup.value.code, ...params }),
  val: null,
  name: ''
})

// 表格列配置
const columns = [
  // {
  //   type: "selection",
  //   width: "55"
  // },
  {
    label: 'ID',
    property: 'id',
    showOverflowTooltip: true,
    width: "160",
  },
  {
    label: $t('编号'),
    property: 'code',
    showOverflowTooltip: true,
  },
  {
    label: $t('名称'),
    property: 'name',
  },
  {
    label: $t('描述'),
    property: 'description',
    format: (row: any, column: any, cellValue: any, index: any) => { return cellValue || "-" }
  },
  {
    label: $t('创建时间'),
    property: 'ctime',
    width: "160",
  },
  {
    label: $t('成员数'),
    property: 'userNum',
    width: "160",
  },
  {
    label: $t('操作'),
    slotName: 'action',
    width: "220",
    fixed: 'right',
  }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
  key: 'batchDelete',
  label: $t('批量删除'),
}]

// 搜索栏配置
const searchList = [{
  key: 'code',
  type: 'input',
  name: $t('用户组编号'),
  placeholder: $t('请输入编号')
}, {
  key: 'name',
  type: 'input',
  name: $t('用户组名称'),
  placeholder: $t('请输入名称')
}]

// 树形组件选中的group发生变化时
const changeGroup = (data: any) => {
  if (data) {
    currentGroup.value = { ...data, id: data.id || "root" };
  }
  refresh.value = Date.now();
}

const handleCreateGroup = async (data: CreateGroupParams | Group, isUpdate: boolean) => {
  try {
    isUpdate ? null : await createGroupApi(data as CreateGroupParams);
    if (groupRef.value) {
      groupRef.value.isShow = false
    }
    refresh.value = Date.now();
    ElMessage({
      type: 'success',
      message: $t('{action}用户组成功', { action: isUpdate ? $t('修改用户组') : $t('创建用户组') }),
    })
    if (tree.value) tree.value.getTreeData(data)
  } catch (e) {
    console.log(e);
  }
}

// 添加用户组
const addGroup = () => {
  const parentGroup = {
    ...currentGroup.value,
    fullname: `${rootGroupName.value}${currentGroup.value?.fullname ? `/${currentGroup.value.fullname}` : ''}`
  }
  emitter.emit("createGroupDialogShow", { data: parentGroup, isUp: false });
}

// 添加子组
const addSubGroup = (val: Group) => {
  const parentGroup = {
    ...val,
    fullname: `${rootGroupName.value}${val?.fullname ? `/${val.fullname}` : ''}`
  }
  emitter.emit("createGroupDialogShow", { data: parentGroup, isUp: false });
}

// 成员管理
const group_user_mng = (val: Group) => {
  router.push({ name: 'customerUser', query: { code: val.code, id: val.id } })
}

// 删除组
const deleteGroup = async (val: Group) => {
  if (tree.value) {
    const value = await tree.value.deleteGroup(val);
    refresh.value = value as any;
  }
}

// 导入用户组
const importFileHandle = async (file: File) => {
  try {
    // await importGroup(file);
    ElMessage({
      type: 'success',
      message: $t('导入成功'),
    })
    refresh.value = Date.now();
    // if (importRef.value) {
    //   importRef.value.isShow = false
    // }
    if (tree.value) tree.value.getTreeData()
  } catch (e) {
    console.log()
  }
}

const exportGroupHandle = async (params: any) => {
  const { search } = params;
  let req = {
    parentCode: currentGroup.value.code === TREE_ROOT.code ? '' : currentGroup.value.code,
  };
  if (search) {
    req = {
      ...req,
      ...search,
    };
  }
  try {
    const data = await exportGroup(req);
    downloadByData(data, `${currentGroup.value.code === TREE_ROOT.code ? $t('所有') : currentGroup.value.code}${$t('用户组')}`, ContentType.SHEET);
    ElMessage({
      type: 'success',
      message: $t('导出成功'),
    })
  } catch (e) {
    console.log()
  }
}

const importGroupHandle = () => {
  emitter.emit("importDialogShow", { type: 'group' });
}

</script>

<style lang="scss" scoped>
.user_group_mng {
  height: 100%;
  display: flex;

  .group_content {
    flex: 1;
    margin-left: 12px;
    width: calc(100% - 252px);
  }
}
</style>