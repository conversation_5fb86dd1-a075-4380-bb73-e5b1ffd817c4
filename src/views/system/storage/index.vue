<template>
  <div class="storage-settings layout-block">
    <el-form
      v-loading="loading"
      ref="ruleFormRef"
      label-position="right"
      :model="ruleForm"
      label-width="150px"
      style="max-width: 555px"
      class="demo-ruleForm"
      :size="formSize"
      status-icon
    >
      <el-form-item :label="$t('存储根路径')" prop="name">
        {{ ruleForm.root }}
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="systemBasic">
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { getSystemInfo } from "@/services/system"
import { $t } from '@/locales'

const loading = ref<boolean>(false)
const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  root: ''
})


async function init(){
  try {
    loading.value = true
    const res = await getSystemInfo()
    if(res.code === 0) {
      ruleForm.root = res.data.storage.root
    }
    loading.value = false
    console.log(res)
  } catch (error) {
    loading.value = false
    console.log(error)    
  }
}
init()
</script>
<style scoped>
.el-descriptions {
  margin-top: 20px;
}
</style>