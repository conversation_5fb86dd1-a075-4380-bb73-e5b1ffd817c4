<template>
  <div class="basic-info layout-block">
    <el-form v-loading="loading" ref="ruleFormRef" :rules="rules" label-position="right" :model="ruleForm"
      label-width="160px" style="max-width: 555px" class="demo-ruleForm" :size="formSize" status-icon>
      <el-form-item :label="$t('编辑类型')" prop="edit_type">
        <el-radio-group v-model="ruleForm.edit_type">
          <el-radio label="single">{{ $t('单人编辑') }}</el-radio>
          <el-radio label="multi">{{ $t('多人编辑') }}</el-radio>
        </el-radio-group>
        <el-alert :title="$t('编辑类型的修改，需要重启服务后生效！')" type="warning" :closable="false" show-icon />
      </el-form-item>
      <template v-if="ruleForm.edit_type === 'single'">
        <el-form-item :label="$t('保存类型')" prop="save_classify">
        <el-radio-group v-model="ruleForm.save_classify">
          <el-radio :label="1">{{ $t('快照') }}</el-radio>
          <el-radio :label="2">{{ $t('增量') }}</el-radio>
        </el-radio-group>
      </el-form-item>
        <el-form-item :label="$t('保存方式')" prop="save_type">
          <el-radio-group v-model="ruleForm.save_type">
            <el-radio :label="0">{{ $t('时间间隔') }}</el-radio>
            <el-radio :label="1">{{ $t('增量个数') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('保存周期')" prop="save_cycle" class="save-cycle">
          <el-slider
            v-model="ruleForm.save_cycle"
            :max="3600"
            show-input>
          </el-slider>
          <el-popover
            placement="bottom-start"
            width="80"
            trigger="hover"
            :content="$t('最大值3600')">
            <template v-slot:reference>
              <span v-if="ruleForm.save_type === 0">{{ $t('(单位：s)') }}</span>
              <span v-if="ruleForm.save_type === 1">{{ $t('(单位：个)') }}</span>
            </template>
          </el-popover>
        </el-form-item>
        <el-form-item :label="$t('编辑页清除数据倒计时')" prop="wait_timeout" class="save-cycle">
          <el-slider
            v-model="ruleForm.wait_timeout"
            :max="3600"
            show-input>
          </el-slider>
          <el-popover
            placement="bottom-start"
            width="80"
            trigger="hover"
            :content="$t('最大值3600')">
            <template v-slot:reference>
              <span>{{ $t('(单位：s)') }}</span>
            </template>
          </el-popover>
        </el-form-item>
      </template>
      <el-form-item>
        <el-button type="primary" @click="onSubmit(ruleFormRef)">{{ $t('提交') }}</el-button>
        <el-button @click="resetForm(ruleFormRef)">{{ $t('重置') }}</el-button>
      </el-form-item>
    </el-form>
    <el-dialog v-model="dialogVisible">
      <img class="w-full" :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="systemBasic">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules, Action} from 'element-plus'
import { getCloudConfig, putCloudConfig } from "@/services/system"
import { useStoreConfig } from "@/stores/config";
import type { PutCloudConfigQuery } from "@/model/system";
import { $t } from '@/locales';
const configStores = useStoreConfig();

const loading = ref<boolean>(false)
const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const ruleForm = reactive({
  edit_type: '',
  save_cycle: 0,
  save_type: 0,
  save_classify: 0,
  wait_timeout: 60
})

const backRuleForm = reactive({
  edit_type: '',
  save_cycle: 0,
  save_type: 0,
  save_classify: 0,
  wait_timeout: 60
})

const rules = reactive<FormRules>({
  edit_type: [
    { required: true, message: $t('请填选择编辑类型'), trigger: 'blur' }
  ],
  save_cycle: [
    {
      required: false,
      message: $t('请填写保存周期'),
      trigger: 'change',
    },
  ],
  save_type: [
    {
      required: false,
      message: $t('请选择保存类型'),
      trigger: 'change',
    },
  ],
  save_classify: [
    {
      required: false,
      message: '',
      trigger: 'change',
    }
  ],
  wait_timeout: [
    {
      required: false,
      message: '',
      trigger: 'change',
    }
  ]
})

const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const obj:PutCloudConfigQuery = {
        edit_type: ruleForm.edit_type,
        save_classify: ruleForm.save_classify,
        wait_timeout: ruleForm.wait_timeout
      }
      const single = {
        save_cycle: ruleForm.save_cycle,
        save_type: ruleForm.save_type
      }

      if(obj.edit_type === 'single') obj.single = single

      if (ruleForm.edit_type !== backRuleForm.edit_type) {
        ElMessageBox.alert($t('编辑类型的修改，需要重启服务后生效！'), $t('Tips:'), {
          confirmButtonText: $t('确认'),
        })
      }

      const res = await putCloudConfig(obj)
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: $t('修改成功'),
        })
        configStores.getConfig();
        init()
      } else {
        ElMessage({
          type: 'error',
          message: $t('修改失败'),
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  ruleForm.edit_type =backRuleForm.edit_type || ''
  ruleForm.save_cycle = backRuleForm.save_cycle || 0
  ruleForm.save_type = backRuleForm.save_type || 0
  ruleForm.save_classify = backRuleForm.save_classify || 0
  ruleForm.wait_timeout = backRuleForm.wait_timeout || 0
  // formEl.resetFields()
}

async function init() {
  try {
    loading.value = true
    const res = await getCloudConfig()
    if (res.code === 0) {
      backRuleForm.edit_type = ruleForm.edit_type = res.data.edit_type
      backRuleForm.save_cycle = ruleForm.save_cycle = res.data.single.save_cycle || 0
      backRuleForm.save_type = ruleForm.save_type = res.data.single.save_type || 0
      backRuleForm.save_classify = ruleForm.save_classify = res.data.save_classify || 0
      backRuleForm.wait_timeout = ruleForm.wait_timeout = res.data.wait_timeout || 0
      
    }
    loading.value = false
    console.log(res)
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
init()


</script>
<style lang="scss" scoped>
.el-descriptions {
  margin-top: 20px;
}

.save-cycle {
  :deep(.el-form-item__content) {
    display: flex;
    justify-content: left;
    align-items: center;

    .el-slider--with-input{
      width: 300px;
    }

    .el-tooltip__trigger {
      margin-left: 10px;
      cursor: pointer;
    }

    .el-input__suffix {
      display: none;
    }
  }

  .unit {
    margin-left: 10px;
  }
}

.save-type {
  :deep(.el-form-item__content) {
    display: flex;
    justify-content: left;
    align-items: center;
  }
}

</style>