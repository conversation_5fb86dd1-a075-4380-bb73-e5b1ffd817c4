<template>
  <div class="install" @click.stop>
    <div class="logo">
      <img :src="logoImgUrl" />
    </div>
    <div class="panel">
      <div class="title">{{ $t('浩辰CAD企业版初始化面板') }}</div>
      <div class="my-[20px]">
        <el-steps :active="active" finish-status="success" align-center>
          <el-step v-for="item in installSteps" :title="item" :key="item" />
        </el-steps>
      </div>
      <el-scrollbar :height="form_max_height" :style="{ height: form_max_height }">
        <el-form label-width="150px">
          <el-card shadow="never" class="mt-10" v-show="active === 0">
            <template #header>
              <div class="card-header">
                <span>{{ $t('初始化类型') }}</span>
              </div>
            </template>
            <el-form-item :label="$t('初始化类型')">
              <el-radio-group v-model="installType" @change="typeChange">
                <el-radio :label="true">{{ $t('系统新装') }}</el-radio>
                <el-radio :label="false">{{ $t('系统恢复') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-card>
        </el-form>

        <el-form
          label-width="150px"
          ref="ruleFormRef"
          :model="ruleForm"
          v-loading="loading"
        >
          <el-card shadow="never" class="mt-10" v-show="active === 1">
            <template #header>
              <div class="card-header">
                <span>{{ $t('站点基本信息') }}</span>
              </div>
            </template>
            <el-form-item :label="$t('系统访问地址')" prop="site_domain" required>
              <el-input v-model="ruleForm.site_domain" clearable />
            </el-form-item>
            <el-form-item :label="$t('服务端HTTP协议')" prop="httpProtocol">
              <el-radio-group v-model="ruleForm.httpProtocol">
                <el-radio label="http" />
                <el-radio label="https" />
              </el-radio-group>
            </el-form-item>
            <el-form-item
              :label="$t('https公钥')"
              prop="httpsKeyFile"
              v-if="ruleForm.httpProtocol === 'https'"
            >
              <el-upload
                class="w-full"
                :on-change="(file: UploadFile, uploadFiles: UploadFiles) => uploadFile(file, uploadFiles, 'httpsKeyFile')"
                :on-remove="(file: UploadFile, uploadFiles: UploadFiles) => removeFile(file, uploadFiles, 'httpsKeyFile')"
                :auto-upload="false"
                :file-list="static_upload.httpsKeyFile"
                :limit="1"
                :list-type="getUploadType('httpsKeyFile')"
              >
                <el-button
                  ><el-icon>
                    <component :is="$ElIcon['Upload']" /> </el-icon
                  >{{ $t('上传文件') }}</el-button
                >
              </el-upload>
            </el-form-item>
            <el-form-item
              :label="$t('https私钥')"
              prop="httpsCertFile"
              v-if="ruleForm.httpProtocol === 'https'"
            >
              <el-upload
                class="w-full"
                :on-change="(file: UploadFile, uploadFiles: UploadFiles) => uploadFile(file, uploadFiles, 'httpsCertFile')"
                :on-remove="(file: UploadFile, uploadFiles: UploadFiles) => removeFile(file, uploadFiles, 'httpsCertFile')"
                :auto-upload="false"
                :limit="1"
                :file-list="static_upload.httpsCertFile"
                :list-type="getUploadType('httpsCertFile')"
              >
                <el-button v-noBlur
                  ><el-icon>
                    <component :is="$ElIcon['Upload']" /> </el-icon
                  >{{ $t('上传文件') }}</el-button
                >
              </el-upload>
            </el-form-item>
            <el-form-item :label="$t('服务端监听端口')" prop="httpPort" required>
              <el-input v-model="ruleForm.httpPort" clearable />
            </el-form-item>
            <el-form-item :label="$t('站点名称')" prop="siteName">
              <el-input v-model="ruleForm.siteName" clearable />
            </el-form-item>
            <el-form-item :label="$t('站点favicon')" prop="siteFavicon">
              <el-upload
                class="w-full"
                :on-change="(file: UploadFile, uploadFiles: UploadFiles) => uploadFile(file, uploadFiles, 'siteFavicon')"
                :on-remove="(file: UploadFile, uploadFiles: UploadFiles) => removeFile(file, uploadFiles, 'siteFavicon')"
                :auto-upload="false"
                :limit="1"
                :file-list="static_upload.siteFavicon"
                :list-type="getUploadType('siteFavicon')"
                :accept="faviconAccept"
              >
                <el-icon>
                  <component :is="$ElIcon['Plus']" />
                </el-icon>
                <template #file="{ file }">
                  <div>
                    <img
                      class="el-upload-list__item-thumbnail"
                      :src="file.url"
                      alt=""
                    />
                    <span class="el-upload-list__item-actions">
                      <span
                        class="el-upload-list__item-preview"
                        @click="handlePictureCardPreview(file)"
                      >
                        <el-icon>
                          <component :is="$ElIcon['ZoomIn']" />
                        </el-icon>
                      </span>
                      <span
                        class="el-upload-list__item-delete"
                        @click="handleRemove(file, 'siteFavicon')"
                      >
                        <el-icon>
                          <component :is="$ElIcon['Delete']" />
                        </el-icon>
                      </span>
                    </span>
                  </div>
                </template>
                <template #tip>
                  <div class="el-upload__tip">
                    {{ uploadFaviconTip }}
                  </div>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item :label="$t('站点logo')" prop="siteLogo">
              <el-upload
                class="w-full"
                :on-change="(file: UploadFile, uploadFiles: UploadFiles) => uploadFile(file, uploadFiles, 'siteLogo')"
                :on-remove="(file: UploadFile, uploadFiles: UploadFiles) => removeFile(file, uploadFiles, 'siteLogo')"
                :auto-upload="false"
                :limit="1"
                :file-list="static_upload.siteLogo"
                :list-type="getUploadType('siteLogo')"
                :accept="logoAccept"
              >
                <el-icon>
                  <Plus />
                </el-icon>
                <template #file="{ file }">
                  <div>
                    <img
                      class="el-upload-list__item-thumbnail"
                      :src="file.url"
                      alt=""
                    />
                    <span class="el-upload-list__item-actions">
                      <span
                        class="el-upload-list__item-preview"
                        @click="handlePictureCardPreview(file)"
                      >
                        <el-icon><zoom-in /></el-icon>
                      </span>
                      <span
                        class="el-upload-list__item-delete"
                        @click="handleRemove(file, 'siteLogo')"
                      >
                        <el-icon>
                          <Delete />
                        </el-icon>
                      </span>
                    </span>
                  </div>
                </template>
                <template #tip>
                  <div class="el-upload__tip">
                    {{ uploadLogoTip }}
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-card>
          <el-card shadow="never" class="mt-10" v-show="active === 2">
            <template #header>
              <div class="card-header">
                <span>{{ $t('数据库信息') }}</span>
              </div>
            </template>
            <el-form-item :label="$t('数据库')" prop="db_type">
              <el-radio-group v-model="ruleForm.db_type" @change="changeDB">
                <el-radio label="mysql" />
                <el-radio label="sqlite3" />
                <el-radio label="postgresql" />
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="ruleForm.db_type !== 'sqlite3'"
              :label="$t('数据库地址')"
              prop="hostname"
            >
              <el-input v-model="ruleForm.hostname" clearable />
            </el-form-item>
            <el-form-item
              v-if="ruleForm.db_type !== 'sqlite3'"
              :label="$t('数据库端口')"
              prop="hostport"
            >
              <el-input v-model="ruleForm.hostport" clearable />
            </el-form-item>
            <el-form-item
              v-if="ruleForm.db_type !== 'sqlite3'"
              :label="$t('数据库名称')"
              prop="database"
            >
              <el-input v-model="ruleForm.database" clearable />
            </el-form-item>
            <el-form-item
              v-if="ruleForm.db_type === 'sqlite3'"
              :label="$t('数据库文件')"
              prop="sqlFilePath"
            >
              <el-input v-model="ruleForm.sqlFilePath" clearable />
            </el-form-item>
            <el-form-item :label="$t('数据表前缀')" prop="prefix" v-show="installType">
              <el-input v-model="ruleForm.prefix" clearable />
            </el-form-item>
            <el-form-item
              v-if="ruleForm.db_type !== 'sqlite3'"
              :label="$t('数据库账号')"
              prop="db_username"
            >
              <el-input v-model="ruleForm.db_username" clearable />
            </el-form-item>
            <el-form-item
              v-if="ruleForm.db_type !== 'sqlite3'"
              :label="$t('数据库密码')"
              prop="db_password"
            >
              <el-input v-model="ruleForm.db_password" clearable />
            </el-form-item>
          </el-card>
          <el-card shadow="never" class="mt-10" v-show="active === 3">
            <template #header>
              <div class="card-header">
                <span>{{ $t('存储信息') }}</span>
              </div>
            </template>
            <el-form-item :label="$t('存储根路径')" prop="rootpath" required>
              <el-input v-model="ruleForm.rootpath" clearable />
            </el-form-item>
          </el-card>
          <el-card shadow="never" class="mt-10" v-show="installType && active === 4">
            <template #header>
              <div class="card-header">
                <span>{{ $t('管理员账户') }}</span>
              </div>
            </template>
            <el-form-item :label="$t('管理员账户')" prop="username" required>
              <el-input v-model="ruleForm.username" clearable />
            </el-form-item>
            <el-form-item :label="$t('管理员密码')" prop="password" required>
              <el-input v-model="ruleForm.password" clearable />
            </el-form-item>
          </el-card>
          <el-card shadow="never" class="mt-10" v-show="!installType && active === 4">
            <template #header>
              <div class="card-header">
                <span>{{ $t('系统恢复') }}</span>
              </div>
            </template>
            <el-form-item :label="$t('备份文件路径')" prop="backup_file_path">
              <el-input v-model="ruleForm.backup_file_path" clearable />
              <span class="tips">{{ $t('文件路径为安装主机上的路径，不是操作主机路径') }}</span>
            </el-form-item>
          </el-card>
        </el-form>
      </el-scrollbar>
      <div class="foot">
        <el-button v-show="active !== 0" @click="prev" size="large">{{ $t('上一步') }}</el-button>
        <el-button v-show="active !== 4" @click="next" size="large">{{ $t('下一步') }}</el-button>
        <el-button v-noBlur type="primary" v-show="active === 4" @click="submitForm(ruleFormRef)" size="large">{{ $t('确定安装') }}</el-button>
      </div>
    </div>
    <el-dialog v-model="dialogVisible">
      <img class="w-full" :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, onUnmounted } from 'vue'
import { uploadStaticFile } from '@/services/file'
import { encryptByMd5, formatSize } from '@/utils/index'
import { $t } from '@/locales'
import { getDefaultInitConfig, installSystem } from '@/services/system'
import type { UploadFile, UploadFiles, FormInstance } from 'element-plus'
import {
  MAX_LOGO_FAVICON_LIMIT,
  faviconAccept,
  logoAccept,
  getUploadFaviconTip,
  getUploadLogoTip,
} from '@/utils/const'
import { sleep } from '@/utils/httpUtil'

type uploadKey = 'httpsKeyFile' | 'httpsCertFile' | 'siteLogo' | 'siteFavicon'
let loadingInstance: any
const form_max_height = ref(
  `${document.body.clientHeight - 65 - 65 - 24 - 24 - 97}px`
)

// 获取国际化的提示文本
const uploadLogoTip = getUploadLogoTip()
const uploadFaviconTip = getUploadFaviconTip()
const ruleFormRef = ref<FormInstance>()
const dialogImageUrl = ref('')
const logoImgUrl = ref(`${import.meta.env.VITE_PUBLIC_PATH}/assets/imgs/logo.png`)
const dialogVisible = ref(false)
const loading = ref(false)
const active = ref(0)
const installType = ref(true)
const create = [$t('初始化类型'), $t('站点基本信息'), $t('数据库信息'), $t('存储信息'), $t('管理员账户')]
const recover = [$t('初始化类型'), $t('站点基本信息'), $t('数据库信息'), $t('存储信息'), $t('系统恢复')]
const createValidateFields = [[], ['site_domain', 'httpPort'], [], ['rootpath'], ['username', 'password']]
const recoverValidateFields = [[], ['site_domain', 'httpPort'], [], ['rootpath'], []]
const installSteps = ref<string[]>(create)

const ruleForm = reactive({
  site_domain: 'http://127.0.0.1:8081',
  httpProtocol: 'http',
  httpsKeyFile: '',
  httpsCertFile: '',
  httpPort: '8081',
  siteName: '',
  siteFavicon: '',
  siteLogo: '',
  rootpath: '/data',
  username: 'admin',
  password: '123456',
  db_type: 'mysql',
  hostname: '',
  hostport: '3306',
  database: '',
  prefix: '',
  db_username: '',
  db_password: '',
  sqlFilePath: '',
  backup_file_path: '',
})

const static_upload = reactive({
  httpsKeyFile: [],
  httpsCertFile: [],
  siteFavicon: [],
  siteLogo: [],
})

onMounted(() => {
  window.addEventListener('resize', onResize)
  getDefaultConfig()
})

onUnmounted(() => {
  window.removeEventListener('resize', onResize)
})

const getDefaultConfig = async () => {
  try {
    const data = await getDefaultInitConfig()
    Object.assign(ruleForm, data)
  } catch (e) {
    console.log(e)
  }
}

const onResize = () => {
  form_max_height.value = `${
    document.body.clientHeight - 65 - 65 - 24 - 24 - 97
  }px`
}

const getUploadType = (key: uploadKey) => {
  if (['siteLogo', 'siteFavicon'].includes(key)) return 'picture-card'
  else return 'text'
}

const handleRemove = (file: UploadFile, key: uploadKey) => {
  // TODO
  if (key) static_upload[key] = []
}

const handlePictureCardPreview = (file: UploadFile) => {
  dialogImageUrl.value = file.url!
  dialogVisible.value = true
}

const uploadFile = async (
  file: UploadFile,
  uploadFiles: UploadFiles,
  key: uploadKey
) => {
  if (['siteLogo', 'siteFavicon'].includes(key)) {
    if (file.size && file.size > MAX_LOGO_FAVICON_LIMIT) {
      ElMessage.error($t('图片大小不能超过{size}', { size: formatSize(MAX_LOGO_FAVICON_LIMIT) + '!' }))
      static_upload[key] = []
      return
    }
  }
  try {
    const { data } = await uploadStaticFile({
      name: file.name,
      file: file.raw ? file.raw : '',
    })
    ruleForm[key] = data.filePath
  } catch (e) {
    console.log(e)
    ruleForm.siteFavicon = ''
    static_upload[key] = []
  }
}

// 重置数据库表单内容
const changeDB = () => {
  ruleForm.database = ''
  ruleForm.db_password = ''
  ruleForm.db_username = ''
  ruleForm.hostname = ''
  ruleForm.hostport = '3306'
  ruleForm.sqlFilePath = ''
  ruleForm.prefix = ''
}

// 删除图片
const removeFile = async (
  file: UploadFile,
  uploadFiles: UploadFiles,
  key: uploadKey
) => {
  ruleForm[key] = ''
}

// 确定安装
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid, fields) => {
    if (valid) {
      // TODO
      try {
        const params = {
          ...ruleForm,
          password: encryptByMd5(ruleForm.password),
          recovery: !installType.value
        }
        loadingInstance = ElLoading.service({
          lock: true,
          text: $t('系统安装中...'),
          // background: 'rgba(0, 0, 0, 0.7)',
          customClass: 'loading-custom-class',
        })
        const { code } = await installSystem(params)
        loadingInstance.close()
        if (code === 0) {
          ElMessageBox.alert(
            $t('安装成功,点击重启系统后生效,预计5秒左右'),
            $t('提示'),
            {
              confirmButtonText: 'OK',
              showClose: false,
            }
          ).then(async () => {
            loading.value = true
            await sleep(5000)
            loading.value = false
            // closeWin()
            const url = `${window.location.origin}/login`
            window.location.replace(url)
          })
        }
      } catch (e) {
        loadingInstance.close()
        console.log(e)
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const closeWin = () => {
  if (
    navigator.userAgent.indexOf('Firefox') != -1 ||
    navigator.userAgent.indexOf('Chrome') != -1
  ) {
    window.location.href = 'about:blank'
    window.close()
  } else {
    window.opener = null
    window.open('', '_self')
    window.close()
  }
}

const typeChange = (value: boolean) => {
  installSteps.value = value ? create : recover
  resetForm()
}

const resetForm = () => {
  if (!ruleFormRef.value) return
  ruleFormRef.value.resetFields()
}

const prev = () => {
  if (active.value > 0) {
    --active.value
  }
}

const next = () => {
  const validateList = installType.value ? createValidateFields : recoverValidateFields
  const fieldsToValidate = validateList[active.value]
  Promise.all(
    fieldsToValidate.map(field => {
      return new Promise((resolve, reject) => {
        if (!ruleFormRef.value) return
        ruleFormRef.value.validateField(field, (errorMessage) => {
          resolve(errorMessage)
        })
      })
    })
  ).then((errorMessage) => {
    const valid = errorMessage.every((errorMessage) => {
      return errorMessage === true;
    });
    if (valid && active.value < 4) {
      ++active.value
    }
  })
}
</script>

<style scoped lang="scss">
.install {
  width: 100%;
  height: 100%;
  padding: 0 25%;
  display: flex;
  flex-direction: column;
  background: url('/assets/imgs/install.png') 0% 0%/100% 100% no-repeat;

  :deep(.el-input) {
    width: 256px !important;
  }
}

.logo {
  border-width: 0px;
  position: absolute;
  left: 70px;
  top: 40px;
  height: 28px;
  display: flex;
}

.panel {
  width: 100%;
  height: 100%;
  background: #f2f2f2;
  padding: 0 24px;
  margin: 24px 0;
  border: none;
  border-radius: 4px;
  -moz-box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.117647058823529);
  -webkit-box-shadow: 0px 2px 12px rgb(0 0 0 / 12%);
  box-shadow: 0px 2px 12px rgb(0 0 0 / 12%);
}

.title {
  font-size: 25px;
  color: #172b4d;
  font-weight: 650;
  text-align: center;
  padding-top: 16px;
  padding-bottom: 16px;
}

.card-header {
  text-align: center;
  font-size: 24px;
  font-weight: 400;
  color: #000;
}

.el-upload__tip {
  color: #989898;
  font-size: 12px;
  line-height: 16px;
  white-space: normal;
}

.foot {
  text-align: center;
  padding-top: 16px;
  padding-bottom: 16px;
}

.tips {
  position: absolute;
  left: 0;
  top: 100%;
  padding-top: 2px;
  color: #989898;
  font-size: 12px;
  line-height: 16px;
  white-space: normal;
}

::v-deep .el-step__head.is-success {
  color: #5E7CE0;
  border-color: #5E7CE0;
}
::v-deep .el-step__title.is-success {
    font-weight: bold;
    color: #5E7CE0;
}
::v-deep .el-step__description.is-success {
    color: #5E7CE0;
}
</style>
