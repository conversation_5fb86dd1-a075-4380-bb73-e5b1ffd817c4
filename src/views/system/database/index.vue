<template>
  <div class="task-settings layout-block">
    <el-form v-loading="loading" ref="ruleFormRef" label-position="right" :model="ruleForm" label-width="150px"
      style="max-width: 555px" class="demo-ruleForm" :size="formSize" status-icon>
      <el-form-item :label="$t('客户端自动连接')" prop="type">
        <el-radio-group v-model="ruleForm.type">
          <el-radio disabled label="mysql" />
          <el-radio disabled label="sqlite3" />
          <el-radio disabled label="postgresql" />
        </el-radio-group>
      </el-form-item>
      <template v-if="ruleForm.type !== 'sqlite3'">
        <el-form-item :label="$t('数据库地址')" prop="host">
          {{ ruleForm.host }}
        </el-form-item>
        <el-form-item :label="$t('数据库端口')" prop="name">
          {{ ruleForm.port }}
        </el-form-item>
        <el-form-item :label="$t('数据库名称')" prop="name">
          {{ ruleForm.database }}
        </el-form-item>
        <el-form-item :label="$t('数据表前缀')" prop="name">
          {{ ruleForm.prefix }}
        </el-form-item>
        <el-form-item :label="$t('数据库账号')" prop="name">
          {{ ruleForm.user }}
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item :label="$t('数据库文件')" prop="db_file">
          {{ ruleForm.db_file }}
        </el-form-item>
        <el-form-item :label="$t('数据表前缀')" prop="prefix">
          {{ ruleForm.prefix }}
        </el-form-item>
      </template>
      <!-- <el-form-item>
        <el-button type="primary" @click="onSubmit">提交</el-button>
        <el-button>重置</el-button>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script setup lang="ts" name="systemBasic">
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { getSystemInfo } from "@/services/system"
import { $t } from '@/locales'

const loading = ref<boolean>(false)

const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  database: '',
  host: '',
  max_idle_conns: 0,
  max_open_conns: 0,
  password: '',
  port: 0,
  prefix: '',
  timeout: '',
  type: '',
  user: '',
  db_file: ''
})

// const onSubmit = async (formEl: FormInstance | undefined) => {
//   if (!formEl) return
//   await formEl.validate((valid, fields) => {
//     if (valid) {
//       console.log('submit!')
//     } else {
//       console.log('error submit!', fields)
//     }
//   })
// }

// const resetForm = (formEl: FormInstance | undefined) => {
//   if (!formEl) return
//   formEl.resetFields()
// }

async function init() {
  try {
    loading.value = true
    const res = await getSystemInfo()
    if (res.code === 0) {
      ruleForm.database = res.data.db.database
      ruleForm.port = res.data.db.port
      ruleForm.host = res.data.db.host
      ruleForm.prefix = res.data.db.prefix
      ruleForm.type = res.data.db.type
      ruleForm.user = res.data.db.user
      ruleForm.password = res.data.db.password
      ruleForm.db_file = res.data.db.db_file
    }
    loading.value = false
    console.log(res)
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
init()

</script>
<style scoped>
.el-descriptions {
  margin-top: 20px;
}
</style>