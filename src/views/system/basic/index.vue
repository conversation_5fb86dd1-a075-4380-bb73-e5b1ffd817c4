<template>
  <div class="basic-info layout-block">
    <el-form v-loading="loading" ref="ruleFormRef" :rules="rules" label-position="right" :model="ruleForm"
      label-width="160px" style="max-width: 600px" class="demo-ruleForm" :size="formSize" status-icon>
      <el-form-item prop="site_name">
        <template #label>
          <span :title="$t('系统名称')" class="form-label-text">{{ $t('系统名称') }}</span>
        </template>
        <el-input v-model="ruleForm.site_name" clearable :placeholder="$t('请输入系统名称')" />
      </el-form-item>
      <el-form-item prop="site_logo">
        <template #label>
          <span :title="$t('站点LOGO')" class="form-label-text">{{ $t('站点LOGO') }}</span>
        </template>
        <el-upload class="w-full"
          :on-change="(file: UploadFile, uploadFiles: UploadFiles) => uploadFile(file, uploadFiles, 'site_logo')"
          :auto-upload="false" :show-file-list="false" :accept="logoAccept">
          <img v-if="static_upload.site_logo" :src="static_upload.site_logo" class="avatar-logo" />
          <el-icon v-else class="avatar-uploader-icon">
            <component :is="$ElIcon['Plus']" />
          </el-icon>
          <template #tip>
            <div class="el-upload__tip">
              {{ uploadLogoTip }}
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item prop="site_favicon">
        <template #label>
          <span :title="$t('站点favicon')" class="form-label-text">{{ $t('站点favicon') }}</span>
        </template>
        <el-upload class="avatar-uploader" :show-file-list="false"
          :on-change="(file: UploadFile, uploadFiles: UploadFiles) => uploadFile(file, uploadFiles, 'site_favicon')"
          :accept="faviconAccept" :auto-upload="false">
          <img v-if="static_upload.site_favicon" :src="static_upload.site_favicon" class="avatar-favicon" />
          <el-icon v-else class="avatar-uploader-icon">
            <component :is="$ElIcon['Plus']" />
          </el-icon>
          <template #tip>
            <div class="el-upload__tip">
              {{ uploadFaviconTip }}
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item prop="site_domain">
        <template #label>
          <span :title="$t('系统访问地址')" class="form-label-text">{{ $t('系统访问地址') }}</span>
        </template>
        <el-input v-model="ruleForm.site_domain" clearable :placeholder="$t('请输入系统访问地址')" />
      </el-form-item>
      <el-form-item prop="site_beian">
        <template #label>
          <span :title="$t('备案信息')" class="form-label-text">{{ $t('备案信息') }}</span>
        </template>
        <el-input v-model="ruleForm.site_beian" clearable :placeholder="$t('请输入备案信息')" />
      </el-form-item>
      <el-form-item prop="site_copyright">
        <template #label>
          <span :title="$t('版权信息')" class="form-label-text">{{ $t('版权信息') }}</span>
        </template>
        <el-input v-model="ruleForm.site_copyright" clearable :placeholder="$t('请输入版权信息')" />
      </el-form-item>
      <el-form-item prop="name">
        <template #label>
          <span :title="$t('系统版本号')" class="form-label-text">{{ $t('系统版本号') }}</span>
        </template>
        <span>{{ ruleForm.site_version }}</span>
      </el-form-item>
      <el-form-item>
        <div class="button-group">
          <el-button type="primary" @click="onSubmit(ruleFormRef)" :title="$t('提交')">
            <span class="btn-text">{{ $t('提交') }}</span>
          </el-button>
          <el-button @click="resetForm(ruleFormRef)" :title="$t('重置')">
            <span class="btn-text">{{ $t('重置') }}</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <el-dialog v-model="dialogVisible">
      <img class="w-full" :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="systemBasic">
import { ref, reactive } from 'vue'
import type { UploadFile, UploadFiles, FormInstance, FormRules, UploadUserFile, UploadProps } from 'element-plus'
import { getSystemInfo, putSystemInfo } from "@/services/system"
import { useStoreConfig } from "@/stores/config";
import { MAX_LOGO_FAVICON_LIMIT, logoAccept, faviconAccept, getUploadLogoTip, getUploadFaviconTip } from '@/utils/const';
import { formatSize } from '@/utils';
import { uploadStaticFile } from '@/services/file';
import { $t } from '@/locales';

type uploadKey = 'site_logo' | 'site_favicon';

const configStores = useStoreConfig();

// 获取国际化的提示文本
const uploadLogoTip = getUploadLogoTip()
const uploadFaviconTip = getUploadFaviconTip()

const loading = ref<boolean>(false)
const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const ruleForm = reactive({
  site_beian: '',
  site_copyright: '',
  site_domain: '',
  site_name: '',
  site_version: '',
  site_favicon: '',
  site_logo: '',
})

const rules = reactive<FormRules>({
  site_beian: [
    { required: true, message: $t('请填写备案信息'), trigger: 'blur' }
  ],
  site_copyright: [
    {
      required: true,
      message: $t('请填写版权信息'),
      trigger: 'change',
    },
  ],
  site_domain: [
    {
      required: true,
      message: $t('请填写系统访问地址'),
      trigger: 'change',
    },
  ],
  site_name: [
    {
      required: true,
      message: $t('请填写系统名称'),
      trigger: 'change',
    },
  ]
})

const static_upload = reactive({
  site_logo: '',
  site_favicon: ''
})

const uploadFile = async (file: UploadFile, uploadFiles: UploadFiles, key: uploadKey) => {
  console.log("file", file)
  console.log("key", key)
  if (["site_logo", "site_favicon"].includes(key)) {
    if (file.size && file.size > MAX_LOGO_FAVICON_LIMIT) {
      ElMessage.error($t('图片大小不能超过 {size}!', { size: formatSize(MAX_LOGO_FAVICON_LIMIT) }))
      static_upload[key] = '';
      return
    }
  }
  try {
    const { data } = await uploadStaticFile({
      name: file.name,
      file: file.raw ? file.raw : ''
    })
    static_upload[key] = data.url
    ruleForm[key] = data.filePath;
  } catch (e) {
    console.log(e);
    ruleForm.site_favicon = '';
    static_upload[key] = '';
  }
}

const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const obj = {
        site_domain: ruleForm.site_domain,
        site_copyright: ruleForm.site_copyright,
        site_beian: ruleForm.site_beian,
        site_name: ruleForm.site_name,
        site_logo: ruleForm.site_logo,
        site_favicon: ruleForm.site_favicon
      }
      const res = await putSystemInfo(obj)
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: $t('修改成功'),
        })
        configStores.getConfig();
        init()
      } else {
        ElMessage({
          type: 'error',
          message: $t('修改失败'),
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  static_upload.site_favicon = `${configStores.site_host}/${configStores.site_favicon}`
  static_upload.site_logo = `${configStores.site_host}/${configStores.site_logo}`
  formEl.resetFields()
}

async function init() {
  try {
    loading.value = true
    const res = await getSystemInfo()
    if (res.code === 0) {
      ruleForm.site_beian = res.data.info.site_beian
      ruleForm.site_copyright = res.data.info.site_copyright
      ruleForm.site_domain = res.data.info.site_domain
      ruleForm.site_favicon = res.data.info.site_favicon
      ruleForm.site_logo = res.data.info.site_logo
      ruleForm.site_name = res.data.info.site_name
      ruleForm.site_version = res.data.info.site_version
    }
    static_upload.site_favicon = `${configStores.site_host}/${res.data.info.site_favicon}`
    static_upload.site_logo = `${configStores.site_host}/${res.data.info.site_logo}`
    loading.value = false
    console.log(res)
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
init()


</script>
<style scoped>
.el-descriptions {
  margin-top: 20px;
}

.avatar-logo {
  width: auto;
  height: 32px;
}

.avatar-favicon {
  width: auto;
  height: 32px;
}
</style>