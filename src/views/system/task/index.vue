<template>
  <div class="task-settings layout-block" v-loading="loading">
    <el-form ref="ruleFormRef" label-position="right" :model="ruleForm" label-width="200px" style="max-width: 555px"
      class="demo-ruleForm" status-icon>
      <el-form-item :label="$t('客户端自动连接')" prop="resource">
        <el-radio-group v-model="ruleForm.allowautoclientlink" @click.stop>
          <el-radio :label="true">{{ $t('允许') }}</el-radio>
          <el-radio :label="false">{{ $t('不允许') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('SQL执行队列大小')" prop="dbstatementcachecount">
        <el-input-number v-model="ruleForm.dbstatementcachecount" class="mx-12" width="200px" controls-position="right"
          :min="1" :max="1000" />
      </el-form-item>
      <el-form-item :label="$t('节点预加载的数量')" prop="load_last_node_count">
        <el-input-number v-model="ruleForm.load_last_node_count" class="mx-12" width="200px" controls-position="right"
          :min="1" :max="1000" />
      </el-form-item>
      <el-form-item :label="$t('任务预加载的时间间隔(秒)')" prop="load_last_task_time">
        <el-input-number v-model="ruleForm.load_last_task_time" class="mx-12" width="200px" controls-position="right"
          :min="1" :max="3600" />
      </el-form-item>
      <el-form-item :label="$t('异步任务超时时间(秒)')" prop="synccommanddeadline">
        <!-- <el-input v-model="ruleForm.name" /> -->
        <el-input-number v-model="ruleForm.synccommanddeadline" class="mx-12" width="200px" controls-position="right"
          :min="1" :max="86400" />
      </el-form-item>
      <el-form-item :label="$t('任务调度缓冲区大小')" prop="taskdispatch_backpressure">
        <el-input-number v-model="ruleForm.taskdispatch_backpressure" width="200px" controls-position="right" :min="1"
          :max="1000" />
      </el-form-item>
      <el-form-item :label="$t('最大转换图纸超时时间(秒)')" prop="timeout">
        <el-input-number v-model="ruleForm.timeout" width="200px" controls-position="right" :min="1"
          :max="3600" />
      </el-form-item>
      <el-form-item>
        <div class="button-group">
          <el-button type="primary" @click="onSubmit(ruleFormRef)" :title="$t('提交')">
            <span class="btn-text">{{ $t('提交') }}</span>
          </el-button>
          <el-button @click="resetForm(ruleFormRef)" :title="$t('重置')">
            <span class="btn-text">{{ $t('重置') }}</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="systemBasic">
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { getSystemInfo, putTaskInfo } from "@/services/system"
import { $t } from '@/locales'

const ruleFormRef = ref<FormInstance>()
const loading = ref(false)
const ruleForm = reactive({
  allowautoclientlink: false,
  dbstatementcachecount: 256,
  load_last_node_count: 128,
  load_last_task_time: 3600,
  synccommanddeadline: 20,
  taskdispatch_backpressure: 10,
  timeout: 600,
})

const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      putTaskInfoReq()
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 修改任务信息请求
const putTaskInfoReq = async () => {
  try {
    const res = await putTaskInfo(ruleForm)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: $t('修改成功'),
      })
      init()
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }
  } catch (error) {
    console.log(error)
  }
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  ruleForm.allowautoclientlink = false
  formEl.resetFields()
}

async function init() {
  try {
    loading.value = true
    const res = await getSystemInfo()
    if (res.code === 0) {
      ruleForm.allowautoclientlink = res.data.task.allowautoclientlink
      ruleForm.dbstatementcachecount = res.data.task.dbstatementcachecount
      ruleForm.load_last_node_count = res.data.task.load_last_node_count
      ruleForm.load_last_task_time = res.data.task.load_last_task_time
      ruleForm.synccommanddeadline = res.data.task.synccommanddeadline
      ruleForm.taskdispatch_backpressure = res.data.task.taskdispatch_backpressure
      ruleForm.timeout = res.data.task.timeout
    }
    loading.value = false
    console.log(res)
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
init()

</script>
<style scoped>
.el-descriptions {
  margin-top: 20px;
}

:deep(.el-input-number) {
  margin-left: 0;
}
</style>