<template>
  <div class="basic-info layout-block">
    <el-form v-loading="loading" ref="ruleFormRef" label-position="right" :model="ruleForm" :rules="rules"
      label-width="120px" style="max-width: 555px" class="demo-ruleForm" :size="formSize" status-icon>
      <el-form-item :label="$t('水印工具')" prop="waterFlag">
        <el-switch v-model="ruleForm.waterFlag" />
      </el-form-item>
      <el-form-item :label="$t('水印文本')" prop="waterText">
        <el-input v-model="ruleForm.waterText" :placeholder="$t('请输入水印文本')" />
        <p class="tip">{{ waterTip }}</p>
      </el-form-item>
      <el-form-item :label="$t('显示图纸小地图')" prop="map">
        <el-switch v-model="ruleForm.map" />
      </el-form-item>
      <el-form-item :label="$t('图章')" prop="stampList">
        <el-upload class="avatar-uploader" :show-file-list="false"
          :on-change="(file: UploadFile) => uploadFile(file)"
          :accept="stampAccept" :auto-upload="false">
          <template v-if="stampListImg.length">
            <div class="img-item-box" v-for="(item, key) in stampListImg" :key="key + 'n'" >
              <div class="img-item-delete" @click.stop="deleteIcon(key)">
                <component class="img-item-delete-icon" :is="$ElIcon['Delete']" />
              </div>
              <img class="stamp-list" :src="item" alt="">
            </div>
          </template>
          <el-icon class="add-icon">
            <component :is="$ElIcon['Plus']" />
          </el-icon>
          <template #tip>
            <div class="el-upload__tip">
              {{ uploadStampTip }}
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item :label="$t('图章宽度(px)')" prop="stampWidth">
        <el-input v-model="ruleForm.stampWidth" @input="handleInput" min="1" max="1000" :placeholder="$t('请输入图章宽度')" />
      </el-form-item>
      <el-form-item :label="$t('图章高度(px)')" prop="stampHeight">
        <el-input v-model="ruleForm.stampHeight" @input="handleInput"  :placeholder="$t('请输入图章高度')" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit(ruleFormRef)">{{ $t('提交') }}</el-button>
        <el-button @click="resetForm(ruleFormRef)">{{ $t('重置') }}</el-button>
      </el-form-item>
    </el-form>
    <el-dialog v-model="dialogVisible">
      <img class="w-full" :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="systemBasic">
import { ref, reactive, watch } from 'vue'
import type { UploadFile, FormInstance, FormRules } from 'element-plus'
import { getOpenDWGConfig, postOpenDWGConfig } from "@/services/system"
import { MAX_LOGO_FAVICON_LIMIT, stampAccept, getUploadStampTip, getWaterTip } from '@/utils/const';
import { formatSize } from '@/utils'
import { $t } from '@/locales';
import { uploadImage, downloadImage } from '@/services/file';

const loading = ref<boolean>(false)
const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const ruleForm = reactive<Recordable>({
  waterFlag: false,
  waterText: "",
  map: false,
  stampList: [],
  stampWidth: 0,
  stampHeight: 0
})

// 获取国际化的提示文本
const uploadStampTip = getUploadStampTip()
const waterTip = getWaterTip()

interface ConfigItem {
  group: string
  key: string
  name: string
  value: string
}

const stampListImg = ref<string[]>([])

const checkFlag = (rule: any, value: any, callback: any) => {
    if (ruleForm.waterFlag && value === '') {
        callback(new Error($t('请输入水印文本')))
    } else {
        callback()
    }
}


const rules = reactive<FormRules>({
  waterText: [
        { validator: checkFlag, trigger: 'blur' },
    ],
    stampList: [
        { required: true, message: $t('请上传图章'), trigger: ['blur', 'change']},
    ],
    stampWidth: [
        { required: true, message: $t('请输入图章宽度'), trigger: 'blur' },
    ],
    stampHeight: [
        { required: true, message: $t('请输入图章高度'), trigger: 'blur' },
    ],
})

const mapStampList = (arr:string[]) => {
  stampListImg.value = []
  arr.forEach(async (v: string) => {
    const res = await downloadImage(v);
    const url = URL.createObjectURL(new Blob([res]));

    stampListImg.value.push((url as string))
  })
}


    const handleInput = (value: string) => {
      let tempValue = value
      tempValue = value.replace(/\D/g, '');
      if (tempValue === '' || tempValue === '0') {
        ruleForm.stampWidth = '';
      } else {
        const parseValue = parseInt(tempValue, 10);

        if(parseValue > 1000) {
          ruleForm.stampWidth = 1000
        }
      }
    };

const formatFormData = function(){
  if(ruleForm.waterFlag) {
    if(!ruleForm.waterText) {
      ElMessage({
        type: 'error',
        message: $t('请输入水印文本'),
      })
      return
    }
  }
  const data:object[] = []
  const keysArr = Object.keys(ruleForm) 
  keysArr.forEach(v => {
    let value = ""
    if(v === "waterFlag" || v === "map") {
      value = ruleForm[v] ? "1" : "0"
    } else if(v === "stampList"){
      value = JSON.stringify(ruleForm[v])
    } else if(v === 'stampWidth'){
      value = ruleForm[v].toString()
    } else if(v === 'stampHeight'){
      value = ruleForm[v].toString()
    } else {
      const data  = ruleForm[v]
      value = data
    }
    const obj:ConfigItem = {
      group: "2d",
      key: v,
      name: v,
      value: value
    }
    data.push(obj)
  })

  return data
}


const uploadFile = async (file: UploadFile) => {
    if (file.size && file.size > MAX_LOGO_FAVICON_LIMIT) {
      ElMessage.error($t('图片大小不能超过 {size}!', { size: formatSize(MAX_LOGO_FAVICON_LIMIT) }))
      return
    }

    if(ruleForm.stampList.length > 9) {
      ElMessage.error($t('最多只能上传10个图章'))
      return
    }
  try {
    const res = await uploadImage({
      image: file.raw ? file.raw : ''
    })
    ruleForm.stampList.push(res.data.key)
    const img = await downloadImage(res.data.key);
    const url = URL.createObjectURL(new Blob([img]));

    stampListImg.value.push((url as string))

  } catch (e) {
    console.log(e);
  }
}

const deleteIcon = (index: number) => {
  ruleForm.stampList.splice(index, 1)
  stampListImg.value.splice(index, 1)
}

const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const data = formatFormData()
      const res = await postOpenDWGConfig(data)
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: $t('修改成功'),
        })
      } else {
        ElMessage({
          type: 'error',
          message: $t('修改失败'),
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

async function init() {
  try {
    loading.value = true
    const data = {
      group: "2d"
    }
    const res = await getOpenDWGConfig(data)
    if (res.code === 0) {
      if(res.data?.list.length) {
        res.data?.list.forEach((v: any) => {
          console.log(v)
          if(v.key === "waterFlag") {
            ruleForm.waterFlag = v.value === "0" ? false : true
          }
          if(v.key === "map") {
            ruleForm.map = v.value === "0" ? false : true
          }

          if(v.key === "stampList") {
            ruleForm.stampList = JSON.parse(v.value)
            mapStampList(ruleForm.stampList)
          }

          if(v.key === "waterText") {
            ruleForm.waterText = v.value
          }

          if(v.key === 'stampWidth') {
            ruleForm.stampWidth = v.value
          }
          
          if(v.key === 'stampHeight'){
            ruleForm.stampHeight = v.value
          }
        })
      }
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
init()


</script>
<style scoped>
.el-descriptions {
  margin-top: 20px;
}

.avatar-logo {
  width: auto;
  height: 32px;
}

.avatar-favicon {
  width: auto;
  height: 32px;
}

.stamp-list {
  width: 40px;
  height: 40px;
}

.add-icon {
  width: 40px;
  height: 40px;
  border: 1px dashed #efefef;
}

.img-item-box {
  width: 40px;
  height: 40px;
  margin-right: 5px;
  position: relative;
}

.img-item-delete {
  width: 40px;
  height: 40px;
  background-color: rgba(0,0,0,0.4);
  font-size: 12px;
  color: #fff;
  position: absolute;
  display: none;
}

.img-item-box:hover .img-item-delete{
  display: block;
}

.img-item-delete-icon {
  width: 15px;
  height: 15px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}

.tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 7px;
}
</style>