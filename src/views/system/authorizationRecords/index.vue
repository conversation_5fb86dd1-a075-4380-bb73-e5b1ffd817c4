<template>
  <div class="user-manage layout-vertical h-full">
    <div class="layout-vertical-filter justify-between layout-select">
      <p class="device-count">
        {{ $t('允许编辑设备数量：{allowCount}，当前已编辑设备数量：{currentCount}', { allowCount: allowEditDeviceCount, currentCount: ingEditDeviceCount }) }}
      </p>
    </div>
    <div class="layout-vertical-filter justify-between layout-select">
      <!-- 搜索区 -->
      <el-form :inline="true" :label-width="100" label-position="left">
        <el-form-item :label="$t('关键字')" class="folder">
          <el-input v-model="listQueryData.keyword" clearable placeholder="" />
        </el-form-item>
        <el-form-item :label="$t('状态')">
          <el-select v-model="listQueryData.status" clearable :placeholder="$t('状态')" filterable>
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('开始编辑时间')">
          <div class="demo-date-picker">
            <div class="block">
              <el-date-picker v-model="tableTime" type="daterange" range-separator="-" :start-placeholder="$t('开始时间')"
                :end-placeholder="$t('结束时间')" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="getTime"/>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex">
        <el-button type="primary" @click="onSubmit">{{ $t('查询') }}</el-button>
        <el-button type="primary" @click="openHistory('')">{{ $t('历史数据') }}</el-button>
      </div>
    </div>
    <div class="layout-vertical-content flex flex-col list" v-loading="loading">
    <!-- 表格 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
        type="index"
        :label="$t('序号')"
        width="50">
      </el-table-column>
        <el-table-column
          prop="browser_id"
          :label="$t('设备号')"
          show-overflow-tooltip
          align="center"
          width="180">
        </el-table-column>
        <el-table-column
          prop="status"
          :label="$t('状态')"
          align="center">
          <template v-slot="scope">
            <span v-if="scope.row.status === 0">{{ $t('全部') }}</span>
            <span v-if="scope.row.status === 1">{{ $t('创建未使用') }}</span>
            <span v-if="scope.row.status === 2">{{ $t('编辑中') }}</span>
            <span v-if="scope.row.status === 3">{{ $t('结束编辑') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="user_info"
          align="center"
          show-overflow-tooltip
          :label="$t('用户')">
        </el-table-column>
        <el-table-column
          prop="ip"
          align="center"
          width="120"
          label="IP">
        </el-table-column>
        <el-table-column
          prop="begin_time"
          align="center"
          :label="$t('开始编辑时间')">
        </el-table-column>
        <el-table-column
          prop="end_time"
          align="center"
          :label="$t('退出编辑时间')">
        </el-table-column>
        <el-table-column
          prop="user_agent"
          align="center"
          show-overflow-tooltip
          :label="$t('浏览器信息')">
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="$t('操作')"
          width="160">
          <template v-slot="scope">
            <el-button @click="openRaiseTips(scope.row)" type="text" v-if="scope.row.status === 2">{{ $t('踢出') }}</el-button>
            <el-button @click="handleOpenHistoryClick(scope.row)" type="text">{{ $t('历史数据') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination class="layout-table-pagination" v-if="total > 0" :total="total"
        :current-page="listQueryData.page" :page-sizes="[10, 20, 30, 40]" :page-size="listQueryData?.limit"
        :layout="'total, sizes, prev, pager, next, jumper'" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    </div>
  </div>

  <el-dialog
    class="my-dialog"
    :title="$t('历史登录记录')"
    v-model="dialogVisible"
    style="min-width: 800px;"
    width="80%">
    <div>
      <div class="layout-vertical-filter justify-between layout-select" style="display: flex;">
        <!-- 搜索区 -->
        <el-form :inline="true" :label-width="100" label-position="left">
          <el-form-item :label="$t('关键字')" class="folder">
            <el-input v-model="historyListQueryData.keyword" clearable placeholder="" />
          </el-form-item>
          <el-form-item :label="$t('开始编辑时间')">
            <div class="demo-date-picker">
              <div class="block">
                <el-date-picker v-model="historyTime" type="daterange" range-separator="-" :start-placeholder="$t('开始时间')"
                  :end-placeholder="$t('结束时间')" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="getHistoryTime"/>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <div class="flex">
          <el-button type="primary" @click="onHistorySubmit">{{ $t('查询') }}</el-button>
        </div>
      </div>
      <el-table
        :data="historyTableData"
        v-loading="historyLoading"
        border
        style="width: 100%">
        <el-table-column
        type="index"
        :label="$t('序号')"
        width="50">
      </el-table-column>
        <el-table-column
          prop="browser_id"
          :label="$t('设备号')"
          show-overflow-tooltip
          align="center"
          width="180">
        </el-table-column>
        <el-table-column
          prop="status"
          :label="$t('状态')"
          align="center">
          <template v-slot="scope">
            <span v-if="scope.row.status === 0">{{ $t('全部') }}</span>
            <span v-if="scope.row.status === 2">{{ $t('编辑中') }}</span>
            <span v-if="scope.row.status === 3">{{ $t('结束编辑') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="user_info"
          align="center"
          show-overflow-tooltip
          :label="$t('用户')">
        </el-table-column>
        <el-table-column
          prop="ip"
          align="center"
          label="IP"
          width="120">
        </el-table-column>
        <el-table-column
          prop="begin_time"
          align="center"
          :label="$t('开始编辑时间')"
        ></el-table-column>
        <el-table-column
          prop="end_time"
          align="center"
          :label="$t('退出编辑时间')"
        ></el-table-column>
        <el-table-column
          prop="user_agent"
          align="center"
          show-overflow-tooltip
          :label="$t('浏览器信息')"
        ></el-table-column>
      </el-table>
      <el-pagination class="layout-table-pagination" style="width: 500px;" v-if="historyTotal > 0" :total="historyTotal"
        :current-page="historyListQueryData.page" :page-sizes="[10, 20, 30, 40]" :page-size="historyListQueryData?.limit"
        :layout="'total, sizes, prev, pager, next, jumper'" @current-change="handleHistoryCurrentChange" @size-change="handleHistorySizeChange" />
    </div>
    <template v-slot:footer>
      <span  class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">{{ $t('关 闭') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="filesFile">
import { ref, onMounted, onUnmounted, computed, watchEffect, watch, nextTick, h, onBeforeUnmount } from 'vue';
import type { ElTable } from 'element-plus'
import { getBrowserIdList, getBrowserHistoryList, postBrowserRaise } from "@/services/system";
import { getBrowserIdCount } from "@/services/file";
import { $t } from '@/locales';

const loading = ref(false)
const dialogVisible = ref(false)
const total = ref(0)

const allowEditDeviceCount = ref(0)
const ingEditDeviceCount = ref(0)

const historyLoading = ref(false)
const historyTotal = ref(0)

const intervalNum = ref<number | null>(null)

const statusList = [
  {
    label: $t("全部"),
    value: 0
  },
  {
    label: $t("创建未使用"),
    value: 1
  },
  {
    label: $t("编辑中"),
    value: 2
  },
  {
    label: $t("结束编辑"),
    value: 3
  }
]

// 定义列表接口查询参数
const listQueryData = ref({
  "page": 1,
  "limit": 10,
  "keyword": "",
  "status": 0,
  "rangeTime": []
})

const historyListQueryData = ref({
  "page": 1,
  "limit": 10,
  "keyword": "",
  "rangeTime": [],
  "browser_id": ""
})

const tableTime = ref([])
const historyTime = ref([])

const tableData = ref([])
const historyTableData = ref([])

const openHistory = (browser_id: any) => {
  historyListQueryData.value.browser_id = browser_id
  historyListQueryData.value.page = 1
  historyListQueryData.value.limit = 10
  historyListQueryData.value.keyword = ''
  historyListQueryData.value.rangeTime = []
  historyTime.value = []
  historyTableData.value = []
  dialogVisible.value = true
  historyListQueryData.value.page = 1;
  getHistoryList()
}

const openRaiseTips = (row: any) => {
  const browser_id = row.browser_id;
  ElMessageBox.confirm(
    $t(`您当前正在对设备号：{browserId} 进行踢出操作，踢出后该设备将立即关闭并退出云原生编辑页面。确认继续进行该操作？`, { browserId: browser_id }),
    $t('提示'),
    {
      confirmButtonText: $t('确认'),
      cancelButtonText: $t('取消'),
      type: 'warning',
    }
  )
  .then(() => {
    handleRaiseClick(browser_id)
  })
}

function starInterval() {
  if(intervalNum.value !== null) clearInterval(intervalNum.value)

  intervalNum.value = setInterval(() => {
    getList()
    getDeviceCount()
  }, 60*1000)

}

// Vue 生命周期钩子：组件卸载时清除定时器
onBeforeUnmount(() => {
  if (intervalNum.value !== null) clearInterval(intervalNum.value)
});

starInterval()

const getHistoryTime = (val: string[]) => {
  historyListQueryData.value.rangeTime = historyTime.value
}

const handleHistorySizeChange = (val: number) => {
  historyListQueryData.value.limit = val
  getHistoryList()

}
const handleHistoryCurrentChange = (val: number) => {
  historyListQueryData.value.page = val
  getHistoryList()
}

const handleSizeChange = (val: number) => {
  listQueryData.value.limit = val
  getList()

}
const handleCurrentChange = (val: number) => {
  listQueryData.value.page = val
  getList()
}

const getDeviceCount = () => {
  const client_id = localStorage.getItem('client_id') || '';
  const browser_id = localStorage.getItem('gstarDeviceId') || '';

  getBrowserIdCount({client_id, browser_id}).then(res => {
    if(res.code === 0) {
      allowEditDeviceCount.value = res.data.allow_num
      ingEditDeviceCount.value = res.data.cur_num
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }
  }).catch(err => {
    ElMessage({
        type: 'error',
        message: err.msg,
      })
  })
}

getDeviceCount()

const getList = () => {
  getBrowserIdList(listQueryData.value).then(res => {
    console.log(res)
    if(res.code === 0) {
      tableData.value = res.data.list
      total.value = res.data.total
      
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }
  }).catch(err => {
    ElMessage({
        type: 'error',
        message: err.msg,
      })
  })
}

getList()

const getHistoryList = () => {
  historyLoading.value = true
  getBrowserIdList
  getBrowserHistoryList(historyListQueryData.value).then(res => {
    if(res.code === 0) {
      historyTableData.value = res.data.list
      historyTotal.value = res.data.total
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }
    historyLoading.value = false
  }).catch(() => {
    historyLoading.value = false
  })
}

// 改变搜索时间
const getTime = (val: string[]) => {
  listQueryData.value.rangeTime = tableTime.value
}


const onSubmit = () => {
  listQueryData.value.page = 1;
  getList()
}

const onHistorySubmit = () => {
  listQueryData.value.page = 1;
  getHistoryList()
}

const handleOpenHistoryClick = (row: any) => {
  openHistory(row.browser_id)
}

const handleRaiseClick = (browser_id: any) => {
  loading.value = true
  const data = {
    browser_id
  }
  postBrowserRaise(data).then(res => {
    if(res.code === 0) {
      ElMessage({
        type: 'success',
        message: $t("当前设备已结束编辑"),
      })
      getList()
      getDeviceCount()
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }
    loading.value = false
  }).catch(err => {
    loading.value = false
    ElMessage({
      type: 'error',
      message: err.msg,
    })
  })
}

</script>

<style lang="scss" scoped>
.upload-button {
  display: inline-block;
  margin-right: 12px;
}

.folder {
  :deep(.el-form-item__label) {
    width: 100px !important;
  }
}

.layout-select {
  padding-bottom: 6px !important;
  :deep(.el-select__wrapper) {
    width: 220px;
  }
}

.list {
  height: v-bind(table_content_height);
  margin-bottom: 0px !important;
}

.fileDrop {
  height: calc(100% - 100px);
}

.file-table {
  height: calc(100% - 72px - 72px);
}

.action-icon {
  cursor: pointer;
  margin-right: 10px;
}


:deep(.el-input__wrapper) {
  width: 220px;
}
.edit-name {
  width: 300px;
}

.size::after {
  content: '132132132';
}

:deep(.el-breadcrumb__inner) {
  cursor: pointer;
}

.dir-name {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.el-loading-parent--relative {
  overflow: hidden;
}

:deep(.el-upload-list__item-info) {
  width: 350px;
}

.file-icon {
  height: 30px;
  max-width: 40px;
  vertical-align: middle;
  margin-right: 10px;
}

:deep(.el-dialog__body) {
  height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.preview-img {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;

  img {
    height: calc(100% - 30px);
    width: auto;
  }
}

.progress {
  width: 150px;
  position: absolute;
  bottom: 2px;
  margin-left: 45px;

  :deep(.el-progress__text) {
    font-size: 12px !important;
    color: #606266;
  }
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.file-note-new {
  position: relative;
  top: -12px;
  width: 26px;
  height: 12px;
  cursor: text;
}

.layout-table-pagination {
  margin: 40px auto;
}

.my-dialog {
  min-width: 800px!important;
}

:deep(.el-table .el-button--text:hover, 
.el-table .el-button--text:focus) {
  background-color: transparent !important;
  border-color: transparent !important;
}

.device-count {
  margin-bottom: 18px;
}
</style>