<template>
  <div class="web-sdk">
    <div class="web-sdk-upload-box">
      <div class="web-sdk-upload">
        <span class="web-sdk-upload-label">{{ $t('文件名：') }}</span>
        <span class="web-sdk-upload-value">{{ fileName }}</span>
        <el-upload
          class="web-sdk-upload-file"
          :multiple="false"
          :limit="1"
          :show-file-list="false"
          :on-change="(file: UploadFile, uploadFiles: UploadFiles) => uploadFile(file, uploadFiles, 'site_favicon')"
          >
          <el-button size="small" type="primary">{{ $t('点击上传') }}</el-button>
        </el-upload>
      </div>
    </div>
    <div class="web-sdk-table">
      <p class="web-sdk-table-label">{{ $t('证书信息') }}</p>
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          prop="license_type"
          align="center"
          :label="$t('应用类型')"
          width="180">
        </el-table-column>
        <el-table-column
          prop="license_domain"
          align="center"
          :label="$t('允许域名')"
          width="180">
        </el-table-column>
        <el-table-column
          prop="license_validity_period"
          align="center"
          :label="$t('有效期')">
        </el-table-column>
        <el-table-column
          prop="license_create_time"
          align="center"
          :label="$t('创建时间')">
        </el-table-column>
        <el-table-column
          prop="license_expiry_time"
          align="center"
          :label="$t('过期时间')">
        </el-table-column>
        <el-table-column
          prop="license_import_time"
          align="center"
          :label="$t('证书导入时间')">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts" name="systemBasic">
import { ref, reactive } from 'vue'
import type { UploadFile, UploadFiles } from 'element-plus'

import { getWebSdkInfo, uploadWebSdkInfo } from "@/services/system"

type uploadKey = 'site_logo' | 'site_favicon';

const loading = ref<boolean>(false)

const fileName = ref<string>('')

const tableData = ref<any[]>([])

const timeHash = ref<any>(null)

const uploadFile = async (file: UploadFile, uploadFiles: UploadFiles, key: uploadKey) => {
  if(!timeHash.value) {
    timeHash.value = setTimeout(() => {
      timeHash.value = null
    }, 200)
    console.log("file", file)
    console.log("key", key)
    fileName.value = file.name
    const formData = new FormData();
    formData.append('file', (file.raw as File));
    // if (["site_logo", "site_favicon"].includes(key)) {
    //   if (file.size && file.size > MAX_LOGO_FAVICON_LIMIT) {
    //     ElMessage.error(`图片大小不能超过 ${formatSize(MAX_LOGO_FAVICON_LIMIT)}!`)
    //     static_upload[key] = '';
    //     return
    //   }
    // }
    loading.value = true
    try {
      const res = await uploadWebSdkInfo(formData)
      console.log("uploadWebSdkInfo", res)
      if(res.code === 0) {
        ElMessage({
          type: "success",
          message: $t("上传成功！"),
        });
        init()
      } else {
        ElMessage({
          type: "error",
          message: $t("上传失败！"),
        });
      }
    } catch (e) {
      console.log(e);
      ElMessage({
        type: "error",
        message: $t("上传失败！"),
      });
    }
    loading.value = false
  }
}



async function init() {
  try {
    loading.value = true
    const res = await getWebSdkInfo()
    console.log("getWebSdkInfo", res)
    if (res.code === 0) {
      tableData.value = [res.data]
    }
    
    loading.value = false
    
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
init()


</script>
<style scoped>
.el-descriptions {
  margin-top: 20px;
}
.web-sdk {
  width: 100%;
  height: 100%;
  background: #fff;
}
.web-sdk-upload-box {
  width: 100%;
  height: 200px;
  position: relative;
}
.web-sdk-upload {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.web-sdk-upload-label {
  width: 100px;
  text-align: right;
}
.web-sdk-upload-value {
  width: 200px;
  display: block;
  height: 30px;
  border: 1px solid #ccc;
  line-height: 30px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 10px;
  margin-right: 10px;
}
.web-sdk-upload-file {
  /* margin-top: 12px; */
}

.web-sdk-table {
  box-sizing: border-box;
  padding: 20px;
}

.web-sdk-table-label {
  padding-bottom: 25px;
}

</style>