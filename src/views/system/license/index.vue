<template>
  <div class="h-full license-wrap">
    <el-card class="license-card-1" body-style="height: calc(100% - 56px)">
      <template #header>
        <div class="font-semibold">{{ $t('版本&许可授权') }}</div>
      </template>
      <el-scrollbar>
        <div v-for="(item, index) in license" class="module-item" :key="item.expiry_time">
          <el-form-item :label="$t('授权码')">
            <p class="form-text" :title="item.license_key">
              {{ item.license_key }}
            </p>
          </el-form-item>
          <el-form-item :label="$t('状态')" prop="name">
            <p class="form-text">
              {{ item.enable ? $t("可用") : $t("不可用") }}
            </p>
          </el-form-item>
          <el-form-item :label="$t('支持用户数')" prop="name">
            <p class="form-text">
              {{ item.license_limit.users }}
            </p>
          </el-form-item>
          <el-form-item :label="$t('支持节点数')" prop="name">
            <p class="form-text">
              {{ item.license_limit.nodes }}
            </p>
          </el-form-item>
          <el-form-item :label="$t('允许登录设备数')" prop="name">
            <p class="form-text">
              {{ item.license_limit.devices }}
            </p>
          </el-form-item>
          <el-form-item :label="$t('三方接入')" prop="name">
            <p class="form-text">
              {{ item.license_limit.sf === 1 ? $t("允许") : $t("不允许") }}
            </p>
          </el-form-item>
          <el-form-item :label="$t('允许编辑设备数')" prop="name">
            <p class="form-text">
              {{ item.license_limit.gcaddevices }}
            </p>
          </el-form-item>
          <el-form-item :label="$t('许可期限')" prop="name">
            <p class="form-text" :class="getClass(item)">
              {{ item.expiry_time_format }}
            </p>
          </el-form-item>
          <div class="module-list" :style="{height: `calc(100% - ${license.length !== (index + 1) ? 18 : 0}px`}" style="width: 180px">
            <el-table :data="item.modules" border style="width: 100%">
              <el-table-column prop="name" :show-overflow-tooltip="true" :label="$t('授权模块')" />
            </el-table>
          </div>
          <p class="remark-title">{{ $t('备注：') }}</p>
          <div class="remark-content">
            <el-form-item :label="$t('2D支持的文件类型')" prop="name" v-if="item.file_type_dict['2F']">
              <p class="form-text" :class="getClass(item)" :title="item.file_type_dict['2F'] ? item.file_type_dict['2F'].join('，') : ''">
                {{ item.file_type_dict['2F'] ? item.file_type_dict['2F'].join('，') : '' }}
              </p>
            </el-form-item>
            <el-form-item :label="$t('3D支持的文件类型')" prop="name" v-if="item.file_type_dict['3F']">
              <p class="form-text" :class="getClass(item)" :title="item.file_type_dict['3F'] ? item.file_type_dict['3F'].join('，') : ''">
                {{ item.file_type_dict['3F'] ? item.file_type_dict['3F'].join('，') : '' }}
              </p>
            </el-form-item>
            <el-form-item :label="$t('2D标准支持的功能')" prop="name" v-if="item.file_type_dict['2Std']">
              <p class="form-text" :class="getClass(item)" :title="item.file_type_dict['2Std'] ? switchName(item.file_type_dict['2Std'] as []) : ''">
                {{ item.file_type_dict['2Std'] ? switchName(item.file_type_dict['2Std'] as []) : '' }}
              </p>
            </el-form-item>
            <el-form-item :label="$t('2D高级支持的功能')" prop="name" v-if="item.file_type_dict['2Pre']">
              <p class="form-text" :class="getClass(item)" :title="item.file_type_dict['2Pre'] ? switchName(item.file_type_dict['2Pre'] as []) : ''">
                {{ item.file_type_dict['2Pre'] ? switchName(item.file_type_dict['2Pre'] as []) : '' }}
              </p>
            </el-form-item>
            <el-form-item :label="$t('2D行业支持的功能')" prop="name" v-if="item.file_type_dict['2Pro']">
              <p class="form-text" :class="getClass(item)" :title="item.file_type_dict['2Pro'] ? switchName(item.file_type_dict['2Pro'] as []) : ''">
                {{ item.file_type_dict['2Pro'] ? switchName(item.file_type_dict['2Pro'] as []) : '' }}
              </p>
            </el-form-item>
            <el-form-item :label="$t('3D标准支持的功能')" prop="name" v-if="item.file_type_dict['3Std']">
              <p class="form-text" :class="getClass(item)" :title="item.file_type_dict['3Std'] ? switchName(item.file_type_dict['3Std'] as []) : ''">
                {{ item.file_type_dict['3Std'] ? switchName(item.file_type_dict['3Std'] as []) : '' }}
              </p>
            </el-form-item>
            <el-form-item :label="$t('3D高级支持的功能')" prop="name" v-if="item.file_type_dict['3Pre']">
              <p class="form-text" :class="getClass(item)" :title="item.file_type_dict['3Pre'] ? switchName(item.file_type_dict['3Pre'] as []) : ''">
                {{ item.file_type_dict['3Pre'] ? switchName(item.file_type_dict['3Pre'] as []) : '' }}
              </p>
            </el-form-item>
            <el-form-item :label="$t('3D行业支持的功能')" prop="name" v-if="item.file_type_dict['3Pro']">
              <p class="form-text" :class="getClass(item)" :title="item.file_type_dict['3Pro'] ? switchName(item.file_type_dict['3Pro'] as []) : ''">
                {{ item.file_type_dict['3Pro'] ? switchName(item.file_type_dict['3Pro'] as []) : '' }}
              </p>
            </el-form-item>
          </div>
          <div class="split-line" v-if="license.length !== (index + 1)"></div>
        </div>
      </el-scrollbar>
    </el-card>
    <el-card class="license-card-2" body-style="height: calc(100% - 56px)">
      <template #header>
        <div class="font-semibold">{{ $t('授权功能列表') }}</div>
      </template>
      <el-table :data="modules" border height="100%">
        <el-table-column prop="id" :label="$t('序号')" width="60">
          <template #default="{ row }">
            <span :class="[!row.usable ? 'usable' : '']">{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('授权功能')">
          <template #default="{ row }">
            <span :class="[!row.usable ? 'usable' : '']">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="expiry_time_format" :label="$t('授权期限')" width="180">
          <template #default="{ row }">
            <span :class="getClass(row)">{{ row.usable ? row.expiry_time_format : $t('未授权') }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="systemBasic">
import { ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { getLicense } from "@/services/system"
import { getFunctionPoint } from '@/utils/const'
import { $t } from '@/locales'

const loading = ref<boolean>(false)

const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()

interface LicenseLimit {
  nodes: number
  org: string
  users: number
  devices: number
  sf: number
}

interface ModulesItem {
  key: string
  name: string
  enable?: boolean
  usable?: boolean
  expiry_time?: number
  expiry_time_format?: string
  id?: number
}

interface DictItem {
  '2F': string[]
  '2Pre'?: string[]
  '2Pro'?: string[]
  '2Std'?: string[]
  '3F': string[]
  '3Pre'?: string[]
  '3Pro'?: string[]
  '3Std'?: string[]
}

interface LicenseItem {
  account_name: string
  counter: number
  enable: boolean
  end_time: number
  expiry_time: number
  expiry_time_format: string
  first_use_time: number
  last_update_timesn: number
  last_update_timestamp: number
  license_key: string
  license_limit: LicenseLimit
  license_name: string
  modules: ModulesItem[]
  span_time: number
  start_time: number
  file_type_dict: DictItem
}
const license = ref<LicenseItem[]>([])

const modules = ref<ModulesItem[]>([])

const getClass = (row: any) => {
  if (!row.usable && !row.enable) {
    return 'usable'
  }
  if (row.expiry_time && row.expiry_time * 1000 - Date.now() <= (7 * 24 * 60 * 60 * 1000)) {
    return 'usable'
  }
  return ''
}

const switchName = (list = []) => {
  const FUNCTION_POINT = getFunctionPoint()
  const nameList = list.map(item => FUNCTION_POINT[item] ? FUNCTION_POINT[item] : item)
  return nameList.join('，')
}

async function init() {
  try {
    loading.value = true
    const res = await getLicense()
    if (res.code === 0) {
      license.value = res.data.license
      modules.value = res.data.modules
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
init()

</script>
<style scoped>
.license-wrap {
  background-color: #fff;
  display: flex;
  justify-content: space-evenly;
  padding: 24px;
}

.license-card-1 {
  width: 40%;
  min-width: 600px;

  :deep(.el-form-item__label) {
    width: 150px !important;
    justify-content: flex-start !important;
  }
  .asterisk-left {
    width: calc(100% - 180px - 12px);
  }
}

.license-card-2 {
  width: 40%;
}

.el-descriptions {
  margin-top: 20px;
}

.split-line {
  width: 100%;
  height: 0;
  border-bottom: 1px dashed #999;
  margin-bottom: 20px;

}

.module-item {
  position: relative;
}

.module-list {
  position: absolute;
  right: 0;
  top: 0;
  /* height: calc(100% - 18px); */
  overflow-y: auto;
  overflow-x: hidden;
  margin-right: 12px;
}

.remark-title {
  color: #606266;
}

.remark-content .el-form-item {
  margin-bottom: 0;
}

.form-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.usable {
  color: red !important;
}
</style>