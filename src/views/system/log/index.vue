<template>
  <div class="h-full">
    <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading" :getList="getListHandle"
      :showFilter="true" :refresh="refresh" :searchList="searchList" ref="rowList">
      <template #tableHeader>
        <el-button v-noBlur type="primary" @click="clearAll" :disabled="noData" :title="$t('清空')">
          <span class="btn-text">{{ $t('清空') }}</span>
        </el-button>
      </template>
    </GList>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import GList from '@/components/GList.vue'
import { getSystemLogList, clearAllSystemLog } from '@/services/system'
import type { TableColumn, TablePagination, TableDataParams } from '@/model/table'
import { useForm } from "@/hooks/useForm"
import { $t } from '@/locales'


const refresh = ref(Date.now())
const rowList = ref<any>(null)

const noData = computed(() => {
  return !rowList.value || rowList.value.tableData.length <= 0
})

const { getListHandle, loading } = useForm({
  getListApi: (params: TableDataParams) => getSystemLogList(params),
  val: null,
  name: ''
})

// 表格列配置
const columns = [
  // {
  //   type: "selection",
  //   width: "55"
  // },
  {
    label: 'ID',
    property: 'id',
    showOverflowTooltip: true,
    width: "100"
  },
  {
    label: $t('用户'),
    property: 'user_name',
  },
  {
    label: $t('操作类型'),
    property: 'method',
    width: "90",
  },
  {
    label: $t('调用接口'),
    property: 'uri',
  },
  {
    label: $t('操作名'),
    property: 'route_name',
  },
  {
    label: $t('操作时间'),
    property: 'start_time_format',
    width: "160",
  },
  // {
  //   label: '参数',
  //   property: 'request',
  // },
  {
    label: $t('结果'),
    property: 'response',
  },
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
  currentPage: 1,
  pageSizes: [10, 20, 30, 40],
  limit: 10,
  layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

const searchList = [{
  key: 'user_name',
  type: 'input',
  name: $t('用户名'),
  placeholder: $t('请输入用户名')
}, {
  key: 'uri',
  type: 'input',
  name: $t('调用接口'),
  placeholder: $t('请输入接口')
}, {
  key: 'rangeTime',
  type: 'dataPicker',
  name: $t('时间'),
}]

const clearAll = () => {
  ElMessageBox.alert($t('你确定要清空所有日志吗') + '?', $t('清空'), {
    confirmButtonText: $t("确认"),
    cancelButtonText: $t("取消"),
    type: "warning",
  }).then(async () => {
    try {
      loading.value = true;
      await clearAllSystemLog();
        ElMessage({
          type: "success",
          message: $t("清空成功"),
        });
        refresh.value = Date.now();
      } catch (e) {
        console.log(e);
      } finally {
        loading.value = false;
      }
  });
}

</script>