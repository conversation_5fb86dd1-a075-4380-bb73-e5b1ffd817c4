<template>
  <div class="h-full">
    <el-tabs v-model="activeName" class="sdk-tabs" @tab-click="handleClick">
      <el-tab-pane :label="$t('2D样例')" name="2d" class="h-full flex">
        <el-scrollbar class="scroll-sdk">
          <el-menu :default-active="activeMenuKey" class="el-menu-vertical-demo" @select="menuSelect"
            :collapse="!isNavExpand">
            <template v-for="(item, i) in fnMenuList">
              <el-sub-menu :index="item.key" :key="item.key + i" v-if="item.children && item.children.length !== 0">
                <template #title>
                  <span>{{ item.title }}</span>
                </template>
                <el-menu-item v-for="(child) in item.children" :index="child.key" :key="child.key">
                  <span>{{ child.title }}</span>
                </el-menu-item>
              </el-sub-menu>
              <el-menu-item :index="item.key" :key="item.key" v-else>
                <span>{{ item.title }}</span>
              </el-menu-item>
            </template>
          </el-menu>
        </el-scrollbar>

        <ShowCode v-if="activeName === '2d'" :title="title" :code="code"></ShowCode>
      </el-tab-pane>

      <el-tab-pane :label="$t('3D样例')" name="3d" class="h-full flex">
        <el-menu :default-active="activeMenuKey" class="el-menu-vertical-demo" @select="menuSelect"
          :collapse="!isNavExpand">
          <template v-for="(item, i) in threeMenuList">
            <el-sub-menu :index="item.key" :key="item.key + i" v-if="item.children && item.children.length !== 0">
              <template #title>
                <span>{{ item.title }}</span>
              </template>
              <el-menu-item v-for="(child) in item.children" :index="child.key" :key="child.key">
                <span>{{ child.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item :index="item.key" :key="item.key" v-else>
              <span>{{ item.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>

        <ShowCode v-if="activeName === '3d'" :title="title" :code="code"></ShowCode>
      </el-tab-pane>
      <el-tab-pane :label="$t('编辑样例')" name="cloudCAD" class="h-full flex">
        <el-menu :default-active="activeMenuKey" class="el-menu-vertical-demo" @select="menuSelect"
          :collapse="!isNavExpand">
          <template v-for="(item, i) in cloudCADMenuList">
            <el-sub-menu :index="item.key" :key="item.key + i" v-if="item.children && item.children.length !== 0">
              <template #title>
                <span>{{ item.title }}</span>
              </template>
              <el-menu-item v-for="(child) in item.children" :index="child.key" :key="child.key">
                <span>{{ child.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item :index="item.key" :key="item.key" v-else>
              <span>{{ item.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>

        <ShowCode v-if="activeName === 'cloudCAD'" :title="title" :code="code"></ShowCode>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { fnMenuList, getCodeByKey, threeMenuList, cloudCADMenuList } from '@/views/sample'
import ShowCode from './ShowCode.vue'
import type { TabsPaneContext } from 'element-plus'
import { $t } from '@/locales'

// const code = ref()
const activeName = ref('2d')
const activeMenuKey = ref('dwg-show')
const isNavExpand = ref(true)

const title = computed(() => {
  switch(activeName.value) {
    case 'cloudCAD':
      return getActionMenu(activeMenuKey.value, cloudCADMenuList)
    case '3d':
      return getActionMenu(activeMenuKey.value, threeMenuList)
    case '2d':
    default:
      return getActionMenu(activeMenuKey.value, fnMenuList)
  }
})

const code = computed(() => {
  return getCodeByKey(activeMenuKey.value) || ''
})

function getActionMenu(key: string, arr: any) {
  let title = ''
  for (const v of arr) {
    if (v?.children) {
      console.log(1)
      title = getActionMenu(key, v.children)
      if (title) {
        return title
      }
    } else {
      if (v.key === key) {
        title = v.title
        // break
        return title
      }
    }
  }
  return title
}

const menuSelect = (index: string) => {
  activeMenuKey.value = index;
}

const handleClick = (tab: TabsPaneContext) => {
  const tabName = tab.props.name
  let _activeMenuKey = 'dwg-show'
  switch (tabName) {
    case 'cloudCAD':
      _activeMenuKey = 'GCADCloudCAD'
      break
    case '3d':
      _activeMenuKey = 'demo3d'
      break
    case '2d':
    default:
      _activeMenuKey = 'dwg-show'
      break
  }

  activeMenuKey.value = _activeMenuKey
}
</script>

<style lang="scss" scoped>
.scroll-sdk {
  max-width: 140px !important;
  min-width: 140px !important;
  background-color: #fff;
}
.el-menu {
  border-right: 0;

  .el-menu-item {
    color: var(--app-color-nav-text);
    font-size: 12px;
  }

  .el-menu-item.is-active {
    font-size: 12px;
    color: var(--app-color-primary);
    background: var(--app-color-nav-item-active);
    max-width: 140px !important;
    min-width: 140px !important;
  }
}

.el-menu-vertical-demo {
  width: 140px;
  min-width: 140px;
  overflow-x: hidden !important;

  :deep(.el-sub-menu) {
    width: 140px;
  }
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
  height: 100%;
}

.sdk-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);
  }
}

.show-code {
  width: 100%;
}

.code {
  width: 50%;

  .control {
    height: 32px;
    margin-bottom: 10px;
  }

  .title {
    line-height: 32px;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
  }

  .btn {
    float: right;
    margin-left: 10px;
  }
}

.preview {
  width: 50%;
}
</style>