<template>
  <div class="show-code">
    <div class="code">
      <div class="control">
        <span class="title">{{ props.title }}</span>
        <el-button type="primary" class="btn" @click.stop="refresh">{{ $t('刷新') }}</el-button>
        <el-button type="primary" class="btn" @click.stop="run">{{ $t('运行') }}</el-button>
      </div>
      <v-ace-editor v-model:value="code" :lang="aceConfig.lang" :theme="aceConfig.theme" :options="aceConfig.options"
        style="height: 100%; width: 100%;" />

    </div>
    <div class="preview"></div>
  </div>
</template>

<script setup lang='ts'>
import { ref, onMounted, watch } from 'vue';
import { $t } from '@/locales';
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-html';
import 'ace-builds/src-noconflict/theme-chrome';

interface Props {
  code: string
  title: string
}

const props = withDefaults(defineProps<Props>(), {
  code: '',
  title: ''
})

const code = ref(props.code)

watch(
  () => props.code,
  () => {
    code.value = props.code
    run()
  },
);

onMounted(async () => {
  code.value = props.code
  refresh()
})

const aceConfig = {
  lang: 'html',
  theme: 'chrome',
  'min-lines': 1,
  'max-lines': 999999,
  options: {
    tabSize: 2,
    showPrintMargin: false,
    fontSize: 12,
    hScrollBarAlwaysVisible: false,
    vScrollBarAlwaysVisible: false,
    customScrollbar: true
  }
}

// 重置刷新
const refresh = () => {
  code.value = props.code
  initIframe()
}

// 运行code
const run = () => {
  initIframe()
}

// 渲染iframe
const initIframe = () => {
  let iframe = document.querySelector("iframe") as any;
  if (iframe) {
    iframe.parentNode.removeChild(iframe);
  }
  iframe = document.createElement('iframe')
  iframe.style.height = '100%'
  iframe.style.width = '100%'
  iframe.style.border = 'none'
  document.querySelector(".preview")?.appendChild(iframe)

  const iframe_dom = iframe.contentDocument || iframe.contentWindow.document;
  if (iframe_dom) {
    iframe_dom.open();
    iframe_dom.write(code.value);
    iframe_dom.close();
  }
}

</script>

<style lang='scss' scoped>
.show-code {
  width: 100%;
  height: 100%;
  display: flex;
}

.code {
  width: 50%;
  height: 100%;

  .control {
    height: 32px;
    margin-bottom: 10px;
  }

  .title {
    line-height: 32px;
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
  }

  .btn {
    float: right;
    margin-left: 10px;
  }
}

.preview {
  width: 50%;
}
</style>