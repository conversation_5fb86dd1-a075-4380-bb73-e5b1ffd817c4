<template>
    <div class="h-full">
        <GList :columns="columns" :pagination="pagination" :showPagination="true" :loading="loading"
            :getList="getListHandle" :showFilter="true" :refresh="refresh" :menuList="menuList" :searchList="searchList">
            <template #tableHeader>
                <el-button v-noBlur type="primary" @click="addThirdParty" :title="$t('添加三方')">
                    <span class="btn-text">{{ $t('添加三方') }}</span>
                </el-button>
            </template>
            <template #enable="{ row }">
                <el-switch v-model="row.enable" :active-value="true" :inactive-value="false"
                    style="--el-switch-on-color: #13ce66" :before-change="() => beforeSwitchChange(row)" />
            </template>
            <template #action="{ row }">
                <div class="layout-action">
                    <span class="action-text" @click="resetToken(row)" :title="$t('重置令牌')">{{ $t('重置令牌') }}</span>
                    <span class="action-text" @click="updateValidityPeriod(row)" :title="$t('设置过期时间')">{{ $t('设置过期时间') }}</span>
                    <span class="action-text" @click="deleteThirdParty(row)" :title="$t('删除')">{{ $t('删除') }}</span>
                    <span class="action-text" @click="openSetPermission(row)" :title="$t('设置权限')">{{ $t('设置权限') }}</span>
                </div>
            </template>
        </GList>
        <CreateComForm ref="comForm" :title="$t('三方令牌')" :default_form="default_value"
            @onCreateOrUpdateSubmit="handleCreateThirdParty" :rules="rules">
            <template #default="{ scope }">
                <el-form-item :label="$t('三方名称')" prop="name" required>
                    <el-input v-model="scope.name" clearable maxlength="128" minlength="1" show-word-limit />
                </el-form-item>
                <el-form-item :label="$t('三方标识')" prop="appKey" required>
                    <el-input v-model="scope.appKey" clearable maxlength="128" minlength="1" show-word-limit />
                </el-form-item>
                <el-form-item :label="$t('过期时间')" prop="validityPeriod" required>
                    <el-date-picker v-model="scope.validityPeriod" format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss" :disabled-date="(time: Date) => disabledDate(time, 'date')"
                        type="datetime" :disabled-hours="(time: Date) => disabledDate(scope.validityPeriod, 'hour')"
                        :disabled-minutes="(time: Date) => disabledDate(scope.validityPeriod, 'minute')"
                        :disabled-seconds="(time: Date) => disabledDate(scope.validityPeriod, 'second')" />
                </el-form-item>
                <el-form-item :label="$t('三方状态')" prop="enable">
                    <el-radio-group v-model="scope.enable">
                        <el-radio :label="true">{{ $t('启用') }}</el-radio>
                        <el-radio :label="false">{{ $t('禁用') }}</el-radio>
                    </el-radio-group>
                </el-form-item>
            </template>
        </CreateComForm>
        <el-dialog v-model="dialogVisible" destroy-on-close width="20%" @close="reset">
            <template #header>
                <div class="drawer-title">{{ $t('设置令牌过期时间') }}</div>
            </template>
            <template #default>
                <el-form>
                    <el-form-item :label="$t('过期时间')">
                        <el-date-picker v-model="newValidityPeriod" format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss" :disabled-date="(time: Date) => disabledDate(time, 'date')"
                            type="datetime" :disabled-hours="(time: Date) => disabledDate(newValidityPeriod, 'hour')"
                            :disabled-minutes="(time: Date) => disabledDate(newValidityPeriod, 'minute')"
                            :disabled-seconds="(time: Date) => disabledDate(newValidityPeriod, 'second')" />
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <div style="flex: auto">
                    <el-button type="primary" @click="submitForm" :disabled="!newValidityPeriod" :title="$t('确认')">
                        <span class="btn-text">{{ $t('确认') }}</span>
                    </el-button>
                </div>
            </template>
        </el-dialog>
        <el-dialog v-model="dialogPermission" destroy-on-close width="30%" @close="reset">
            <template #header>
                <div class="drawer-title">{{ $t('设置权限') }}</div>
            </template>
            <template #default>
                <el-form
                ref="ruleFormRef"
                :model="ruleForm"
                label-width="160px"
                status-icon
                >
                    <el-form-item label="appKey" prop="appKey">
                        <el-input v-model="ruleForm.appKey" />
                    </el-form-item>
                    <el-form-item :label="$t('三方token')" prop="thirdToken">
                        <el-input v-model="ruleForm.thirdToken" />
                    </el-form-item>
                    <el-form-item :label="$t('文件校验地址')" prop="fileDownloadCheckUrl">
                        <el-input v-model="ruleForm.fileDownloadCheckUrl" />
                    </el-form-item>
                    <el-form-item :label="$t('向浩辰推送文件接口')" prop="dataServerMethod">
                        <el-input v-model="ruleForm.dataServerMethod" />
                    </el-form-item>
                    <el-form-item :label="$t('获取外部参照文件地址')" prop="referenceFileServer">
                        <el-input v-model="ruleForm.referenceFileServer" />
                    </el-form-item>
                    <el-form-item :label="$t('安全级别')" prop="authenticate">
                        <el-radio-group v-model="ruleForm.authenticate">
                            <el-radio label="none">{{ $t('无认证') }}</el-radio>
                            <el-radio label="weak">{{ $t('弱认证') }}</el-radio>
                            <el-radio label="force">{{ $t('强认证') }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t('安全级别')" prop="useCheckFile">
                        <el-radio-group v-model="ruleForm.useCheckFile">
                            <el-radio :label="true">{{ $t('开') }}</el-radio>
                            <el-radio :label="false">{{ $t('关') }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    
                </el-form>
            </template>
            <template #footer>
                <div style="flex: auto">
                    <el-button type="primary" @click="submitPermission" :title="$t('确认')">
                        <span class="btn-text">{{ $t('确认') }}</span>
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import GList from '@/components/GList.vue'
import CreateComForm from '@/components/CreateComForm.vue'
import { getThirdPartyList, createThirdPartyApi, delThirdParty, disableThirdParty, enableThirdParty, resetThirdParty, setThirdPartyValidityPeriod, getLicense, setThirdServerConf } from "@/services/system"
import type { TableColumn, TablePagination, TableDataParams, Explose } from '@/model/table'
import { useForm } from "@/hooks/useForm"
import { disabledDate } from '@/utils/index'
import type { CreateThirdPartyParams, ThirdParty } from '@/model/thirdParty'
import { validateUserName } from '@/utils/validate'
import { $t } from '@/locales'

const refresh = ref(Date.now())
const dialogVisible = ref(false);
const dialogPermission = ref(false);

const currentUpdateThirdParty = ref();
const newValidityPeriod = ref();
const comForm = ref<Explose | null>(null)
const disabled = ref<boolean>(false)


interface RuleForm { 
    appKey: string
    authenticate: string
    dataServerMethod: string
    fileDownloadCheckUrl: string
    referenceFileServer: string
    thirdToken: string
    useCheckFile: boolean
}

const ruleForm = reactive<RuleForm>({
    appKey: '',
    authenticate: 'none',
    dataServerMethod: '',
    fileDownloadCheckUrl: '',
    referenceFileServer: '',
    thirdToken: '',
    useCheckFile: true
})

onMounted(async () => {
    const { data = {} } = await getLicense()
    disabled.value = data?.license?.every((item: any) => item?.license_limit?.sf === 0)
})

const validateAppKey = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error($t('请输入三方标识')))
    }
    else if (!validateUserName(value)) {
        callback(new Error($t("三方标识: 2~128个字符，只能使用字母、数字、下划线，需要以字母开头")))
    }
    else {
        callback()
    }
}
const validatePeriod = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error($t('请输入过期时间')))
    } else {
        callback()
    }
}
const validateName = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error($t('请输入三方名称')))
    } else {
        callback()
    }
}

const rules = reactive({
    name: [{ validator: validateName, trigger: 'blur' }, { validator: validateName, trigger: 'change' }],
    appKey: [{ validator: validateAppKey, trigger: 'blur' }, { validator: validateAppKey, trigger: 'change' }],
    validityPeriod: [{ validator: validatePeriod, trigger: 'blur' }, { validator: validatePeriod, trigger: 'change' }],
})

const { getListHandle, loading } = useForm({
    getListApi: (params: TableDataParams) => getThirdPartyList(params),
    val: null,
    name: ''
})

// 节点表单默认值
const default_value = {
    name: '',
    appKey: '',
    validityPeriod: '',
    enable: true,
} as unknown as ThirdParty

// 表格列配置
const columns = [
    // {
    //     type: "selection",
    //     width: "55"
    // },
    {
        label: 'ID',
        property: 'id',
        showOverflowTooltip: true,
        width: "100"
    },
    {
        label: $t('名称'),
        property: 'name',
    },
    {
        label: $t('标识'),
        property: 'appKey',
        showOverflowTooltip: true,
    },
    {
        label: $t('令牌'),
        property: 'token',
        showOverflowTooltip: true,
    },
    {
        label: $t('状态'),
        property: 'enable',
        slotName: 'enable',
        width: "65"
    },
    {
        label: $t('创建时间'),
        property: 'ctime',
        width: "160",
    },
    {
        label: $t('修改时间'),
        property: 'utime',
        width: "160",
    },
    {
        label: $t('令牌过期时间'),
        property: 'validityPeriod',
        width: "160",
    },
    {
        label: $t('操作'),
        slotName: 'action',
        fixed: 'right',
        width: "320",
    }
] as unknown as typeof TableColumn[]

// 分页配置
const pagination = {
    currentPage: 1,
    pageSizes: [10, 20, 30, 40],
    limit: 10,
    layout: "total, sizes, prev, pager, next, jumper",
} as unknown as TablePagination

// 列表操作项按钮配置
const menuList = [{
    key: 'batchDelete',
    label: $t('批量删除'),
}]

// 搜索栏配置
const searchList = [{
    key: 'name',
    type: 'input',
    name: $t('名称'),
    placeholder: $t('名称')
}, {
    key: 'appKey',
    type: 'input',
    name: $t('标识'),
    placeholder: $t('标识')
}, {
    key: 'token',
    type: 'input',
    name: $t('令牌'),
    placeholder: $t('令牌'),
}]

const beforeSwitchChange = async (val: ThirdParty) => {
    const title = val.enable ? $t('禁用') : $t('启用');
    const fn = val.enable ? disableThirdParty : enableThirdParty;
    return new Promise<boolean>((resolve) => {
        ElMessageBox.alert(
            $t('你确定要{action}{name}令牌吗', { action: title, name: val.name || $t("这个") }),
            title,
            {
                confirmButtonText: $t("确认"),
                cancelButtonText: $t("取消"),
                type: "warning",
            }
        )
            .then(async () => {
                try {
                    await fn(val.id);
                    ElMessage({
                        type: "success",
                        message: $t('{action}成功', { action: title }),
                    });
                    resolve(true);
                    refresh.value = Date.now();
                } catch (e) {
                    console.log(e);
                    resolve(false);
                }
            })
            .catch(() => {
                resolve(false);
            });
    });
};

// 显示创建任务类型弹窗
const addThirdParty = () => {
    if (disabled.value){
        ElMessage({type: 'error',message: $t('三方调用未获得授权')})
        return;
    } 
    if (comForm.value) {
        comForm.value.show();
    }
}

// 创建/编辑任务类型
const handleCreateThirdParty = async (data: CreateThirdPartyParams, isUpdate: boolean) => {
    try {
        await createThirdPartyApi(data as CreateThirdPartyParams);
        if (comForm.value) {
            comForm.value.isShow = false
        }
        refresh.value = Date.now();
        ElMessage({
            type: 'success',
            message: $t('{action}三方成功', { action: isUpdate ? $t('修改三方') : $t('创建三方') }),
        })
    } catch (e) {
        console.log(e);
    }
}

// 重置令牌
const resetToken = async (val: ThirdParty) => {
    ElMessageBox.alert($t('你确定要重置{name}令牌吗', { name: val.name }) + '?', $t('重置'), {
        confirmButtonText: $t("确认"),
        cancelButtonText: $t("取消"),
        type: "warning",
    }).then(async () => {
        try {
            await resetThirdParty(val.id);
            ElMessage({
                type: 'success',
                message: $t('重置成功'),
            })
            refresh.value = Date.now();
        } catch (e) {
            console.log(e);
        }
    })
}

// 设置过期时间
const updateValidityPeriod = (val: ThirdParty) => {
    // TODO
    dialogVisible.value = true;
    currentUpdateThirdParty.value = val;
}

const submitForm = async () => {
    try {
        setThirdPartyValidityPeriod(currentUpdateThirdParty.value.id, newValidityPeriod.value)
        ElMessage({
            type: 'success',
            message: $t('设置成功'),
        })
        dialogVisible.value = false;
        refresh.value = Date.now();
    } catch (e) {
        console.log(e);
    }
}

const reset = () => {
    currentUpdateThirdParty.value = '';
    newValidityPeriod.value = '';
}

// 删除三方账户
const deleteThirdParty = async (val: ThirdParty) => {
    const { deleteHandle } = useForm({
        delApi: (id) => delThirdParty(id),
        val: val,
        name: $t('三方账户')
    })
    deleteHandle({params: val.id}).then((v: number) => {
        refresh.value = v;
    });
}

const openSetPermission = (val: any) => {
    dialogPermission.value = true
    
    ruleForm.appKey = val.appKey
    ruleForm.authenticate = val.authenticate
    ruleForm.dataServerMethod = val.dataServerMethod
    ruleForm.fileDownloadCheckUrl = val.fileDownloadCheckUrl
    ruleForm.referenceFileServer = val.referenceFileServer
    ruleForm.thirdToken = val.thirdToken
    ruleForm.useCheckFile = val.useCheckFile
}
const submitPermission = async () => {
    try {
        setThirdServerConf(ruleForm)
        ElMessage({
            type: 'success',
            message: $t('设置成功'),
        })
        dialogPermission.value = false;
        setTimeout(() => {
            refresh.value = Date.now();
        }, 50)
    } catch (e) {
        console.log(e);
    }
}
</script>

<style></style>