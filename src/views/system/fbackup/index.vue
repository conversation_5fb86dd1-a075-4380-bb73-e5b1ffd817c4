<template>
  <div class="task-settings layout-block" v-loading="loading" @click.stop>
    <el-form ref="ruleFormRef" label-position="right" :model="ruleForm" label-width="200px" style="max-width: 555px"
      class="demo-ruleForm" status-icon :rules="rules">
      <el-form-item prop="resource">
        <template #label>
          <span :title="$t('是否启用')" class="form-label-text">{{ $t('是否启用') }}</span>
        </template>
        <el-switch v-model="ruleForm.enable" />
      </el-form-item>
      <el-form-item>
        <template #label>
          <span :title="$t('备份文件名(范例)')" class="form-label-text">{{ $t('备份文件名(范例)') }}</span>
        </template>
        <span>{{ example_name }}</span>
      </el-form-item>
      <el-form-item prop="prefix">
        <template #label>
          <span :title="$t('备份文件前缀')" class="form-label-text">{{ $t('备份文件前缀') }}</span>
        </template>
        <el-input v-model="ruleForm.prefix" width="200px" clearable />
      </el-form-item>
      <el-form-item>
        <template #label>
          <span :title="$t('备份文件日期样式')" class="form-label-text">{{ $t('备份文件日期样式') }}</span>
        </template>
        <span>{{ filename_date_format }}</span>
      </el-form-item>
      <el-form-item prop="backup_path">
        <template #label>
          <span :title="$t('备份路径')" class="form-label-text">{{ $t('备份路径') }}</span>
        </template>
        <!-- <el-input v-model="ruleForm.name" /> -->
        <el-input v-model="ruleForm.backup_path" width="200px" clearable />
      </el-form-item>
      <el-form-item prop="checked_cycle">
        <template #label>
          <span :title="$t('备份策略')" class="form-label-text">{{ $t('备份策略') }}</span>
        </template>
        <el-radio-group v-model="ruleForm.checked_cycle">
          <el-radio v-for="item in cycleList" :key="item.type" :label="item.type">{{ item.name }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="backup_time">
        <template #label>
          <span :title="$t('备份开始时间')" class="form-label-text">{{ $t('备份开始时间') }}</span>
        </template>
        <el-date-picker v-model="ruleForm.backup_time" type="datetime" :placeholder="$t('备份开始时间')"
          :disabled-date="(time: Date) => disabledDate(time, 'date')"
          :disabled-hours="(time: Date) => disabledDate(ruleForm.backup_time, 'hour')"
          :disabled-minutes="(time: Date) => disabledDate(ruleForm.backup_time, 'minute')"
          :disabled-seconds="(time: Date) => disabledDate(ruleForm.backup_time, 'second')" />
      </el-form-item>
      <el-form-item prop="checked_content">
        <template #label>
          <span :title="$t('备份内容')" class="form-label-text">{{ $t('备份内容') }}</span>
        </template>
        <!-- <el-input
          v-model="ruleForm.content"
          width="200px"
        /> -->
        <el-checkbox-group v-model="ruleForm.checked_content">
          <el-checkbox v-for="item in contentList" :key="item.type" :label="item.type"> {{ item.name }} </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item>
        <div class="button-group">
          <el-button type="primary" @click="onSubmit(ruleFormRef)" :title="$t('提交')">
            <span class="btn-text">{{ $t('提交') }}</span>
          </el-button>
          <el-button @click="resetForm(ruleFormRef)" :title="$t('重置')">
            <span class="btn-text">{{ $t('重置') }}</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <el-divider></el-divider>
    <el-form ref="recoveryRuleFormRef" :model="recoveryRuleForm" label-position="left" label-width="200px"
      style="max-width: 555px" :rules="recoverRules">
      <el-form-item :label="$t('系统备份恢复')" prop="backup_file_path">
        <el-input v-model="recoveryRuleForm.backup_file_path" :placeholder="$t('请输入')"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="recover(recoveryRuleFormRef)">
          <span class="btn-text">{{ $t('恢复') }}</span>
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="systemBasic">
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { getSystemInfo, putBackupInfo, recoverySystem } from "@/services/system"
import type { PutBackupInfoQuery } from '@/model/system'
import { disabledDate, getRequireRules } from '@/utils/index'
import { $t } from '@/locales'

let loadingInstance: any;
const ruleFormRef = ref<FormInstance>()
const recoveryRuleFormRef = ref<FormInstance>()
const loading = ref(false)
const ruleForm = reactive<PutBackupInfoQuery>({
  enable: false,
  prefix: '',
  backup_path: '',
  checked_cycle: '',
  checked_content: [],
  backup_time: ''
})

const recoveryRuleForm = reactive({
  backup_file_path: '' // 备份文件路径
})

interface contentItem {
  enable?: boolean
  name: string
  type: string
}
// 备份内容列表
const contentList = ref<contentItem[]>([])

// 定义备份文件名范例
const example_name = ref('')

// 定义日期格式
const filename_date_format = ref('')

const rule = getRequireRules([{ key: 'prefix' }, { key: 'backup_path' }, { key: 'checked_cycle', action: $t('选择') }, { key: 'backup_time' }, { key: 'checked_content', action: $t('选择') }]) || {}

const rules = reactive(rule)

const validatePath = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error($t('请输入备份文件路径')))
  } else {
    callback()
  }
}

const recoverRules = reactive({
  backup_file_path: [{ validator: validatePath, trigger: 'blur' }, { validator: validatePath, trigger: 'change' }],
})

// 备份策略列表
const cycleList = ref<contentItem[]>([])

const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      putTaskInfoReq()
    } else {
      console.log('error submit!', fields)
    }
  })
}

const recover = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: $t('系统备份恢复中...'),
        // background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'loading-custom-class'
      });
      try {
        await recoverySystem(recoveryRuleForm.backup_file_path)
        ElMessage({
          type: 'success',
          message: $t('恢复完成'),
        })
        recoveryRuleForm.backup_file_path = ""
      } catch (e) {
        console.log(e);
      } finally {
        loadingInstance.close()
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 修改任务信息请求
const putTaskInfoReq = async () => {
  try {
    const res = await putBackupInfo(ruleForm)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: $t('修改成功'),
      })
      init()
    } else {
      ElMessage({
        type: 'error',
        message: res.msg,
      })
    }
  } catch (error) {
    console.log(error)
  }
}

// 重置数据
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

async function init() {
  try {
    loading.value = true
    const res = await getSystemInfo()
    if (res.code === 0) {
      ruleForm.enable = res.data.backup.enable
      ruleForm.prefix = res.data.backup.prefix
      ruleForm.backup_path = res.data.backup.backup_path
      ruleForm.checked_cycle = res.data.backup.checked_cycle
      ruleForm.checked_content = res.data.backup.checked_content
      ruleForm.backup_time = res.data.backup.backup_time
      contentList.value = res.data.backup.content
      cycleList.value = res.data.backup.cycle
      example_name.value = res.data.backup.example_name
      filename_date_format.value = res.data.backup.filename_date_format
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
init()

</script>
<style scoped>
.el-descriptions {
  margin-top: 20px;
}

:deep(.el-input-number) {
  margin-left: 0;
}
</style>