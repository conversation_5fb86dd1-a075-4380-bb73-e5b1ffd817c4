import { ref, shallowRef, onUnmounted, onMounted } from "vue";
import { gstarSDKEvents, type gstarSDKInstance } from "@/model/gstarSDK";
import { GSTAR_SDK_DYNAMIC_PW, upload_file_type } from "@/utils/const";
import { getGlobalObject, getFileSha1 } from "@/utils";
import { setFileMeta, getFileMeta } from "@/services/file";
import { type FileItem, EFILEMATEKEY } from "@/model/file";
import {
    downloadFileByStorageId,
    uploadFileByStorageId,
} from "@/services/file";
import { getTransformPDFTaskConfig } from "@/utils/transform";

interface Props {
    sdkParams: gstarSDKInstance;
    sdkUrl: string;
    fileItem: {
        fileName: string;
        fileId: number;
    };
    getMetaApi?: (
        retries: number,
        layoutName?: string,
        metaKey?: string,
        taskConfig?: any,
        isPrint?: boolean
    ) => Promise<any>;
}

const events = ["switchLayout", "noteChange"];
const METAKEY = "note";

export function useSDK(props: Props) {
    const { sdkParams, sdkUrl, fileItem, getMetaApi } = props;
    const gstarSDK = ref<any>(null);

    /** 实例化sdk */
    const initSDK = async () => {
        if (!gstarSDK.value) {
            try {
                await getGlobalObject(sdkUrl, "Module");
                gstarSDK.value = new window.GStarSDK(sdkParams);
                gstarSDK.value.setDynamicPW(GSTAR_SDK_DYNAMIC_PW);
                gstarSDK.value.enableZoom();
                gstarSDK.value.enablePan();
                initOperation();
                listenEvents();
            } catch (e) {
                console.log("实例化sdk失败,原因是: ", e);
            }
        }
    };

    /** 初始化操作 */
    const initOperation = () => {
        gstarSDK.value.func.save.savePDF.customSavePDF = saveAsPDF;
        gstarSDK.value.func.save.saveAs.customSaveAs = saveAs;
        gstarSDK.value.func.save.print.customPrint = print;
    };
    /** 监听event事件 */
    const listenEvents = () => {
        events.forEach((event) => {
            gstarSDK.value.on(event, (eventName: string, data: any) => {
                const value =
                    Object.prototype.toString.call(data) === "[object Object]"
                        ? JSON.parse(JSON.stringify(data))
                        : data;
                eventHandle(event, value);
            });
        });
    };

    // 显示批注
    const showNotes = async () => {
        try {
            const { data } = await getFileMeta({
                fileId: fileItem.fileId,
                metaKey: METAKEY,
            });
            console.log("data", data);
            if (data.note) {
                const res = await downloadFileByStorageId(
                    {
                        storageId: JSON.parse(data.note).storageId,
                        fileName: fileItem.fileName,
                    },
                    "json"
                );
                gstarSDK.value.notes.show(res);
            }
        } catch (e) {
            console.log(e);
        }
    };

    // 另存为
    const saveAs = (params: any) => {
        console.log("params", params);
        gstarSDK.value.Tips.showProgress(
            0,
            gstarSDK.value.locals.get("converting")
        );
        if (getMetaApi)
            getMetaApi(1, "", EFILEMATEKEY.SAVE_DWG_META, {
                dwgVersionType: params.dwgver,
            });
    };

    // 另存为PDF
    const saveAsPDF = async (params: any) => {
        const taskConfig = getTransformPDFTaskConfig(params);
        console.log("taskConfig", taskConfig);
        gstarSDK.value.Tips.showProgress(
            0,
            gstarSDK.value.locals.get("converting")
        );
        if (getMetaApi)
            getMetaApi(1, "", EFILEMATEKEY.SAVE_PDF_META, taskConfig);
    };

    // 打印
    const print = (params: any) => {
        const taskConfig = getTransformPDFTaskConfig(params);
        gstarSDK.value.Tips.showProgress(
            0,
            gstarSDK.value.locals.get("converting")
        );
        if (getMetaApi)
            getMetaApi(1, "", EFILEMATEKEY.SAVE_PDF_META, taskConfig, true);
    };

    // eventHandle
    const eventHandle = (event: string, value: Recordable) => {
        switch (event) {
            case gstarSDKEvents.LAYOUT_CHANGE:
                switchLayout(value);
                break;
            case gstarSDKEvents.NOTES_CHANGE:
                noteChange(value);
                break;
            default:
                return;
        }
    };

    // 改变布局
    const switchLayout = (val: any) => {
        // TODO
        console.log(val);
        const { globalName } = val;
        if (globalName && getMetaApi) getMetaApi(1, globalName);
    };

    // 改变批注
    const noteChange = async (value: Recordable) => {
        try {
            const data = await uploadFileToStorage(
                JSON.stringify(gstarSDK.value.notes.data)
            );
            setFileMeta({
                fileId: fileItem.fileId,
                metaKey: METAKEY,
                value: JSON.stringify(data),
                version: '1'
            });
        } catch (e) {
            console.log(e);
        }
    };
    const uploadFileToStorage = async (val: string) => {
        const blob = new Blob([val], {
            type: "application/json",
        });
        const file = new File([blob], fileItem.fileName, {
            type: "application/json",
        });
        const sha1 = await getFileSha1(file);
        const { data } = await uploadFileByStorageId({
            attribute: JSON.stringify({
                replace: true,
                attach_to: fileItem.fileId,
                digest: sha1,
                file_type: upload_file_type.NOTE,
            }),
            file,
        });
        return data;
    };
    // 注销sdk
    const destroySDK = () => {
        try {
            if (gstarSDK.value) {
                gstarSDK.value.destroy();
                gstarSDK.value = null;
            }
        } catch (e) {
            console.log(e);
        }
    };

    onMounted(() => {
        initSDK();
    });

    /** 卸载sdk */
    onUnmounted(() => {
        destroySDK();
    });

    return {
        gstarSDK,
        showNotes,
        destroySDK,
        initSDK,
    };
}
