/** 上传hook函数,需要支持文件文件夹上传,断点续传和秒传等功能 */
import type { NewUploadFile } from "@/model/file";
import { uploadState, type UploadItem } from "@/stores/upload";
import type { UploadUserFile } from "element-plus";
import { emitter } from "@/utils/mitt";
import { ref } from "vue";
import { guid, getExt, getShortName, formatSize } from "@/utils";
import { UPLOAD_STATUS } from "@/utils/const";

import UploadManager from "@/utils/UploadManager"

import { useUserStore } from "@/stores/user"

interface Props {
    toFolderId: number;
    fileList: UploadUserFile[];
    interfaceFun?: (data: any, cb: any) => void;
    type?: number;
    replace: boolean;
}
export function useUpload(props: Props) {
    // TODO
    const fileList = ref<UploadUserFile[]>(props.fileList);
    const uploadStore = uploadState();
    // 声明上传列表
    const uploadFileList = ref<UploadItem[]>([]);
    const userStores = useUserStore();
    const token = userStores.token;

    // 创建请求
    const createReq = async (file: NewUploadFile, uid: string) => {
        const type = props.type ? props.type : 1
        // console.log("**********************", file.raw)
        const upload = new UploadManager({
            file: file?.raw || file || "",
            type,
            folderId: props.toFolderId,
            replace: props.replace,
            path: type === 1 ? file.raw?.webkitRelativePath : "",
            token,
            uid
        })


        try {
            upload.start(uid)
            upload.on("progress", (ename: string, res: any) => {
                const progressPercent = (res.loaded / res.total * 100).toFixed(2);
                // console.log("progressPercent", res?.uid, res, progressPercent)
                uploadStore.updateUploadReqProgress(res?.uid, parseFloat(progressPercent))
            })

            upload.on("success", (ename: string, res: any) => {
                // console.log("success", res)
                // 刷新列表
                // emitter.emit("refreshFile", 'file');

                // 更新上传完成当前这一项的状态
                uploadStore.updateUploadReqState(res?.uid, UPLOAD_STATUS.SUCCESS);

                // 更新上传完成当前这一项在上传中的数据
                uploadStore.updateUploadInState(res?.uid, UPLOAD_STATUS.SUCCESS);

                // 更新上传中的个数
                uploadStore.updateUploadReqIn();

                // 更新完成上传数量
                uploadStore.computedFinishUpload();
            })


            // if (props.interfaceFun) {
            //     uploadQuery.replace = false
            //     await props.interfaceFun(uploadQuery, getUploadProgress);
            // } else {
            //     uploadQuery.path = file.raw?.webkitRelativePath,
            //     await uploadFile(uploadQuery, getUploadProgress);
            // }


        } catch (error: any) {
            console.error('err', error)
            // 更新上传完成当前这一项的状态
            uploadStore.updateUploadReqState(uid, UPLOAD_STATUS.FAIL);

            // 更新上传完成当前这一项在上传中的数据
            uploadStore.updateUploadInState(uid, UPLOAD_STATUS.FAIL, error.msg);

            // 更新上传中的个数
            uploadStore.updateUploadReqIn();

            // 更新完成上传数量
            uploadStore.computedFinishUpload();
        }
    };

    // 生成上传请求数据并存储
    const createUploadReq = () => {
        fileList.value?.forEach((file: any) => {
            const obj: UploadItem = {
                name: file.name,
                size: file.size || 0,
                formatSize: formatSize(file.size || 0),
                status: 0,
                shortName: getShortName(file.name),
                nameExt: getExt(file.name),
                fn: createReq,
                uid: guid(),
                file,
            };
            uploadFileList.value.unshift(obj);
        });

        setTimeout(updateData, 200);
        return fileList.value?.length;
    };

    // 更新store中的上传数据
    const updateData = function () {
        uploadStore.updateUploadReqArr(uploadFileList.value);
        uploadStore.updateUploadReqIn();
        uploadFileList.value = [];
    };
    return {
        fileList,
        createUploadReq,
    };
}
