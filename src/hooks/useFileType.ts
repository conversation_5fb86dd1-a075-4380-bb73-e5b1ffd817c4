import { onMounted, ref } from "vue";
import { getFileTypes } from "@/services/file";

export function useFileType() {
    const fileTypes = ref()
    const limitTypes = ref('')

    const getFileType = async () => {
        try {
            const { code, data } = await getFileTypes()
            if (code === 0) {
                const types = data.typeList
                const typeKeys = Object.keys(types) 
                fileTypes.value = Object.fromEntries(
                    Object.entries(types).map(([key, value]) => [value, key])
                )
                limitTypes.value = typeKeys.filter(v => v !== 'dir').map(v => '.' + v).join()
            }
        } catch (error: any) {
            console.log(error)
        }
    }

    onMounted(() => {
        getFileType()
    })

    return {
        fileTypes,
        limitTypes
    }
}
