import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from "@/stores/user"

export const useLeaveEdit = () => {
  const { currentRoute } = useRouter()
  const route = currentRoute.value
  const fileId = route.query.id

  onMounted(() => {
    window.addEventListener('beforeunload', (e: any) => beforeunloadHandler(e))
  })

  const beforeunloadHandler = (event: any) => {
    // console.log(`%c event123456 ${event.currentTarget.performance.navigation.type}`, 'color: #0f0;font-size: 30px;')
    if (event.currentTarget.performance.navigation.type !== 1) {
      leaveEdit()
    }
  }

  const leaveEdit = () => {
    const id = fileId as string
    const userStores = useUserStore();
    const token = userStores.token;
    if ((window as any).navigator.sendBeacon) {
      const api_endpoint = `${window.$globalConfig.api_endpoint}/api/v2/_edit/_leave?Authorization=${token}`;
      navigator.sendBeacon(api_endpoint, JSON.stringify({
        fileId: Number(id),
        devId: localStorage.getItem('browserId') ?? ''
      }));
    } else {
      fetch(`${window.$globalConfig.api_endpoint}/api/v2/_edit/_leave`, {
        headers: {
          Authorization: token
        },
        method: 'POST',
        body: JSON.stringify({
          fileId: Number(id),
          devId: localStorage.getItem('browserId') ?? ''
        })
      })
    }
  }
};
