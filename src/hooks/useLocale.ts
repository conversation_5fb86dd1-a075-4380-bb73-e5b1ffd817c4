import { i18n } from "@/locales";
import { useLocaleStoreWithOut } from "@/stores/locale";
import { unref, computed } from "vue";
import type { LocaleType } from "@/model/public";

function setI18nLanguage(locale: LocaleType) {
    const localeStore = useLocaleStoreWithOut();

    if (i18n.mode === "legacy") {
        i18n.global.locale = locale;
    } else {
        (i18n.global.locale as any).value = locale;
    }
    localeStore.setLocaleInfo({ locale });
    document.querySelector("html")?.setAttribute("lang", locale);
}

export function useLocale() {
    const localeStore = useLocaleStoreWithOut();
    const getLocale = computed(() => localeStore.getLocale);

    async function changeLocale(locale: LocaleType) {
        const globalI18n = i18n.global;
        const currentLocale = unref(globalI18n.locale);
        if (currentLocale === locale) {
            return locale;
        }
        setI18nLanguage(locale);
        return locale;
    }

    return {
        getLocale,
        changeLocale,
    };
}
