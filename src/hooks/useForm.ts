import { ref } from "vue";
import { getEnableStatus } from "@/utils/const";
import type { TableDataParams } from "@/model/table";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance } from "element-plus";
import { $t } from '@/locales';

/** 列表公共操作hook */

// 定义入参格式
interface Props {
    delApi?: (val: any) => Promise<any>;
    editApi?: (id: string, data: any) => Promise<any>;
    getListApi?: (params: any) => Promise<any>;
    commonApi?: (params?: any) => Promise<any>;
    submitCallBack?: () => void;
    resetFormCallBack?: () => void;
    val: any;
    name: string;
    action?: string;
}

export function useForm(props: Props) {
    const {
        delApi,
        editApi,
        getListApi,
        commonApi,
        submitCallBack,
        resetFormCallBack,
        val,
        name,
        action,
    } = props;

    const refresh = ref<any>(null);
    const loading = ref(false);

    /* 调用list接口获取数据 */
    const getListHandle = async (params: TableDataParams) => {
        if (!getListApi) return;
        return new Promise<any>((resolve, reject) => {
            loading.value = true;
            const { page, limit, search } = params;
            let req = {
                page,
                limit,
            };
            if (search) {
                req = {
                    ...req,
                    ...search,
                };
            }
            getListApi(req)
                .then((res) => {
                    const { data } = res;
                    resolve({
                        list: data.list,
                        total: data.total,
                    });
                })
                .catch((e) => {
                    reject(e);
                })
                .finally(() => {
                    loading.value = false;
                });
        });
    };

    /* 调用删除接口请求数据 */
    const deleteHandle = (data?: Recordable) => {
        const { params , key } = data || { params: '', key: ''}
        return new Promise<any>((resolve) => {
            ElMessageBox.alert(
                $t('你确定要删除{name}{type}吗', { name: key ? val[key] : val.name || $t("这个"), type: name }),
                $t("删除"),
                {
                    confirmButtonText: $t("确认"),
                    cancelButtonText: $t("取消"),
                    type: "warning",
                }
            ).then(async () => {
                try {
                    if (!delApi) return true;
                    const { code } = await delApi(params);
                    if (code === 0) {
                        ElMessage({
                            type: "success",
                            message: $t("删除成功"),
                        });
                        resolve(Date.now());
                    }
                } catch (e) {
                    console.log(e);
                }
            });
        });
    };

    /* 调用编辑接口改变状态 */
    const editHandle = () => {
        return new Promise<boolean>((resolve) => {
            const ENABLE_STATUS = getEnableStatus();
            const key = Object.prototype.hasOwnProperty.call(val, "enable")
                ? "enable"
                : "status";
            ElMessageBox.alert(
                $t('你确定要{action}{name}{type}吗', {
                    action: ENABLE_STATUS[val[key] as number],
                    name: val.name || $t("这个"),
                    type: name
                }),
                ENABLE_STATUS[val[key] as number],
                {
                    confirmButtonText: $t("确认"),
                    cancelButtonText: $t("取消"),
                    type: "warning",
                }
            )
                .then(async () => {
                    try {
                        if (!editApi) return true;
                        await editApi(val.id, {
                            ...val,
                            [key]: 1 ^ (val[key] as number),
                        });
                        ElMessage({
                            type: "success",
                            message: $t('{action}成功', { action: ENABLE_STATUS[val[key] as number] }),
                        });
                        resolve(true);
                    } catch (e) {
                        console.log(e);
                        resolve(false);
                    }
                })
                .catch(() => {
                    resolve(false);
                });
        });
    };

    /** 重置表单 */
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) return;
        formEl.resetFields();
        if (resetFormCallBack) resetFormCallBack();
    };
    /** 提交表单 */
    const submitForm = (formEl: FormInstance | undefined) => {
        if (!formEl) return;
        formEl.validate(async (valid, fields) => {
            if (valid) {
                // TODO
                try {
                    if (submitCallBack) submitCallBack();
                } catch (e) {
                    console.log(e);
                }
            } else {
                console.log("error submit!", fields);
            }
        });
    };
    /* 调用删除接口请求数据 */
    const commonHandle = (data?: Recordable) => {
        const { params, key } = data || { params: '', key: ''}
        return new Promise<any>((resolve) => {
            ElMessageBox.alert(
                $t('你确定要{action}{name}{type}吗', {
                    action: action,
                    name: key ? val[key] : val.name || $t("这个"),
                    type: name
                }),
                action,
                {
                    confirmButtonText: $t("确认"),
                    cancelButtonText: $t("取消"),
                    type: "warning",
                }
            ).then(async () => {
                try {
                    if (!commonApi) return true;
                    const { code } = await commonApi(params);
                    if (code === 0) {
                        ElMessage({
                            type: "success",
                            message: $t('{action}成功', { action: action }),
                        });
                        resolve(Date.now());
                    }
                } catch (e) {
                    console.log(e);
                }
            });
        });
    };

    return {
        refresh,
        loading,
        deleteHandle,
        editHandle,
        getListHandle,
        commonHandle,
        resetForm,
        submitForm,
    };
}
