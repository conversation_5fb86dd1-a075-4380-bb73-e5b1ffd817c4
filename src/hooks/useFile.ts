import { computed } from 'vue';
import { isType } from '@/utils';
import type { FileItem } from '@/model/file';

export const useFile = (item: FileItem) => {
  // 判断是否是pdf文件
  const isPDF = computed(() => {
    return isType(item, 'pdf');
  });
  // 判断是否是图片
  const isImg = computed(() => {
    return isType(item, 'image');
  });
  // 判断是否是office
  const isOffice = computed(() => {
    return isType(item, 'word') || isType(item, 'excel') || isType(item, 'ppt');
  });
  // 判断是否是txt
  const isTxt = computed(() => {
    return isType(item, 'txt')
  });
  // 判断是否是markdown
  const isMarkdown = computed(() => {
    return isType(item, 'md');
  });
  // 判断是否可以预览
  const canPreview = computed(() => {
    return isPDF.value || isImg.value || isTxt.value || isMarkdown.value;
  });

  return {
    isPDF,
    isImg,
    isOffice,
    isTxt,
    isMarkdown,
    canPreview,
  };
};
