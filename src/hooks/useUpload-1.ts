/** 上传hook函数,需要支持文件文件夹上传,断点续传和秒传等功能 */
import type { NewUploadFile, UploadQuery } from "@/model/file";
import { uploadState, type UploadItem } from "@/stores/upload";
import type { UploadUserFile } from "element-plus";
import { emitter } from "@/utils/mitt";
import { ref } from "vue";
import { uploadFile } from "@/services/file";
import { guid, getExt, getShortName, formatSize, getFileSha1 } from "@/utils";
import { UPLOAD_STATUS } from "@/utils/const";
import { calcFileSha1 } from "@/utils/sha1";

interface Props {
    toFolderId: number;
    fileList: UploadUserFile[];
    interfaceFun?: (data: any, cb: any) => void;
    type?: string;
}
export function useUpload(props: Props) {
    // TODO
    const fileList = ref<UploadUserFile[]>(props.fileList);
    const uploadStore = uploadState();
    // 声明上传列表
    const uploadFileList = ref<UploadItem[]>([]);

    // 创建请求
    const createReq = async (file: NewUploadFile, uid: string) => {
        const type = props.type ? props.type : 'file'
        const uploadQuery: UploadQuery = {
            folderId: props.toFolderId,
            replace: true,
            etag: "",
            file: file.raw ? file.raw : "",
        };

        try {
            if (file.raw) {
                const logKey = `计算sha1 ${file.name} (size:${file.raw.size}) ${Math.random()}`
                console.time(logKey)
                if (file.raw.size > 1024 * 1024 * 1000) {
                    uploadQuery.etag = await calcFileSha1(file.raw, 0, sha1 => {
                        console.log(sha1)
                    }, null)
                } else {
                    uploadQuery.etag = await getFileSha1(file.raw);
                }
                console.timeLog(logKey, ' result:', uploadQuery.etag)
            }

            const getUploadProgress = (evt: ProgressEvent) => {
                const progressPercent = (evt.loaded / evt.total * 100).toFixed(2);
                uploadStore.updateUploadReqProgress(uid, parseFloat(progressPercent));
            }

            if (props.interfaceFun) {
                uploadQuery.replace = false
                await props.interfaceFun(uploadQuery, getUploadProgress);
            } else {
                uploadQuery.path = file.raw?.webkitRelativePath,
                    await uploadFile(uploadQuery, getUploadProgress);
            }

            // 刷新列表
            emitter.emit("refreshFile", type);

            // 更新上传完成当前这一项的状态
            uploadStore.updateUploadReqState(uid, UPLOAD_STATUS.SUCCESS);

            // 更新上传完成当前这一项在上传中的数据
            uploadStore.updateUploadInState(uid, UPLOAD_STATUS.SUCCESS);

            // 更新上传中的个数
            uploadStore.updateUploadReqIn();

            // 更新完成上传数量
            uploadStore.computedFinishUpload();
        } catch (error: any) {
            console.error('err', error)
            // 更新上传完成当前这一项的状态
            uploadStore.updateUploadReqState(uid, UPLOAD_STATUS.FAIL);

            // 更新上传完成当前这一项在上传中的数据
            uploadStore.updateUploadInState(uid, UPLOAD_STATUS.FAIL, error.msg);

            // 更新上传中的个数
            uploadStore.updateUploadReqIn();

            // 更新完成上传数量
            uploadStore.computedFinishUpload();
        }
    };

    // 生成上传请求数据并存储
    const createUploadReq = () => {
        fileList.value?.forEach((file: any) => {
            const obj: UploadItem = {
                name: file.name,
                size: file.size || 0,
                formatSize: formatSize(file.size || 0),
                status: 0,
                shortName: getShortName(file.name),
                nameExt: getExt(file.name),
                fn: createReq,
                uid: guid(),
                file,
            };
            uploadFileList.value.unshift(obj);
        });

        setTimeout(updateData, 200);
        return fileList.value?.length;
    };

    // 更新store中的上传数据
    const updateData = function () {
        uploadStore.updateUploadReqArr(uploadFileList.value);
        uploadStore.updateUploadReqIn();
        uploadFileList.value = [];
    };
    return {
        fileList,
        createUploadReq,
    };
}
