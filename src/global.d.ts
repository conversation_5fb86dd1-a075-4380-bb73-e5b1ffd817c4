/**  因为TS直接引用第三方JS库的时候，虽然可以用，但是是没有类型检查，所以我们需要做一个声明文件，来声明这些第三方库的类型，这样在使用第三方库的时候，就有类型了 **/
export {}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $ElIcon: any
  }
}
declare module "element-plus";
declare module '*.vue' {
  import Vue from 'vue'
  export default Vue
}

declare module "cloudCadUtils";

declare global {
  //读取key值
  type KeyofType<T> = keyof T  
  //读取值, P为keyof出来的key值
  type GetType<T,P> = P extends KeyofType<T> ? T[P]:never    
  //结果类似 keyof type1 | keyof type2 | keyof type3
  type MergeKeyType<T extends Array<any>> = T extends [infer X,...infer Rect]? KeyofType<X>|MergeKeyType<Rect>:never  
  //结果类似(P extends keyof type1 ? type1[P]:never)|(P extends keyof type2 ? type2[P]:never), 例:P等于num,则结果为number|string|boolean
  type MergeValueType<T extends Array<any>,P> = T extends [infer X,...infer Rect]? GetType<X,P>|MergeValueType<Rect,P>:never  
  //合并整个值
  type GetMerge<T extends Array<any>> = {
    [P in MergeKeyType<T>]:MergeValueType<T,P>
  }
}

declare module 'Frontend/generated/theme' {
  export function applyTheme(root: HTMLElement): void;
}
