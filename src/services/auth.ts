import Axios from "./index";
import type { ResponseData } from "@/model/public";
import { ContentType } from "@/utils/const";

export interface ILoginData {
    /** 登录类型 */
    type?: "user_name" | "email" | "phone";
    /** 用户名 */
    user_name?: string;
    /** 邮箱 */
    email?: string;
    /** 手机号 */
    phone?: string;
    /** 密码 */
    password: string;
    /** 重定向 */
    redirect?: string;
    /** 路径query参数 */
    otherQuery?: object;
}

// 登录
export const login = (params: any): Promise<ResponseData | any> => {
    const formData = new FormData();
    for (const key in params) {
        formData.append(key, params[key]);
    }
    return Axios({
        url: "/_user/_login",
        method: "post",
        data: formData,
        headers: {
            "Content-Type": ContentType.UPLOAD,
        },
    });
};

// 登出
export const logout = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_user/_logout",
        method: "post",
    });
};

// 获取权限列表树
export const getAuth = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_auth/_tree",
        method: "get",
    });
};

// 获取sdk权限
export const getSDKAuth = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_license/_jssdkformatinfo",
        method: "get",
    });
};

// 检查token是否失效
export const checkLogin = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_user/_check_login",
        method: "get",
    });
};