import Axios from "./index";
import type { ResponseData } from "@/model/public";
import type { TableDataParams } from "@/model/table";
import type { CreateNodeParams } from "@/model/node";
import { ContentType } from "@/utils/const";

// 获取节点列表
export const getNodeList = (
  params: TableDataParams
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_node/_getlist",
    method: "get",
    params,
  });
};

// 创建节点
export const createNodeApi = (
  data: CreateNodeParams
): Promise<ResponseData> => {
  return Axios.post(`/_node`, data);
};

// 删除节点
export const delNode = (id: string): Promise<ResponseData | any> => {
  return Axios({
    url: `/_node/${id}`,
    method: "delete",
  });
};

// 更新节点
export const editNode = (
  id: string,
  data: object
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_node/${id}`,
    method: "put",
    data: {
      ...data,
    },
  });
};

// 查询一个节点信息
export const getNodeInfo = (
  id: string | number
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_node/${id}`,
    method: "get",
  });
};

// 检测节点
export const checkNode = (id: string | number): Promise<ResponseData | any> => {
  return Axios({
    url: `/_node/${id}/_check`,
    method: "get",
  });
};

// 获取未绑定节点
export const getNotBindNodes = (
  params: TableDataParams
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_node/_notbind",
    method: "get",
    params,
  });
};

// 绑定一个节点
export const bindNode = (
  id: number,
  index: number
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_node/${id}/_bind`,
    method: "get",
    params: { index },
  });
};

// 下载一个客户端配置文件
export const downloadClientConfig = (
  id: string
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_node/_client_config_file/${id}`,
    method: "get",
  });
};

// 下载客户端程序
export const downloadClient = (
  id: string,
  os: string,
  cb: (evt: ProgressEvent) => void
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_node/_client`,
    method: "get",
    params: {
      os,
      id,
    },
    responseType: "arraybuffer",
    onDownloadProgress: cb
  });
};
