import Axios from "./index";
import type { ResponseData } from "@/model/public";
import type { TableDataParams } from "@/model/table";
import type { CreateTaskTypeParams } from "@/model/taskScheduler";

// 获取列表
export const getPlanTaskListApi = (
    params: TableDataParams
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_tasktype/_getPlanTaskList",
        method: "get",
        params,
    });
};

// 获取任务类型列表
export const getTaskTypeApi = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_tasktype/_getPlanTaskTypeList",
        method: "get",
    });
};

// 创建任务
export const createTaskApi = (
    data: CreateTaskTypeParams
): Promise<ResponseData> => {
    return Axios.post(`/_tasktype/_addPlanTask`, data);
};

// 删除任务
export const delTaskApi = (
    id: string | number
): Promise<ResponseData> => {
    return Axios.post(`/_tasktype/_removePlanTask`, { id });
};

// 执行任务
export const runTaskApi = (
    id: string | number
): Promise<ResponseData> => {
    return Axios.post(`/_tasktype/_runPlanTask`, { id });
};

// 更新任务
export const editTaskApi = (
    id: string | number,
    data: object
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_tasktype/_systask/${id}`,
        method: "put",
        data: {
            ...data,
        },
    });
};

// 获取记录列表
export const getRecordListApi = (
    params: TableDataParams
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_tasktype/_getPlanTaskRecordList",
        method: "get",
        params,
    });
};
