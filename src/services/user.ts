import Axios from "./index";
import type { ResponseData } from "@/model/public";
import type { CreateUserParams } from "@/model/user";
import type { TableDataParams } from "@/model/table";
import { ContentType } from "@/utils/const";
import type { ExportParams } from "@/model/group";

/**
 * @function 创建用户
 * @description 用来添加用户
 * @param email {String} 用户邮箱
 * @param first_name {String} 名
 * @param last_name {String} 姓
 * @param menu_ids {Array} 用户组ID
 * @param password {String} 密码
 * @param phone {String} 手机号
 * @param status {Number} 状态 0 禁用 1 启用
 * @param user_name {String} 用户名称
 * @return void
 * <AUTHOR>
 */

// 创建用户
export const createUserApi = (
  data: CreateUserParams
): Promise<ResponseData> => {
  return Axios.post(`/_user`, data);
};

// 获取用户列表
export const getUserList = (
  params: TableDataParams
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_user/_getlist",
    method: "get",
    params,
  });
};

// 查询某一个用户信息
export const getUserById = (id: string): Promise<ResponseData | any> => {
  return Axios({
    url: `/_user/${id}`,
    method: "get",
  });
};

// 获取用户自身信息
export const getUserSelfInfo = (): Promise<ResponseData | any> => {
  return Axios({
    url: `/_user/_self_info`,
    method: "get",
  });
};

// 更新用户自身信息
export const changeUserSelfInfo = (
  params: Recordable
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_user/_self_info`,
    method: "patch",
    data: {
      avatar: params.avatar,
      email: params.email,
      phone: params.phone
    },
  });
};

// 删除用户
export const delUser = (id: string): Promise<ResponseData | any> => {
  return Axios({
    url: `/_user/${id}`,
    method: "delete",
  });
};

// 更新用户
export const editUser = (
  id: string,
  data: object
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_user`,
    method: "put",
    data: {
      ...data,
    },
  });
};

// 用户修改密码
export const editUserPwd = (params: { [key: string]: string }) => {
  const formData = new FormData();
  for (const key in params) {
    formData.append(key, params[key]);
  }
  return Axios({
    url: "/_user/_alter_passwd",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": ContentType.UPLOAD,
    },
  });
};

// 重置用户密码
export const resetUserPwd = (params: { [key: string]: string }) => {
  const formData = new FormData();
  for (const key in params) {
    if (params[key]) formData.append(key, params[key]);
  }
  return Axios({
    url: "/_user/_reset_passwd",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": ContentType.UPLOAD,
    },
  });
};

// 获取配置了权限的用户列表
export const getAuthUserList = (
  params: TableDataParams
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_user/_auth/_getlist",
    method: "get",
    params,
  });
};

// 模糊搜索用户
export const getUserBySearch = (
  keyword: string
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_user/_search",
    method: "get",
    params: { keyword },
  });
};

// 设置用户权限
export const updateUserAuth = (
  data: Recordable,
  id: number | string
): Promise<ResponseData> => {
  return Axios.post(`/_user/_auth/${id}`, data);
};

// 批量设置用户权限
export const updateUserAuthBatch = (
  data: Recordable
): Promise<ResponseData> => {
  return Axios.post(`/_user/_auth/_batch`, data);
};

// 禁用用户
export const disableUser = (
  id: string | number
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_user/_disable`,
    method: "post",
    data: {
      id,
    },
  });
};

// 启用用户
export const enableUser = (
  id: string | number
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_user/_enable`,
    method: "post",
    data: {
      id,
    },
  });
};

// 导入
export const importUser = (file: File): Promise<ResponseData | any> => {
  const formData = new FormData();
  formData.append("file", file);
  return Axios({
    url: "/_user/_import",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": ContentType.UPLOAD,
    },
  });
};

// 导出
export const exportUser = (
  params: ExportParams
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_user/_export",
    method: "get",
    params,
    responseType: "arraybuffer",
    headers: {
      "Content-Type": ContentType.BINARY,
    },
  });
};

// 下载模板
export const downloadImportUserTemplate = (): Promise<ResponseData | any> => {
  return Axios({
    url: "/_user/_download_import_template",
    method: "get",
    responseType: "arraybuffer",
    headers: {
      "Content-Type": ContentType.BINARY,
    },
  });
};
