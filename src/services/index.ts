/** 封装axios
 *  目前简单暴漏axios实例，没有过度封装，待后续复杂后考虑添加一层，缺少retry ,batch等
 *  目前兼容俩种方式,后面慢慢合并为一个
 */
import axios from "axios";
import type {
  AxiosResponse,
  AxiosRequestConfig,
  AxiosError,
  AxiosInstance,
} from "axios";
import type { ResponseData } from "@/model/public";
import { ContentType, noPermissionCode, whiteList } from "@/utils/const";
import { getErrorMessage } from "@/utils/http-message";
import { useUserStore } from "@/stores/user";
import { isNetworkError } from "@/utils/httpUtil";
import { emitter } from "@/utils/mitt";
import gstarMessage from "@/utils/showMessage";
import { $t } from '@/locales';
import { ElMessageBox } from "element-plus";

const DEFAULT_HEADERS = { "Content-Type": ContentType.JSON };
let messageBoxFlag = 0;

const messageOnce = new gstarMessage()

const defaultConfig: AxiosRequestConfig = {
  // baseURL: import.meta.env.VITE_API_URL as any,
  timeout: 1000 * 60 * 30,
  headers: { "Content-Type": ContentType.JSON },
};

// 配置新建一个 axios 实例
const service = axios.create({
  // baseURL: import.meta.env.VITE_API_URL as any,
  timeout: 1000 * 60 * 30,
  headers: { "Content-Type": ContentType.JSON },
});

const errorHandler = (code: number, msg: string, ignore = false) => {
  if (noPermissionCode.includes(code)) {
    if (messageBoxFlag === 0) {
      // `token` 过期或者账号已在别处登录
      messageBoxFlag = 1;
      ElMessageBox.alert($t("token已失效，请重新登录"), $t("提示"), {
        confirmButtonText: "OK",
        showClose: false,
      }).then(() => {
        messageBoxFlag = 0;
        emitter.emit("logout");
      });
    }
  } else if (code === 500000) {
    // 系统未安装跳转到安装页
    // ElMessage.error(msg);
    // const userStores = useUserStore();
    // userStores.gotoInstall();
  } else {
    if (!ignore) {
      messageOnce.error(msg)
      // ElMessage.error(msg);
    }
  }
};

// 添加请求拦截器
service.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 获取url
    config.url = getUrl(config.url);
    // 在发送请求之前做些什么 token
    const userStores = useUserStore();
    const token = userStores.token;
    // 有一些白名单的请求不需要配置token， 比如登录...
    const isWhiteRequest = whiteList.includes(window.location.pathname);
    if (token)
      (<any>config.headers).common["Authorization"] = token;

    config.headers = { ...DEFAULT_HEADERS, ...config.headers };
    //根据配置文件动态设置baseurl
    const { api_endpoint } = window.$globalConfig;
    config.baseURL = api_endpoint;
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 添加响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 对响应数据做点什么
    const res: ResponseData = response.data;
    if (res?.code) {
      errorHandler(
        res.code,
        $t(`resCode.${res.code}`) !== `resCode.${res.code}`
          ? $t(`resCode.${res.code}`)
          : res.msg,
        res.code === 440600
      );
      return Promise.reject(res);
    }
    return Promise.resolve(res);
  },
  (error: AxiosError) => {
    // 对响应错误做点什么
    const errorMessage = getErrorMessage(error);
    errorHandler(error.response?.status as number, errorMessage);
    return Promise.reject(error);
  }
);

// 后续判断url逻辑
const getUrl = (url = "") => {
  if (!url) return "";
  return `${import.meta.env.VITE_GLOB_API_URL_PREFIX as string}/${
    import.meta.env.VITE_GLOB_API_URL_VERSION as string
  }${url}`;
};

class Http {
  // TODO 剩余请求合并,缓存请求等
  ignore?: boolean;
  noCheckToken?: boolean;
  constructor() {
    this.interceptorsRequest();
    this.interceptorsResponse();
  }
  /** 保存当前Axios实例对象 */
  private static axiosInstance: AxiosInstance = axios.create(defaultConfig);
  // 添加请求拦截器
  private interceptorsRequest(): void {
    Http.axiosInstance.interceptors.request.use(
      (config: AxiosRequestConfig) => {
        // 获取url
        config.url = getUrl(config.url);
        // 在发送请求之前做些什么 token
        const userStores = useUserStore();
        const token = userStores.token;
        // 有一些白名单的请求不需要配置token， 比如登录...
        const isWhiteRequest = whiteList.includes(window.location.pathname);
        if (token && !isWhiteRequest && !this.noCheckToken)
          (<any>config.headers).common["Authorization"] = token;

        config.headers = { ...DEFAULT_HEADERS, ...config.headers };
        //根据配置文件动态设置baseurl
        const { api_endpoint } = window.$globalConfig;
        config.baseURL = api_endpoint;
        return config;
      },
      (error) => {
        // 对请求错误做些什么
        return Promise.reject(error);
      }
    );
  }
  // 添加响应拦截器
  private interceptorsResponse(): void {
    Http.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        // 对响应数据做点什么
        const res: ResponseData = response.data;
        if (res?.code) {
          errorHandler(res.code, res.msg, this.ignore);
          return Promise.reject(res);
        }
        return Promise.resolve(res);
      },
      (error: AxiosError) => {
        // 对响应错误做点什么
        const errorMessage = getErrorMessage(error);
        errorHandler(
          error.response?.status as number,
          errorMessage,
          this.ignore
        );
        return Promise.reject(error);
      }
    );
  }
  public async request(
    endpoint: string,
    method: TMethod,
    param?: AxiosRequestConfig,
    options: Recordable = {},
    retries = 0
  ): Promise<any> {
    const req_opt: HttpRequestConfig = {
      method,
      url: endpoint,
      ...param,
      ...options,
    };
    this.ignore = options.ignore;
    this.noCheckToken = options.noCheckToken;
    return new Promise((resolve, reject) => {
      Http.axiosInstance
        .request(req_opt)
        .then((response: AxiosResponse) => {
          resolve(response);
        })
        .catch((error) => {
          if (retries > 0) {
            // 网络无法连接
            if (isNetworkError(error.message)) {
              // 重试
              return this.request(endpoint, method, param, options, --retries);
            }
          }
          reject(error);
        });
    });
  }
}

// 导出 axios 实例
export default service;
export const http = new Http();
