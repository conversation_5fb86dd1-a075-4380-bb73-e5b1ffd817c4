import Axios from "./index";
import type { ResponseData } from "@/model/public";
import type { TableDataParams } from "@/model/table";
import { ContentType } from "@/utils/const";

// 获取模板文件列表
export const getTemplateFileList = (params: TableDataParams): Promise<ResponseData | any> => {
  return Axios({
      url: "/_st/_file/_templateFileList",
      method: "get",
      params,
  });
};

// 设置模板启用状态
export const setTemplateEnable = (data: {enable: number, fileId: number}): Promise<ResponseData | any> => {
  return Axios({
      url: "/_st/_file/_setTemplateEnable",
      method: "post",
      data,
  });
}

// 上传模板文件
export const uploadTemplateFile = (data: any): Promise<ResponseData | any> => {
  const form = new FormData();
  form.append("etag", data.etag);
  form.append("file", data.file);
  form.append("replace", data.replace);
  return Axios({
    url: "/_st/_file/_uploadTemplateFile",
    method: "post",
    data: form,
    headers: {
      "Content-Type": ContentType.UPLOAD,
    },
  });
}

// 获取模板类型文件
export const getTemplateTypeList = (): Promise<ResponseData | any> => {
  return Axios({
      url: "/_st/_file/_templateTypeList",
      method: "get",
  });
}

// 通过模板创建文件
export const createFileByTemplate = (data: {
  etag: string;
  fileName: string;
  fileSize: number;
  folderId: number;
  id: string;
  replace: boolean;
}): Promise<ResponseData | any> => {
  return Axios({
      url: "/_st/_file/_createFileByTemplate",
      method: "post",
      data,
  });
}
