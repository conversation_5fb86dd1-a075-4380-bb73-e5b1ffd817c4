import Axios from "./index";
import type { ResponseData } from "@/model/public";
import type { TableDataParams } from "@/model/table";
import type { CreateTaskTypeParams } from "@/model/taskType";

// 获取任务类型列表
export const getTaskTypeList = (
    params: TableDataParams
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_tasktype/_getlist",
        method: "get",
        params,
    });
};

// 获取任务类型类型列表
export const getTypeListInTaskType = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_tasktype/_typelist",
        method: "get",
    });
};

// 根据任务类型类型获取应用列表
export const getAppListByTaskType = (
    type: string
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_tasktype/_applist",
        method: "get",
        params: { type },
    });
};

// 创建任务类型
export const createTaskTypeApi = (
    data: CreateTaskTypeParams
): Promise<ResponseData> => {
    return Axios.post(`/_tasktype`, data);
};

// 删除任务类型
export const delTaskType = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_tasktype/${id}`,
        method: "delete",
    });
};

// 更新任务类型
export const editTaskType = (
    id: string | number,
    data: object
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_tasktype/${id}`,
        method: "put",
        data: {
            ...data,
        },
    });
};

// 更新一个系统后台服务
export const editSysTaskType = (
    id: string | number,
    data: object
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_tasktype/_systask/${id}`,
        method: "put",
        data: {
            ...data,
        },
    });
};