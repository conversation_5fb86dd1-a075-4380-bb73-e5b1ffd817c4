import { ContentType } from '@/utils/const';
import Axios, { http } from './index';
import type { ResponseData } from '@/model/public';

interface UploadModifyRecord {
  fileId: number;
  editor?: string;
  baseId: number;
  srcStorageId?: number;
  lastModify?: string;
  currModify?: string;
  file: File;
}

// 上传增量文件
export const uploadModifyRecord = (params: UploadModifyRecord): Promise<ResponseData | any> => {
  const formData = new FormData();
  for (const key in params) {
    formData.append(key, params[key as keyof UploadModifyRecord] as any);
  }
  return http.request(
    "/_st/_filemodify/_uploadModifyRecord",
    "POST",
    { data: formData },
    {
      headers: {
        "Content-Type": ContentType.UPLOAD,
      },
      ignore: true,
    }
  );
};

// 添加历史版本
export const addHistoryTag = (data: {
  baseStorageId: number,
  fileId: number,
  lastRecordId: number,
  userTag: string,
}): Promise<ResponseData | any> => {
  return Axios({
    url: '/_st/_fileTag/_addFileHistoryTag',
    method: 'post',
    data,
  });
}

// 获取历史版本列表
export const getHistoryTagList = (data: {
  status: 0 | 1 | 2, // 0:未完成归并的，1：归并完成的，2:全部
  fileId: number,
  sign: string
}): Promise<ResponseData | any> => {
  return Axios({
    url: '/_st/_fileTag/_getFileHistoryTag',
    method: 'get',
    params: { ...data },
  });
}

// 删除历史版本
export const deleteHistoryTag = (data: {
  fileId: number,
  userTag: string,
  verId: number
}): Promise<ResponseData | any> => {
  return Axios({
    url: '/_st/_fileTag/_delFileHistoryTag',
    method: 'post',
    data,
  });
}

// 获取字体信息列表
export const getFontInfoList = (data: {
  folderId: number,
  limit: number
}): Promise<ResponseData | any> => {
  return Axios({
    url: '/_st/_font/_fontInfoList',
    method: 'get',
    params: { ...data },
  });
}

// 获取字模列表
export const getFontMatrixList = (data: {
  folderId: number,
  limit: number,
  requiredFont: string,
  requiredStr: string
}): Promise<ResponseData | any> => {
  return Axios({
    url: '/_st/_font/_fontMatrix',
    method: 'get',
    params: { ...data },
  });
}