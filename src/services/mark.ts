import Axios from "./index";
import type { ResponseData } from "@/model/public";

// 创建书签
export const createMarkApi = (data: {
    center: string
    etag: string
    fileId: number
    name: string
    scale: number
    id: string
}): Promise<ResponseData> => {
    return Axios.post(`/_st/_file/_setBookMark`, data);
};


// 获取书签
export const getMarkApi = (data: {
    etag: string
    fileId: number
}): Promise<ResponseData | any> => {
    return Axios({
        url: `/_st/_file/_getBookMark`,
        method: "get",
        params: data
    });
};


// 删除书签
export const deleteMarkApi = (data: {
    delId: string
    etag: string
    fileId: number
}): Promise<ResponseData | any> => {
    return Axios({
        url: `/_st/_file/_delBookMark`,
        method: "delete",
        params: data
    });
};

