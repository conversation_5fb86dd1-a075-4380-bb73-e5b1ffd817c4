import Axios from "./index";
import type { ResponseData } from "@/model/public";

// 获取批注列表
export const getNoteList = (
    fileId: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_note/_getlist",
        method: "get",
        params: { fileId },
    });
};

// 创建批注
export const createNoteApi = (data: {
    content: string;
    fileId: number;
}): Promise<ResponseData> => {
    return Axios.post(`/_note`, data);
};

// 创建书签
export const createMarkApi = (data: {
    center: string
    etag: string
    fileId: number
    name: string
    scale: number
    id: string
}): Promise<ResponseData> => {
    return Axios.post(`/_st/_file/_setBookMark`, data);
};
