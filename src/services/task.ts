import Axios from "./index";
import type { ResponseData } from "@/model/public";
import type { TableDataParams } from "@/model/table";
import { ContentType } from "@/utils/const";

// 获取任务类型列表
export const getTaskList = (
    params: TableDataParams
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_task/_getlist",
        method: "get",
        params,
    });
};

// 查询任务信息

// 删除任务
export const delTask = (id: string | number): Promise<ResponseData | any> => {
    return Axios({
        url: `/_task/${id}`,
        method: "delete",
    });
};

// 取消任务
export const cancelTask = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_task/${id}/_cancel`,
        method: "get",
    });
};

// 重新执行任务
export const retryTask = (id: string | number): Promise<ResponseData | any> => {
    return Axios({
        url: `/_task/${id}/_re-execute`,
        method: "get",
    });
};

// 清空任务
export const clearAllTask = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_task/_clear",
        method: "get",
    });
};

// 查询一个任务信息
export const getTaskInfo = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_task/${id}`,
        method: "get",
    });
};

// 获取一个任务进度
export const getTaskProgress = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_task/${id}/_progress`,
        method: "get",
    });
};

// 获取任务错误输出
export const getTaskErrorLog = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_task/${id}/_stderr`,
        method: "get",
    });
};

// 获取任务标准输出
export const getTaskStandardLog = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_task/${id}/_stdout`,
        method: "get",
    });
};

// 导出错误日志
export const exportTaskErrorLog = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_task/${id}/_exportstderr`,
        method: "get",
        responseType: "arraybuffer",
        headers: {
            "Content-Type": ContentType.BINARY,
        },
    });
};

// 导出标准日志
export const exportTaskStandardLog = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_task/${id}/_exportstdout`,
        method: "get",
        responseType: "arraybuffer",
        headers: {
            "Content-Type": ContentType.BINARY,
        },
    });
};


interface IInitTaskParams {
    fileId: string | number,
    type: string
    version?: string
    config?: {
            [key:string]: any
        } | string,
}
// 创建一个任务
export const initTask = (
  params: IInitTaskParams
): Promise<ResponseData | any> => {
  const data = params
  if (!data.config) data.config = {}
  data.config = JSON.stringify(data.config)
  data.fileId = parseInt(data.fileId as string)
  return Axios({
      url: `/_task`,
      method: "post",
      data
  });
}

// 提升任务优先级
export const setTaskPriority = (
  params: {
    id: number
    priority: number
  }
): Promise<ResponseData | any> => {
  const data = params
  return Axios({
      url: `/_task/${data.id}/_priority`,
      method: "put",
      data
  });
};

// 检测是否可以被转换
export const checkIsConvert = (data: {
    fileId: number,
    version?: string,
    type: string,
}): Promise<ResponseData | any> => {
    return Axios({
        url: `/_st/_file/${data.fileId}/_isConvert`,
        method: "get",
        params: { type: data.type, version: data.version },
    });
  };