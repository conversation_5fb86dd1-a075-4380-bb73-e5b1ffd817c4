import Axios from "./index";
import type { ResponseData } from "@/model/public";

interface shortcut {
  fileId: number;
  fileThumbnail?: string;
  fileType?: number;
  fileVersion: string;
  fileEtag?: string;
  name: string;
}

// 获取快捷方式
export const getShortcutList = (): Promise<ResponseData | any> => {
  return Axios({
      url: "/_shortcut/_getlist",
      method: "get",
  });
};

// 创建快捷方式
export const createShortcutApi = (
  data: shortcut
): Promise<ResponseData> => {
  return Axios.post(`/_shortcut`, data);
};

// 删除快捷方式
export const delShortcutApi = (id: string | number): Promise<ResponseData | any> => {
  return Axios({
      url: `/_shortcut/${id}`,
      method: "delete",
  });
};

// 判断快捷方式是否有效
export const checkShortcut = (id: string | number): Promise<ResponseData | any> => {
  return Axios({
      url: `/_shortcut/${id}/_isvalid`,
      method: "get",
  });
};