import Axios from "./index";
import type { CreateGroupParams, ExportParams } from "@/model/group";
import type { ResponseData } from "@/model/public";
import type { TableDataParams } from "@/model/table";
import { ContentType } from "@/utils/const";

// 创建用户组
export const createGroupApi = (
    data: CreateGroupParams
): Promise<ResponseData> => {
    return Axios.post(`/_group`, data);
};

// 获取用户组列表
export const getGroupList = (
    params: TableDataParams
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_group/_getlist",
        method: "get",
        params,
    });
};

// 获取用户组树
export const getGroupTree = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_group/_gettree",
        method: "get",
    });
};

// 删除用户组
export const delGroup = (id: string): Promise<ResponseData | any> => {
    return Axios({
        url: `/_group/${id}`,
        method: "delete",
    });
};

// 通过用户组Id获取用户列表
export const getUserListByGroupId = (
    id: string,
    params: TableDataParams
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_group/_userlist`,
        method: "get",
        params: {
            id,
            ...params,
        },
    });
};

// 获取配置了权限的用户组列表
export const getAuthGroupList = (
    params: TableDataParams
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_group/_auth/_getlist",
        method: "get",
        params,
    });
};

// 模糊搜索用户组
export const getGroupBySearch = (
    keyword: string
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_group/_search",
        method: "get",
        params: { keyword },
    });
};

// 设置用户组权限
export const updateGroupAuth = (
    data: Recordable,
    id: number | string
): Promise<ResponseData> => {
    return Axios.post(`/_group/_auth/${id}`, data);
};

// 批量设置用户组权限
export const updateGroupAuthBatch = (
    data: Recordable
): Promise<ResponseData> => {
    return Axios.post(`/_group/_auth/_batch`, data);
};

// 批量添加某用户到用户组
export const addUserToGroupBatch = (
    data: Recordable
): Promise<ResponseData> => {
    return Axios.post(`/_group/_batch_user`, data);
};

// 导入
export const importGroup = (file: File): Promise<ResponseData | any> => {
    const formData = new FormData();
    formData.append("file", file);
    return Axios({
        url: "/_group/_import",
        method: "post",
        data: formData,
        headers: {
            "Content-Type": ContentType.UPLOAD,
        },
    });
};

// 导出
export const exportGroup = (
    params: ExportParams
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_group/_export",
        method: "get",
        params,
        responseType: "arraybuffer",
        headers: {
            "Content-Type": ContentType.BINARY,
        },
    });
};

// 下载模板
export const downloadImportGroupTemplate = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_group/_download_import_template",
        method: "get",
        responseType: "arraybuffer",
        headers: {
            "Content-Type": ContentType.BINARY,
        },
    });
};
