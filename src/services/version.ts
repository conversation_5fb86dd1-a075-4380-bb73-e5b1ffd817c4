import Axios from "./index";
import type { ResponseData } from "@/model/public";

interface versionBaseLine {
  fileId: number;
  fileVersion: string;
  name: string;
  fileEtag?: string,
  fileThumbnail?: string,
  fileType?: number,
}

// 获取版本基准列表
export const getVersionBaseLineList = (): Promise<ResponseData | any> => {
  return Axios({
    url: "/_version_baseline/_getlist",
    method: "get",
  });
};

// 创建版本基准
export const createVersionBaseLineApi = (
  data: versionBaseLine
): Promise<ResponseData> => {
  return Axios.post(`/_version_baseline`, data);
};

// 删除版本基准
export const delVersionBaseLineApi = (
  id: string | number
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_version_baseline/${id}`,
    method: "delete",
  });
};
