import Axios, { http } from "./index";
import type { ResponseData } from "@/model/public";
import { ContentType } from "@/utils/const";
import type {
    PutSystemInfoQuery,
    PutTaskInfoQuery,
    PutSaveInfoQuery,
    PutBackupInfoQuery,
    PutCloudConfigQuery
} from "@/model/system";

import type { TableDataParams } from "@/model/table";
import type { CreateThirdPartyParams } from "@/model/thirdParty";

// 获取系统基本信息
export const getSystemInfo = (): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_info`,
        method: "get",
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

// 修改系统基本信息
export const putSystemInfo = (
    data: PutSystemInfoQuery
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_base`,
        method: "put",
        data,
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

// 修改存储配置信息
export const putSaveInfo = (
    data: PutSaveInfoQuery
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_storage`,
        method: "put",
        data,
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

// 修改任务配置信息
export const putTaskInfo = (
    data: PutTaskInfoQuery
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_task`,
        method: "put",
        data,
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

// 修改备份配置信息
export const putBackupInfo = (
    data: PutBackupInfoQuery
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_backup`,
        method: "put",
        data,
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

/** 三方令牌接口 */

// 获取第三方token列表
export const getThirdPartyList = (
    params: TableDataParams
): Promise<ResponseData | any> => {
    return Axios({
        url: "/_system/_third_party/_getlist",
        method: "get",
        params,
    });
};

// 创建新的三方令牌
export const createThirdPartyApi = (
    data: CreateThirdPartyParams
): Promise<ResponseData> => {
    return Axios.post(`/_system/_third_party`, data);
};

// 删除三方令牌
export const delThirdParty = (id: string): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_third_party`,
        method: "delete",
        data: {
            id,
        },
    });
};

// 禁用令牌
export const disableThirdParty = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_third_party/_disable`,
        method: "post",
        data: {
            id,
        },
    });
};

// 启用令牌
export const enableThirdParty = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_third_party/_enable`,
        method: "post",
        data: {
            id,
        },
    });
};

// 重置令牌
export const resetThirdParty = (
    id: string | number
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_third_party/_reset_token`,
        method: "post",
        data: {
            id,
        },
    });
};

// 更改令牌过期时间
export const setThirdPartyValidityPeriod = (
    id: number,
    validityPeriod: string
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_third_party/_set_validity_period`,
        method: "post",
        data: {
            id,
            validityPeriod,
        },
    });
};

// 设置三方接入权限
export const setThirdServerConf = (
    formData:any
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_third_party/_setThirdServerConf`,
        method: "post",
        data: formData
    });
};

// 安装首页
export const getInstallHome = (): Promise<ResponseData | any> => {
    return Axios({
        url: `/install`,
        method: "get",
    });
};

// 安装系统
export const installSystem = (params: any): Promise<ResponseData | any> => {
    const formData = new FormData();
    for (const key in params) {
        formData.append(key, params[key]);
    }
    return Axios({
        url: `/install`,
        method: "post",
        data: formData,
        headers: {
            "Content-Type": ContentType.UPLOAD,
        },
    });
};

// 校验是否安装
export const checkInstall = (): Promise<ResponseData | any> => {
    return Axios({
        url: `/check_install`,
        method: "get",
    });
};

// 重启系统
export const restartSystem = (): Promise<ResponseData | any> => {
    return Axios({
        url: `/restart`,
        method: "get",
    });
};

// 获取系统基本信息 不校验token
export const getSystemBasicInfo = (): Promise<ResponseData | any> => {
    return http.request(
        "/_system/_base_info",
        "GET",
        {},
        {
            ignore: true,
            noCheckToken: true,
        }
    );
};

// 获取系统安装默认配置信息 不校验token
export const getDefaultInitConfig = (): Promise<ResponseData | any> => {
    return http.request(
        "/_get_default_init_config",
        "GET",
        {},
        {
            ignore: true,
            noCheckToken: true,
        }
    );
};

// 获取三方请求日志列表
export const getThirdPartyLogList = (params: TableDataParams): Promise<ResponseData | any> => {
    return Axios({
        url: "/_system/_third_party_log/_getlist",
        method: "get",
        params,
    });
};

// 清空三方日志请求列表
export const clearAllThirdPartyLog = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_system/_third_party_log/_clear",
        method: "delete",
    });
};

// 获取系统请求日志列表
export const getSystemLogList = (params: TableDataParams): Promise<ResponseData | any> => {
    return Axios({
        url: "/_system/_log/_getlist",
        method: "get",
        params,
    });
};

// 清空系统请求日志列表
export const clearAllSystemLog = (): Promise<ResponseData | any> => {
    return Axios({
        url: "/_system/_log/_clear",
        method: "delete",
    });
}

// 系统备份恢复
export const recoverySystem = (path: string): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_recovery`,
        method: "post",
        data: {
            backup_file_path: path,
        },
    });
};

// 获取授权许可证
export const getLicense = (): Promise<ResponseData | any> => {
    return http.request(
        "/license_info",
        "GET",
        {},
        {
            ignore: true,
            noCheckToken: true,
        }
    );
};


// 开图配置信息
export const postOpenDWGConfig = (data: any): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_open_drawing_config/_batch_save`,
        method: "post",
        data: data,
    });
};


// 获取开图配置信息
export const getOpenDWGConfig = (data: any): Promise<ResponseData | any> => {
    console.log(data)
    return Axios({
        url: `/_system/_open_drawing_config/_getlist`,
        method: "get",
        params: data,
    });
};

// 获取云原生配置信息
export const getCloudConfig = (): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_gcadcloud_info`,
        method: "get",
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

// 修改云原生配置信息
export const putCloudConfig = (
    data: PutCloudConfigQuery
): Promise<ResponseData | any> => {
    return Axios({
        url: `/_system/_gcadcloud`,
        method: "put",
        data,
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

// 获取websdk证书信息
export const getWebSdkInfo = (): Promise<ResponseData | any> => {
    return Axios({
        url: `/sdk/license/getinfo`,
        method: "get",
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

// 上传websdk证书信息
export const uploadWebSdkInfo = (formData: any): Promise<ResponseData | any> => {
    return Axios({
        url: `/sdk/license/import`,
        method: "post",
        data: formData,
        headers: {
            "Content-Type": ContentType.UPLOAD,
        },
    });
};

// 授权使用记录
export const getBrowserIdList = (params: any): Promise<ResponseData | any> => {
    return Axios({
        url: `/browserid/_getlist`,
        method: "get",
        params,
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

// 历史使用记录
export const getBrowserHistoryList = (params: any): Promise<ResponseData | any> => {
    return Axios({
        url: `/browserid/_gethistorylist`,
        method: "get",
        params,
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};

// 踢出正在编辑
export const postBrowserRaise = (formData: any): Promise<ResponseData | any> => {
    return Axios({
        url: `/browserid/_raise`,
        method: "post",
        data: formData,
        headers: {
            "Content-Type": ContentType.JSON,
        },
    });
};