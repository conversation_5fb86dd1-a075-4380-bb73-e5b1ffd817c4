import Axios, { http } from "./index";
import type { ResponseData } from "@/model/public";
import { ContentType } from "@/utils/const";
import type {
  DownloadQuery,
  GetFileListQuery,
  RenameFileQuery,
  CreateFolderQuery,
  UploadQuery,
  DeleteFileQuery,
  GetRecycledListQuery,
  RecycledBatchQuery,
  GetTreeQuery,
  MoveFileQuery,
  SearchGroupQuery,
  SetFileRightQuery,
  GetFontListQuery,
  FileRightListQuery,
  RecycledDeleteBatchQuery,
  GetFileByStorageIdQuery,
  recoverQuestData,
} from "@/model/file";
import { downloadByUrl, escapedUrl } from "@/utils";
import type { TableDataParams } from "@/model/table";

// 获取文件列表
export const getFileList = (
  params: GetFileListQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_fd/_dirDetailList`,
    method: "get",
    params,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 获取文件信息
export const getFileInfo = (
  fileId: string | number
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_file/${fileId}`,
    method: "get",
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 下载文件
export const downloadFile = (params: DownloadQuery) => {
  const url = `/_st/_file/_download?fileName=${params.fileName}&fileId=${params.fileId}`;
  const translate_url = escapedUrl(url);
  downloadByUrl(translate_url);
};

export const getFileSource = (
  params: DownloadQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_file/_download?fileName=${params.fileName}&fileId=${params.fileId}`,
    method: "get",
    params,
    responseType: "arraybuffer",
  });
};

// 文件重命名
export const renameFile = (
  data: RenameFileQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_file/rename`,
    method: "put",
    data,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 创建文件夹
export const createFolder = (
  data: CreateFolderQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_fd`,
    method: "post",
    data,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 上传文件
export const uploadFile = (
  data: UploadQuery,
  cb: (evt: ProgressEvent) => void
): Promise<ResponseData | any> => {
  const form = new FormData();
  form.append("folderId", data.folderId ? data.folderId.toString() : "");
  form.append("replace", data.replace ? data.replace.toString() : "");
  form.append("etag", data.etag);
  form.append("file", data.file);
  if (data.path) form.append("path", data.path);
  return http.request(
    "/_st/_file/_upload",
    "POST",
    { data: form, onUploadProgress: cb },
    {
      headers: {
        "Content-Type": ContentType.UPLOAD,
      },
      ignore: true,
    }
  );
};

// 删除文件
export const deleteFile = (
  data: DeleteFileQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_file/batch`,
    method: "delete",
    data,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 获取回收站列表
export const getRecycledList = (
  params: GetRecycledListQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_fd/recycleBinList`,
    method: "get",
    params,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 回收站批量删除
export const deleteRecycledBatch = (
  data: RecycledDeleteBatchQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_file/recycleBin/batch`,
    method: "delete",
    data,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 回收站批量还原
export const restoreRecycledBatch = (
  data: RecycledBatchQuery
): Promise<ResponseData | any> => {
  return http.request(
    "/_st/_file/restoreRecycleBin/batch",
    "PUT",
    { data },
    {
      headers: {
        "Content-Type": ContentType.JSON,
      },
      ignore: true,
    }
  );
};

// 获取目录层级树
export const getDirTree = (
  params: GetTreeQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_fd/_dirTree`,
    method: "get",
    params,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 移动文件
export const moveFile = (data: MoveFileQuery): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_fd/mv`,
    method: "post",
    data,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 拷贝文件
export const copyFile = (data: MoveFileQuery): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_fd/copy`,
    method: "post",
    data,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};

// 安装应用
export const installApplication = (
  data: UploadQuery,
  cb: (evt: ProgressEvent) => void
): Promise<ResponseData | any> => {
  const form = new FormData();
  form.append("etag", data.etag);
  form.append("file", data.file);
  return Axios({
    url: `/_st/_file/_uploadAppZipFile`,
    method: "post",
    data: form,
    headers: {
      "Content-Type": ContentType.UPLOAD,
    },
    onUploadProgress: cb,
  });
};

// 模糊查询用户和用户组
export const searchGroupPeople = (
  params: SearchGroupQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_file/_authority/_search",
    method: "get",
    params,
  });
};

// 文件授权列表
export const getFileRightList = (
  params: FileRightListQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_file/_authorityList`,
    method: "get",
    params,
  });
};

// 设置文件权限
export const postFileRight = (
  data: SetFileRightQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_file/_authority",
    method: "post",
    data,
  });
};

// 获取字体列表
export const getFontList = (
  params: GetFontListQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_font/_fontList",
    method: "get",
    params,
  });
};

// 上传字体
export const uploadFont = (data: any): Promise<ResponseData | any> => {
  const form = new FormData();
  form.append("etag", data.etag);
  form.append("file", data.file);
  form.append("folderId", data.folderId);
  form.append("replace", data.replace);
  return Axios({
    url: "/_st/_font/_upload",
    method: "post",
    data: form,
    headers: {
      "Content-Type": ContentType.UPLOAD,
    },
  });
};

// 下载字体文件
export const downloadFontFile = (
  name: string,
  id: number
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_font/_download",
    method: "get",
    responseType: "arraybuffer",
    headers: {
      "Content-Type": ContentType.BINARY,
    },
    params: {
      fileName: name,
      fileId: id,
      attch: "filName",
    },
  });
};

// 静态文件上传
export const uploadStaticFile = (params: any): Promise<ResponseData | any> => {
  const formData = new FormData();
  for (const key in params) {
    formData.append(key, params[key]);
  }
  return Axios({
    url: "/_static_file_upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": ContentType.UPLOAD,
    },
  });
};

// 头像上传
export const uploadImage = (params: any): Promise<ResponseData | any> => {
  const formData = new FormData();
  for (const key in params) {
    formData.append(key, params[key]);
  }
  return Axios({
    url: "/_image",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": ContentType.UPLOAD,
    },
  });
};

// 头像下载
export const downloadImage = (key: string): Promise<ResponseData | any> => {
  return Axios({
    url: "/_image",
    method: "get",
    params: {
      key,
    },
    responseType: "arraybuffer",
  });
};

// 获取文件类型
export const getFileTypes = (): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_file/_typeList",
    method: "get",
  });
};

//  根据storageId下载文件 storageId 附加文件(缩略图,扩展信息...)的存储id，下载附加文件时调用该接口
export const downloadFileByStorageId = (
  params: GetFileByStorageIdQuery,
  type?: resType
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_storage/_download",
    method: "get",
    responseType: type || "arraybuffer",
    params,
  });
};

export const uploadFileByStorageId = (
  params: any
): Promise<ResponseData | any> => {
  const formData = new FormData();
  for (const key in params) {
    formData.append(key, params[key]);
  }
  return Axios({
    url: "/_st/_storage/_upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": ContentType.UPLOAD,
    },
  });
};

// 获取文件元信息
export const getFileMeta = (params: {
  fileId: number | string;
  metaKey: string;
}): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_file/_getMeta",
    method: "get",
    params,
  });
};

// 设置元信息
export const setFileMeta = (data: {
  fileId: number | string;
  metaKey: string;
  value: string;
  version: string;
}): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_file/_setMeta",
    method: "post",
    data,
  });
};

// 获取文件权限列表
export const getFilePermList = (
  params: TableDataParams
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_file/_getFilePermList",
    method: "get",
    params,
  });
};

// 删除文件权限列表
export const deleteFilePerm = (
  data: DeleteFileQuery[]
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_file/_batchDelFilePermList`,
    method: "delete",
    data,
    headers: {
      "Content-Type": ContentType.JSON,
    },
  });
};
// 获取文件历史版本列表
export const getFileHistory = (fileId: number): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_file/${fileId}/history`,
    method: "get",
  });
};

// 恢复文件版本
export const recoverFile = (
  data: recoverQuestData
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_fd/recover`,
    method: "put",
    data,
  });
};

// 根据etag判断文件是否已上传
export const checkFileExit = (data: string): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_file/_checkAppStatus",
    method: "get",
    params: {
      etag: data,
    },
  });
};

// 判断文件是否有参照物
export const checkFileHasXRef = (
  fileId: string,
  version?: string
): Promise<ResponseData | any> => {
  return Axios({
    url: `/_st/_file/${fileId}/_hasXRef`,
    method: "get",
    params: {
      version,
    },
  });
};

// 获取材质列表
export const getMaterialList = (
  params: GetFontListQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_material/_materialList",
    method: "get",
    params,
  });
};

// 上传材质
export const uploadMaterial = (
  data: UploadQuery,
  cb: (evt: ProgressEvent) => void
): Promise<ResponseData | any> => {
  const form = new FormData();
  form.append("etag", data.etag);
  form.append("file", data.file);
  form.append("folderId", data.folderId ? data.folderId.toString() : "");
  form.append("replace", data.replace ? data.replace.toString() : "");
  return http.request(
    "/_st/_material/_upload",
    "POST",
    { data: form, onUploadProgress: cb },
    {
      headers: {
        "Content-Type": ContentType.UPLOAD,
      },
      ignore: true,
    }
  );
};

// 获取材质列表
export const getPartList = (
  params: GetFontListQuery
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_part/_partList",
    method: "get",
    params,
  });
};

// 上传材质
export const uploadPart = (
  data: UploadQuery,
  cb: (evt: ProgressEvent) => void
): Promise<ResponseData | any> => {
  const form = new FormData();
  form.append("etag", data.etag);
  form.append("file", data.file);
  form.append("folderId", data.folderId ? data.folderId.toString() : "");
  form.append("replace", data.replace ? data.replace.toString() : "");
  return http.request(
    "/_st/_part/_upload",
    "POST",
    { data: form, onUploadProgress: cb },
    {
      headers: {
        "Content-Type": ContentType.UPLOAD,
      },
      ignore: true,
    }
  );
};

// 申请编辑文件（编辑加锁）
export const requestEditFile = (data: {
  fileId: number | string;
  devId: string;
}): Promise<ResponseData | any> => {
  return Axios({
    url: "/_edit/_request",
    method: "post",
    data,
  });
};

// 脱离编辑状态（释放编辑锁）
export const leaveEditFile = (data: {
  fileId: number | string;
  devId: string;
}): Promise<ResponseData | any> => {
  return Axios({
    url: "/_edit/_leave",
    method: "post",
    data,
  });
};

// 抢夺别人编辑权限（重新编辑加锁）
export const snatchEdit = (data: {
  fileId: number | string;
  devId: string;
}): Promise<ResponseData | any> => {
  return Axios({
    url: "/_edit/_snatch",
    method: "post",
    data,
  });
};

// 查询未归并文件数量
export const getMergeData = (
  fileId: number | string
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_edit/_getWaitMergeFileNums",
    method: "get",
    params: {
      fileId
    }
  });
};

// 放弃归并（将未归并的记录设置为放弃状态）
export const giveUpMerge = (data: {
  fileId: number | string;
}): Promise<ResponseData | any> => {
  return Axios({
    url: "/_edit/_giveupMerge",
    method: "post",
    data
  });
};

// 检查文件是否需要重新转换
export const isNeedReConvert = (
  fileId: number | string,
  version: string,
  metaKey: string
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_file/_isNeedReConvert",
    method: "get",
    params: {
      fileId,
      version,
      metaKey
    }
  });
};

// 获取文件夹文件列表（向下翻页）
export const getFileListByDrop = (params: any): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_fd/_getFileList",
    method: "get",
    params
  });
};

// 通过文件路径获取文件信息
export const getFileInfoByPath = (path: string): Promise<ResponseData | any> => {
  return Axios({
    url: "/_st/_file/_getFileInfoByPath",
    method: "get",
    params: {
      filePath: path
    }
  });
};



// 获取当前设备数
export const getBrowserIdCount = (params: any): Promise<ResponseData | any> => {
  return Axios({
    url: "/browserid/_count",
    method: "get",
    params
  });
};