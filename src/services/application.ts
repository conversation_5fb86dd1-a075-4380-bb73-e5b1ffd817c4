import Axios from "./index";
import type { ResponseData } from "@/model/public";
import { ContentType } from "@/utils/const";
import type { TableDataParams } from "@/model/table";

// 获取应用列表
export const getApplicationList = (
  params: TableDataParams
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_application/_getlist",
    method: "get",
    params,
  });
};

// 安装应用
export const installApplication = (
  id: number | string
): Promise<ResponseData | any> => {
  return Axios.get(`/_application/${id}/_install`);
};

// 卸载应用
export const uninstallApplication = (
  id: number | string
): Promise<ResponseData | any> => {
  return Axios.get(`/_application/${id}/_uninstall`);
};

// 查询应用
export const getApplicationById = (id: number): Promise<ResponseData | any> => {
  return Axios.get(`/_application/${id}`);
};

// 卸载多个应用
export const uninstallApplicationList = (
  params: number[]
): Promise<ResponseData | any> => {
  return Axios({
    url: "/_application/_uninstalllist",
    method: "get",
    params,
  });
};
