import socket from '@/utils/socket'
import type { IEventRegister } from './index'

const events:IEventRegister[] = []

// events.push({
//   ename: 'set_connection',
//   cb: (ename:string, data:any) => {
//     console.log('set_connection', ename, data)
//     if (data) {
//       localStorage.setItem("connectionInfo", JSON.stringify(data));
//     }
//   }
// })

export default {
  // 集体订阅
  sub: () => {
    events.forEach(({ ename, cb }) => {
      socket.on(ename, cb)
    })
  },
  // 集体解绑
  unsub: () => {
    events.forEach(({ ename}) => {
      socket.off(ename)
    })
  }
}