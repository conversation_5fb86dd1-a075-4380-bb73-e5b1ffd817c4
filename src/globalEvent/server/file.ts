import type { IEventRegister } from './index'
import { ElNotification } from 'element-plus'
import { $t } from '@/locales'

const events:IEventRegister[] = []

events.push({
  ename: 'file.note.change',
  cb: (ename:string, data:any) => {
    const fileId = new URLSearchParams(window.location.search).get("fileId")
    if (fileId && Number(fileId) === data.fileId) {
      ElNotification({
        type: 'success',
        message: $t('notification.file_node_change', {fullName: data.username, fileName: data.fileName}),
        duration: 3000
      })
    }
  }
})

export default events