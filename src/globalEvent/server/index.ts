// socket 订阅 服务端相关全局事件处理
import socket from '@/utils/socket'
import { enableSocketPathList } from '@/utils/const';
import file from "./file"
import task from "./task"

export interface IEventRegister {
  ename: string,
  cb: (ename:string, data:any) => void
}

const allGlobalEvents: IEventRegister[] = [
  ...file,
  ...task,
]

export default {
  // 集体订阅
  sub: () => {
    if (enableSocketPathList.includes(window.location.pathname)) {
      allGlobalEvents.forEach(({ ename, cb }) => {
        socket.on(ename, cb)
      })
    }
  },
  // 集体解绑
  unsub: () => {
    if (enableSocketPathList.includes(window.location.pathname)) {
      allGlobalEvents.forEach(({ ename}) => {
        socket.off(ename)
      })
    }
  }
}