import type { IEventRegister } from "./index";
import { ElNotification } from "element-plus";
import { decodeQueryParams } from "@/utils"
import { TaskTypeMaps } from "@/utils/const";
import { useUserStore } from "@/stores/user";
import { $t } from '@/locales'

const events: IEventRegister[] = [];

const sockets = [
  // "task.create.ok",
  // "task.create.error",
  // "task.start.ok",
  // "task.cancel.ok",
  "task.end.ok",
  "task.end.error",
];

const taskDescription = {
  DWGVisualization: "开图",
  SaveAsDWG: "另存pdf",
  SaveAsPDF: "下载dwg",
  MakeDWGThumbnail: "生成缩略图",
  MakeDWGBigThumbnail: "生成缩略图大图",
  BuildingVisualization: "rvt开图",
  MakeBuildingThumbnail: "生成缩略图",
  ConvertPDF: "PDF转DWG",
  SelfCheck: "系统自检",
  ManufacturingVisualization: "制造业图纸开图",
  MakeManufacturingThumbnail: "制造业图纸生成缩略图",
  MFGSkpVisualization: "制造业skp图纸开图",
  MakeMFGSkpThumbnail: "制造业skp图纸生成缩略图",
  GetDWGInfo: "获取dwg图纸信息",
}

sockets.forEach((socket) => {
  events.push({
    ename: socket,
    cb: (ename: string, data: any) => {
      try {
        const fileId = new URLSearchParams(window.location.search).get("fileId")
        if ((/Preview(2|3)d/).test(window.location.pathname) && fileId && Number(fileId) !== data.fileId) {
          // 如果是预览页面，并且不是当前文件的，则不显示通知
          return
        }
        const { socket } = decodeQueryParams(window.location.search)
        const taskName = taskDescription[TaskTypeMaps[data.subType - 1] as (keyof typeof taskDescription)] || '未知任务'
        const socketType = socket.includes("error") ? "error" : "success";
        const userStores = useUserStore();
        const isSelfPreviewPageError = socketType === 'error' && userStores.id === data.uid && (/Preview(2|3)d/).test(window.location.pathname)
        ElNotification({
          type: socketType,
          message: $t(`notification.${socket.replaceAll(".", "_")}`, {
            taskObj: data.fileName || data.nodeName,
            taskName,
            errReason: data.application_error_msg || "Error ！"
          }),
          duration: isSelfPreviewPageError ? 0 : 3000,
        });
      } catch (error) {
        console.log(error);
      }
    },
  });
});

export default events;
