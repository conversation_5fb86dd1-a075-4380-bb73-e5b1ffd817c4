import type { IEventRegister } from "./index";
import socket from "@/utils/socket";
import { useUserStore } from "@/stores/user";
import { useStoreRouter } from "@/stores/router";
import { useStoreConfig } from "@/stores/config";
import router from "@/router";
import type { RouteLocationRaw } from "vue-router";
import { uploadState } from "@/stores/upload";

const events: IEventRegister[] = [];

// 用户相关初始化逻辑，用于刷新页面/用户登录后
function initUserInfo() {
  const userStores = useUserStore();
  const routerStores = useStoreRouter();

  // 如果登录了
  if (userStores.token) {
    // 连接socketio
    const { socketIo_url, key } = window.$globalConfig
    socket.connect({
      token: userStores.token,
      url: socketIo_url,
      key: key,
      uid: userStores.id,
      uname: userStores.user_name,
      did: localStorage.getItem("browserId") || '',
      conn_type: 0
    });
    // socket监听token失效事件
    console.log('监听失效事件')
    socket.on('user.token.invalid', handleTokenInvalid)
    // 根据权限生成可访问的路由
    if (userStores.menu.length > 0) {
      // 根据auth生成可访问的 Routes
      routerStores.setRoutes(userStores.menu);
    }
    userStores.getInfo()
  }
}
// token失效
const handleTokenInvalid = async () => {
  console.log('收到 token 失效')
  // 推出登录清空user与router相关信息、记录
  const userStores = useUserStore();
  const routerStores = useStoreRouter();
  const uploadStore = uploadState()
  userStores.reset();
  routerStores.reset();
  uploadStore.reset();
  // 跳转登录
  goPage("/login");
}

// 获取系统配置
function getSystemInfo() {
  const configStores = useStoreConfig();

  configStores.getConfig();
}
// 推出登录清空user与router相关信息、记录
async function clearUserRouterInfo() {
  const userStores = useUserStore();
  const routerStores = useStoreRouter();
  const uploadStore = uploadState()
  await userStores.logout();
  userStores.reset();
  routerStores.reset();
  uploadStore.reset();
}

// 页面跳转
function goPage(to: RouteLocationRaw) {
  router.push(to);
}

// 登录成功事件处理
events.push({
  ename: "loginSuccessful",
  cb: (data: any) => {
    console.log("loginSuccessful");
    initUserInfo();
    // 跳转相应页面
    goPage(data.to || "/");
  },
});

// 新进入页面 事件
events.push({
  ename: "enterPage",
  cb: (data: any) => {
    console.log("enterPage");
    initUserInfo();
    getSystemInfo();
  },
});

// 注销事件
events.push({
  ename: "logout",
  cb: async (data: any) => {
    
    // 推出登录清空user与router相关信息、记录
    await clearUserRouterInfo();
    // socket 断联
    console.log("logout");
    setTimeout(() => {
      socket.off('user.token.invalid')
      socket.close()
    }, 500)
    // 跳转登录
    goPage("/login");
  },
});

export default events;
