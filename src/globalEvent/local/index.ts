// 前端端相关全局事件处理
import { emitter } from "@/utils/mitt"
import loginOut from './loginOut'

export interface IEventRegister {
  ename: string,
  cb: (data:any) => void
}

const allGlobalEvents: IEventRegister[] = [
  ...loginOut
]

export default {
  // 集体订阅
  sub: () => {
    allGlobalEvents.forEach(({ ename, cb }) => {
      emitter.on(ename, cb)
    })
  },
  // 集体解绑
  unsub: () => {
    allGlobalEvents.forEach(({ ename, cb }) => {
      emitter.off(ename, cb)
    })
  }
}