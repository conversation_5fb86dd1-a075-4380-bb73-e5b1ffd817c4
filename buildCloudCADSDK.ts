import { defineConfig } from "vite";
import { resolve } from "path";
import react from '@vitejs/plugin-react'

export default defineConfig((mode) => {
    return {
      publicDir: false,
      plugins: [
        react({
          babel: {
            presets: [
              [
                '@babel/preset-env',
                {
                  targets: 'Chrome 57',
                  modules: false,
                },
              ],
            ],
          },
        })
      ],
        resolve: {
          alias: {
              "@": resolve(__dirname, "src"),
          },
        },
        optimizeDeps: {
            
        },
        build: {
          target: 'es2015',
          emptyOutDir: false,
          // sourcemap: 'inline',
          sourcemap: false,
          lib: {
            entry: `src/app/CloudCAD/GCADCloudCAD.ts`,
            name: 'GCADCloudCAD',
            formats: ['umd'],
            // output filename;
            fileName: "assets/GCADCloudCAD",
          },
          // chunkSizeWarningLimit: 2000,
          // cssCodeSplit: true, //css 拆分
          // sourcemap: false, //不生成sourcemap
          minify: 'terser', //是否禁用最小化混淆，esbuild打包速度最快，terser打包体积最小。
          // assetsInlineLimit: 5000, //小于该值 图片将打包成Base64
          terserOptions: {
              compress: {
                  drop_console: true,
                  drop_debugger: true
              }
          },
          rollupOptions: {
            treeshake: true,
            external: ['crypto-js'],
          },
        },
        define: {
          '__SOCKETIO_MODE__': JSON.stringify('sharedWorker'),
          '__CLIENT_VERSION__': JSON.stringify(process.env.npm_package_version),
        }
    };
});
