@echo off
setlocal enabledelayedexpansion

cd dist
:: 生成当前目录下唯一的临时目录名称（利用 %RANDOM% 变量）
set "TMP_DIR=tmp_keep_%RANDOM%"
mkdir "%TMP_DIR%"

:: 创建临时目录内需要的子目录
mkdir "%TMP_DIR%\assets"
mkdir "%TMP_DIR%\assets\imgs"
mkdir "%TMP_DIR%\static\js"

echo 正在移动允许保留的文件和目录...

:: 移动当前目录下的 cloudCAD.html
if exist "cloudCAD.html" move "cloudCAD.html" "%TMP_DIR%\"

:: assets 下的文件
if exist "assets\CloudCADBoot.umd.js" move "assets\CloudCADBoot.umd.js" "%TMP_DIR%\assets\"
if exist "assets\crypto-js.min.js" move "assets\crypto-js.min.js" "%TMP_DIR%\assets\"
if exist "assets\GCADCloudCAD.umd.js" move "assets\GCADCloudCAD.umd.js" "%TMP_DIR%\assets\"

:: assets\imgs 下的文件
if exist "assets\imgs\loading.png" move "assets\imgs\loading.png" "%TMP_DIR%\assets\imgs\"
if exist "assets\imgs\loading_logo.png" move "assets\imgs\loading_logo.png" "%TMP_DIR%\assets\imgs\"

:: assets\CloudCAD 整个目录（包含所有内容）
if exist "assets\CloudCAD" move "assets\CloudCAD" "%TMP_DIR%\assets\"

:: static\js 下的文件
if exist "static\js\cloudCAD.js" move "static\js\cloudCAD.js" "%TMP_DIR%\static\js\"
if exist "static\js\modulepreload-polyfill.js" move "static\js\modulepreload-polyfill.js" "%TMP_DIR%\static\js\"

echo 正在删除其他文件和目录...
:: 删除当前目录下的所有目录（排除临时目录和本脚本所在目录）
for /d %%i in (*) do (
    if /i not "%%i"=="%TMP_DIR%" (
        rd /s /q "%%i"
    )
)

:: 删除当前目录下的所有文件（排除本脚本文件）
for %%i in (*) do (
    if /i not "%%i"=="%~nx0" (
        if /i not "%%i"=="%TMP_DIR%" (
            del /q "%%i"
        )
    )
)

echo 正在将保留的文件和目录移回当前目录...
:: 将临时目录中的所有内容移回当前目录
for /d %%i in ("%TMP_DIR%\*") do (
    move "%%i" .
)
for %%i in ("%TMP_DIR%\*") do (
    move "%%i" .
)

rd /s /q "%TMP_DIR%"

echo 操作完成。
pause
