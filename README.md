# 简介

浩辰私有云(图纸管理系统)，使用了最新的 `Vue3`、`Vite`、`Element-Plus`、`TypeScript`、`Pinia`、`Tailwindcss` 等主流技术开发

## 文档

-   [Vue3](https://cn.vuejs.org/) (渐进式 JavaScript 框架)
-   [Vite](https://cn.vitejs.dev/) (下一代的前端工具链 为开发提供极速响应)
-   [TypeScript](https://www.typescriptlang.org/docs/) (JavaScript 的类型化超集 静态类型检查器)
-   [Pinia](https://pinia.vuejs.org/zh/index.html) (Vue 的存储库)
-   [Tailwind](https://www.tailwindcss.cn/docs/guides/vue-3-vite) (CSS 框架)
-   [Element-plus](https://element-plus.org/zh-CN/guide/design.html) (基于 Vue 3，面向设计师和开发者的组件库)

## 项目结构

```bash
vue3-src

├── public                     # 公共静态资源（考虑上传到cdn）
├── src                        # 主要源码
│   ├── components             # 全局通用组件
│   ├── config                 # 全局配置
│   ├── directives             # 全局通用指令
│   ├── hooks                  # 全局Hooks (组合式API)
│   ├── locales                # 国际化 (后续添加)
│   ├── model                  # 全部model
│   ├── plugins                # 插件 (element-plus等)
│   ├── router                 # router
│   ├── service                # 服务层（用于处理和其他服务之间的调用，api层放在这）
│   ├── stores                  # 全局store管理（Pinia）
│   ├── styles                 # 全局样式
│   ├── utils                  # 全局工具类
│   ├── views                  # 业务页面
│   ├── App.vue                # 主应用组件
│   ├── main.ts                # 入口
├── tests/                     # tests （后续单元测试存放位置，考虑vite-test）
├── .env.xxx                   # vite env 变量配置
├── .eslintrc.js               # eslint config
├── package.json               # package.json
├── postcss.config.cjs         # postcss config
├── tailwind.config.cjs        # tailwind config
├── tsconfig.json              # typescript config
└── vue.config.js              # vue-cli config
```

## 开发

安装

```bash
npm install

```

启动

```bash
npm run dev
```

打包

```bash
npm run build
```


## socket.io调试注意事项

开发期间将socket对象挂载到全局window上，然后在控制台使用类似如下语句即可订阅相关消息

socket.on('file', (ename, data) => {
    console.log(ename, ' 接受到的信息:', data)
})

取消订阅使用如下语句

socket.off('file')