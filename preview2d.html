<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
  <title>2D CAD览图版</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    #webCAD_2D {
      width: 100%;
      height: calc(100vh - 50px);
    }

    #header-2d {
      height: 50px;
      justify-content: space-between;
      padding-left: 16px;
      padding-right: 16px;
      display: flex;
      background-color: #000;
      color: #fff;
    }

    .header-2d-left {
      font-family: PingFangSC-Regular;
      font-size: 16px;
      height: 100%;
      letter-spacing: 0;
      line-height: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
    }

    .header-2d-right {
      display: flex;
      align-items: center;
      font-size: 24;

      svg {
        width: 1em;
        height: 1em;
        cursor: pointer;
        margin-right: 1rem;
      }
    }

    .wrap2d-layer {

      /* 隐藏布局按钮 */
      .GStarSDK-func-btn-layout {
        display: none !important;
      }

      /* 隐藏图层按钮 */
      .GStarSDK-func-btn-layer {
        display: none !important;
      }

      /* 需要将布局和图层都隐藏时才需要这个 */
      .GStarSDK-pc-toolsbar-floatbar {
        display: none !important;
      }
    }
  </style>
</head>

<body>
  <div id="header-2d">
    <div class="header-2d-left"></div>
    <div class="header-2d-right">
      <svg id="svg-close" data-v-1533ae48="" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
        <path fill="currentColor"
          d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z">
        </path>
      </svg>
    </div>
  </div>
  <div id="webCAD_2D"></div>
  <script type="text/javascript" src="/assets/crypto-js.min.js"></script>
  <script type="text/javascript" src="/assets/GCAD.umd.js"></script>
  <script type="module">
    import { getUserInfo, getQueryObj, errorHandler, getConfig } from '/src/app/common/utils.ts'

    async function start() {
      const userInfo = getUserInfo()
      const token = userInfo.token
      errorHandler(token)
      const query = getQueryObj()
      if (!query.fileId) {
        alert('fileId不能为空')
        return
      }
      const config = await getConfig()
      const titleDom = document.querySelector('.header-2d-left')
      titleDom.innerText = query.fileName

      const closeDom = document.querySelector('#svg-close')
      if (query.hideFunction === 'true') {
        const dom = document.querySelector('#webCAD_2D')
        dom && dom.classList.add('wrap2d-layer')
      }

      // 单人编辑抢占逻辑所需参数
      const cloudCadUtilsParams = {
        serverHost: config?.api_endpoint,
        token: token,
        uid: userInfo.id,
        uname: userInfo.user_name
      }

      var gstarCAD = await window.GCAD({
        vendorcode: token, //必须，开发者的编码.(由服务器指定生成）
        element: "webCAD_2D",  //容器ID，CAD图纸将这个id对应的容器里面显示
        param: {
          // 图纸服务相关参数
          cadServer: config?.api_endpoint, // 可选,浩辰CAD服务器地址。 当不指定是：https://cloudapi.gstarcad.com
          fileDownLoadUrl: '', // 可选（与fileId二选一）待转换文件的下载地址
          fileId: query.fileId, // 可选（与fileDownLoadUrl二选一）此FILEID是开发商服务器向浩辰服务器PUSH文件时返回的文件ID
          fileToken: '', // 可选,文件信息。用于从数据服务器获取文件。
          fileName: query.fileName, // 可选,显示的文件名称。（该值会参与文件校验）
          etag: query.etag, // 可选,文件的Etag标识数据。（该值会参与文件校验）
          version: query.version, // 可选,文件版本,默认V1
          size: '', // 可选,文件的大小。（该值会参与文件校验）
          userToken: '', // 可选,用户的信息。用于从数据服务器获取文件。（该值不会参与文件校验）
          isXRef: query.isXRef,
          hideFunction: query.hideFunction,
          // 支持GStarSDK实例化构造函数的其他属性,参见其他样例。例：语言配置
          language: 'zh', // GStarSDK属性，设置语言为简中
        }
      })
      closeDom.addEventListener('click', () => {
        gstarCAD && gstarCAD._destroy()
        window.close()
      })

      // 添加弹框，暂只用于点击编辑
      const dialog = document.createElement('vaadin-dialog')
      let dialogText = 'test'
      let dialogCancelText = '取消'
      let dialogOkText = ''
      let handleDialogConfirm = () => {}
      let handleDialogCancel = () => {
        dialog.opened = false
      }

      dialog.noCloseOnEsc = true
      dialog.renderer = (root) => {
        if (!root.firstElementChild) {
          const container = document.createElement('div');
          container.classList.add('dialog-container');
          container.innerHTML = `
            <style>
              .dialog-container {
                border-box: box-sizing;
              }
              .dialog-text {
                height: 50px;
              }

              .dialog-btn-container {
                display: flex;
                justify-content: flex-end;
                grid-gap: 10px;
              }
            </style>
            <div class="dialog-text">
              <p class="dialog-text">${dialogText}</p>
            </div>
            <div class="dialog-btn-container">
            ${dialogCancelText ? `<vaadin-button theme="error" id="cancel-btn">${dialogCancelText}</vaadin-button>` : ""}
            ${dialogOkText ? `<vaadin-button theme="primary" id="confirm-btn">${dialogOkText}</vaadin-button>` : ""}
            </div>
            `;
          root.appendChild(container);

          // 添加按钮点击事件
          const confirmBtn = dialogOkText ? container.querySelector('#confirm-btn') : null;
          const cancelBtn = dialogCancelText ? container.querySelector('#cancel-btn') : null;

          confirmBtn?.addEventListener('click', () => {
            handleDialogConfirm()
          });

          cancelBtn?.addEventListener('click', () => {
            handleDialogCancel()
          });
        }
      };
      dialog.noCloseOnOutsideClick = true
      document.body.appendChild(dialog)
      // 单人编辑抢占逻辑
      const cloudCadUtils = new gstarCAD.CloudCadUtils(cloudCadUtilsParams)
      console.log('cloudCadUtils', cloudCadUtils)
      // 点击编辑
      gstarSDK.on('functionTrigger', (msg, funcName) => {
        if (funcName === 'edit') {
          console.log('点击了编辑')
          let client_id = localStorage.getItem('client_id') || '';
          let browser_id = localStorage.getItem('gstarDeviceId') || '';
          // 判断是否可以进入编辑
          fetch(`${config?.api_endpoint}/api/v2/browserid/_count?client_id=${client_id}&browser_id=${browser_id}`, {
            method: 'GET',
            headers: {
              Authorization: token
            }
          })
          .then(res => res.json())
          .then(({ data }) => {
            console.log('设备数量信息', data)
            if (!data.allow_entry) {
              return Promise.reject('已达到当前时段最大用户链接数')
            }
          })
          // 判断当前图纸是否有正在编辑
          .then(() => {
            console.log('进入请求编辑流程', cloudCadUtils)
            cloudCadUtils.requestEditDrawing({
              enableHeader: true,
              fileId: Number(query.fileId)
            }, true)
            console.log('end 进入请求编辑流程')
          })
          .catch(err => {
            dialogText = err
            dialog.opened = true
            console.error(err)
          })
        } 
      })
    }
    start()
  </script>
</body>

</html>