<!DOCTYPE html>
<html>
<head>
    <title>Text Styles Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-item {
            margin-bottom: 10px;
            padding: 8px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        .language-switch {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .language-switch button {
            margin-left: 10px;
            padding: 8px 16px;
            background: #0052CC;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .language-switch button:hover {
            background: #003d99;
        }
        .current-lang {
            font-weight: bold;
            color: #0052CC;
        }
    </style>
</head>
<body>
    <div class="language-switch">
        <span>当前语言: <span id="current-language" class="current-lang">中文</span></span>
        <button onclick="switchToEnglish()">Switch to English</button>
        <button onclick="switchToChinese()">切换到中文</button>
    </div>

    <h1>文本样式和布局测试页面</h1>
    
    <div class="test-section">
        <div class="test-title">1. 顶部水平菜单文本测试 (horizontal-header-menu)</div>
        <div class="test-item">
            <strong>修复内容:</strong> 为顶部菜单项添加了最大宽度120px和省略号处理
        </div>
        <div class="test-item">
            <strong>测试方法:</strong> 访问 http://localhost:2023/files/file 查看顶部菜单项
        </div>
        <div class="test-item">
            <strong>预期效果:</strong> 长文本会显示省略号，鼠标悬停显示完整文本
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">2. 左侧导航菜单文本测试 (layout-nav)</div>
        <div class="test-item">
            <strong>修复内容:</strong> 为导航菜单项添加了最大宽度120px和省略号处理
        </div>
        <div class="test-item">
            <strong>测试方法:</strong> 查看左侧导航菜单中的菜单项
        </div>
        <div class="test-item">
            <strong>预期效果:</strong> 长文本会显示省略号，鼠标悬停显示完整文本
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">3. 分页组件文本测试 (next-page)</div>
        <div class="test-item">
            <strong>修复内容:</strong> 为分页按钮添加了最大宽度80px和省略号处理
        </div>
        <div class="test-item">
            <strong>测试方法:</strong> 查看页面底部的分页组件
        </div>
        <div class="test-item">
            <strong>预期效果:</strong> "上一页"、"下一页"按钮文本过长时显示省略号
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">4. Flex布局换行间距测试</div>
        <div class="test-item">
            <strong>修复内容:</strong> 为flex容器添加了gap属性，解决换行元素间距问题
        </div>
        <div class="test-item">
            <strong>测试方法:</strong> 缩小浏览器窗口宽度，观察按钮换行后的间距
        </div>
        <div class="test-item">
            <strong>预期效果:</strong> 换行后的元素有适当的上下间距(8px)和左右间距(12px)
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">5. 子元素对齐问题修复</div>
        <div class="test-item">
            <strong>修复内容:</strong> 修复了/files/file页面中.items-center元素的子元素对齐问题
        </div>
        <div class="test-item">
            <strong>具体修复:</strong> 统一了上传按钮组和操作按钮组的容器样式，都使用.button-group类
        </div>
        <div class="test-item">
            <strong>测试方法:</strong> 访问文件管理页面，观察操作区域的两个按钮组是否垂直对齐
        </div>
        <div class="test-item">
            <strong>预期效果:</strong> 上传按钮组和删除/移动按钮组在垂直方向上完美对齐
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">6. 省略文本悬停显示功能</div>
        <div class="test-item">
            <strong>修复内容:</strong> 为所有省略文本添加了悬停显示完整内容的功能
        </div>
        <div class="test-item">
            <strong>具体修复:</strong>
            <ul>
                <li>移除了光标样式改变，只使用原生title属性</li>
                <li>为所有按钮添加了:title属性，确保按钮文本可以悬停显示</li>
                <li>为表单标签添加了title属性</li>
                <li>为文件名等关键文本添加了v-ellipsis指令</li>
            </ul>
        </div>
        <div class="test-item">
            <strong>测试方法:</strong> 将鼠标悬停在被省略的文本或按钮上
        </div>
        <div class="test-item">
            <strong>预期效果:</strong> 显示完整的文本内容tooltip，无光标样式变化
        </div>
        <div class="test-item">
            <strong>修复的页面:</strong>
            <ul>
                <li>/files/file - 查询、重置、新建文件、新建文件夹、删除、移动按钮</li>
                <li>/customer/user - 创建用户、添加用户到用户组按钮</li>
                <li>/customer/group - 添加用户组按钮</li>
                <li>/files/recycled - 永久删除、还原按钮</li>
                <li>/system/log - 清空按钮</li>
                <li>/system/thirdPartyLog - 清空按钮</li>
                <li>/system/thirdParty - 添加三方、确认按钮</li>
                <li>/system/task - 提交、重置按钮</li>
                <li>权限视图组件 - 编辑权限、添加、保存所有、取消、全选、清空按钮</li>
                <li>上传组件 - 上传按钮</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">7. 表单标签超出宽度问题修复</div>
        <div class="test-item">
            <strong>修复内容:</strong> 修复了表单标签超出宽度导致的显示错乱问题
        </div>
        <div class="test-item">
            <strong>具体修复:</strong>
            <ul>
                <li>增加了GList组件的标签宽度从85px到120px</li>
                <li>增加了系统基本信息页面的标签宽度从120px到160px</li>
                <li>为表单标签添加了省略号和title属性</li>
                <li>创建了灵活的标签宽度样式系统</li>
            </ul>
        </div>
        <div class="test-item">
            <strong>测试方法:</strong> 访问/files/template、/files/font、/system/basic等页面
        </div>
        <div class="test-item">
            <strong>预期效果:</strong> 表单标签不再超出容器，长标签显示省略号并可悬停查看完整内容
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">5. 按钮和表单文本测试</div>
        <div class="test-item">
            <strong>修复内容:</strong> 为按钮文本添加了最大宽度120px，表单标签100px
        </div>
        <div class="test-item">
            <strong>测试方法:</strong> 查看搜索区域的按钮和表单标签
        </div>
        <div class="test-item">
            <strong>预期效果:</strong> 长文本会显示省略号，保持布局整洁
        </div>
    </div>

    <h2>测试步骤:</h2>
    <ol>
        <li>访问 <a href="http://localhost:2023/files/file" target="_blank">http://localhost:2023/files/file</a></li>
        <li>点击右上角的语言切换按钮切换到英文</li>
        <li>观察各个区域的文本显示是否正常</li>
        <li>缩小浏览器窗口测试响应式布局</li>
        <li>检查换行后的元素间距是否合适</li>
    </ol>

    <h2>CSS类说明:</h2>
    <ul>
        <li><code>.header-menu-text</code> - 顶部菜单文本样式 (max-width: 120px)</li>
        <li><code>.nav-menu-text</code> - 导航菜单文本样式 (max-width: 120px)</li>
        <li><code>.pagination-btn-text</code> - 分页按钮文本样式 (max-width: 80px)</li>
        <li><code>.btn-text</code> - 通用按钮文本样式 (max-width: 120px)</li>
        <li><code>.form-label-text</code> - 表单标签文本样式 (max-width: 100px)</li>
        <li><code>.action-text</code> - 操作按钮文本样式 (max-width: 80px)</li>
        <li><code>.table-cell-text</code> - 表格单元格文本样式</li>
        <li><code>.table-cell-filename</code> - 表格文件名样式 (max-width: 300px)</li>
        <li><code>.layout-flex</code> - 统一的flex布局容器</li>
        <li><code>.button-group</code> - 按钮组容器，统一间距</li>
    </ul>

    <h2>已处理的页面:</h2>
    <ul>
        <li>✅ <strong>文件管理模块</strong>：文件管理、模板管理、字体管理、材质管理、零件管理、回收站</li>
        <li>✅ <strong>用户管理模块</strong>：用户管理、用户组管理、权限策略、文件权限管理</li>
        <li>✅ <strong>系统设置模块</strong>：基本信息、任务设置、备份恢复、三方接入、系统日志、三方访问日志</li>
        <li>✅ <strong>登录页面</strong>：登录按钮</li>
        <li>✅ <strong>全局组件</strong>：
            <ul>
                <li>顶部水平菜单 (header.vue)</li>
                <li>左侧导航菜单 (nav.vue)</li>
                <li>分页组件 (Pagination.vue)</li>
                <li>上传组件 (UploadFile.vue)</li>
                <li>文件块组件 (FileItemBlock.vue)</li>
                <li>通用列表组件 (GList.vue)</li>
                <li>菜单列表组件 (MenuList.vue)</li>
                <li>通用表单组件 (CreateComForm.vue)</li>
                <li>权限视图组件 (permissionView.vue)</li>
            </ul>
        </li>
    </ul>

    <h2>修复的问题:</h2>
    <ul>
        <li>✅ 统一了所有flex布局的换行间距</li>
        <li>✅ 处理了所有过长文本的显示问题</li>
        <li>✅ 创建了统一的CSS变量系统</li>
        <li>✅ 建立了可复用的样式类库</li>
        <li>✅ 确保了国际化兼容性</li>
        <li>✅ <strong>新增：为省略文本添加了悬停显示功能</strong></li>
        <li>✅ <strong>新增：修复了表单标签超出宽度问题</strong></li>
        <li>✅ <strong>新增：修复了子元素对齐问题</strong></li>
    </ul>

    <script>
        // 显示当前语言
        function updateCurrentLanguage() {
            const localeStore = localStorage.getItem('locale-store');
            const currentLang = document.getElementById('current-language');
            if (localeStore) {
                const parsed = JSON.parse(localeStore);
                currentLang.textContent = parsed.locale === 'en' ? 'English' : '中文';
            } else {
                currentLang.textContent = '中文';
            }
        }
        
        function switchToEnglish() {
            localStorage.setItem('locale-store', JSON.stringify({locale: 'en'}));
            updateCurrentLanguage();
            alert('已切换到英文，请刷新页面查看效果');
        }
        
        function switchToChinese() {
            localStorage.setItem('locale-store', JSON.stringify({locale: 'zh_CN'}));
            updateCurrentLanguage();
            alert('已切换到中文，请刷新页面查看效果');
        }
        
        // 页面加载时更新当前语言显示
        updateCurrentLanguage();
    </script>
</body>
</html>
